<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.weihengtech</groupId>
    <artifactId>ecos-hub-server</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <modules>
        <module>hub-application</module>
        <module>hub-common</module>
        <module>hub-core</module>
        <module>hub-web</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.8.RELEASE</version>
        <relativePath/>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring.boot.versoin>2.3.8.RELEASE</spring.boot.versoin>
        <hystrix.version>2.2.7.RELEASE</hystrix.version>
        <mybatis.starter.version>2.1.2</mybatis.starter.version>
        <mybatis.version>3.5.2</mybatis.version>
        <mysql.connector.version>8.0.22</mysql.connector.version>
        <mybatis.plus.starter.version>3.2.0</mybatis.plus.starter.version>
        <mybatis.plus.generator.version>2.0</mybatis.plus.generator.version>
        <pagehelper.version>1.2.3</pagehelper.version>
        <druid.version>1.2.9</druid.version>
        <dynamic.datasource.version>3.4.1</dynamic.datasource.version>
        <lindorm.java.native.version>1.0.0</lindorm.java.native.version>
        <aliyun.sdk.oss.version>3.16.0</aliyun.sdk.oss.version>
        <iot20180120.version>3.0.0</iot20180120.version>
        <tea.openapi.version>0.0.19</tea.openapi.version>
        <tsdb.version>0.3.6</tsdb.version>
        <ecos.iot.sdk.version>8.0.2.0-SNAPSHOT</ecos.iot.sdk.version>
        <redisson.version>3.16.8</redisson.version>
        <prometheus.version>1.9.3</prometheus.version>
        <xxl.version>2.3.1</xxl.version>
        <cglib.version>3.2.7</cglib.version>
        <knife4j.version>3.0.3</knife4j.version>
        <wh.auth.version>8.0.0.2-SNAPSHOT</wh.auth.version>
        <influx.client.version>8.0.0.2-SNAPSHOT</influx.client.version>
        <easy.excel.version>4.0.1</easy.excel.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Framework -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring.boot.versoin}</version>
                <!-- 排除掉默认的日志框架 -->
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
                <version>${hystrix.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                <version>${hystrix.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring.boot.versoin}</version>
            </dependency>

            <!-- spring 相关组件 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring.boot.versoin}</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.versoin}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>${spring.boot.versoin}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${spring.boot.versoin}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-log4j2</artifactId>
                <version>${spring.boot.versoin}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>${spring-retry.version}</version>
            </dependency>

            <!-- 数据库相关 -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.baomidou</groupId>
                        <artifactId>mybatis-plus-generator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis.plus.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${mybatis.plus.generator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic.datasource.version}</version>
            </dependency>

            <!-- sdk -->
            <dependency>
                <groupId>com.aliyun.lindorm</groupId>
                <artifactId>lindorm-tsdb-client</artifactId>
                <version>${lindorm.java.native.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun.sdk.oss.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>iot20180120</artifactId>
                <version>${iot20180120.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>tea-openapi</artifactId>
                <version>${tea.openapi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>hitsdb-client</artifactId>
                <version>${tsdb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.weihengtech.lib</groupId>
                <artifactId>ecos-iot-sdk</artifactId>
                <version>${ecos.iot.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.weihengtech.influx</groupId>
                <artifactId>influx-client</artifactId>
                <version>${influx.client.version}</version>
                <classifier>plain</classifier>
            </dependency>

            <!-- redisson 客户端 -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- 单元测试 -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit-jupiter.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- 监控 -->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>${prometheus.version}</version>
            </dependency>

            <!-- xxl job -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl.version}</version>
            </dependency>

            <!-- cglib -->
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>${cglib.version}</version>
            </dependency>

            <!-- json -->
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-kotlin</artifactId>
                <version>${jackson-bom.version}</version>
            </dependency>

            <!-- Swagger -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- wh-auth -->
            <dependency>
                <groupId>com.weihengtech</groupId>
                <artifactId>auth-resource</artifactId>
                <version>${wh.auth.version}</version>
            </dependency>
            <!-- easyexcel -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easy.excel.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>1.4.13</version>
                <configuration>
                    <repository>${project.artifactId}</repository>
                    <tag>latest</tag>
                    <buildArgs>
                        <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                    </buildArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>