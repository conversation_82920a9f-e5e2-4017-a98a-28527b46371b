{"modules": {"config": {"DeviceInformation": [30001, 30025], "ARMInformation": [30042, 30098, 30107, 30147, 32071, 32080, 32095, 32095, 34059, 34062], "US_ARMInformation": [30042, 30098, 30107, 30147, 32071, 32080, 32095, 32095, 34059, 34062, 34204, 34214], "ARMOLDInformation": [30042, 30098, 32071, 32080], "RunningStatus": [30026, 30041], "DC_Parameters": [31001, 31011, 31012, 31013, 31034, 31035], "AC_Parameters": [31014, 31033], "Single_AC_Parameters": [31014, 31018, 31029, 31033], "Us_AC_Parameters": [31014, 31023, 31029, 31033], "Meter_Parameter": [33001, 33017], "Single_Meter_Parameter": [33001, 33004, 33013, 33017], "Us_Meter_Parameter": [33001, 33008, 33013, 33017], "EPS_Parameter": [32001, 32018, 33003, 33011], "Single_EPS_Parameter": [32001, 32006, 33003, 33003], "Us_EPS_Parameter": [32001, 32012], "Battery_Parameter": [32019, 32035, 32040, 32088], "Battery_Parameter_1": [32123, 32135, 32140, 32188], "Battery_Parameter_2": [32223, 32235, 32240, 32288], "Battery_Parameter_3": [32323, 32335, 32340, 32388], "Battery_Parameter_4": [32423, 32435, 32440, 32488], "Battery_Parameter_5": [32523, 32535, 32540, 32588], "Battery_Parameter_6": [32623, 32635, 32640, 32688], "Battery_Parameter_7": [32723, 32735, 32740, 32788], "Battery_Parameter_8": [32823, 32835, 32840, 32888], "WeiHengBatteryInfo": [0, 63, 64, 98, 100, 143, 200, 231, 400, 415], "WeiHengBatteryInternalData": [10035, 10060], "SafetyParameter": [44001, 44001]}, "settings": {"EnergySetting": [32081, 32084, 33032, 33033, 34001, 34024], "BasicInformation": [40001, 40035], "BatterySetting": [40046, 40046, 41002, 41042, 41043, 41106, 41107, 41142, 41143, 41166], "Execute": [42001, 42018], "AutoTest": [34027, 34058, 42016, 42018, 44008, 44036], "AutoTestThree": [34027, 34058, 34063, 34094, 34095, 34126, 42016, 42018, 44008, 44036], "DspCalibration": [43001, 43019, 43029, 43032], "SafetyParameter": [44001, 44045, 46011, 46011], "ProtectFunctionEnable": [40049, 40050, 40067, 40067, 45001, 45035, 41036, 41040, 40038, 40044, 48120, 48121], "ProtectFunctionEnableMeter": [40038, 40044], "ActiveLimit": [46001, 46004], "ReactiveLimit": [47001, 47021], "Setting": [46005, 46042], "EmsArmSetting": [48003, 48020, 40045, 40045], "EmsArmSettingBatter": [42064, 42066, 48003, 48020, 40045, 40048], "ParameterSetting": [40049, 40050, 40067, 40067, 42001, 42014, 41036, 41040, 40038, 40044, 48016, 48016, 48120, 48121], "WeiHengBatteryInfoSetting": [150, 157], "PvEnableSetting": [42019, 42019], "AcMeterType": [40043, 40043]}}, "config": {"DeviceInformation": {"InverterType": {"start": 30001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDeviceType", "unit": "", "multiply": 1.0, "prop": "InverterType"}, "DSP1_Version": {"start": 30002, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "sDSP1_ver", "unit": "", "multiply": 1.0, "prop": "Dsp1Version"}, "DSP2_Version": {"start": 30010, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "sDSP2_ver", "unit": "", "multiply": 1.0, "prop": "Dsp2Version"}, "PowerBoardHardwareVersion": {"start": 30018, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "dwHardWare_Version", "unit": "", "multiply": 1.0, "prop": "PowerBoardHardwareVersion"}}, "ARMInformation": {"DeviceSn": {"start": 30042, "len": 10, "slaveId": 1, "type": "ASCII", "alias": "sSerialNumber", "unit": "", "multiply": 1.0, "prop": "DeviceSn"}, "Model_Name": {"start": 30052, "len": 9, "slaveId": 1, "type": "ASCII", "alias": "sModelName", "unit": "", "multiply": 1.0, "prop": "ModelName"}, "Brand": {"start": 30061, "len": 6, "slaveId": 1, "type": "ASCII", "alias": "sBrandName", "unit": "", "prop": "Brand"}, "Factory": {"start": 30067, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "sManufacturer", "unit": "", "multiply": 1.0, "prop": "Factory"}, "EmsSubVersion": {"start": 30075, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "EmsSubVersion", "unit": "", "multiply": 1.0, "prop": "EmsSubVersion"}, "BMS_Software_Version": {"start": 30083, "len": 16, "slaveId": 1, "type": "ASCII", "alias": "sBMS_vers", "unit": "", "multiply": 1.0, "prop": "BmsSoftwareVersion"}, "EmsHardwareVersion": {"start": 30107, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "", "unit": "", "multiply": 1.0, "prop": "EmsHardwareVersion"}, "GaugeVersion": {"start": 30115, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "sZ100_ver", "unit": "", "multiply": 1.0, "prop": "GaugeVersion"}, "BmsHardwareVersion": {"start": 30123, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "sBMS_hw_version", "unit": "", "multiply": 1.0, "prop": "BmsHardwareVersion"}, "BmsManufacturer": {"start": 30131, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "sBMS_Manufacturer", "unit": "", "multiply": 1.0, "prop": "BmsManufacturer"}, "EMS_Version": {"start": 30139, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "EMS_ver(ARM)", "unit": "", "multiply": 1.0, "prop": "EmsSoftwareVersion"}, "AteUsbStatus": {"start": 30147, "len": 1, "slaveId": 1, "type": "U16", "alias": "AteUsbOK", "unit": "", "multiply": 1.0, "prop": "AteUsbStatus"}, "Dsp1SubVersion1": {"start": 34059, "len": 1, "slaveId": 1, "type": "U16", "alias": "Dsp1SubVersion", "unit": "", "multiply": 1.0, "prop": "Dsp1SubVersion"}, "Dsp1SubVersion2": {"start": 34060, "len": 1, "slaveId": 1, "type": "U16", "alias": "Dsp1SubVersion", "unit": "", "multiply": 1.0, "prop": "Dsp1SubVersion"}, "Dsp2SubVersion1": {"start": 34061, "len": 1, "slaveId": 1, "type": "U16", "alias": "Dsp2SubVersion", "unit": "", "multiply": 1.0, "prop": "Dsp2SubVersion"}, "Dsp2SubVersion2": {"start": 34062, "len": 1, "slaveId": 1, "type": "U16", "alias": "Dsp2SubVersion", "unit": "", "multiply": 1.0, "prop": "Dsp2SubVersion"}, "BmsSn": {"start": 32071, "len": 10, "slaveId": 1, "type": "ASCII", "alias": "BatSerialNumber", "unit": "", "multiply": 1.0, "prop": "BmsSn"}, "ParallelNumber": {"start": 32095, "len": 1, "slaveId": 1, "type": "U16", "alias": "parallel_num", "unit": "", "multiply": 1.0, "prop": "ParallelNumber"}}, "US_ARMInformation": {"DeviceSn": {"start": 30042, "len": 10, "slaveId": 1, "type": "ASCII", "alias": "sSerialNumber", "unit": "", "multiply": 1.0, "prop": "DeviceSn"}, "Model_Name": {"start": 30052, "len": 9, "slaveId": 1, "type": "ASCII", "alias": "sModelName", "unit": "", "multiply": 1.0, "prop": "ModelName"}, "Brand": {"start": 30061, "len": 6, "slaveId": 1, "type": "ASCII", "alias": "sBrandName", "unit": "", "prop": "Brand"}, "Factory": {"start": 30067, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "sManufacturer", "unit": "", "multiply": 1.0, "prop": "Factory"}, "EmsSubVersion": {"start": 30075, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "EmsSubVersion", "unit": "", "multiply": 1.0, "prop": "EmsSubVersion"}, "BMS_Software_Version": {"start": 30083, "len": 16, "slaveId": 1, "type": "ASCII", "alias": "sBMS_vers", "unit": "", "multiply": 1.0, "prop": "BmsSoftwareVersion"}, "EmsHardwareVersion": {"start": 30107, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "", "unit": "", "multiply": 1.0, "prop": "EmsHardwareVersion"}, "GaugeVersion": {"start": 30115, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "sZ100_ver", "unit": "", "multiply": 1.0, "prop": "GaugeVersion"}, "BmsHardwareVersion": {"start": 30123, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "sBMS_hw_version", "unit": "", "multiply": 1.0, "prop": "BmsHardwareVersion"}, "BmsManufacturer": {"start": 30131, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "sBMS_Manufacturer", "unit": "", "multiply": 1.0, "prop": "BmsManufacturer"}, "EMS_Version": {"start": 30139, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "EMS_ver(ARM)", "unit": "", "multiply": 1.0, "prop": "EmsSoftwareVersion"}, "AteUsbStatus": {"start": 30147, "len": 1, "slaveId": 1, "type": "U16", "alias": "AteUsbOK", "unit": "", "multiply": 1.0, "prop": "AteUsbStatus"}, "Dsp1SubVersion1": {"start": 34059, "len": 1, "slaveId": 1, "type": "U16", "alias": "Dsp1SubVersion", "unit": "", "multiply": 1.0, "prop": "Dsp1SubVersion"}, "Dsp1SubVersion2": {"start": 34060, "len": 1, "slaveId": 1, "type": "U16", "alias": "Dsp1SubVersion", "unit": "", "multiply": 1.0, "prop": "Dsp1SubVersion"}, "Dsp2SubVersion1": {"start": 34061, "len": 1, "slaveId": 1, "type": "U16", "alias": "Dsp2SubVersion", "unit": "", "multiply": 1.0, "prop": "Dsp2SubVersion"}, "Dsp2SubVersion2": {"start": 34062, "len": 1, "slaveId": 1, "type": "U16", "alias": "Dsp2SubVersion", "unit": "", "multiply": 1.0, "prop": "Dsp2SubVersion"}, "BmsSn": {"start": 32071, "len": 10, "slaveId": 1, "type": "ASCII", "alias": "BatSerialNumber", "unit": "", "multiply": 1.0, "prop": "BmsSn"}, "ParallelNumber": {"start": 32095, "len": 1, "slaveId": 1, "type": "U16", "alias": "parallel_num", "unit": "", "multiply": 1.0, "prop": "ParallelNumber"}, "ArcDspSoftwareVersion": {"start": 34204, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "sDSPArc_ver", "unit": "", "multiply": 1.0, "prop": "ArcDspSoftwareVersion"}, "ArcDspSubVersion": {"start": 34211, "len": 2, "slaveId": 1, "type": "U16", "alias": "sDSPArc_Subversion", "unit": "", "multiply": 1.0, "prop": "ArcDspSubVersion"}, "ArcDspBootLoaderSoftwareVersion": {"start": 34213, "len": 2, "slaveId": 1, "type": "U16", "alias": "sDSPArc_CpuBootVer", "unit": "", "multiply": 1.0, "prop": "ArcDspBootLoaderSoftwareVersion"}}, "RunningStatus": {"Run_Mode": {"start": 30026, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwRunMode", "unit": "", "multiply": 1.0, "prop": "RunMode"}, "Wait_Time": {"start": 30027, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwWaitTime", "unit": "s", "multiply": 1.0, "prop": "WaitTime"}, "Error1": {"start": 30028, "len": 2, "slaveId": 1, "type": "BIT", "alias": "dwError1", "unit": "", "multiply": 1.0, "prop": "Error1"}, "Error2": {"start": 30030, "len": 2, "slaveId": 1, "type": "BIT", "alias": "dwError2", "unit": "", "multiply": 1.0, "prop": "Error2"}, "Error3": {"start": 30032, "len": 2, "slaveId": 1, "type": "BIT", "alias": "dwError3", "unit": "", "multiply": 1.0, "prop": "Error3"}, "Inverter_Sink_Temp": {"start": 30034, "len": 1, "slaveId": 1, "type": "S16", "alias": "Temperature of Inverter sink", "unit": "℃", "multiply": 10.0, "prop": "InverterSinkTemp"}, "BatterySinkTemp": {"start": 30035, "len": 1, "slaveId": 1, "type": "S16", "alias": "Temperature of buck bosst sink", "unit": "℃", "multiply": 10.0, "prop": "BatterySinkTemp"}, "AirTemp": {"start": 30036, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwAir_Temp", "unit": "℃", "multiply": 10.0, "prop": "AirTemp"}, "CapTemp": {"start": 30037, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCAP_Temp", "unit": "℃", "multiply": 10.0, "prop": "CapTemp"}, "InverterErrorCode": {"start": 30038, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwErrCode_DSP", "unit": "", "multiply": 1.0, "prop": "InverterErrorCode"}, "BatteryErrorCode": {"start": 30039, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwErrCode_BAT", "unit": "", "multiply": 1.0, "prop": "BatteryErrorCode"}, "ArmErrorCode": {"start": 30040, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwErrCode_EMS", "unit": "", "multiply": 1.0, "prop": "ArmErrorCode"}}, "DC_Parameters": {"DcvBus": {"start": 31001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVbus", "unit": "V", "multiply": 10.0, "prop": "DcvBus"}, "DcvPv1": {"start": 31002, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVpv1", "unit": "V", "multiply": 10.0, "prop": "DcvPv1"}, "DciPv1": {"start": 31003, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwIpv1", "unit": "A", "multiply": 10.0, "prop": "DciPv1"}, "DcvPv2": {"start": 31004, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVpv2", "unit": "V", "multiply": 10.0, "prop": "DcvPv2"}, "DciPv2": {"start": 31005, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwIpv2", "unit": "A", "multiply": 10.0, "prop": "DciPv2"}, "DcvPv3": {"start": 31006, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVpv3", "unit": "V", "multiply": 10.0, "prop": "DcvPv3"}, "DciPv3": {"start": 31007, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwIpv3", "unit": "A", "multiply": 10.0, "prop": "DciPv3"}, "DcvPv4": {"start": 31008, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVpv4", "unit": "A", "multiply": 10.0, "prop": "DcvPv4"}, "DciPv4": {"start": 31009, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwIpv4", "unit": "A", "multiply": 10.0, "prop": "DciPv4"}, "DcpPv1": {"start": 31010, "len": 1, "slaveId": 1, "type": "U16", "alias": "Pv Power 1", "unit": "W", "multiply": 1.0, "prop": "DcpPv1"}, "DcpPv2": {"start": 31011, "len": 1, "slaveId": 1, "type": "U16", "alias": "Pv Power 2", "unit": "W", "multiply": 1.0, "prop": "DcpPv2"}, "DcpPv3": {"start": 31012, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPpv3", "unit": "W", "multiply": 10.0, "prop": "DcpPv3"}, "DcpPv4": {"start": 31013, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPpv4", "unit": "W", "multiply": 10.0, "prop": "DcpPv4"}, "DcvBusP": {"start": 31034, "len": 1, "slaveId": 1, "type": "U16", "alias": "BusP voltage", "unit": "V", "multiply": 10.0, "prop": "DcvBusP"}, "DcvBusM": {"start": 31035, "len": 1, "slaveId": 1, "type": "U16", "alias": "BusM voltage", "unit": "V", "multiply": 10.0, "prop": "DcvBusM"}}, "AC_Parameters": {"AcVacR": {"start": 31014, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_R(1 Phase)", "unit": "V", "multiply": 10.0, "prop": "AcVacR"}, "AcIacR": {"start": 31015, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwIac_R(1 Phase)", "unit": "A", "multiply": 10.0, "prop": "AcIacR"}, "AcFreqR": {"start": 31016, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_R", "unit": "Hz", "multiply": 100.0, "prop": "AcFreqR"}, "AcPacR": {"start": 31017, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwPac_R", "unit": "W", "multiply": 1.0, "prop": "AcPacR"}, "AcVacS": {"start": 31019, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_S(3 Phase)", "unit": "V", "multiply": 10.0, "prop": "AcVacS"}, "AcIacS": {"start": 31020, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_S(3 Phase)", "unit": "A", "multiply": 10.0, "prop": "AcIacS"}, "AcFreqS": {"start": 31021, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_S", "unit": "Hz", "multiply": 100.0, "prop": "AcFreqS"}, "AcPacS": {"start": 31022, "len": 2, "slaveId": 1, "type": "S32", "alias": "Pac_S", "unit": "W", "multiply": 1.0, "prop": "AcPacS"}, "AcVacT": {"start": 31024, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_T(3 Phase)", "unit": "V", "multiply": 10.0, "prop": "AcVacT"}, "AcIacT": {"start": 31025, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwIac_T(3 Phase)", "unit": "A", "multiply": 10.0, "prop": "AcIacT"}, "AcFreqT": {"start": 31026, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_T", "unit": "Hz", "multiply": 100.0, "prop": "AcFreqT"}, "AcPacT": {"start": 31027, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwPac_T", "unit": "W", "multiply": 1.0, "prop": "AcPacT"}, "Power": {"start": 31029, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwPower", "unit": "W", "multiply": 1.0, "prop": "Power"}, "ReactivePower": {"start": 31031, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwReactivePwr", "unit": "VA", "multiply": 1.0, "prop": "ReactivePower"}, "Pf": {"start": 31033, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwPwrFactor", "unit": "", "multiply": 100.0, "prop": "Pf"}}, "Single_AC_Parameters": {"AcVacR": {"start": 31014, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_R(1 Phase)", "unit": "V", "multiply": 10.0, "prop": "AcVacR"}, "AcIacR": {"start": 31015, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwIac_R(1 Phase)", "unit": "A", "multiply": 10.0, "prop": "AcIacR"}, "AcFreqR": {"start": 31016, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_R", "unit": "Hz", "multiply": 100.0, "prop": "AcFreqR"}, "AcPacR": {"start": 31017, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwPac_R", "unit": "W", "multiply": 1.0, "prop": "AcPacR"}, "Power": {"start": 31029, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwPower", "unit": "W", "multiply": 1.0, "prop": "Power"}, "ReactivePower": {"start": 31031, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwReactivePwr", "unit": "VA", "multiply": 1.0, "prop": "ReactivePower"}, "Pf": {"start": 31033, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwPwrFactor", "unit": "", "multiply": 100.0, "prop": "Pf"}}, "Us_AC_Parameters": {"AcVacL1": {"start": 31014, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_R(1 Phase)", "unit": "V", "multiply": 10.0, "prop": "AcVacL1"}, "AcIacL1": {"start": 31015, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwIac_R(1 Phase)", "unit": "A", "multiply": 10.0, "prop": "AcIacL1"}, "AcFreqL1": {"start": 31016, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_R", "unit": "Hz", "multiply": 100.0, "prop": "AcFreqL1"}, "AcPacL1": {"start": 31017, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwPac_R", "unit": "W", "multiply": 1.0, "prop": "AcPacL1"}, "AcVacL2": {"start": 31019, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_S(3 Phase)", "unit": "V", "multiply": 10.0, "prop": "AcVacL2"}, "AcIacL2": {"start": 31020, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwIac_S(3 Phase)", "unit": "A", "multiply": 10.0, "prop": "AcIacL2"}, "AcFreqL2": {"start": 31021, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_S", "unit": "Hz", "multiply": 100.0, "prop": "AcFreqL2"}, "AcPacL2": {"start": 31022, "len": 2, "slaveId": 1, "type": "S32", "alias": "Pac_S", "unit": "W", "multiply": 1.0, "prop": "AcPacL2"}, "Power": {"start": 31029, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwPower", "unit": "W", "multiply": 1.0, "prop": "Power"}, "ReactivePower": {"start": 31031, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwReactivePwr", "unit": "VA", "multiply": 1.0, "prop": "ReactivePower"}, "Pf": {"start": 31033, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwPwrFactor", "unit": "", "multiply": 100.0, "prop": "Pf"}}, "Meter_Parameter": {"VacR": {"start": 33001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_R_Meter(1 Phase)", "unit": "V", "multiply": 10.0, "prop": "VacR"}, "IacR": {"start": 33002, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwIac_R_Meter(1 Phase)", "unit": "A", "multiply": 10.0, "prop": "IacR"}, "FreqR": {"start": 33003, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_R_Meter", "unit": "Hz", "multiply": 100.0, "prop": "FreqR"}, "PacR": {"start": 33004, "len": 1, "slaveId": 1, "type": "S16", "alias": "dwPac_R_Meter", "unit": "W", "multiply": 1.0, "prop": "PacR"}, "VacS": {"start": 33005, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_S_Meter", "unit": "V", "multiply": 10.0, "prop": "VacS"}, "IacS": {"start": 33006, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwIac_S_Meter", "unit": "A", "multiply": 10.0, "prop": "IacS"}, "FreqS": {"start": 33007, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_S_Meter", "unit": "Hz", "multiply": 100.0, "prop": "FreqS"}, "PacS": {"start": 33008, "len": 1, "slaveId": 1, "type": "S16", "alias": "dwPac_S_Meter", "unit": "W", "multiply": 1.0, "prop": "PacS"}, "VacT": {"start": 33009, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_T_Meter", "unit": "V", "multiply": 10.0, "prop": "VacT"}, "IacT": {"start": 33010, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwIac_T_Meter", "unit": "A", "multiply": 10.0, "prop": "IacT"}, "FreqT": {"start": 33011, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_T_Meter", "unit": "Hz", "multiply": 100.0, "prop": "FreqT"}, "PacT": {"start": 33012, "len": 1, "slaveId": 1, "type": "S16", "alias": "dwPac_T_Meter", "unit": "W", "multiply": 1.0, "prop": "PacT"}, "ActivePower": {"start": 33013, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwPower_Meter", "unit": "W", "multiply": 1.0, "prop": "ActivePower"}, "ReactivePower": {"start": 33015, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwReactivePwr_Meter", "unit": "VA", "multiply": 1.0, "prop": "ReactivePower"}, "Pf": {"start": 33017, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwPwrFactor_Meter", "unit": "", "multiply": 100.0, "prop": "Pf"}}, "Single_Meter_Parameter": {"VacR": {"start": 33001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_R_Meter(1 Phase)", "unit": "V", "multiply": 10.0, "prop": "VacR"}, "IacR": {"start": 33002, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwIac_R_Meter(1 Phase)", "unit": "A", "multiply": 10.0, "prop": "IacR"}, "FreqR": {"start": 33003, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_R_Meter", "unit": "Hz", "multiply": 100.0, "prop": "FreqR"}, "PacR": {"start": 33004, "len": 1, "slaveId": 1, "type": "S16", "alias": "dwPac_R_Meter", "unit": "W", "multiply": 1.0, "prop": "PacR"}, "ActivePower": {"start": 33013, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwPower_Meter", "unit": "W", "multiply": 1.0, "prop": "ActivePower"}, "ReactivePower": {"start": 33015, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwReactivePwr_Meter", "unit": "VA", "multiply": 1.0, "prop": "ReactivePower"}, "Pf": {"start": 33017, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwPwrFactor_Meter", "unit": "", "multiply": 100.0, "prop": "Pf"}}, "Us_Meter_Parameter": {"VacL1": {"start": 33001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_R_Meter(1 Phase)", "unit": "V", "multiply": 10.0, "prop": "VacL1"}, "IacL1": {"start": 33002, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwIac_R_Meter(1 Phase)", "unit": "A", "multiply": 10.0, "prop": "IacL1"}, "FreqL1": {"start": 33003, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_R_Meter", "unit": "Hz", "multiply": 100.0, "prop": "FreqL1"}, "PacL1": {"start": 33004, "len": 1, "slaveId": 1, "type": "S16", "alias": "dwPac_R_Meter", "unit": "W", "multiply": 1.0, "prop": "PacL1"}, "VacL2": {"start": 33005, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_S_Meter", "unit": "V", "multiply": 10.0, "prop": "VacL2"}, "IacL2": {"start": 33006, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwIac_S_Meter", "unit": "A", "multiply": 10.0, "prop": "IacL2"}, "FreqL2": {"start": 33007, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_S_Meter", "unit": "Hz", "multiply": 100.0, "prop": "FreqL2"}, "PacL2": {"start": 33008, "len": 1, "slaveId": 1, "type": "S16", "alias": "dwPac_S_Meter", "unit": "W", "multiply": 1.0, "prop": "PacL2"}, "ActivePower": {"start": 33013, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwPower_Meter", "unit": "W", "multiply": 1.0, "prop": "ActivePower"}, "ReactivePower": {"start": 33015, "len": 2, "slaveId": 1, "type": "S32", "alias": "dwReactivePwr_Meter", "unit": "VA", "multiply": 1.0, "prop": "ReactivePower"}, "Pf": {"start": 33017, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwPwrFactor_Meter", "unit": "", "multiply": 100.0, "prop": "Pf"}}, "EPS_Parameter": {"VoltageR": {"start": 32001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPS_Volt_R", "unit": "V", "multiply": 10.0, "prop": "VoltageR"}, "CurrentR": {"start": 32002, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPS_Current_R", "unit": "A", "multiply": 10.0, "prop": "CurrentR"}, "QR": {"start": 32003, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwEPS_Q_R", "unit": "VA", "multiply": 1.0, "prop": "QR"}, "PR": {"start": 32005, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwEPS_P_R", "unit": "W", "multiply": 1.0, "prop": "PR"}, "VoltageS": {"start": 32007, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPS_Volt_S", "unit": "V", "multiply": 10.0, "prop": "VoltageS"}, "CurrentS": {"start": 32008, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPS_Current_S", "unit": "A", "multiply": 10.0, "prop": "CurrentS"}, "QS": {"start": 32009, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwEPS_Q_S", "unit": "VA", "multiply": 1.0, "prop": "QS"}, "PS": {"start": 32011, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwEPS_P_S", "unit": "W", "multiply": 1.0, "prop": "PS"}, "VoltageT": {"start": 32013, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPS_Volt_T", "unit": "V", "multiply": 10.0, "prop": "VoltageT"}, "CurrentT": {"start": 32014, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPS_Current_T", "unit": "A", "multiply": 10.0, "prop": "CurrentT"}, "QT": {"start": 32015, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwEPS_Q_T", "unit": "VA", "multiply": 1.0, "prop": "QT"}, "PT": {"start": 32017, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwEPS_P_T", "unit": "W", "multiply": 1.0, "prop": "PT"}, "FreqR": {"start": 33003, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_R_Meter", "unit": "Hz", "multiply": 100.0, "prop": "FreqR"}, "FreqS": {"start": 33007, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_S_Meter", "unit": "Hz", "multiply": 100.0, "prop": "FreqS"}, "FreqT": {"start": 33011, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_T_Meter", "unit": "Hz", "multiply": 100.0, "prop": "FreqT"}}, "Single_EPS_Parameter": {"VoltageR": {"start": 32001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPS_Volt_R", "unit": "V", "multiply": 10.0, "prop": "VoltageR"}, "CurrentR": {"start": 32002, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPS_Current_R", "unit": "A", "multiply": 10.0, "prop": "CurrentR"}, "QR": {"start": 32003, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwEPS_Q_R", "unit": "VA", "multiply": 1.0, "prop": "QR"}, "PR": {"start": 32005, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwEPS_P_R", "unit": "W", "multiply": 1.0, "prop": "PR"}, "FreqR": {"start": 33003, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFreq_R_Meter", "unit": "Hz", "multiply": 100.0, "prop": "FreqR"}}, "Us_EPS_Parameter": {"VoltageL1": {"start": 32001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPS_Volt_R", "unit": "V", "multiply": 10.0, "prop": "VoltageL1"}, "CurrentL1": {"start": 32002, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPS_Current_R", "unit": "A", "multiply": 10.0, "prop": "CurrentL1"}, "QL1": {"start": 32003, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwEPS_Q_R", "unit": "VA", "multiply": 1.0, "prop": "QL1"}, "PL1": {"start": 32005, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwEPS_P_R", "unit": "W", "multiply": 1.0, "prop": "PL1"}, "VoltageL2": {"start": 32007, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPS_Volt_S", "unit": "V", "multiply": 10.0, "prop": "VoltageL2"}, "CurrentL2": {"start": 32008, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPS_Current_S", "unit": "A", "multiply": 10.0, "prop": "CurrentL2"}, "QL2": {"start": 32009, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwEPS_Q_S", "unit": "VA", "multiply": 1.0, "prop": "QL2"}, "PL2": {"start": 32011, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwEPS_P_S", "unit": "W", "multiply": 1.0, "prop": "PL2"}}, "Battery_Parameter": {"VoltageDsp": {"start": 32019, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVolt_DSP", "unit": "V", "multiply": 10.0, "prop": "VoltageDsp"}, "CurrentDsp": {"start": 32020, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwBatCurr_DSP", "unit": "A", "multiply": 100.0, "prop": "CurrentDsp"}, "PowerDsp": {"start": 32021, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwBatPower_DSP", "unit": "W", "multiply": 1.0, "prop": "PowerDsp"}, "VoltageBms": {"start": 32023, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVolt_BMS", "unit": "V", "multiply": 10.0, "prop": "VoltageBms"}, "CurrentBms": {"start": 32024, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwBatCurr_BMS", "unit": "A", "multiply": 100.0, "prop": "CurrentBms"}, "PowerBms": {"start": 32025, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwBatPower_BMS", "unit": "W", "multiply": 1.0, "prop": "PowerBms"}, "Soc": {"start": 32033, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCapacity", "unit": "%", "multiply": 1.0, "prop": "Soc"}, "Soh": {"start": 32040, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatSOH", "unit": "%", "multiply": 1.0, "prop": "Soh"}, "ErrorH32": {"start": 32041, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError1", "unit": "", "multiply": 1.0, "prop": "ErrorH32"}, "Error2H32": {"start": 32043, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError2", "unit": "", "multiply": 1.0, "prop": "Error2H32"}, "Error3H32": {"start": 32045, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError3", "unit": "", "multiply": 1.0, "prop": "Error3H32"}, "BmsLaterChgCapacity": {"start": 32049, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_ChrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterChgCapacity"}, "BmsLaterDischgCapacity": {"start": 32050, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_DischrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterDischgCapacity"}, "BasicStatus": {"start": 32051, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatBasicStatus", "unit": "", "multiply": 1.0, "prop": "BasicStatus"}, "FaultH16": {"start": 32052, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatFault", "unit": "", "multiply": 1.0, "prop": "FaultH16"}, "AlarmL16": {"start": 32053, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatAlarm", "unit": "", "multiply": 1.0, "prop": "AlarmL16"}, "Temperature": {"start": 32054, "len": 1, "slaveId": 1, "type": "S16", "alias": "swBatTemp", "unit": "℃", "multiply": 10.0, "prop": "Temperature"}, "CycleTimes": {"start": 32055, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCycletimes", "unit": "", "multiply": 1.0, "prop": "CycleTimes"}, "MaxVoltageCharge": {"start": 32056, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltmaxChg", "unit": "V", "multiply": 10.0, "prop": "MaxVoltageCharge"}, "MaxCurrentCharge": {"start": 32057, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCurrmaxChg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentCharge"}, "MinVoltageDischarge": {"start": 32058, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltminDischg", "unit": "V", "multiply": 10.0, "prop": "MinVoltageDischarge"}, "MaxCurrentDischarge": {"start": 32059, "len": 1, "slaveId": 1, "type": "U16", "alias": "swBatCurrmaxDischg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentDischarge"}, "RemainCapacity": {"start": 32060, "len": 2, "slaveId": 1, "type": "U32", "alias": "RemainCapacity", "unit": "Wh", "multiply": 1.0, "prop": "RemainCapacity"}, "SwitchStatus": {"start": 32062, "len": 1, "slaveId": 1, "type": "U16", "alias": "SwitchStatus", "unit": "", "multiply": 1.0, "prop": "SwitchStatus"}, "CellMaxVoltage": {"start": 32063, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMaxVoltage"}, "CellMaxVoltageId": {"start": 32064, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMaxVoltageId"}, "CellMinVoltage": {"start": 32065, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMinVoltage"}, "CellMinVoltageId": {"start": 32066, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMinVoltageId"}, "CellMaxTemp": {"start": 32067, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMaxTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMaxTemp"}, "CellMaxTempId": {"start": 32068, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxTempID", "unit": "", "multiply": 1.0, "prop": "CellMaxTempId"}, "CellMinTemp": {"start": 32069, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMinTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMinTemp"}, "CellMinTempId": {"start": 32070, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinTempID", "unit": "", "multiply": 1.0, "prop": "CellMinTempId"}, "BatSerialNumber": {"start": 32071, "len": 10, "slaveId": 1, "type": "ASCII", "alias": "BatSerialNumber", "unit": "", "multiply": 1.0, "prop": "BatSerialNumber"}, "BateTotalCharge": {"start": 32081, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_Charge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalCharge"}, "BateTotalDischarge": {"start": 32083, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_DisCharge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalDischarge"}, "Error4H32": {"start": 32085, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error4H32"}, "Error5H32": {"start": 32087, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error5H32"}}, "Battery_Parameter_1": {"VoltageBms": {"start": 32123, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVolt_BMS", "unit": "V", "multiply": 10.0, "prop": "VoltageBms"}, "CurrentBms": {"start": 32124, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwBatCurr_BMS", "unit": "A", "multiply": 100.0, "prop": "CurrentBms"}, "PowerBms": {"start": 32125, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwBatPower_BMS", "unit": "W", "multiply": 1.0, "prop": "PowerBms"}, "BmsVersion1": {"start": 32127, "len": 6, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[0]~[5]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soc": {"start": 32133, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCapacity", "unit": "%", "multiply": 1.0, "prop": "Soc"}, "BmsVersion2": {"start": 32134, "len": 2, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[6]~[7]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soh": {"start": 32140, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatSOH", "unit": "%", "multiply": 1.0, "prop": "Soh"}, "ErrorH32": {"start": 32141, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError1", "unit": "", "multiply": 1.0, "prop": "ErrorH32"}, "Error2H32": {"start": 32143, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError2", "unit": "", "multiply": 1.0, "prop": "Error2H32"}, "Error3H32": {"start": 32145, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError3", "unit": "", "multiply": 1.0, "prop": "Error3H32"}, "BmsLaterChgCapacity": {"start": 32149, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_ChrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterChgCapacity"}, "BmsLaterDischgCapacity": {"start": 32150, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_DischrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterDischgCapacity"}, "BasicStatus": {"start": 32151, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatBasicStatus", "unit": "", "multiply": 1.0, "prop": "BasicStatus"}, "FaultH16": {"start": 32152, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatFault", "unit": "", "multiply": 1.0, "prop": "FaultH16"}, "AlarmL16": {"start": 32153, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatAlarm", "unit": "", "multiply": 1.0, "prop": "AlarmL16"}, "Temperature": {"start": 32154, "len": 1, "slaveId": 1, "type": "S16", "alias": "swBatTemp", "unit": "℃", "multiply": 10.0, "prop": "Temperature"}, "CycleTimes": {"start": 32155, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCycletimes", "unit": "", "multiply": 1.0, "prop": "CycleTimes"}, "MaxVoltageCharge": {"start": 32156, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltmaxChg", "unit": "V", "multiply": 10.0, "prop": "MaxVoltageCharge"}, "MaxCurrentCharge": {"start": 32157, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCurrmaxChg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentCharge"}, "MinVoltageDischarge": {"start": 32158, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltminDischg", "unit": "V", "multiply": 10.0, "prop": "MinVoltageDischarge"}, "MaxCurrentDischarge": {"start": 32159, "len": 1, "slaveId": 1, "type": "U16", "alias": "swBatCurrmaxDischg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentDischarge"}, "RemainCapacity": {"start": 32160, "len": 2, "slaveId": 1, "type": "U32", "alias": "RemainCapacity", "unit": "Wh", "multiply": 1.0, "prop": "RemainCapacity"}, "SwitchStatus": {"start": 32162, "len": 1, "slaveId": 1, "type": "U16", "alias": "SwitchStatus", "unit": "", "multiply": 1.0, "prop": "SwitchStatus"}, "CellMaxVoltage": {"start": 32163, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMaxVoltage"}, "CellMaxVoltageId": {"start": 32164, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMaxVoltageId"}, "CellMinVoltage": {"start": 32165, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMinVoltage"}, "CellMinVoltageId": {"start": 32166, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMinVoltageId"}, "CellMaxTemp": {"start": 32167, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMaxTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMaxTemp"}, "CellMaxTempId": {"start": 32168, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxTempID", "unit": "", "multiply": 1.0, "prop": "CellMaxTempId"}, "CellMinTemp": {"start": 32169, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMinTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMinTemp"}, "CellMinTempId": {"start": 32170, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinTempID", "unit": "", "multiply": 1.0, "prop": "CellMinTempId"}, "BatSerialNumber": {"start": 32171, "len": 10, "slaveId": 1, "type": "ASCII", "alias": "BatSerialNumber", "unit": "", "multiply": 1.0, "prop": "BatSerialNumber"}, "BateTotalCharge": {"start": 32181, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_Charge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalCharge"}, "BateTotalDischarge": {"start": 32183, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_DisCharge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalDischarge"}, "Error4H32": {"start": 32185, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error4H32"}, "Error5H32": {"start": 32187, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error5H32"}, "ParallelNum": {"start": 32095, "len": 1, "slaveId": 1, "type": "U16", "alias": "parallel_num", "unit": "", "multiply": 1.0, "prop": "ParallelNum"}}, "Battery_Parameter_2": {"VoltageBms": {"start": 32223, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVolt_BMS", "unit": "V", "multiply": 10.0, "prop": "VoltageBms"}, "CurrentBms": {"start": 32224, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwBatCurr_BMS", "unit": "A", "multiply": 100.0, "prop": "CurrentBms"}, "PowerBms": {"start": 32225, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwBatPower_BMS", "unit": "W", "multiply": 1.0, "prop": "PowerBms"}, "BmsVersion1": {"start": 32227, "len": 6, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[0]~[5]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soc": {"start": 32233, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCapacity", "unit": "%", "multiply": 1.0, "prop": "Soc"}, "BmsVersion2": {"start": 32234, "len": 2, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[6]~[7]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soh": {"start": 32240, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatSOH", "unit": "%", "multiply": 1.0, "prop": "Soh"}, "ErrorH32": {"start": 32241, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError1", "unit": "", "multiply": 1.0, "prop": "ErrorH32"}, "Error2H32": {"start": 32243, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError2", "unit": "", "multiply": 1.0, "prop": "Error2H32"}, "Error3H32": {"start": 32245, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError3", "unit": "", "multiply": 1.0, "prop": "Error3H32"}, "BmsLaterChgCapacity": {"start": 32249, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_ChrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterChgCapacity"}, "BmsLaterDischgCapacity": {"start": 32250, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_DischrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterDischgCapacity"}, "BasicStatus": {"start": 32251, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatBasicStatus", "unit": "", "multiply": 1.0, "prop": "BasicStatus"}, "FaultH16": {"start": 32252, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatFault", "unit": "", "multiply": 1.0, "prop": "FaultH16"}, "AlarmL16": {"start": 32253, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatAlarm", "unit": "", "multiply": 1.0, "prop": "AlarmL16"}, "Temperature": {"start": 32254, "len": 1, "slaveId": 1, "type": "S16", "alias": "swBatTemp", "unit": "℃", "multiply": 10.0, "prop": "Temperature"}, "CycleTimes": {"start": 32255, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCycletimes", "unit": "", "multiply": 1.0, "prop": "CycleTimes"}, "MaxVoltageCharge": {"start": 32256, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltmaxChg", "unit": "V", "multiply": 10.0, "prop": "MaxVoltageCharge"}, "MaxCurrentCharge": {"start": 32257, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCurrmaxChg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentCharge"}, "MinVoltageDischarge": {"start": 32258, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltminDischg", "unit": "V", "multiply": 10.0, "prop": "MinVoltageDischarge"}, "MaxCurrentDischarge": {"start": 32259, "len": 1, "slaveId": 1, "type": "U16", "alias": "swBatCurrmaxDischg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentDischarge"}, "RemainCapacity": {"start": 32260, "len": 2, "slaveId": 1, "type": "U32", "alias": "RemainCapacity", "unit": "Wh", "multiply": 1.0, "prop": "RemainCapacity"}, "SwitchStatus": {"start": 32262, "len": 1, "slaveId": 1, "type": "U16", "alias": "SwitchStatus", "unit": "", "multiply": 1.0, "prop": "SwitchStatus"}, "CellMaxVoltage": {"start": 32263, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMaxVoltage"}, "CellMaxVoltageId": {"start": 32264, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMaxVoltageId"}, "CellMinVoltage": {"start": 32265, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMinVoltage"}, "CellMinVoltageId": {"start": 32266, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMinVoltageId"}, "CellMaxTemp": {"start": 32267, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMaxTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMaxTemp"}, "CellMaxTempId": {"start": 32268, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxTempID", "unit": "", "multiply": 1.0, "prop": "CellMaxTempId"}, "CellMinTemp": {"start": 32269, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMinTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMinTemp"}, "CellMinTempId": {"start": 32270, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinTempID", "unit": "", "multiply": 1.0, "prop": "CellMinTempId"}, "BatSerialNumber": {"start": 32271, "len": 10, "slaveId": 1, "type": "ASCII", "alias": "BatSerialNumber", "unit": "", "multiply": 1.0, "prop": "BatSerialNumber"}, "BateTotalCharge": {"start": 32281, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_Charge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalCharge"}, "BateTotalDischarge": {"start": 32283, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_DisCharge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalDischarge"}, "Error4H32": {"start": 32285, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error4H32"}, "Error5H32": {"start": 32287, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error5H32"}, "ParallelNum": {"start": 32095, "len": 1, "slaveId": 1, "type": "U16", "alias": "parallel_num", "unit": "", "multiply": 1.0, "prop": "ParallelNum"}}, "Battery_Parameter_3": {"VoltageBms": {"start": 32323, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVolt_BMS", "unit": "V", "multiply": 10.0, "prop": "VoltageBms"}, "CurrentBms": {"start": 32324, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwBatCurr_BMS", "unit": "A", "multiply": 100.0, "prop": "CurrentBms"}, "PowerBms": {"start": 32325, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwBatPower_BMS", "unit": "W", "multiply": 1.0, "prop": "PowerBms"}, "BmsVersion1": {"start": 32327, "len": 6, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[0]~[5]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soc": {"start": 32333, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCapacity", "unit": "%", "multiply": 1.0, "prop": "Soc"}, "BmsVersion2": {"start": 32334, "len": 2, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[6]~[7]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soh": {"start": 32340, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatSOH", "unit": "%", "multiply": 1.0, "prop": "Soh"}, "ErrorH32": {"start": 32341, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError1", "unit": "", "multiply": 1.0, "prop": "ErrorH32"}, "Error2H32": {"start": 32343, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError2", "unit": "", "multiply": 1.0, "prop": "Error2H32"}, "Error3H32": {"start": 32345, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError3", "unit": "", "multiply": 1.0, "prop": "Error3H32"}, "BmsLaterChgCapacity": {"start": 32349, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_ChrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterChgCapacity"}, "BmsLaterDischgCapacity": {"start": 32350, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_DischrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterDischgCapacity"}, "BasicStatus": {"start": 32351, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatBasicStatus", "unit": "", "multiply": 1.0, "prop": "BasicStatus"}, "FaultH16": {"start": 32352, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatFault", "unit": "", "multiply": 1.0, "prop": "FaultH16"}, "AlarmL16": {"start": 32353, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatAlarm", "unit": "", "multiply": 1.0, "prop": "AlarmL16"}, "Temperature": {"start": 32354, "len": 1, "slaveId": 1, "type": "S16", "alias": "swBatTemp", "unit": "℃", "multiply": 10.0, "prop": "Temperature"}, "CycleTimes": {"start": 32355, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCycletimes", "unit": "", "multiply": 1.0, "prop": "CycleTimes"}, "MaxVoltageCharge": {"start": 32356, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltmaxChg", "unit": "V", "multiply": 10.0, "prop": "MaxVoltageCharge"}, "MaxCurrentCharge": {"start": 32357, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCurrmaxChg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentCharge"}, "MinVoltageDischarge": {"start": 32358, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltminDischg", "unit": "V", "multiply": 10.0, "prop": "MinVoltageDischarge"}, "MaxCurrentDischarge": {"start": 32359, "len": 1, "slaveId": 1, "type": "U16", "alias": "swBatCurrmaxDischg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentDischarge"}, "RemainCapacity": {"start": 32360, "len": 2, "slaveId": 1, "type": "U32", "alias": "RemainCapacity", "unit": "Wh", "multiply": 1.0, "prop": "RemainCapacity"}, "SwitchStatus": {"start": 32362, "len": 1, "slaveId": 1, "type": "U16", "alias": "SwitchStatus", "unit": "", "multiply": 1.0, "prop": "SwitchStatus"}, "CellMaxVoltage": {"start": 32363, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMaxVoltage"}, "CellMaxVoltageId": {"start": 32364, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMaxVoltageId"}, "CellMinVoltage": {"start": 32365, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMinVoltage"}, "CellMinVoltageId": {"start": 32366, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMinVoltageId"}, "CellMaxTemp": {"start": 32367, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMaxTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMaxTemp"}, "CellMaxTempId": {"start": 32368, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxTempID", "unit": "", "multiply": 1.0, "prop": "CellMaxTempId"}, "CellMinTemp": {"start": 32369, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMinTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMinTemp"}, "CellMinTempId": {"start": 32370, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinTempID", "unit": "", "multiply": 1.0, "prop": "CellMinTempId"}, "BatSerialNumber": {"start": 32371, "len": 10, "slaveId": 1, "type": "ASCII", "alias": "BatSerialNumber", "unit": "", "multiply": 1.0, "prop": "BatSerialNumber"}, "BateTotalCharge": {"start": 32381, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_Charge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalCharge"}, "BateTotalDischarge": {"start": 32383, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_DisCharge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalDischarge"}, "Error4H32": {"start": 32385, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error4H32"}, "Error5H32": {"start": 32387, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error5H32"}, "ParallelNum": {"start": 32095, "len": 1, "slaveId": 1, "type": "U16", "alias": "parallel_num", "unit": "", "multiply": 1.0, "prop": "ParallelNum"}}, "Battery_Parameter_4": {"VoltageBms": {"start": 32423, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVolt_BMS", "unit": "V", "multiply": 10.0, "prop": "VoltageBms"}, "CurrentBms": {"start": 32424, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwBatCurr_BMS", "unit": "A", "multiply": 100.0, "prop": "CurrentBms"}, "PowerBms": {"start": 32425, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwBatPower_BMS", "unit": "W", "multiply": 1.0, "prop": "PowerBms"}, "BmsVersion1": {"start": 32427, "len": 6, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[0]~[5]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soc": {"start": 32433, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCapacity", "unit": "%", "multiply": 1.0, "prop": "Soc"}, "BmsVersion2": {"start": 32434, "len": 2, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[6]~[7]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soh": {"start": 32440, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatSOH", "unit": "%", "multiply": 1.0, "prop": "Soh"}, "ErrorH32": {"start": 32441, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError1", "unit": "", "multiply": 1.0, "prop": "ErrorH32"}, "Error2H32": {"start": 32443, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError2", "unit": "", "multiply": 1.0, "prop": "Error2H32"}, "Error3H32": {"start": 32445, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError3", "unit": "", "multiply": 1.0, "prop": "Error3H32"}, "BmsLaterChgCapacity": {"start": 32449, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_ChrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterChgCapacity"}, "BmsLaterDischgCapacity": {"start": 32450, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_DischrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterDischgCapacity"}, "BasicStatus": {"start": 32451, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatBasicStatus", "unit": "", "multiply": 1.0, "prop": "BasicStatus"}, "FaultH16": {"start": 32452, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatFault", "unit": "", "multiply": 1.0, "prop": "FaultH16"}, "AlarmL16": {"start": 32453, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatAlarm", "unit": "", "multiply": 1.0, "prop": "AlarmL16"}, "Temperature": {"start": 32454, "len": 1, "slaveId": 1, "type": "S16", "alias": "swBatTemp", "unit": "℃", "multiply": 10.0, "prop": "Temperature"}, "CycleTimes": {"start": 32455, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCycletimes", "unit": "", "multiply": 1.0, "prop": "CycleTimes"}, "MaxVoltageCharge": {"start": 32456, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltmaxChg", "unit": "V", "multiply": 10.0, "prop": "MaxVoltageCharge"}, "MaxCurrentCharge": {"start": 32457, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCurrmaxChg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentCharge"}, "MinVoltageDischarge": {"start": 32458, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltminDischg", "unit": "V", "multiply": 10.0, "prop": "MinVoltageDischarge"}, "MaxCurrentDischarge": {"start": 32459, "len": 1, "slaveId": 1, "type": "U16", "alias": "swBatCurrmaxDischg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentDischarge"}, "RemainCapacity": {"start": 32460, "len": 2, "slaveId": 1, "type": "U32", "alias": "RemainCapacity", "unit": "Wh", "multiply": 1.0, "prop": "RemainCapacity"}, "SwitchStatus": {"start": 32462, "len": 1, "slaveId": 1, "type": "U16", "alias": "SwitchStatus", "unit": "", "multiply": 1.0, "prop": "SwitchStatus"}, "CellMaxVoltage": {"start": 32463, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMaxVoltage"}, "CellMaxVoltageId": {"start": 32464, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMaxVoltageId"}, "CellMinVoltage": {"start": 32465, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMinVoltage"}, "CellMinVoltageId": {"start": 32466, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMinVoltageId"}, "CellMaxTemp": {"start": 32467, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMaxTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMaxTemp"}, "CellMaxTempId": {"start": 32468, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxTempID", "unit": "", "multiply": 1.0, "prop": "CellMaxTempId"}, "CellMinTemp": {"start": 32469, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMinTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMinTemp"}, "CellMinTempId": {"start": 32470, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinTempID", "unit": "", "multiply": 1.0, "prop": "CellMinTempId"}, "BatSerialNumber": {"start": 32471, "len": 10, "slaveId": 1, "type": "ASCII", "alias": "BatSerialNumber", "unit": "", "multiply": 1.0, "prop": "BatSerialNumber"}, "BateTotalCharge": {"start": 32481, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_Charge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalCharge"}, "BateTotalDischarge": {"start": 32483, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_DisCharge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalDischarge"}, "Error4H32": {"start": 32485, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error4H32"}, "Error5H32": {"start": 32487, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error5H32"}, "ParallelNum": {"start": 32095, "len": 1, "slaveId": 1, "type": "U16", "alias": "parallel_num", "unit": "", "multiply": 1.0, "prop": "ParallelNum"}}, "Battery_Parameter_5": {"VoltageBms": {"start": 32523, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVolt_BMS", "unit": "V", "multiply": 10.0, "prop": "VoltageBms"}, "CurrentBms": {"start": 32524, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwBatCurr_BMS", "unit": "A", "multiply": 100.0, "prop": "CurrentBms"}, "PowerBms": {"start": 32525, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwBatPower_BMS", "unit": "W", "multiply": 1.0, "prop": "PowerBms"}, "BmsVersion1": {"start": 32527, "len": 6, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[0]~[5]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soc": {"start": 32533, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCapacity", "unit": "%", "multiply": 1.0, "prop": "Soc"}, "BmsVersion2": {"start": 32534, "len": 2, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[6]~[7]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soh": {"start": 32540, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatSOH", "unit": "%", "multiply": 1.0, "prop": "Soh"}, "ErrorH32": {"start": 32541, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError1", "unit": "", "multiply": 1.0, "prop": "ErrorH32"}, "Error2H32": {"start": 32543, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError2", "unit": "", "multiply": 1.0, "prop": "Error2H32"}, "Error3H32": {"start": 32545, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError3", "unit": "", "multiply": 1.0, "prop": "Error3H32"}, "BmsLaterChgCapacity": {"start": 32549, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_ChrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterChgCapacity"}, "BmsLaterDischgCapacity": {"start": 32550, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_DischrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterDischgCapacity"}, "BasicStatus": {"start": 32551, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatBasicStatus", "unit": "", "multiply": 1.0, "prop": "BasicStatus"}, "FaultH16": {"start": 32552, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatFault", "unit": "", "multiply": 1.0, "prop": "FaultH16"}, "AlarmL16": {"start": 32553, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatAlarm", "unit": "", "multiply": 1.0, "prop": "AlarmL16"}, "Temperature": {"start": 32554, "len": 1, "slaveId": 1, "type": "S16", "alias": "swBatTemp", "unit": "℃", "multiply": 10.0, "prop": "Temperature"}, "CycleTimes": {"start": 32555, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCycletimes", "unit": "", "multiply": 1.0, "prop": "CycleTimes"}, "MaxVoltageCharge": {"start": 32556, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltmaxChg", "unit": "V", "multiply": 10.0, "prop": "MaxVoltageCharge"}, "MaxCurrentCharge": {"start": 32557, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCurrmaxChg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentCharge"}, "MinVoltageDischarge": {"start": 32558, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltminDischg", "unit": "V", "multiply": 10.0, "prop": "MinVoltageDischarge"}, "MaxCurrentDischarge": {"start": 32559, "len": 1, "slaveId": 1, "type": "U16", "alias": "swBatCurrmaxDischg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentDischarge"}, "RemainCapacity": {"start": 32560, "len": 2, "slaveId": 1, "type": "U32", "alias": "RemainCapacity", "unit": "Wh", "multiply": 1.0, "prop": "RemainCapacity"}, "SwitchStatus": {"start": 32562, "len": 1, "slaveId": 1, "type": "U16", "alias": "SwitchStatus", "unit": "", "multiply": 1.0, "prop": "SwitchStatus"}, "CellMaxVoltage": {"start": 32563, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMaxVoltage"}, "CellMaxVoltageId": {"start": 32564, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMaxVoltageId"}, "CellMinVoltage": {"start": 32565, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMinVoltage"}, "CellMinVoltageId": {"start": 32566, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMinVoltageId"}, "CellMaxTemp": {"start": 32567, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMaxTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMaxTemp"}, "CellMaxTempId": {"start": 32568, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxTempID", "unit": "", "multiply": 1.0, "prop": "CellMaxTempId"}, "CellMinTemp": {"start": 32569, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMinTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMinTemp"}, "CellMinTempId": {"start": 32570, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinTempID", "unit": "", "multiply": 1.0, "prop": "CellMinTempId"}, "BatSerialNumber": {"start": 32571, "len": 10, "slaveId": 1, "type": "ASCII", "alias": "BatSerialNumber", "unit": "", "multiply": 1.0, "prop": "BatSerialNumber"}, "BateTotalCharge": {"start": 32581, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_Charge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalCharge"}, "BateTotalDischarge": {"start": 32583, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_DisCharge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalDischarge"}, "Error4H32": {"start": 32585, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error4H32"}, "Error5H32": {"start": 32587, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error5H32"}, "ParallelNum": {"start": 32095, "len": 1, "slaveId": 1, "type": "U16", "alias": "parallel_num", "unit": "", "multiply": 1.0, "prop": "ParallelNum"}}, "Battery_Parameter_6": {"VoltageBms": {"start": 32623, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVolt_BMS", "unit": "V", "multiply": 10.0, "prop": "VoltageBms"}, "CurrentBms": {"start": 32624, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwBatCurr_BMS", "unit": "A", "multiply": 100.0, "prop": "CurrentBms"}, "PowerBms": {"start": 32625, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwBatPower_BMS", "unit": "W", "multiply": 1.0, "prop": "PowerBms"}, "BmsVersion1": {"start": 32627, "len": 6, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[0]~[5]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soc": {"start": 32633, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCapacity", "unit": "%", "multiply": 1.0, "prop": "Soc"}, "BmsVersion2": {"start": 32634, "len": 2, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[6]~[7]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soh": {"start": 32640, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatSOH", "unit": "%", "multiply": 1.0, "prop": "Soh"}, "ErrorH32": {"start": 32641, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError1", "unit": "", "multiply": 1.0, "prop": "ErrorH32"}, "Error2H32": {"start": 32643, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError2", "unit": "", "multiply": 1.0, "prop": "Error2H32"}, "Error3H32": {"start": 32645, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError3", "unit": "", "multiply": 1.0, "prop": "Error3H32"}, "BmsLaterChgCapacity": {"start": 32649, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_ChrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterChgCapacity"}, "BmsLaterDischgCapacity": {"start": 32650, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_DischrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterDischgCapacity"}, "BasicStatus": {"start": 32651, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatBasicStatus", "unit": "", "multiply": 1.0, "prop": "BasicStatus"}, "FaultH16": {"start": 32652, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatFault", "unit": "", "multiply": 1.0, "prop": "FaultH16"}, "AlarmL16": {"start": 32653, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatAlarm", "unit": "", "multiply": 1.0, "prop": "AlarmL16"}, "Temperature": {"start": 32654, "len": 1, "slaveId": 1, "type": "S16", "alias": "swBatTemp", "unit": "℃", "multiply": 10.0, "prop": "Temperature"}, "CycleTimes": {"start": 32655, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCycletimes", "unit": "", "multiply": 1.0, "prop": "CycleTimes"}, "MaxVoltageCharge": {"start": 32656, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltmaxChg", "unit": "V", "multiply": 10.0, "prop": "MaxVoltageCharge"}, "MaxCurrentCharge": {"start": 32657, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCurrmaxChg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentCharge"}, "MinVoltageDischarge": {"start": 32658, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltminDischg", "unit": "V", "multiply": 10.0, "prop": "MinVoltageDischarge"}, "MaxCurrentDischarge": {"start": 32659, "len": 1, "slaveId": 1, "type": "U16", "alias": "swBatCurrmaxDischg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentDischarge"}, "RemainCapacity": {"start": 32660, "len": 2, "slaveId": 1, "type": "U32", "alias": "RemainCapacity", "unit": "Wh", "multiply": 1.0, "prop": "RemainCapacity"}, "SwitchStatus": {"start": 32662, "len": 1, "slaveId": 1, "type": "U16", "alias": "SwitchStatus", "unit": "", "multiply": 1.0, "prop": "SwitchStatus"}, "CellMaxVoltage": {"start": 32663, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMaxVoltage"}, "CellMaxVoltageId": {"start": 32664, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMaxVoltageId"}, "CellMinVoltage": {"start": 32665, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMinVoltage"}, "CellMinVoltageId": {"start": 32666, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMinVoltageId"}, "CellMaxTemp": {"start": 32667, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMaxTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMaxTemp"}, "CellMaxTempId": {"start": 32668, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxTempID", "unit": "", "multiply": 1.0, "prop": "CellMaxTempId"}, "CellMinTemp": {"start": 32669, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMinTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMinTemp"}, "CellMinTempId": {"start": 32670, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinTempID", "unit": "", "multiply": 1.0, "prop": "CellMinTempId"}, "BatSerialNumber": {"start": 32671, "len": 10, "slaveId": 1, "type": "ASCII", "alias": "BatSerialNumber", "unit": "", "multiply": 1.0, "prop": "BatSerialNumber"}, "BateTotalCharge": {"start": 32681, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_Charge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalCharge"}, "BateTotalDischarge": {"start": 32683, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_DisCharge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalDischarge"}, "Error4H32": {"start": 32685, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error4H32"}, "Error5H32": {"start": 32687, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error5H32"}, "ParallelNum": {"start": 32095, "len": 1, "slaveId": 1, "type": "U16", "alias": "parallel_num", "unit": "", "multiply": 1.0, "prop": "ParallelNum"}}, "Battery_Parameter_7": {"VoltageBms": {"start": 32723, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVolt_BMS", "unit": "V", "multiply": 10.0, "prop": "VoltageBms"}, "CurrentBms": {"start": 32724, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwBatCurr_BMS", "unit": "A", "multiply": 100.0, "prop": "CurrentBms"}, "PowerBms": {"start": 32725, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwBatPower_BMS", "unit": "W", "multiply": 1.0, "prop": "PowerBms"}, "BmsVersion1": {"start": 32727, "len": 6, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[0]~[5]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soc": {"start": 32733, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCapacity", "unit": "%", "multiply": 1.0, "prop": "Soc"}, "BmsVersion2": {"start": 32734, "len": 2, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[6]~[7]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soh": {"start": 32740, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatSOH", "unit": "%", "multiply": 1.0, "prop": "Soh"}, "ErrorH32": {"start": 32741, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError1", "unit": "", "multiply": 1.0, "prop": "ErrorH32"}, "Error2H32": {"start": 32743, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError2", "unit": "", "multiply": 1.0, "prop": "Error2H32"}, "Error3H32": {"start": 32745, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError3", "unit": "", "multiply": 1.0, "prop": "Error3H32"}, "BmsLaterChgCapacity": {"start": 32749, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_ChrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterChgCapacity"}, "BmsLaterDischgCapacity": {"start": 32750, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_DischrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterDischgCapacity"}, "BasicStatus": {"start": 32751, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatBasicStatus", "unit": "", "multiply": 1.0, "prop": "BasicStatus"}, "FaultH16": {"start": 32752, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatFault", "unit": "", "multiply": 1.0, "prop": "FaultH16"}, "AlarmL16": {"start": 32753, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatAlarm", "unit": "", "multiply": 1.0, "prop": "AlarmL16"}, "Temperature": {"start": 32754, "len": 1, "slaveId": 1, "type": "S16", "alias": "swBatTemp", "unit": "℃", "multiply": 10.0, "prop": "Temperature"}, "CycleTimes": {"start": 32755, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCycletimes", "unit": "", "multiply": 1.0, "prop": "CycleTimes"}, "MaxVoltageCharge": {"start": 32756, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltmaxChg", "unit": "V", "multiply": 10.0, "prop": "MaxVoltageCharge"}, "MaxCurrentCharge": {"start": 32757, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCurrmaxChg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentCharge"}, "MinVoltageDischarge": {"start": 32758, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltminDischg", "unit": "V", "multiply": 10.0, "prop": "MinVoltageDischarge"}, "MaxCurrentDischarge": {"start": 32759, "len": 1, "slaveId": 1, "type": "U16", "alias": "swBatCurrmaxDischg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentDischarge"}, "RemainCapacity": {"start": 32760, "len": 2, "slaveId": 1, "type": "U32", "alias": "RemainCapacity", "unit": "Wh", "multiply": 1.0, "prop": "RemainCapacity"}, "SwitchStatus": {"start": 32762, "len": 1, "slaveId": 1, "type": "U16", "alias": "SwitchStatus", "unit": "", "multiply": 1.0, "prop": "SwitchStatus"}, "CellMaxVoltage": {"start": 32763, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMaxVoltage"}, "CellMaxVoltageId": {"start": 32764, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMaxVoltageId"}, "CellMinVoltage": {"start": 32765, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMinVoltage"}, "CellMinVoltageId": {"start": 32766, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMinVoltageId"}, "CellMaxTemp": {"start": 32767, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMaxTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMaxTemp"}, "CellMaxTempId": {"start": 32768, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxTempID", "unit": "", "multiply": 1.0, "prop": "CellMaxTempId"}, "CellMinTemp": {"start": 32769, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMinTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMinTemp"}, "CellMinTempId": {"start": 32770, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinTempID", "unit": "", "multiply": 1.0, "prop": "CellMinTempId"}, "BatSerialNumber": {"start": 32771, "len": 10, "slaveId": 1, "type": "ASCII", "alias": "BatSerialNumber", "unit": "", "multiply": 1.0, "prop": "BatSerialNumber"}, "BateTotalCharge": {"start": 32781, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_Charge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalCharge"}, "BateTotalDischarge": {"start": 32783, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_DisCharge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalDischarge"}, "Error4H32": {"start": 32785, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error4H32"}, "Error5H32": {"start": 32787, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error5H32"}, "ParallelNum": {"start": 32095, "len": 1, "slaveId": 1, "type": "U16", "alias": "parallel_num", "unit": "", "multiply": 1.0, "prop": "ParallelNum"}}, "Battery_Parameter_8": {"VoltageBms": {"start": 32823, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVolt_BMS", "unit": "V", "multiply": 10.0, "prop": "VoltageBms"}, "CurrentBms": {"start": 32824, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwBatCurr_BMS", "unit": "A", "multiply": 100.0, "prop": "CurrentBms"}, "PowerBms": {"start": 32825, "len": 2, "slaveId": 1, "type": "S32", "alias": "uwBatPower_BMS", "unit": "W", "multiply": 1.0, "prop": "PowerBms"}, "BmsVersion1": {"start": 32827, "len": 6, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[0]~[5]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soc": {"start": 32833, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCapacity", "unit": "%", "multiply": 1.0, "prop": "Soc"}, "BmsVersion2": {"start": 32834, "len": 2, "slaveId": 1, "type": "ASCII", "alias": "bms_ver[6]~[7]", "unit": "", "multiply": 1.0, "prop": "BmsVersion"}, "Soh": {"start": 32840, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatSOH", "unit": "%", "multiply": 1.0, "prop": "Soh"}, "ErrorH32": {"start": 32841, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError1", "unit": "", "multiply": 1.0, "prop": "ErrorH32"}, "Error2H32": {"start": 32843, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError2", "unit": "", "multiply": 1.0, "prop": "Error2H32"}, "Error3H32": {"start": 32845, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBatError3", "unit": "", "multiply": 1.0, "prop": "Error3H32"}, "BmsLaterChgCapacity": {"start": 32849, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_ChrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterChgCapacity"}, "BmsLaterDischgCapacity": {"start": 32850, "len": 1, "slaveId": 1, "type": "U16", "alias": "BMS_Laster_DischrgCapacity", "unit": "", "multiply": 1.0, "prop": "BmsLaterDischgCapacity"}, "BasicStatus": {"start": 32851, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatBasicStatus", "unit": "", "multiply": 1.0, "prop": "BasicStatus"}, "FaultH16": {"start": 32852, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatFault", "unit": "", "multiply": 1.0, "prop": "FaultH16"}, "AlarmL16": {"start": 32853, "len": 1, "slaveId": 1, "type": "BIT", "alias": "uwBatAlarm", "unit": "", "multiply": 1.0, "prop": "AlarmL16"}, "Temperature": {"start": 32854, "len": 1, "slaveId": 1, "type": "S16", "alias": "swBatTemp", "unit": "℃", "multiply": 10.0, "prop": "Temperature"}, "CycleTimes": {"start": 32855, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCycletimes", "unit": "", "multiply": 1.0, "prop": "CycleTimes"}, "MaxVoltageCharge": {"start": 32856, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltmaxChg", "unit": "V", "multiply": 10.0, "prop": "MaxVoltageCharge"}, "MaxCurrentCharge": {"start": 32857, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCurrmaxChg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentCharge"}, "MinVoltageDischarge": {"start": 32858, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatVoltminDischg", "unit": "V", "multiply": 10.0, "prop": "MinVoltageDischarge"}, "MaxCurrentDischarge": {"start": 32859, "len": 1, "slaveId": 1, "type": "U16", "alias": "swBatCurrmaxDischg", "unit": "A", "multiply": 100.0, "prop": "MaxCurrentDischarge"}, "RemainCapacity": {"start": 32860, "len": 2, "slaveId": 1, "type": "U32", "alias": "RemainCapacity", "unit": "Wh", "multiply": 1.0, "prop": "RemainCapacity"}, "SwitchStatus": {"start": 32862, "len": 1, "slaveId": 1, "type": "U16", "alias": "SwitchStatus", "unit": "", "multiply": 1.0, "prop": "SwitchStatus"}, "CellMaxVoltage": {"start": 32863, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMaxVoltage"}, "CellMaxVoltageId": {"start": 32864, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMaxVoltageId"}, "CellMinVoltage": {"start": 32865, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltage", "unit": "V", "multiply": 1000.0, "prop": "CellMinVoltage"}, "CellMinVoltageId": {"start": 32866, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinVoltageID", "unit": "", "multiply": 1.0, "prop": "CellMinVoltageId"}, "CellMaxTemp": {"start": 32867, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMaxTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMaxTemp"}, "CellMaxTempId": {"start": 32868, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMaxTempID", "unit": "", "multiply": 1.0, "prop": "CellMaxTempId"}, "CellMinTemp": {"start": 32869, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCellMinTemp", "unit": "℃", "multiply": 10.0, "prop": "CellMinTemp"}, "CellMinTempId": {"start": 32870, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCellMinTempID", "unit": "", "multiply": 1.0, "prop": "CellMinTempId"}, "BatSerialNumber": {"start": 32871, "len": 10, "slaveId": 1, "type": "ASCII", "alias": "BatSerialNumber", "unit": "", "multiply": 1.0, "prop": "BatSerialNumber"}, "BateTotalCharge": {"start": 32881, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_Charge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalCharge"}, "BateTotalDischarge": {"start": 32883, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_DisCharge", "unit": "kWh", "multiply": 10.0, "prop": "BateTotalDischarge"}, "Error4H32": {"start": 32885, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error4H32"}, "Error5H32": {"start": 32887, "len": 2, "slaveId": 1, "type": "BIT", "alias": "uwBmsAlarmExtension", "unit": "", "multiply": 1.0, "prop": "Error5H32"}, "ParallelNum": {"start": 32095, "len": 1, "slaveId": 1, "type": "U16", "alias": "parallel_num", "unit": "", "multiply": 1.0, "prop": "ParallelNum"}}, "WeiHengBatteryInfo": {"Brand": {"start": 0, "len": 16, "slaveId": 201, "type": "ASCII", "alias": "brand", "unit": "", "multiply": 1.0, "prop": "Brand"}, "Model": {"start": 16, "len": 16, "slaveId": 201, "type": "ASCII", "alias": "model", "unit": "", "multiply": 1.0, "prop": "Model"}, "Sn": {"start": 32, "len": 32, "slaveId": 201, "type": "ASCII", "alias": "sn", "unit": "", "multiply": 1.0, "prop": "Sn"}, "HardwareVersion": {"start": 64, "len": 16, "slaveId": 201, "type": "ASCII", "alias": "hardware_version", "unit": "", "multiply": 1.0, "prop": "HardwareVersion"}, "HardwareVersionCode": {"start": 80, "len": 1, "slaveId": 201, "type": "U16", "alias": "hardware_version_code", "unit": "", "multiply": 1.0, "prop": "HardwareVersionCode"}, "InternalBatteryPackType": {"start": 81, "len": 1, "slaveId": 201, "type": "U16", "alias": "internal_battery_pack_type", "unit": "", "multiply": 1.0, "prop": "InternalBatteryPackType"}, "SoftwareVersion": {"start": 82, "len": 16, "slaveId": 201, "type": "ASCII", "alias": "software_version", "unit": "", "multiply": 1.0, "prop": "SoftwareVersion"}, "SoftwareVersionCode": {"start": 98, "len": 1, "slaveId": 201, "type": "U16", "alias": "software_version_code", "unit": "", "multiply": 1.0, "prop": "SoftwareVersionCode"}, "SystemState": {"start": 100, "len": 1, "slaveId": 201, "type": "U16", "alias": "system_state", "unit": "", "multiply": 1.0, "prop": "SystemState"}, "SystemUptime": {"start": 101, "len": 2, "slaveId": 201, "type": "U32R", "alias": "system_uptime", "unit": "", "multiply": 1.0, "prop": "SystemUptime"}, "CurrentCode": {"start": 103, "len": 8, "slaveId": 201, "type": "BIT", "alias": "current_code", "unit": "", "multiply": 1.0, "prop": "CurrentCode"}, "FaultSaveCode": {"start": 111, "len": 8, "slaveId": 201, "type": "BIT", "alias": "fault_save_code", "unit": "", "multiply": 1.0, "prop": "FaultSaveCode"}, "AccumulativeInputCharge": {"start": 119, "len": 2, "slaveId": 201, "type": "U32", "alias": "accumulative_input_charge", "unit": "", "multiply": 1.0, "prop": "AccumulativeInputCharge"}, "AccumulativeOutputCharge": {"start": 121, "len": 2, "slaveId": 201, "type": "U32", "alias": "accumulative_output_charge", "unit": "", "multiply": 1.0, "prop": "AccumulativeOutputCharge"}, "AccumulativeInputEnergy": {"start": 123, "len": 2, "slaveId": 201, "type": "U32", "alias": "accumulative_input_energy", "unit": "", "multiply": 1.0, "prop": "AccumulativeInputEnergy"}, "AccumulativeOutputEnergy": {"start": 125, "len": 2, "slaveId": 201, "type": "U32", "alias": "accumulative_output_energy", "unit": "", "multiply": 1.0, "prop": "AccumulativeOutputEnergy"}, "InternalTemperature": {"start": 127, "len": 1, "slaveId": 201, "type": "U16", "alias": "internal_temperature", "unit": "", "multiply": 10.0, "prop": "InternalTemperature"}, "Soc": {"start": 130, "len": 1, "slaveId": 201, "type": "U16", "alias": "soc", "unit": "", "multiply": 1.0, "prop": "Soc"}, "Soh": {"start": 131, "len": 1, "slaveId": 201, "type": "U16", "alias": "soh", "unit": "", "multiply": 1.0, "prop": "Soh"}, "SohReady": {"start": 132, "len": 1, "slaveId": 201, "type": "U16", "alias": "soh_ready", "unit": "", "multiply": 1.0, "prop": "So<PERSON><PERSON><PERSON>y"}, "FullChargeCapacity": {"start": 133, "len": 2, "slaveId": 201, "type": "U32", "alias": "full_charge_capacity", "unit": "", "multiply": 10.0, "prop": "FullChargeCapacity"}, "RemainingCapacity": {"start": 135, "len": 2, "slaveId": 201, "type": "U32", "alias": "remaining_capacity", "unit": "", "multiply": 10.0, "prop": "RemainingCapacity"}, "AvailableEnergy": {"start": 137, "len": 2, "slaveId": 201, "type": "U32", "alias": "available_energy", "unit": "", "multiply": 0.1, "prop": "AvailableEnergy"}, "ResistorUpdateCount": {"start": 139, "len": 1, "slaveId": 201, "type": "U16", "alias": "resistor_update_count", "unit": "", "multiply": 1.0, "prop": "ResistorUpdateCount"}, "EocStatus": {"start": 140, "len": 1, "slaveId": 201, "type": "U16", "alias": "eoc_status", "unit": "", "multiply": 1.0, "prop": "EocStatus"}, "CycleCount": {"start": 141, "len": 1, "slaveId": 201, "type": "U16", "alias": "cycle_count", "unit": "", "multiply": 1.0, "prop": "CycleCount"}, "RequireForceCharge": {"start": 142, "len": 1, "slaveId": 201, "type": "U16", "alias": "require_force_charge", "unit": "", "multiply": 1.0, "prop": "RequireForceCharge"}, "ContactorOcCount": {"start": 143, "len": 1, "slaveId": 201, "type": "U16", "alias": "contactor_oc_count", "unit": "", "multiply": 1.0, "prop": "ContactorOcCount"}, "BatteryInSerial": {"start": 200, "len": 1, "slaveId": 201, "type": "U16", "alias": "battery_in_serial", "unit": "", "multiply": 1.0, "prop": "BatteryInSerial"}, "TemperatureChannels": {"start": 201, "len": 1, "slaveId": 201, "type": "U16", "alias": "temperature_channels", "unit": "", "multiply": 1.0, "prop": "TemperatureChannels"}, "Voltage": {"start": 202, "len": 1, "slaveId": 201, "type": "U16", "alias": "voltage", "unit": "", "multiply": 10.0, "prop": "Voltage"}, "Current": {"start": 203, "len": 1, "slaveId": 201, "type": "U16", "alias": "current", "unit": "", "multiply": 10.0, "prop": "Current"}, "ChargeCurrentLimit": {"start": 204, "len": 1, "slaveId": 201, "type": "U16", "alias": "charge_current_limit", "unit": "", "multiply": 10.0, "prop": "ChargeCurrentLimit"}, "DischargeCurrentLimit": {"start": 205, "len": 1, "slaveId": 201, "type": "U16", "alias": "discharge_current_limit", "unit": "", "multiply": 10.0, "prop": "DischargeCurrentLimit"}, "ChargeVoltageLimit": {"start": 206, "len": 1, "slaveId": 201, "type": "U16", "alias": "charge_voltage_limit", "unit": "", "multiply": 10.0, "prop": "ChargeVoltageLimit"}, "DischargeVoltageLimit": {"start": 207, "len": 1, "slaveId": 201, "type": "U16", "alias": "discharge_voltage_limit", "unit": "", "multiply": 10.0, "prop": "DischargeVoltageLimit"}, "HighestCellVoltage": {"start": 208, "len": 1, "slaveId": 201, "type": "U16", "alias": "highest_cell_voltage", "unit": "", "multiply": 1000.0, "prop": "HighestCellVoltage"}, "HighestCellVoltageId": {"start": 209, "len": 1, "slaveId": 201, "type": "U16", "alias": "highest_cell_voltage_id", "unit": "", "multiply": 1.0, "prop": "HighestCellVoltageId"}, "LowestCellVoltage": {"start": 210, "len": 1, "slaveId": 201, "type": "U16", "alias": "lowest_cell_voltage", "unit": "", "multiply": 1000.0, "prop": "LowestCellVoltage"}, "LowestCellVoltageId": {"start": 211, "len": 1, "slaveId": 201, "type": "U16", "alias": "lowest_cell_voltage_id", "unit": "", "multiply": 1.0, "prop": "LowestCellVoltageId"}, "HighestCellTemperature": {"start": 212, "len": 1, "slaveId": 201, "type": "U16", "alias": "highest_cell_temperature", "unit": "", "multiply": 10.0, "prop": "HighestCellTemperature"}, "HighestCellTemperatureId": {"start": 213, "len": 1, "slaveId": 201, "type": "U16", "alias": "highest_cell_temperature_id", "unit": "", "multiply": 1.0, "prop": "HighestCellTemperatureId"}, "LowestCellTemperature": {"start": 214, "len": 1, "slaveId": 201, "type": "U16", "alias": "lowest_cell_temperature", "unit": "", "multiply": 10.0, "prop": "LowestCellTemperature"}, "LowestCellTemperatureId": {"start": 215, "len": 1, "slaveId": 201, "type": "U16", "alias": "lowest_cell_temperature_id", "unit": "", "multiply": 1.0, "prop": "LowestCellTemperatureId"}, "CellFaultBits": {"start": 216, "len": 16, "slaveId": 201, "type": "BIT", "alias": "cell_fault_bits", "unit": "", "multiply": 1.0, "prop": "CellFaultBits"}, "Z100SoftwareVersion": {"start": 400, "len": 16, "slaveId": 201, "type": "ASCII", "alias": "z100_software_version", "unit": "", "multiply": 1.0, "prop": "Z100SoftwareVersion"}, "Voltages": {"start": 1000, "len": 256, "slaveId": 201, "type": "U16", "alias": "voltages", "unit": "", "multiply": 1000.0, "prop": "Voltages"}, "Temperatures": {"start": 2000, "len": 256, "slaveId": 201, "type": "S16", "alias": "temperatures", "unit": "", "multiply": 10.0, "prop": "Temperatures"}, "Resistance": {"start": 3000, "len": 256, "slaveId": 201, "type": "U16", "alias": "resistance", "unit": "", "multiply": 1000.0, "prop": "Resistance"}}, "WeiHengBatteryInternalData": {"WatermarkDefault": {"start": 10035, "len": 1, "slaveId": 201, "type": "U16", "alias": "watermark_default", "unit": "", "multiply": 1.0, "prop": "WatermarkDefault"}, "WatermarkCan": {"start": 10036, "len": 1, "slaveId": 201, "type": "U16", "alias": "watermark_can", "unit": "", "multiply": 1.0, "prop": "WatermarkCan"}, "WatermarkResistance": {"start": 10037, "len": 1, "slaveId": 201, "type": "U16", "alias": "watermark_resistance", "unit": "", "multiply": 1.0, "prop": "WatermarkResistance"}, "WatermarkFlashUpdate": {"start": 10038, "len": 1, "slaveId": 201, "type": "U16", "alias": "watermark_flash_update", "unit": "", "multiply": 1.0, "prop": "WatermarkFlashUpdate"}, "WatermarkModbus": {"start": 10039, "len": 1, "slaveId": 201, "type": "U16", "alias": "watermark_modbus", "unit": "", "multiply": 1.0, "prop": "WatermarkModbus"}, "WatermarkPl455": {"start": 10040, "len": 1, "slaveId": 201, "type": "U16", "alias": "watermark_pl455", "unit": "", "multiply": 1.0, "prop": "WatermarkPl455"}, "FreeHeap": {"start": 10041, "len": 1, "slaveId": 201, "type": "U16", "alias": "free_heap", "unit": "", "multiply": 1.0, "prop": "FreeHeap"}, "AdcFps": {"start": 10042, "len": 1, "slaveId": 201, "type": "U16", "alias": "adc_fps", "unit": "", "multiply": 10.0, "prop": "AdcFps"}, "BatteryPackFps": {"start": 10043, "len": 1, "slaveId": 201, "type": "U16", "alias": "battery_pack_fps", "unit": "", "multiply": 10.0, "prop": "BatteryPackFps"}, "Pl455Fps": {"start": 10044, "len": 1, "slaveId": 201, "type": "U16", "alias": "pl455_fps", "unit": "", "multiply": 10.0, "prop": "Pl455Fps"}, "BatteryPackRunTimeMs": {"start": 10051, "len": 1, "slaveId": 201, "type": "U16", "alias": "battery_pack_run_time_ms", "unit": "", "multiply": 1.0, "prop": "BatteryPackRunTimeMs"}, "Pl455RunTmeMs": {"start": 10052, "len": 1, "slaveId": 201, "type": "U16", "alias": "pl455_run_tme_ms", "unit": "", "multiply": 1.0, "prop": "Pl455RunTmeMs"}, "InIap": {"start": 10053, "len": 1, "slaveId": 201, "type": "U16", "alias": "in_IAP", "unit": "", "multiply": 1.0, "prop": "InIap"}, "FaultFromBus": {"start": 10058, "len": 1, "slaveId": 201, "type": "U16", "alias": "fault_from_bus", "unit": "", "multiply": 1.0, "prop": "FaultFromBus"}, "HydrogenCh1": {"start": 10059, "len": 1, "slaveId": 201, "type": "U16", "alias": "hydrogen_ch1", "unit": "", "multiply": 1.0, "prop": "HydrogenCh1"}, "HydrogenCh2": {"start": 10060, "len": 1, "slaveId": 201, "type": "U16", "alias": "hydrogen_ch2", "unit": "", "multiply": 1.0, "prop": "HydrogenCh2"}}, "SafetyParameter": {"StepPower": {"start": 44001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwSafety_ID", "unit": "", "multiply": 1.0, "prop": "SafetyID"}}}, "settings": {"EnergySetting": {"EtotalCharge": {"start": 32081, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_Charge", "unit": "kWh", "multiply": 10.0, "prop": "ETotalCharge"}, "EtotalDischarge": {"start": 32083, "len": 2, "slaveId": 1, "type": "U32", "alias": "BatETotal_DisCharge", "unit": "kWh", "multiply": 10.0, "prop": "ETotalDischarge"}, "EtotalPvToGrid": {"start": 33032, "len": 2, "slaveId": 1, "type": "U32", "alias": "dwETotal_ToGrid_PV", "unit": "", "multiply": 10.0, "prop": "ETotalPvToGrid"}, "EtotalToGrid": {"start": 34001, "len": 2, "slaveId": 1, "type": "U32", "alias": "dwETotal_ToGrid", "unit": "", "multiply": 10.0, "prop": "ETotalToGrid"}, "EtotalFromGrid": {"start": 34003, "len": 2, "slaveId": 1, "type": "U32", "alias": "dwETotal_FromGrid", "unit": "", "multiply": 10.0, "prop": "ETotalFromGrid"}, "EtotalPv": {"start": 34005, "len": 2, "slaveId": 1, "type": "U32", "alias": "dwETotal_Pv", "unit": "", "multiply": 10.0, "prop": "ETotalPv"}, "EtotalEps": {"start": 34011, "len": 2, "slaveId": 1, "type": "U32", "alias": "dwETotal_EPS", "unit": "", "multiply": 10.0, "prop": "ETotalEps"}, "Htotal": {"start": 34013, "len": 2, "slaveId": 1, "type": "U32", "alias": "dwHTotal", "unit": "", "multiply": 1.0, "prop": "HTotal"}, "HtotalGridMode": {"start": 34015, "len": 2, "slaveId": 1, "type": "U32", "alias": "dwHTotal_GridMode", "unit": "", "multiply": 1.0, "prop": "HTotalGridMode"}, "HtotalEpsMode": {"start": 34017, "len": 2, "slaveId": 1, "type": "U32", "alias": "dwHTotal_EPSMode", "unit": "", "multiply": 1.0, "prop": "HTotalEpsMode"}, "EtodayPv": {"start": 34019, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEToday_Pv", "unit": "", "multiply": 10.0, "prop": "ETodayPv"}, "EtodayCharge": {"start": 34020, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEToday_Charge", "unit": "", "multiply": 10.0, "prop": "ETodayCharge"}, "EtodayDischarge": {"start": 34021, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEToday_DisCharge", "unit": "", "multiply": 10.0, "prop": "ETodayDischarge"}, "EtodayToGrid": {"start": 34022, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEToday_ToGrid", "unit": "", "multiply": 10.0, "prop": "ETodayToGrid"}, "EtodayFromGrid": {"start": 34023, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEToday_FromGrid", "unit": "", "multiply": 10.0, "prop": "ETodayFromGrid"}, "EtodayEps": {"start": 34024, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEToday_EPS", "unit": "", "multiply": 10.0, "prop": "ETodayEps"}}, "BasicInformation": {"Sn": {"start": 40001, "len": 10, "slaveId": 1, "type": "ASCII", "alias": "sSerialNumber", "unit": "", "multiply": 1.0, "prop": "Sn"}, "ModeName": {"start": 40011, "len": 9, "slaveId": 1, "type": "ASCII", "alias": "sModleName", "unit": "", "multiply": 1.0, "prop": "ModeName"}, "BrandName": {"start": 40020, "len": 6, "slaveId": 1, "type": "ASCII", "alias": "sBrandName", "unit": "", "multiply": 1.0, "prop": "BrandName"}, "Manufacturer": {"start": 40026, "len": 8, "slaveId": 1, "type": "ASCII", "alias": "sManufacturer", "unit": "", "multiply": 1.0, "prop": "Manufacturer"}, "Rtc": {"start": 40034, "len": 2, "slaveId": 1, "type": "U32", "alias": "uwRTC", "unit": "", "multiply": 1.0, "prop": "Rtc"}}, "BatterySetting": {"DischargeToGridFlag": {"start": 40046, "len": 1, "slaveId": 1, "type": "U16", "alias": "DisChargeToGridFlag", "unit": "", "multiply": 1.0, "prop": "DischargeToGridFlag"}, "MinBatteryCapacity": {"start": 41002, "len": 1, "slaveId": 1, "type": "U16", "alias": "Battery MinCapcity(0-100)", "unit": "", "multiply": 1.0, "prop": "MinBatteryCapacity"}, "ChargeModeCode": {"start": 41003, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatCapcity_Min", "unit": "", "multiply": 1.0, "prop": "ChargeModeCode"}, "ChargeBeginHour1": {"start": 41008, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime1_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeBeginHour1"}, "ChargeBeginMinute1": {"start": 41009, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime1_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeBeginMinute1"}, "ChargeEndHour1": {"start": 41010, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime1_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeEndHour1"}, "ChargeEndMinute1": {"start": 41011, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime1_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeEndMinute1"}, "ChargePower1": {"start": 41012, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power charge  set point 1", "unit": "", "multiply": 1.0, "prop": "ChargePower1"}, "DisChargeBeginHour1": {"start": 41013, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime1_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginHour1"}, "DisChargeBeginMinute1": {"start": 41014, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime1_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginMinute1"}, "DisChargeEndHour1": {"start": 41015, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerEndTime1_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeEndHour1"}, "DisChargeEndMinute1": {"start": 41016, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDishargerEndTime1_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeEndMinute1"}, "DisChargePower1": {"start": 41017, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power    discharge set point  1", "unit": "", "multiply": 1.0, "prop": "DisChargePower1"}, "ChargeBeginHour2": {"start": 41018, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime1_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeBeginHour2"}, "ChargeBeginMinute2": {"start": 41019, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime1_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeBeginMinute2"}, "ChargeEndHour2": {"start": 41020, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime2_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeEndHour2"}, "ChargeEndMinute2": {"start": 41021, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime2_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeEndMinute2"}, "ChargePower2": {"start": 41022, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power  charge set point 2", "unit": "", "multiply": 1.0, "prop": "ChargePower2"}, "DisChargeBeginHour2": {"start": 41023, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime2_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginHour2"}, "DisChargeBeginMinute2": {"start": 41024, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime2_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginMinute2"}, "DisChargeEndHour2": {"start": 41025, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerEndTime2_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeEndHour2"}, "DisChargeEndMinute2": {"start": 41026, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDishargerEndTime2_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeEndMinute2"}, "DisChargePower2": {"start": 41027, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power     discharge set point  2", "unit": "", "multiply": 1.0, "prop": "DisChargePower2"}, "MaxFeedIn": {"start": 41037, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwMaxFeedin_SoftLimit", "unit": "", "multiply": 1.0, "prop": "MaxFeedIn"}, "EpsMinBatteryCapacity": {"start": 41042, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPSBatCapcityMin", "unit": "", "multiply": 1.0, "prop": "EpsMinBatteryCapacity"}, "ChargeBeginHour3": {"start": 41043, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime3_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeBeginHour3"}, "ChargeBeginMinute3": {"start": 41044, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime3_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeBeginMinute3"}, "ChargeEndHour3": {"start": 41045, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime3_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeEndHour3"}, "ChargeEndMinute3": {"start": 41046, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime3_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeEndMinute3"}, "ChargePower3": {"start": 41047, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power  charge set point 3", "unit": "", "multiply": 1.0, "prop": "ChargePower3"}, "DisChargeBeginHour3": {"start": 41048, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime3_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginHour3"}, "DisChargeBeginMinute3": {"start": 41049, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime3_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginMinute3"}, "DisChargeEndHour3": {"start": 41050, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerEndTime3_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeEndHour3"}, "DisChargeEndMinute3": {"start": 41051, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDishargerEndTime3_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeEndMinute3"}, "DisChargePower3": {"start": 41052, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power     discharge set point  3", "unit": "", "multiply": 1.0, "prop": "DisChargePower3"}, "ChargeBeginHour4": {"start": 41053, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime4_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeBeginHour4"}, "ChargeBeginMinute4": {"start": 41054, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime4_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeBeginMinute4"}, "ChargeEndHour4": {"start": 41055, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime4_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeEndHour4"}, "ChargeEndMinute4": {"start": 41056, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime4_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeEndMinute4"}, "ChargePower4": {"start": 41057, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power  charge set point 4", "unit": "", "multiply": 1.0, "prop": "ChargePower4"}, "DisChargeBeginHour4": {"start": 41058, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime4_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginHour4"}, "DisChargeBeginMinute4": {"start": 41059, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime4_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginMinute4"}, "DisChargeEndHour4": {"start": 41060, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerEndTime4_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeEndHour4"}, "DisChargeEndMinute4": {"start": 41061, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDishargerEndTime4_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeEndMinute4"}, "DisChargePower4": {"start": 41062, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power     discharge set point  4", "unit": "", "multiply": 1.0, "prop": "DisChargePower4"}, "ChargeBeginHour5": {"start": 41063, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime5_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeBeginHour5"}, "ChargeBeginMinute5": {"start": 41064, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime4_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeBeginMinute5"}, "ChargeEndHour5": {"start": 41065, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime5_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeEndHour5"}, "ChargeEndMinute5": {"start": 41066, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime5_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeEndMinute5"}, "ChargePower5": {"start": 41067, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power  charge set point 5", "unit": "", "multiply": 1.0, "prop": "ChargePower5"}, "DisChargeBeginHour5": {"start": 41068, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime5_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginHour5"}, "DisChargeBeginMinute5": {"start": 41069, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime5_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginMinute5"}, "DisChargeEndHour5": {"start": 41070, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerEndTime5_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeEndHour5"}, "DisChargeEndMinute5": {"start": 41071, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDishargerEndTime5_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeEndMinute5"}, "DisChargePower5": {"start": 41072, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power     discharge set point  5", "unit": "", "multiply": 1.0, "prop": "DisChargePower5"}, "ChargeBeginHour6": {"start": 41073, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime6_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeBeginHour6"}, "ChargeBeginMinute6": {"start": 41074, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime6_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeBeginMinute6"}, "ChargeEndHour6": {"start": 41075, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime6_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeEndHour6"}, "ChargeEndMinute6": {"start": 41076, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime6_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeEndMinute6"}, "ChargePower6": {"start": 41077, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power  charge set point 6", "unit": "", "multiply": 1.0, "prop": "ChargePower6"}, "DisChargeBeginHour6": {"start": 41078, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime6_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginHour6"}, "DisChargeBeginMinute6": {"start": 41079, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime6_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginMinute6"}, "DisChargeEndHour6": {"start": 41080, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerEndTime6_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeEndHour6"}, "DisChargeEndMinute6": {"start": 41081, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDishargerEndTime6_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeEndMinute6"}, "DisChargePower6": {"start": 41082, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power     discharge set point  6", "unit": "", "multiply": 1.0, "prop": "DisChargePower6"}, "ChargeBeginHour7": {"start": 41083, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime7_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeBeginHour7"}, "ChargeBeginMinute7": {"start": 41084, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime7_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeBeginMinute7"}, "ChargeEndHour7": {"start": 41085, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime7_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeEndHour7"}, "ChargeEndMinute7": {"start": 41086, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime7_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeEndMinute7"}, "ChargePower7": {"start": 41087, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power  charge set point 7", "unit": "", "multiply": 1.0, "prop": "ChargePower7"}, "DisChargeBeginHour7": {"start": 41088, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime7_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginHour7"}, "DisChargeBeginMinute7": {"start": 41089, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime7_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginMinute7"}, "DisChargeEndHour7": {"start": 41090, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerEndTime7_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeEndHour7"}, "DisChargeEndMinute7": {"start": 41091, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDishargerEndTime7_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeEndMinute7"}, "DisChargePower7": {"start": 41092, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power     discharge set point  7", "unit": "", "multiply": 1.0, "prop": "DisChargePower7"}, "ChargeBeginHour8": {"start": 41093, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime8_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeBeginHour8"}, "ChargeBeginMinute8": {"start": 41094, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime8_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeBeginMinute8"}, "ChargeEndHour8": {"start": 41095, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime8_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeEndHour8"}, "ChargeEndMinute8": {"start": 41096, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime8_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeEndMinute8"}, "ChargePower8": {"start": 41097, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power  charge set point 8", "unit": "", "multiply": 1.0, "prop": "ChargePower8"}, "DisChargeBeginHour8": {"start": 41098, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime8_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginHour8"}, "DisChargeBeginMinute8": {"start": 41099, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime8_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginMinute8"}, "DisChargeEndHour8": {"start": 41100, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerEndTime8_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeEndHour8"}, "DisChargeEndMinute8": {"start": 41101, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDishargerEndTime8_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeEndMinute8"}, "DisChargePower8": {"start": 41102, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power     discharge set point  8", "unit": "", "multiply": 1.0, "prop": "DisChargePower8"}, "ChargeBeginHour9": {"start": 41103, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime9_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeBeginHour9"}, "ChargeBeginMinute9": {"start": 41104, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime9_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeBeginMinute9"}, "ChargeEndHour9": {"start": 41105, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime9_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeEndHour9"}, "ChargeEndMinute9": {"start": 41106, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime9_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeEndMinute9"}, "ChargePower9": {"start": 41107, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power  charge set point 9", "unit": "", "multiply": 1.0, "prop": "ChargePower9"}, "DisChargeBeginHour9": {"start": 41108, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime9_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginHour9"}, "DisChargeBeginMinute9": {"start": 41109, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime9_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginMinute9"}, "DisChargeEndHour9": {"start": 41110, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerEndTime9_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeEndHour9"}, "DisChargeEndMinute9": {"start": 41111, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDishargerEndTime9_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeEndMinute9"}, "DisChargePower9": {"start": 41112, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power     discharge set point  9", "unit": "", "multiply": 1.0, "prop": "DisChargePower9"}, "ChargeBeginHour10": {"start": 41113, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime10_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeBeginHour10"}, "ChargeBeginMinute10": {"start": 41114, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime10_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeBeginMinute10"}, "ChargeEndHour10": {"start": 41115, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime10_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeEndHour10"}, "ChargeEndMinute10": {"start": 41116, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime10_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeEndMinute10"}, "ChargePower10": {"start": 41117, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power  charge set point 10", "unit": "", "multiply": 1.0, "prop": "ChargePower10"}, "DisChargeBeginHour10": {"start": 41118, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime10_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginHour10"}, "DisChargeBeginMinute10": {"start": 41119, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime10_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginMinute10"}, "DisChargeEndHour10": {"start": 41120, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerEndTime10_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeEndHour10"}, "DisChargeEndMinute10": {"start": 41121, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDishargerEndTime10_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeEndMinute10"}, "DisChargePower10": {"start": 41122, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power     discharge set point  10", "unit": "", "multiply": 1.0, "prop": "DisChargePower10"}, "ChargeBeginHour11": {"start": 41123, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime11_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeBeginHour11"}, "ChargeBeginMinute11": {"start": 41124, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime11_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeBeginMinute11"}, "ChargeEndHour11": {"start": 41125, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime11_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeEndHour11"}, "ChargeEndMinute11": {"start": 41126, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime11_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeEndMinute11"}, "ChargePower11": {"start": 41127, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power  charge set point 11", "unit": "", "multiply": 1.0, "prop": "ChargePower11"}, "DisChargeBeginHour11": {"start": 41128, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime11_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginHour11"}, "DisChargeBeginMinute11": {"start": 41129, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime11_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginMinute11"}, "DisChargeEndHour11": {"start": 41130, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerEndTime11_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeEndHour11"}, "DisChargeEndMinute11": {"start": 41131, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDishargerEndTime11_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeEndMinute11"}, "DisChargePower11": {"start": 41132, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power     discharge set point  11", "unit": "", "multiply": 1.0, "prop": "DisChargePower11"}, "ChargeBeginHour12": {"start": 41133, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime12_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeBeginHour12"}, "ChargeBeginMinute12": {"start": 41134, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerStartTime12_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeBeginMinute12"}, "ChargeEndHour12": {"start": 41135, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime12_Hour", "unit": "", "multiply": 1.0, "prop": "ChargeEndHour12"}, "ChargeEndMinute12": {"start": 41136, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwChargerEndTime12_Minute", "unit": "", "multiply": 1.0, "prop": "ChargeEndMinute12"}, "ChargePower12": {"start": 41137, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power  charge set point 12", "unit": "", "multiply": 1.0, "prop": "ChargePower12"}, "DisChargeBeginHour12": {"start": 41138, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime12_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginHour12"}, "DisChargeBeginMinute12": {"start": 41139, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerStartTime12_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeBeginMinute12"}, "DisChargeEndHour12": {"start": 41140, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDischargerEndTime12_Hour", "unit": "", "multiply": 1.0, "prop": "DisChargeEndHour12"}, "DisChargeEndMinute12": {"start": 41141, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDishargerEndTime12_Minute", "unit": "", "multiply": 1.0, "prop": "DisChargeEndMinute12"}, "DisChargePower12": {"start": 41142, "len": 1, "slaveId": 1, "type": "U16", "alias": "Power     discharge set point  12", "unit": "", "multiply": 1.0, "prop": "DisChargePower12"}, "ChargeAbandonPv1": {"start": 41143, "len": 1, "slaveId": 1, "type": "U16", "alias": "Chg1_AbandonPV", "unit": "", "multiply": 1.0, "prop": "ChargeAbandonPv1"}, "DisChargeAbandonPv1": {"start": 41144, "len": 1, "slaveId": 1, "type": "U16", "alias": "DisChg1_AbandonPV", "unit": "", "multiply": 1.0, "prop": "DisChargeAbandonPv1"}, "ChargeAbandonPv2": {"start": 41145, "len": 1, "slaveId": 1, "type": "U16", "alias": "Chg2_AbandonPV", "unit": "", "multiply": 1.0, "prop": "ChargeAbandonPv2"}, "DisChargeAbandonPv2": {"start": 41146, "len": 1, "slaveId": 1, "type": "U16", "alias": "DisChg2_AbandonPV", "unit": "", "multiply": 1.0, "prop": "DisChargeAbandonPv2"}, "ChargeAbandonPv3": {"start": 41147, "len": 1, "slaveId": 1, "type": "U16", "alias": "Chg3_AbandonPV", "unit": "", "multiply": 1.0, "prop": "ChargeAbandonPv3"}, "DisChargeAbandonPv3": {"start": 41148, "len": 1, "slaveId": 1, "type": "U16", "alias": "DisChg3_AbandonPV", "unit": "", "multiply": 1.0, "prop": "DisChargeAbandonPv3"}, "ChargeAbandonPv4": {"start": 41149, "len": 1, "slaveId": 1, "type": "U16", "alias": "Chg4_AbandonPV", "unit": "", "multiply": 1.0, "prop": "ChargeAbandonPv4"}, "DisChargeAbandonPv4": {"start": 41150, "len": 1, "slaveId": 1, "type": "U16", "alias": "DisChg4_AbandonPV", "unit": "", "multiply": 1.0, "prop": "DisChargeAbandonPv4"}, "ChargeAbandonPv5": {"start": 41151, "len": 1, "slaveId": 1, "type": "U16", "alias": "Chg5_AbandonPV", "unit": "", "multiply": 1.0, "prop": "ChargeAbandonPv5"}, "DisChargeAbandonPv5": {"start": 41152, "len": 1, "slaveId": 1, "type": "U16", "alias": "DisChg5_AbandonPV", "unit": "", "multiply": 1.0, "prop": "DisChargeAbandonPv5"}, "ChargeAbandonPv6": {"start": 41153, "len": 1, "slaveId": 1, "type": "U16", "alias": "Chg6_AbandonPV", "unit": "", "multiply": 1.0, "prop": "ChargeAbandonPv6"}, "DisChargeAbandonPv6": {"start": 41154, "len": 1, "slaveId": 1, "type": "U16", "alias": "DisChg6_AbandonPV", "unit": "", "multiply": 1.0, "prop": "DisChargeAbandonPv6"}, "ChargeAbandonPv7": {"start": 41155, "len": 1, "slaveId": 1, "type": "U16", "alias": "Chg7_AbandonPV", "unit": "", "multiply": 1.0, "prop": "ChargeAbandonPv7"}, "DisChargeAbandonPv7": {"start": 41156, "len": 1, "slaveId": 1, "type": "U16", "alias": "DisChg7_AbandonPV", "unit": "", "multiply": 1.0, "prop": "DisChargeAbandonPv7"}, "ChargeAbandonPv8": {"start": 41157, "len": 1, "slaveId": 1, "type": "U16", "alias": "Chg8_AbandonPV", "unit": "", "multiply": 1.0, "prop": "ChargeAbandonPv8"}, "DisChargeAbandonPv8": {"start": 41158, "len": 1, "slaveId": 1, "type": "U16", "alias": "DisChg8_AbandonPV", "unit": "", "multiply": 1.0, "prop": "DisChargeAbandonPv8"}, "ChargeAbandonPv9": {"start": 41159, "len": 1, "slaveId": 1, "type": "U16", "alias": "Chg9_AbandonPV", "unit": "", "multiply": 1.0, "prop": "ChargeAbandonPv9"}, "DisChargeAbandonPv9": {"start": 41160, "len": 1, "slaveId": 1, "type": "U16", "alias": "DisChg9_AbandonPV", "unit": "", "multiply": 1.0, "prop": "DisChargeAbandonPv9"}, "ChargeAbandonPv10": {"start": 41161, "len": 1, "slaveId": 1, "type": "U16", "alias": "Chg10_AbandonPV", "unit": "", "multiply": 1.0, "prop": "ChargeAbandonPv10"}, "DisChargeAbandonPv10": {"start": 41162, "len": 1, "slaveId": 1, "type": "U16", "alias": "DisChg10_AbandonPV", "unit": "", "multiply": 1.0, "prop": "DisChargeAbandonPv10"}, "ChargeAbandonPv11": {"start": 41163, "len": 1, "slaveId": 1, "type": "U16", "alias": "Chg11_AbandonPV", "unit": "", "multiply": 1.0, "prop": "ChargeAbandonPv11"}, "DisChargeAbandonPv11": {"start": 41164, "len": 1, "slaveId": 1, "type": "U16", "alias": "DisChg11_AbandonPV", "unit": "", "multiply": 1.0, "prop": "DisChargeAbandonPv11"}, "ChargeAbandonPv12": {"start": 41165, "len": 1, "slaveId": 1, "type": "U16", "alias": "Chg12_AbandonPV", "unit": "", "multiply": 1.0, "prop": "ChargeAbandonPv12"}, "DisChargeAbandonPv12": {"start": 41166, "len": 1, "slaveId": 1, "type": "U16", "alias": "DisChg12_AbandonPV", "unit": "", "multiply": 1.0, "prop": "DisChargeAbandonPv12"}}, "Execute": {"RemoteOff": {"start": 42001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwRemote_Off", "unit": "", "multiply": 1.0, "prop": "<PERSON><PERSON><PERSON><PERSON>"}, "AteMode": {"start": 42005, "len": 1, "slaveId": 1, "type": "U16", "alias": "wFirmwareStatus", "unit": "", "multiply": 1.0, "prop": "AteMode"}, "Overload": {"start": 42006, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFlag_OverLoadEn", "unit": "", "multiply": 1.0, "prop": "Overload"}, "RatedPower": {"start": 42009, "len": 1, "slaveId": 1, "type": "U16", "alias": "Model(Rated Power)", "unit": "", "multiply": 1.0, "prop": "RatedPower"}, "VpvStart": {"start": 42010, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVpvStart", "unit": "", "multiply": 10.0, "prop": "VpvStart"}, "AccessGridType": {"start": 42012, "len": 1, "slaveId": 1, "type": "U16", "alias": "AC_Input", "unit": "", "multiply": 1.0, "prop": "AccessGridType"}, "UpsEnable": {"start": 42013, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPS_Mute", "unit": "", "multiply": 1.0, "prop": "UpsEnable"}, "PvConnectMode": {"start": 42014, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPVConnectMode", "unit": "", "multiply": 1.0, "prop": "PvConnectMode"}, "GridLossCheckType": {"start": 42015, "len": 1, "slaveId": 1, "type": "U16", "alias": "GridLossCheckType", "unit": "", "multiply": 1.0, "prop": "GridLossCheckType"}, "ExternalSignal": {"start": 42016, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwExternalSignal", "unit": "", "multiply": 1.0, "prop": "ExternalSignal"}, "LocalCmd": {"start": 42017, "len": 1, "slaveId": 1, "type": "U16", "alias": "LocalCmd", "unit": "", "multiply": 1.0, "prop": "LocalCmd"}, "AutoTestEnable": {"start": 42018, "len": 1, "slaveId": 1, "type": "U16", "alias": "AutoTestEnable", "unit": "", "multiply": 1.0, "prop": "AutoTestEnable"}}, "DspCalibration": {"StepPower": {"start": 43001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwStep_PowerCali", "unit": "", "multiply": 1.0, "prop": "<PERSON><PERSON><PERSON><PERSON>"}, "InvPower": {"start": 43002, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPower_Cali", "unit": "", "multiply": 1.0, "prop": "InvPower"}, "Vac": {"start": 43004, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_Cali", "unit": "", "multiply": 10.0, "prop": "VAc"}, "Vpv1": {"start": 43005, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVpv1_Cali", "unit": "", "multiply": 10.0, "prop": "VPv1"}, "Vpv2": {"start": 43006, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVpv2_Cali", "unit": "", "multiply": 10.0, "prop": "VPv2"}, "Vbus": {"start": 43008, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVbus_Cali", "unit": "", "multiply": 10.0, "prop": "VBus"}, "Vbattery": {"start": 43010, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVbatt_Cali", "unit": "", "multiply": 10.0, "prop": "VBattery"}, "FuncEnPfCalib": {"start": 43011, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwFuncEnPfCalib", "unit": "", "multiply": 1.0, "prop": "FuncEnPfCalib"}, "ReacPwrOffsetX10": {"start": 43012, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwReacPwrOffset_x10", "unit": "", "multiply": 10.0, "prop": "ReacPwrOffsetX10"}, "Ibattery": {"start": 43013, "len": 1, "slaveId": 1, "type": "S16", "alias": "wBuckBstCurrCali", "unit": "", "multiply": 100.0, "prop": "IBattery"}, "Veps": {"start": 43016, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVolt_Cali_Eps", "unit": "", "multiply": 10.0, "prop": "VEps"}, "Ieps": {"start": 43017, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEpsLoadCurrCali", "unit": "", "multiply": 100.0, "prop": "IEps"}, "Ipv1": {"start": 43018, "len": 1, "slaveId": 1, "type": "S16", "alias": "wPvCurr1Cali", "unit": "", "multiply": 10.0, "prop": "IPv1"}, "Ipv2": {"start": 43019, "len": 1, "slaveId": 1, "type": "S16", "alias": "wPvCurr2Cali", "unit": "", "multiply": 10.0, "prop": "IPv2"}, "Vpv3": {"start": 43029, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwVpv3_Cali", "unit": "", "multiply": 10.0, "prop": "VPv3"}, "Vpv4": {"start": 43030, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwVpv4_Cali", "unit": "", "multiply": 10.0, "prop": "VPv4"}, "Ipv3": {"start": 43031, "len": 1, "slaveId": 1, "type": "S16", "alias": "wPvCurr3Cali", "unit": "", "multiply": 10.0, "prop": "IPv3"}, "Ipv4": {"start": 43032, "len": 1, "slaveId": 1, "type": "S16", "alias": "wPvCurr4Cali", "unit": "", "multiply": 10.0, "prop": "IPv4"}}, "SafetyParameter": {"StepPower": {"start": 44001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwSafety_ID", "unit": "", "multiply": 1.0, "prop": "<PERSON><PERSON><PERSON><PERSON>"}, "StartTime": {"start": 44003, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwStartTime", "unit": "", "multiply": 1.0, "prop": "StartTime"}, "Ov3": {"start": 44004, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OV3", "unit": "", "multiply": 10.0, "prop": "Ov3"}, "Ov3T": {"start": 44005, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OVT3", "unit": "", "multiply": 1.0, "prop": "Ov3T"}, "Ov2": {"start": 44006, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OV2", "unit": "", "multiply": 10.0, "prop": "Ov2"}, "Ov2T": {"start": 44007, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OVT2", "unit": "", "multiply": 1.0, "prop": "Ov2T"}, "Ov1": {"start": 44008, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OV1", "unit": "", "multiply": 10.0, "prop": "Ov1"}, "Ov1T": {"start": 44009, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OVT1", "unit": "", "multiply": 1.0, "prop": "Ov1T"}, "Uv1": {"start": 44010, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UV1", "unit": "", "multiply": 10.0, "prop": "Uv1"}, "Uv1T": {"start": 44011, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UVT1", "unit": "", "multiply": 1.0, "prop": "Uv1T"}, "Uv2": {"start": 44012, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UV2", "unit": "", "multiply": 10.0, "prop": "Uv2"}, "Uv2T": {"start": 44013, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UVT2", "unit": "", "multiply": 1.0, "prop": "Uv2T"}, "Uv3": {"start": 44014, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UV3", "unit": "", "multiply": 10.0, "prop": "Uv3"}, "Uv3T": {"start": 44015, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UVT3", "unit": "", "multiply": 1.0, "prop": "Uv3T"}, "OvRec": {"start": 44016, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OVRec", "unit": "", "multiply": 10.0, "prop": "OvRec"}, "OvRecT": {"start": 44017, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OVRecT", "unit": "", "multiply": 1.0, "prop": "OvRecT"}, "UvRec": {"start": 44018, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UVRec", "unit": "", "multiply": 10.0, "prop": "UvRec"}, "UvRecT": {"start": 44019, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UVRecT", "unit": "", "multiply": 1.0, "prop": "UvRecT"}, "Of3": {"start": 44020, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OF3", "unit": "", "multiply": 100.0, "prop": "Of3"}, "Of3T": {"start": 44021, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OFT3", "unit": "", "multiply": 1.0, "prop": "Of3T"}, "Of2": {"start": 44022, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OF2", "unit": "", "multiply": 100.0, "prop": "Of2"}, "Of2T": {"start": 44023, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OFT2", "unit": "", "multiply": 1.0, "prop": "Of2T"}, "Of1": {"start": 44024, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OF1", "unit": "", "multiply": 100.0, "prop": "Of1"}, "Of1T": {"start": 44025, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OFT1", "unit": "", "multiply": 1.0, "prop": "Of1T"}, "Uf1": {"start": 44026, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UF1", "unit": "", "multiply": 100.0, "prop": "Uf1"}, "Uf1T": {"start": 44027, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UFT1", "unit": "", "multiply": 1.0, "prop": "Uf1T"}, "Uf2": {"start": 44028, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UF2", "unit": "", "multiply": 100.0, "prop": "Uf2"}, "Uf2T": {"start": 44029, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UFT2", "unit": "", "multiply": 1.0, "prop": "Uf2T"}, "Uf3": {"start": 44030, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UF3", "unit": "", "multiply": 100.0, "prop": "Uf3"}, "Uf3T": {"start": 44031, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UFT3", "unit": "", "multiply": 1.0, "prop": "Uf3T"}, "OfRec": {"start": 44032, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OFRec", "unit": "", "multiply": 100.0, "prop": "OfRec"}, "OfRecT": {"start": 44033, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OFRecT", "unit": "", "multiply": 1.0, "prop": "OfRecT"}, "UfRec": {"start": 44034, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UFRec", "unit": "", "multiply": 100.0, "prop": "UfRec"}, "UfRecT": {"start": 44035, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_UFRecT", "unit": "", "multiply": 1.0, "prop": "UfRecT"}, "Ov10Min": {"start": 44036, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVac_OV10min", "unit": "", "multiply": 10.0, "prop": "Ov10Min"}, "ReconnectTime": {"start": 44045, "len": 1, "slaveId": 1, "type": "U16", "alias": "ReconnectTime", "unit": "", "multiply": 1.0, "prop": "ReconnectTime"}, "OfPowerRateLimit": {"start": 46011, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPower_Rate_Limit", "unit": "", "multiply": 100.0, "prop": "OfPowerRateLimit"}}, "ProtectFunctionEnable": {"HouseSideGridLimit": {"start": 40049, "len": 1, "slaveId": 1, "type": "U16", "alias": "HouseSideGridPowerLimit", "unit": "", "multiply": 1.0, "prop": "HouseSideGridLimit"}, "MachineSideGridLimit": {"start": 40050, "len": 1, "slaveId": 1, "type": "U16", "alias": "MachineSideGridPowerLimit", "unit": "", "multiply": 1.0, "prop": "MachineSideGridLimit"}, "EpsParallel": {"start": 40067, "len": 1, "slaveId": 1, "type": "U16", "alias": "epsParallel", "unit": "", "multiply": 1.0, "prop": "EpsParallel"}, "FunEnHex": {"start": 45001, "len": 2, "slaveId": 1, "type": "U32", "alias": "dwFunctionEn", "unit": "", "multiply": 1.0, "prop": "FunEnHex"}, "IsoLimit": {"start": 45004, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwISO_OvValue", "unit": "", "multiply": 1.0, "prop": "IsoLimit"}, "DciLimit": {"start": 45005, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwDCI_OvValue", "unit": "", "multiply": 1.0, "prop": "DciLimit"}, "GfcLimit": {"start": 45006, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwGFCI_OvValue", "unit": "", "multiply": 1.0, "prop": "GfcLimit"}, "VpvHighLimit": {"start": 45007, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVpvHigh", "unit": "", "multiply": 10.0, "prop": "VpvHighLimit"}, "VpvLowLimit": {"start": 45008, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwVpvLow", "unit": "", "multiply": 10.0, "prop": "VpvLowLimit"}, "OverTemper": {"start": 45015, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_OVER_TEMPER", "unit": "", "multiply": 1.0, "prop": "OverTemper"}, "GfciEnable": {"start": 45016, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_GFCI", "unit": "", "multiply": 1.0, "prop": "GfciEnable"}, "IsoEnable": {"start": 45017, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_ISO", "unit": "", "multiply": 1.0, "prop": "IsoEnable"}, "DciEnable": {"start": 45018, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_DCI", "unit": "", "multiply": 1.0, "prop": "DciEnable"}, "LvrtEnable": {"start": 45019, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_LVRT", "unit": "", "multiply": 1.0, "prop": "LvrtEnable"}, "GridVoltProject": {"start": 45020, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_GridV", "unit": "", "multiply": 1.0, "prop": "GridVoltProject"}, "GridFreqProject": {"start": 45021, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_GridF", "unit": "", "multiply": 1.0, "prop": "GridFreqProject"}, "IsLandEnable": {"start": 45022, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_ISLAND", "unit": "", "multiply": 1.0, "prop": "IsLandEnable"}, "FanEnable": {"start": 45023, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_FAN", "unit": "", "multiply": 1.0, "prop": "FanEnable"}, "ShadowMppt": {"start": 45024, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_SHADOW_MPPT", "unit": "", "multiply": 1.0, "prop": "ShadowMppt"}, "MinLoad10": {"start": 45025, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_VDE_ADD_10min", "unit": "", "multiply": 1.0, "prop": "MinLoad10"}, "OverFreqDerate": {"start": 45026, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_VDE_OVER_F", "unit": "", "multiply": 1.0, "prop": "OverFreqDerate"}, "Grid110Per": {"start": 45027, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_GridV_110_PER", "unit": "", "multiply": 1.0, "prop": "Grid110Per"}, "OverPowerU": {"start": 45028, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_OVER_U_PWR", "unit": "", "multiply": 1.0, "prop": "OverPowerU"}, "BuzzerEnable": {"start": 45029, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_BUZZER", "unit": "", "multiply": 1.0, "prop": "BuzzerEnable"}, "UnderFreqDerate": {"start": 45030, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUN_EN_under frequency derating", "unit": "", "multiply": 1.0, "prop": "UnderFreqDerate"}, "PeChk": {"start": 45031, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_PEChk", "unit": "", "multiply": 1.0, "prop": "PeChk"}, "GridNChk": {"start": 45032, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_GridNChk", "unit": "", "multiply": 1.0, "prop": "GridNChk"}, "FuncRstAfci": {"start": 45033, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_RST_AFCI", "unit": "", "multiply": 1.0, "prop": "FuncRstAfci"}, "FuncEnAfci": {"start": 45034, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_AFCI", "unit": "", "multiply": 1.0, "prop": "FuncEnAfci"}, "FuncEnFrt": {"start": 45035, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_FRT", "unit": "", "multiply": 1.0, "prop": "FuncEnFrt"}, "VppWorkMode": {"start": 41036, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwWorkMode", "unit": "", "multiply": 1.0, "prop": "VppWorkMode"}, "MaxFeedInSoftLimit": {"start": 41037, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwMaxFeedin_SoftLimit", "unit": "", "multiply": 1.0, "prop": "MaxFeedInSoftLimit"}, "MaxFeedInHardLimit": {"start": 41038, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwMaxFeedin_HardLimit", "unit": "", "multiply": 1.0, "prop": "MaxFeedInHardLimit"}, "MaxFeedInSoftEn": {"start": 41039, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwSoftLimitEn", "unit": "", "multiply": 1.0, "prop": "MaxFeedInSoftEn"}, "MaxFeedInHardEn": {"start": 41040, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwHardLimitEn", "unit": "", "multiply": 1.0, "prop": "MaxFeedInHardEn"}, "MeterEnable": {"start": 40038, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwMeter_FuncEn", "unit": "", "multiply": 1.0, "prop": "MeterEnable"}, "MeterType": {"start": 40043, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwMeter_Type", "unit": "", "multiply": 1.0, "prop": "MeterType"}, "FeedPhase": {"start": 40044, "len": 1, "slaveId": 1, "type": "U16", "alias": "FeedPhase", "unit": "", "multiply": 1.0, "prop": "FeedPhase"}, "ParallelWorkMode": {"start": 48120, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "ParallelWorkMode"}, "ParallelNum": {"start": 48121, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "ParallelNum"}}, "ActiveLimit": {"PwrManager": {"start": 46001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPwrManagerEn", "unit": "", "multiply": 1.0, "prop": "PwrManager"}, "PwrCtrlMode": {"start": 46002, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPwrCtrlMode", "unit": "", "multiply": 1.0, "prop": "PwrCtrlMode"}, "PwrLimitPercent": {"start": 46003, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPwrlimit_Percent", "unit": "", "multiply": 1.0, "prop": "PwrLimitPercent"}, "ResponseTime": {"start": 46004, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwResponse_Time", "unit": "", "multiply": 1.0, "prop": "ResponseTime"}}, "ReactiveLimit": {"Manager": {"start": 47001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwQManagerEn", "unit": "", "multiply": 1.0, "prop": "Manager"}, "CtrlMode": {"start": 47002, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwQCtrlMode", "unit": "", "multiply": 1.0, "prop": "CtrlMode"}, "FixPf": {"start": 47005, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwFixed_CosPhi", "unit": "", "multiply": 1.0, "prop": "FixPf"}, "P1": {"start": 47006, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCosPhiP_P1", "unit": "", "multiply": 1.0, "prop": "P1"}, "Pf1": {"start": 47007, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCosPhiP_CosPhi1", "unit": "", "multiply": 1.0, "prop": "Pf1"}, "P2": {"start": 47008, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwCosPhiP_P2", "unit": "", "multiply": 1.0, "prop": "P2"}, "Pf2": {"start": 47009, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwCosPhiP_CosPhi2", "unit": "", "multiply": 1.0, "prop": "Pf2"}, "FixedQ": {"start": 47010, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwFixed_Q", "unit": "", "multiply": 1.0, "prop": "FixedQ"}, "U1": {"start": 47011, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwQU_U1", "unit": "", "multiply": 100.0, "prop": "U1"}, "Q1": {"start": 47012, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwQU_Q1", "unit": "", "multiply": 100.0, "prop": "Q1"}, "U2": {"start": 47013, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwQU_U2", "unit": "", "multiply": 100.0, "prop": "U2"}, "Q2": {"start": 47014, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwQU_Q2", "unit": "", "multiply": 100.0, "prop": "Q2"}, "U3": {"start": 47015, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwQU_U3", "unit": "", "multiply": 100.0, "prop": "U3"}, "Q3": {"start": 47016, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwQU_Q3", "unit": "", "multiply": 100.0, "prop": "Q3"}, "U4": {"start": 47017, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwQU_U4", "unit": "", "multiply": 100.0, "prop": "U4"}, "Q4": {"start": 47018, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwQU_Q4", "unit": "", "multiply": 100.0, "prop": "Q4"}, "ResponseT": {"start": 47019, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwQU_Response_T", "unit": "", "multiply": 1.0, "prop": "ResponseT"}, "OverExcited": {"start": 47020, "len": 1, "slaveId": 1, "type": "U16", "alias": "OverExcited", "unit": "", "multiply": 1.0, "prop": "OverExcited"}, "FilterTimeConst": {"start": 47021, "len": 1, "slaveId": 1, "type": "U16", "alias": "filterTimeConst", "unit": "", "multiply": 1.0, "prop": "FilterTimeConst"}}, "Setting": {"OfFreqStart": {"start": 46005, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwOverFreqStart", "unit": "", "multiply": 100.0, "prop": "OfFreqStart"}, "OfFreqStop": {"start": 46006, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwOverFreqStop", "unit": "", "multiply": 100.0, "prop": "OfFreqStop"}, "OfFreqBack": {"start": 46007, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwOverFreqBack", "unit": "", "multiply": 100.0, "prop": "OfFreqBack"}, "OfRecoverTime": {"start": 46008, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwOverFreqRecover_time", "unit": "", "multiply": 1.0, "prop": "OfRecoverTime"}, "OfDeRatingTime": {"start": 46009, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwOverFreqDeratiing_time", "unit": "", "multiply": 1.0, "prop": "OfDeRatingTime"}, "OfMode": {"start": 46010, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwOverFreqMode", "unit": "", "multiply": 1.0, "prop": "OfMode"}, "OfPowerRateLimit": {"start": 46011, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPower_Rate_Limit", "unit": "", "multiply": 100.0, "prop": "OfPowerRateLimit"}, "OfPwr": {"start": 46012, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_VDE_OVER_F", "unit": "", "multiply": 1.0, "prop": "OfPwr"}, "OvPwr": {"start": 46013, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_OVER_U_PWR", "unit": "", "multiply": 1.0, "prop": "OvPwr"}, "DelayTime1": {"start": 46014, "len": 1, "slaveId": 1, "type": "U16", "alias": "Delay_Time1", "unit": "", "multiply": 1.0, "prop": "DelayTime1"}, "DelayTime2": {"start": 46015, "len": 1, "slaveId": 1, "type": "U16", "alias": "Delay_Time2", "unit": "", "multiply": 1.0, "prop": "DelayTime2"}, "OvU1": {"start": 46016, "len": 1, "slaveId": 1, "type": "U16", "alias": "U1_OV", "unit": "", "multiply": 100.0, "prop": "OvU1"}, "OvP1": {"start": 46017, "len": 1, "slaveId": 1, "type": "U16", "alias": "P1_OV", "unit": "", "multiply": 100.0, "prop": "OvP1"}, "OvU2": {"start": 46018, "len": 1, "slaveId": 1, "type": "U16", "alias": "U2_OV", "unit": "", "multiply": 100.0, "prop": "OvU2"}, "OvP2": {"start": 46019, "len": 1, "slaveId": 1, "type": "U16", "alias": "P2_OV", "unit": "", "multiply": 100.0, "prop": "OvP2"}, "OvU3": {"start": 46020, "len": 1, "slaveId": 1, "type": "U16", "alias": "U3_OV", "unit": "", "multiply": 100.0, "prop": "OvU3"}, "OvP3": {"start": 46021, "len": 1, "slaveId": 1, "type": "U16", "alias": "P3_OV", "unit": "", "multiply": 100.0, "prop": "OvP3"}, "OvU4": {"start": 46022, "len": 1, "slaveId": 1, "type": "U16", "alias": "U4_OV", "unit": "", "multiply": 100.0, "prop": "OvU4"}, "OvP4": {"start": 46023, "len": 1, "slaveId": 1, "type": "U16", "alias": "P4_OV", "unit": "", "multiply": 100.0, "prop": "OvP4"}, "UfPwr": {"start": 46024, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUN_EN_under frequency derating", "unit": "", "multiply": 1.0, "prop": "UfPwr"}, "UfStart": {"start": 46025, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwUnderFreqStart", "unit": "", "multiply": 100.0, "prop": "UfStart"}, "UfStop": {"start": 46026, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwUnderFreqStop", "unit": "", "multiply": 100.0, "prop": "UfStop"}, "UfBack": {"start": 46027, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwUnderFreqBack ", "unit": "", "multiply": 100.0, "prop": "UfBack"}, "UfRecoverTime": {"start": 46028, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwUnderFreqRecover_time", "unit": "", "multiply": 1.0, "prop": "UfRecoverTime"}, "UfDeRatingTime": {"start": 46029, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwUnderrFreqDeratiing_time", "unit": "", "multiply": 1.0, "prop": "UfDeRatingTime"}, "UfMode": {"start": 46030, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwUnderrFreqMode ", "unit": "", "multiply": 1.0, "prop": "UfMode"}, "UvPwr": {"start": 46031, "len": 1, "slaveId": 1, "type": "U16", "alias": "FUNC_EN_UNDER_U_PWR", "unit": "", "multiply": 1.0, "prop": "UvPwr"}, "UvU1": {"start": 46032, "len": 1, "slaveId": 1, "type": "U16", "alias": "U1_UV", "unit": "", "multiply": 100.0, "prop": "UvU1"}, "UvP1": {"start": 46033, "len": 1, "slaveId": 1, "type": "U16", "alias": "P1_UV", "unit": "", "multiply": 100.0, "prop": "UvP1"}, "UvU2": {"start": 46034, "len": 1, "slaveId": 1, "type": "U16", "alias": "U2_UV", "unit": "", "multiply": 100.0, "prop": "UvU2"}, "UvP2": {"start": 46035, "len": 1, "slaveId": 1, "type": "U16", "alias": "P2_UV", "unit": "", "multiply": 100.0, "prop": "UvP2"}, "UvU3": {"start": 46036, "len": 1, "slaveId": 1, "type": "U16", "alias": "U3_UV", "unit": "", "multiply": 100.0, "prop": "UvU3"}, "UvP3": {"start": 46037, "len": 1, "slaveId": 1, "type": "U16", "alias": "P3_UV", "unit": "", "multiply": 100.0, "prop": "UvP3"}, "UvU4": {"start": 46038, "len": 1, "slaveId": 1, "type": "U16", "alias": "U4_UV", "unit": "", "multiply": 100.0, "prop": "UvU4"}, "UvP4": {"start": 46039, "len": 1, "slaveId": 1, "type": "U16", "alias": "P4_UV", "unit": "", "multiply": 100.0, "prop": "UvP4"}, "FilterTimeConst": {"start": 46040, "len": 1, "slaveId": 1, "type": "U16", "alias": "filterTimeConst", "unit": "", "multiply": 1.0, "prop": "FilterTimeConst"}, "OverPref": {"start": 46041, "len": 1, "slaveId": 1, "type": "U16", "alias": "Pref", "unit": "", "multiply": 1.0, "prop": "OverPref"}, "UnderPref": {"start": 46042, "len": 1, "slaveId": 1, "type": "U16", "alias": "Pref", "unit": "", "multiply": 1.0, "prop": "Under<PERSON><PERSON>f"}}, "EmsArmSettingBatter": {"MeterCTFactor": {"start": 40045, "len": 1, "slaveId": 1, "type": "U16", "alias": "MeterCTFactor", "unit": "", "multiply": 1.0, "prop": "MeterCTFactor"}, "ThPhaseUnbalanceOutputEnable": {"start": 40047, "len": 1, "slaveId": 1, "type": "U16", "alias": "ThPhaseUnbalanceOutputEnable", "unit": "", "multiply": 1.0, "prop": "ThPhaseUnbalanceOutputEnable"}, "ChangeMeterPhase": {"start": 40048, "len": 1, "slaveId": 1, "type": "U16", "alias": "ChangeMeterPhase", "unit": "", "multiply": 1.0, "prop": "ChangeMeterPhase"}, "MeterSelfTest": {"start": 42064, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwMeterSelfTest", "unit": "", "multiply": 1.0, "prop": "MeterSelfTest"}, "EmsFactoryReset": {"start": 42065, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEmsFactoryReset", "unit": "", "multiply": 1.0, "prop": "EmsFactoryReset"}, "DisselMotorEnable": {"start": 42066, "len": 1, "slaveId": 1, "type": "U16", "alias": "DisselMotorEnable", "unit": "", "multiply": 1.0, "prop": "DisselMotorEnable"}, "RemoteDspControl": {"start": 48003, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPowerOn", "unit": "", "multiply": 1.0, "prop": "RemoteDspControl"}, "ChargeDisChargePowerValue": {"start": 48004, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwPowerRef", "unit": "", "multiply": 1.0, "prop": "ChargeDisChargePowerValue"}, "EmsControlCommand": {"start": 48005, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEmsCmd", "unit": "", "multiply": 1.0, "prop": "EmsControlCommand"}, "PowerLimitByBmsChg": {"start": 48006, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwPowerLimitByBMSChg", "unit": "", "multiply": 1.0, "prop": "PowerLimitByBmsChg"}, "PowerLineByBmsDisChg": {"start": 48007, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPowerLimitByBMSDisChg", "unit": "", "multiply": 1.0, "prop": "PowerLineByBmsDisChg"}, "PowerLineByDrmChg": {"start": 48008, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwPowerLimitByDRMChg", "unit": "", "multiply": 1.0, "prop": "PowerLineByDrmChg"}, "PowerLineByDrmDischg": {"start": 48009, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPowerLimitByDRMDisChg", "unit": "", "multiply": 1.0, "prop": "PowerLineByDrmDischg"}, "RstartAll": {"start": 48010, "len": 1, "slaveId": 1, "type": "U16", "alias": "Restart all ", "unit": "", "multiply": 1.0, "prop": "RstartAll"}, "DrmMode": {"start": 48011, "len": 1, "slaveId": 1, "type": "U16", "alias": "DRM Mode", "unit": "", "multiply": 1.0, "prop": "DrmMode"}, "DrmEnable": {"start": 48012, "len": 1, "slaveId": 1, "type": "U16", "alias": "Drm Enable", "unit": "", "multiply": 1.0, "prop": "DrmEnable"}, "PowerRefInvLimit": {"start": 48015, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPowerRefInv", "unit": "", "multiply": 1.0, "prop": "PowerRefInvLimit"}, "BatEnable": {"start": 48016, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatEnable", "unit": "", "multiply": 1.0, "prop": "BatEnable"}, "PvCharge": {"start": 48019, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPVCharge", "unit": "", "multiply": 1.0, "prop": "PvCharge"}, "BatSource": {"start": 48020, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatSource", "unit": "", "multiply": 1.0, "prop": "BatSource"}}, "EmsArmSetting": {"MeterCTFactor": {"start": 40045, "len": 1, "slaveId": 1, "type": "U16", "alias": "MeterCTFactor", "unit": "", "multiply": 1.0, "prop": "MeterCTFactor"}, "RemoteDspControl": {"start": 48003, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPowerOn", "unit": "", "multiply": 1.0, "prop": "RemoteDspControl"}, "ChargeDisChargePowerValue": {"start": 48004, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwPowerRef", "unit": "", "multiply": 1.0, "prop": "ChargeDisChargePowerValue"}, "EmsControlCommand": {"start": 48005, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEmsCmd", "unit": "", "multiply": 1.0, "prop": "EmsControlCommand"}, "PowerLimitByBmsChg": {"start": 48006, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwPowerLimitByBMSChg", "unit": "", "multiply": 1.0, "prop": "PowerLimitByBmsChg"}, "PowerLineByBmsDisChg": {"start": 48007, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPowerLimitByBMSDisChg", "unit": "", "multiply": 1.0, "prop": "PowerLineByBmsDisChg"}, "PowerLineByDrmChg": {"start": 48008, "len": 1, "slaveId": 1, "type": "S16", "alias": "uwPowerLimitByDRMChg", "unit": "", "multiply": 1.0, "prop": "PowerLineByDrmChg"}, "PowerLineByDrmDischg": {"start": 48009, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPowerLimitByDRMDisChg", "unit": "", "multiply": 1.0, "prop": "PowerLineByDrmDischg"}, "RstartAll": {"start": 48010, "len": 1, "slaveId": 1, "type": "U16", "alias": "Restart all ", "unit": "", "multiply": 1.0, "prop": "RstartAll"}, "DrmMode": {"start": 48011, "len": 1, "slaveId": 1, "type": "U16", "alias": "DRM Mode", "unit": "", "multiply": 1.0, "prop": "DrmMode"}, "DrmEnable": {"start": 48012, "len": 1, "slaveId": 1, "type": "U16", "alias": "Drm Enable", "unit": "", "multiply": 1.0, "prop": "DrmEnable"}, "PowerRefInvLimit": {"start": 48015, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPowerRefInv", "unit": "", "multiply": 1.0, "prop": "PowerRefInvLimit"}, "BatEnable": {"start": 48016, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatEnable", "unit": "", "multiply": 1.0, "prop": "BatEnable"}, "PvCharge": {"start": 48019, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPVCharge", "unit": "", "multiply": 1.0, "prop": "PvCharge"}, "BatSource": {"start": 48020, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatSource", "unit": "", "multiply": 1.0, "prop": "BatSource"}}, "ParameterSetting": {"HouseSideGridLimit": {"start": 40049, "len": 1, "slaveId": 1, "type": "U16", "alias": "HouseSideGridPowerLimit", "unit": "", "multiply": 1.0, "prop": "HouseSideGridLimit"}, "MachineSideGridLimit": {"start": 40050, "len": 1, "slaveId": 1, "type": "U16", "alias": "MachineSideGridPowerLimit", "unit": "", "multiply": 1.0, "prop": "MachineSideGridLimit"}, "EpsParallel": {"start": 40067, "len": 1, "slaveId": 1, "type": "U16", "alias": "epsParallel", "unit": "", "multiply": 1.0, "prop": "EpsParallel"}, "RemoteOff": {"start": 42001, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwRemote_Off", "unit": "", "multiply": 1.0, "prop": "<PERSON><PERSON><PERSON><PERSON>"}, "AccessGridType": {"start": 42012, "len": 1, "slaveId": 1, "type": "U16", "alias": "AC_Input", "unit": "", "multiply": 1.0, "prop": "AccessGridType"}, "UpsEnable": {"start": 42013, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwEPS_Mute", "unit": "", "multiply": 1.0, "prop": "UpsEnable"}, "PvConnectMode": {"start": 42014, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwPVConnectMode", "unit": "", "multiply": 1.0, "prop": "PvConnectMode"}, "VppWorkMode": {"start": 41036, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwWorkMode", "unit": "", "multiply": 1.0, "prop": "VppWorkMode"}, "MaxFeedInSoftLimit": {"start": 41037, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwMaxFeedin_SoftLimit", "unit": "", "multiply": 1.0, "prop": "MaxFeedInSoftLimit"}, "MaxFeedInHardLimit": {"start": 41038, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwMaxFeedin_HardLimit", "unit": "", "multiply": 1.0, "prop": "MaxFeedInHardLimit"}, "MaxFeedInSoftEn": {"start": 41039, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwSoftLimitEn", "unit": "", "multiply": 1.0, "prop": "MaxFeedInSoftEn"}, "MaxFeedInHardEn": {"start": 41040, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwHardLimitEn", "unit": "", "multiply": 1.0, "prop": "MaxFeedInHardEn"}, "MeterEnable": {"start": 40038, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwMeter_FuncEn", "unit": "", "multiply": 1.0, "prop": "MeterEnable"}, "MeterType": {"start": 40043, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwMeter_Type", "unit": "", "multiply": 1.0, "prop": "MeterType"}, "FeedPhase": {"start": 40044, "len": 1, "slaveId": 1, "type": "U16", "alias": "FeedPhase", "unit": "", "multiply": 1.0, "prop": "FeedPhase"}, "BatEnable": {"start": 48016, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwBatEnable", "unit": "", "multiply": 1.0, "prop": "BatEnable"}, "ParallelWorkMode": {"start": 48120, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "ParallelWorkMode"}, "ParallelNum": {"start": 48121, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "ParallelNum"}}, "WeiHengBatteryInfoSetting": {"StartCmd": {"start": 150, "len": 1, "slaveId": 201, "type": "U16", "alias": "start_cmd", "unit": "", "multiply": 1.0, "prop": "StartCmd"}, "ResetCmd": {"start": 151, "len": 1, "slaveId": 201, "type": "U16", "alias": "reset_cmd", "unit": "", "multiply": 1.0, "prop": "ResetCmd"}, "ForceChargeCmd": {"start": 152, "len": 1, "slaveId": 201, "type": "U16", "alias": "force_charge_cmd", "unit": "", "multiply": 1.0, "prop": "ForceChargeCmd"}, "MaskComCmd": {"start": 153, "len": 1, "slaveId": 201, "type": "U16", "alias": "mask_com_cmd", "unit": "", "multiply": 1.0, "prop": "MaskComCmd"}, "EnableResistorUpdate": {"start": 154, "len": 1, "slaveId": 201, "type": "U16", "alias": "enable_resistor_update", "unit": "", "multiply": 1.0, "prop": "EnableResistorUpdate"}, "MaskHwUvFaults": {"start": 155, "len": 1, "slaveId": 201, "type": "U16", "alias": "mask_hw_uv_faults", "unit": "", "multiply": 1.0, "prop": "MaskHwUvFaults"}, "MaskHydrogenFault": {"start": 156, "len": 1, "slaveId": 201, "type": "U16", "alias": "mask_hydrogen_fault", "unit": "", "multiply": 1.0, "prop": "MaskHydrogenFault"}, "SystemResetCmd": {"start": 157, "len": 1, "slaveId": 201, "type": "U16", "alias": "system_reset_cmd", "unit": "", "multiply": 1.0, "prop": "SystemResetCmd"}, "SystemHardwareResetCmd": {"start": 158, "len": 1, "slaveId": 201, "type": "U16", "alias": "system_hardware_reset_cmd", "unit": "", "multiply": 1.0, "prop": "SystemHardwareResetCmd"}}, "AutoTest": {"TripLimitSet59S1": {"start": 44036, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TripLimitSet59S1"}, "TripLimitSet59S2": {"start": 44008, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TripLimitSet59S2"}, "TripLimitSet27S1": {"start": 44010, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TripLimitSet27S1"}, "TripLimitSet27S2": {"start": 44012, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TripLimitSet27S2"}, "TripTimeSet59S2": {"start": 44009, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1000.0, "prop": "TripTimeSet59S2"}, "TripTimeSet27S1": {"start": 44011, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1000.0, "prop": "TripTimeSet27S1"}, "TripTimeSet27S2": {"start": 44013, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1000.0, "prop": "TripTimeSet27S2"}, "TestResult59S1": {"start": 34027, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult59S1"}, "TestResult59S2": {"start": 34028, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult59S2"}, "TestResult27S1": {"start": 34029, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult27S1"}, "TestResult27S2": {"start": 34030, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult27S2"}, "Vac59S1": {"start": 34035, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "Vac59S1"}, "Vac59S2": {"start": 34036, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "Vac59S2"}, "Vac27S1": {"start": 34037, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "Vac27S1"}, "Vac27S2": {"start": 34038, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "Vac27S2"}, "VacOff59S1": {"start": 34043, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOff59S1"}, "VacOff59S2": {"start": 34044, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOff59S2"}, "VacOff27S1": {"start": 34045, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOff27S1"}, "VacOff27S2": {"start": 34046, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOff27S2"}, "TimeOff59S1": {"start": 34051, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TimeOff59S1"}, "TimeOff59S2": {"start": 34052, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOff59S2"}, "TimeOff27S1": {"start": 34053, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOff27S1"}, "TimeOff27S2": {"start": 34054, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOff27S2"}, "TripLimitSet81GtS1": {"start": 44024, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "TripLimitSet81GtS1"}, "TripLimitSet81LtS1": {"start": 44026, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "TripLimitSet81LtS1"}, "TripLimitSet81GtS2": {"start": 44022, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "TripLimitSet81GtS2"}, "TripLimitSet81LtS2": {"start": 44028, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "TripLimitSet81LtS2"}, "TripTimeSet81GtS1": {"start": 44025, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1000.0, "prop": "TripTimeSet81GtS1"}, "TripTimeSet81LtS1": {"start": 44027, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1000.0, "prop": "TripTimeSet81LtS1"}, "TripTimeSet81GtS2": {"start": 44023, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1000.0, "prop": "TripTimeSet81GtS2"}, "TripTimeSet81LtS2": {"start": 44029, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1000.0, "prop": "TripTimeSet81LtS2"}, "TestResult81GtS1": {"start": 34031, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult81GtS1"}, "TestResult81LtS1": {"start": 34032, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult81LtS1"}, "TestResult81GtS2": {"start": 34033, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult81GtS2"}, "TestResult81LtS2": {"start": 34034, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult81LtS2"}, "Fac81GtS1": {"start": 34039, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "Fac81GtS1"}, "Fac81LtS1": {"start": 34040, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "Fac81LtS1"}, "Fac81GtS2": {"start": 34041, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "Fac81GtS2"}, "Fac81LtS2": {"start": 34042, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "Fac81LtS2"}, "FacOff81GtS1": {"start": 34047, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOff81GtS1"}, "FacOff81LtS1": {"start": 34048, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOff81LtS1"}, "FacOff81GtS2": {"start": 34049, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOff81GtS2"}, "FacOff81LtS2": {"start": 34050, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOff81LtS2"}, "TimeOff81GtS1": {"start": 34055, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOff81GtS1"}, "TimeOff81LtS1": {"start": 34056, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOff81LtS1"}, "TimeOff81GtS2": {"start": 34057, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOff81GtS2"}, "TimeOff81LtS2": {"start": 34058, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOff81LtS2"}, "ExternalSignal": {"start": 42016, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwExternalSignal", "unit": "", "multiply": 1.0, "prop": "ExternalSignal"}, "LocalCmd": {"start": 42017, "len": 1, "slaveId": 1, "type": "U16", "alias": "LocalCmd", "unit": "", "multiply": 1.0, "prop": "LocalCmd"}, "AutoTestEnable": {"start": 42018, "len": 1, "slaveId": 1, "type": "U16", "alias": "AutoTestEnable", "unit": "", "multiply": 1.0, "prop": "AutoTestEnable"}}, "AutoTestThree": {"TripLimitSet59S1": {"start": 44036, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TripLimitSet59S1"}, "TripLimitSet59S2": {"start": 44008, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TripLimitSet59S2"}, "TripLimitSet27S1": {"start": 44010, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TripLimitSet27S1"}, "TripLimitSet27S2": {"start": 44012, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TripLimitSet27S2"}, "TripTimeSet59S2": {"start": 44009, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1000.0, "prop": "TripTimeSet59S2"}, "TripTimeSet27S1": {"start": 44011, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1000.0, "prop": "TripTimeSet27S1"}, "TripTimeSet27S2": {"start": 44013, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1000.0, "prop": "TripTimeSet27S2"}, "TestResult59S1": {"start": 34027, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult59S1"}, "TestResult59S2": {"start": 34028, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult59S2"}, "TestResult27S1": {"start": 34029, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult27S1"}, "TestResult27S2": {"start": 34030, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult27S2"}, "TestResultS59S1": {"start": 34063, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultS59S1"}, "TestResultS59S2": {"start": 34064, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultS59S2"}, "TestResultS27S1": {"start": 34065, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultS27S1"}, "TestResultS27S2": {"start": 34066, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultS27S2"}, "TestResultT59S1": {"start": 34095, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultT59S1"}, "TestResultT59S2": {"start": 34096, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultT59S2"}, "TestResultT27S1": {"start": 34097, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultT27S1"}, "TestResultT27S2": {"start": 34098, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultT27S2"}, "Vac59S1": {"start": 34035, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "Vac59S1"}, "Vac59S2": {"start": 34036, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "Vac59S2"}, "Vac27S1": {"start": 34037, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "Vac27S1"}, "Vac27S2": {"start": 34038, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "Vac27S2"}, "VacS59S1": {"start": 34071, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacS59S1"}, "VacS59S2": {"start": 34072, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacS59S2"}, "VacS27S1": {"start": 34073, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacS27S1"}, "VacS27S2": {"start": 34074, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacS27S2"}, "VacT59S1": {"start": 34103, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacT59S1"}, "VacT59S2": {"start": 34104, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacT59S2"}, "VacT27S1": {"start": 34105, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacT27S1"}, "VacT27S2": {"start": 34106, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacT27S2"}, "VacOff59S1": {"start": 34043, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOff59S1"}, "VacOff59S2": {"start": 34044, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOff59S2"}, "VacOff27S1": {"start": 34045, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOff27S1"}, "VacOff27S2": {"start": 34046, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOff27S2"}, "VacOffS59S1": {"start": 34079, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOffS59S1"}, "VacOffS59S2": {"start": 34080, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOffS59S2"}, "VacOffS27S1": {"start": 34081, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOffS27S1"}, "VacOffS27S2": {"start": 34082, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOffS27S2"}, "VacOffT59S1": {"start": 34111, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOffT59S1"}, "VacOffT59S2": {"start": 34112, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOffT59S2"}, "VacOffT27S1": {"start": 34113, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOffT27S1"}, "VacOffT27S2": {"start": 34114, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "VacOffT27S2"}, "TimeOff59S1": {"start": 34051, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TimeOff59S1"}, "TimeOff59S2": {"start": 34052, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOff59S2"}, "TimeOff27S1": {"start": 34053, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOff27S1"}, "TimeOff27S2": {"start": 34054, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOff27S2"}, "TimeOffS59S1": {"start": 34087, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TimeOffS59S1"}, "TimeOffS59S2": {"start": 34088, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOffS59S2"}, "TimeOffS27S1": {"start": 34089, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOffS27S1"}, "TimeOffS27S2": {"start": 34090, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOffS27S2"}, "TimeOffT59S1": {"start": 34119, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TimeOffT59S1"}, "TimeOffT59S2": {"start": 34120, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOffT59S2"}, "TimeOffT27S1": {"start": 34121, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOffT27S1"}, "TimeOffT27S2": {"start": 34122, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOffT27S2"}, "TripLimitSet81GtS1": {"start": 44024, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "TripLimitSet81GtS1"}, "TripLimitSet81LtS1": {"start": 44026, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "TripLimitSet81LtS1"}, "TripLimitSet81GtS2": {"start": 44022, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "TripLimitSet81GtS2"}, "TripLimitSet81LtS2": {"start": 44028, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "TripLimitSet81LtS2"}, "TripTimeSet81GtS1": {"start": 44025, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1000.0, "prop": "TripTimeSet81GtS1"}, "TripTimeSet81LtS1": {"start": 44027, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1000.0, "prop": "TripTimeSet81LtS1"}, "TripTimeSet81GtS2": {"start": 44023, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1000.0, "prop": "TripTimeSet81GtS2"}, "TripTimeSet81LtS2": {"start": 44029, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1000.0, "prop": "TripTimeSet81LtS2"}, "TestResult81GtS1": {"start": 34031, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult81GtS1"}, "TestResult81LtS1": {"start": 34032, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult81LtS1"}, "TestResult81GtS2": {"start": 34033, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult81GtS2"}, "TestResult81LtS2": {"start": 34034, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResult81LtS2"}, "TestResultS81GtS1": {"start": 34067, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultS81GtS1"}, "TestResultS81LtS1": {"start": 34068, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultS81LtS1"}, "TestResultS81GtS2": {"start": 34069, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultS81GtS2"}, "TestResultS81LtS2": {"start": 34070, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultS81LtS2"}, "TestResultT81GtS1": {"start": 34099, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultT81GtS1"}, "TestResultT81LtS1": {"start": 34100, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultT81LtS1"}, "TestResultT81GtS2": {"start": 34101, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultT81GtS2"}, "TestResultT81LtS2": {"start": 34102, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 1.0, "prop": "TestResultT81LtS2"}, "Fac81GtS1": {"start": 34039, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "Fac81GtS1"}, "Fac81LtS1": {"start": 34040, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "Fac81LtS1"}, "Fac81GtS2": {"start": 34041, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "Fac81GtS2"}, "Fac81LtS2": {"start": 34042, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "Fac81LtS2"}, "FacS81GtS1": {"start": 34075, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacS81GtS1"}, "FacS81LtS1": {"start": 34076, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacS81LtS1"}, "FacS81GtS2": {"start": 34077, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacS81GtS2"}, "FacS81LtS2": {"start": 34078, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacS81LtS2"}, "FacT81GtS1": {"start": 34107, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacT81GtS1"}, "FacT81LtS1": {"start": 34108, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacT81LtS1"}, "FacT81GtS2": {"start": 34109, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacT81GtS2"}, "FacT81LtS2": {"start": 34110, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacT81LtS2"}, "FacOff81GtS1": {"start": 34047, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOff81GtS1"}, "FacOff81LtS1": {"start": 34048, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOff81LtS1"}, "FacOff81GtS2": {"start": 34049, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOff81GtS2"}, "FacOff81LtS2": {"start": 34050, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOff81LtS2"}, "FacOffS81GtS1": {"start": 34083, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOffS81GtS1"}, "FacOffS81LtS1": {"start": 34084, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOffS81LtS1"}, "FacOffS81GtS2": {"start": 34085, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOffS81GtS2"}, "FacOffS81LtS2": {"start": 34086, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOffS81LtS2"}, "FacOffT81GtS1": {"start": 34115, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOffT81GtS1"}, "FacOffT81LtS1": {"start": 34116, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOffT81LtS1"}, "FacOffT81GtS2": {"start": 34117, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOffT81GtS2"}, "FacOffT81LtS2": {"start": 34118, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 100.0, "prop": "FacOffT81LtS2"}, "TimeOff81GtS1": {"start": 34055, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOff81GtS1"}, "TimeOff81LtS1": {"start": 34056, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOff81LtS1"}, "TimeOff81GtS2": {"start": 34057, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOff81GtS2"}, "TimeOff81LtS2": {"start": 34058, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOff81LtS2"}, "TimeOffS81GtS1": {"start": 34091, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOffS81GtS1"}, "TimeOffS81LtS1": {"start": 34092, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOffS81LtS1"}, "TimeOffS81GtS2": {"start": 34093, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOffS81GtS2"}, "TimeOffS81LtS2": {"start": 34094, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOffS81LtS2"}, "TimeOffT81GtS1": {"start": 34123, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOffT81GtS1"}, "TimeOffT81LtS1": {"start": 34124, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOffT81LtS1"}, "TimeOffT81GtS2": {"start": 34125, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOffT81GtS2"}, "TimeOffT81LtS2": {"start": 34126, "len": 1, "slaveId": 1, "type": "U16", "alias": "", "unit": "", "multiply": 10.0, "prop": "TimeOffT81LtS2"}, "ExternalSignal": {"start": 42016, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwExternalSignal", "unit": "", "multiply": 1.0, "prop": "ExternalSignal"}, "LocalCmd": {"start": 42017, "len": 1, "slaveId": 1, "type": "U16", "alias": "LocalCmd", "unit": "", "multiply": 1.0, "prop": "LocalCmd"}, "AutoTestEnable": {"start": 42018, "len": 1, "slaveId": 1, "type": "U16", "alias": "AutoTestEnable", "unit": "", "multiply": 1.0, "prop": "AutoTestEnable"}}, "PvEnableSetting": {"PvOff": {"start": 42019, "len": 1, "slaveId": 1, "type": "U16", "alias": "<PERSON>v<PERSON>ff", "unit": "", "multiply": 1.0, "prop": "<PERSON>v<PERSON>ff"}}, "AcMeterType": {"MeterType": {"start": 40043, "len": 1, "slaveId": 1, "type": "U16", "alias": "uwMeter_Type", "unit": "", "multiply": 1.0, "prop": "MeterType"}}}, "subsystem": {"dsp": {"0.0": {"cn": "从错误中恢复", "en": "Recover From E<PERSON>r"}, "1.0": {"cn": "PV1过压", "en": "PV1 Voltage Fault"}, "2.0": {"cn": "PV1过流", "en": "PV1 Current Fault"}, "3.0": {"cn": "PV2过压", "en": "PV2 Voltage Fault"}, "4.0": {"cn": "PV2过流", "en": "PV2 Current Fault"}, "5.0": {"cn": "预留", "en": "Reserve"}, "6.0": {"cn": "预留", "en": "Reserve"}, "7.0": {"cn": "预留", "en": "Reserve"}, "8.0": {"cn": "预留", "en": "Reserve"}, "9.0": {"cn": "BUS过压故障", "en": "Bus Voltage Overtrip Fault"}, "10.0": {"cn": "无市电", "en": "<PERSON><PERSON>"}, "11.0": {"cn": "电网电压故障", "en": "Grid Voltage Undertrip/Overtrip Fault"}, "12.0": {"cn": "电网电流故障", "en": "Grid Current Overtrip Fault"}, "13.0": {"cn": "电网频率故障", "en": "Grid Frequency Undertrip/Overtrip Fault"}, "14.0": {"cn": "逆变器温度故障", "en": "Inverter Temperature Fault"}, "15.0": {"cn": "DCI过流", "en": "DC Injection Current Overtrip"}, "16.0": {"cn": "漏电流故障", "en": "Leakage Current Overtrip"}, "17.0": {"cn": "绝缘阻抗过低", "en": "Insulation Resistance Too Low"}, "18.0": {"cn": "预留", "en": "Reserve"}, "19.0": {"cn": "预留", "en": "Reserve"}, "20.0": {"cn": "预留", "en": "Reserve"}, "21.0": {"cn": "预留", "en": "Reserve"}, "22.0": {"cn": "预留", "en": "Reserve"}, "23.0": {"cn": "预留", "en": "Reserve"}, "24.0": {"cn": "预留", "en": "Reserve"}, "25.0": {"cn": "预留", "en": "Reserve"}, "26.0": {"cn": "电池电压故障", "en": "Battery Voltage Fault"}, "27.0": {"cn": "电池电流故障", "en": "Battery Current Fault"}, "28.0": {"cn": "电池温度故障", "en": "Battery Temperature Fault"}, "29.0": {"cn": "预留", "en": "Reserve"}, "30.0": {"cn": "预留", "en": "Reserve"}, "31.0": {"cn": "预留", "en": "Reserve"}, "32.0": {"cn": "预留", "en": "Reserve"}, "33.0": {"cn": "预留", "en": "Reserve"}, "34.0": {"cn": "预留", "en": "Reserve"}, "35.0": {"cn": "预留", "en": "Reserve"}, "36.0": {"cn": "预留", "en": "Reserve"}, "37.0": {"cn": "负载电压故障", "en": "Load Voltage Overtrip Fault"}, "38.0": {"cn": "负载电流故障", "en": "Load Current Overtrip Fault"}, "39.0": {"cn": "过载故障", "en": "Overload Fault"}, "40.0": {"cn": "负载输出短路故障", "en": "Output Short Circuit Fault"}, "41.0": {"cn": "N-PE之间电压高", "en": "The voltage from Grid Line N to PE is highe"}, "42.0": {"cn": "L-N反接", "en": "L-N Line Is Connected Reversely"}, "43.0": {"cn": "LVRT", "en": "LVRT Fault"}, "44.0": {"cn": "风扇故障", "en": "<PERSON>"}, "45.0": {"cn": "烟感告警", "en": "Smoke Detect Warning"}, "46.0": {"cn": "降额运行告警", "en": "Power Derate Warning"}, "47.0": {"cn": "自检故障", "en": "Self Checking Fault"}, "48.0": {"cn": "电网Relay故障", "en": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "49.0": {"cn": "EPS Relay故障", "en": "EPS <PERSON>lay <PERSON>"}, "50.0": {"cn": "ByPass Relay故障", "en": "Bypass Relay Fault"}, "51.0": {"cn": "存储器故障", "en": "Storage IC Fault"}, "52.0": {"cn": "通信故障", "en": "DSP <PERSON><PERSON>"}, "53.0": {"cn": "孤岛", "en": "Grid Isolation Fault"}, "54.0": {"cn": "环境温度低", "en": "Environment Temperature Too Low"}, "55.0": {"cn": "软件版本主副不一致", "en": "Software Version Fault"}, "56.0": {"cn": "预留", "en": "Reserve"}, "57.0": {"cn": "预留", "en": "Reserve"}, "58.0": {"cn": "预留", "en": "Reserve"}, "59.0": {"cn": "预留", "en": "Reserve"}, "60.0": {"cn": "预留", "en": "Reserve"}}, "arm": {"0.0": {"cn": "从错误中恢复", "en": "Recover From E<PERSON>r"}, "1.0": {"cn": "逆变器信息丢失", "en": "DSP Info Loss"}, "2.0": {"cn": "逆变器数据丢失", "en": "DSP Data Loss"}, "4.0": {"cn": "BMS通信异常", "en": "BMS Data Loss"}, "8.0": {"cn": "电表通信异常", "en": "Meter Data Loss"}}, "bms": {"0.0": {"cn": "从错误中恢复", "en": "Recover From E<PERSON>r"}, "1.0": {"cn": "单体低压告警", "en": "Battery Cell Low Voltage Alarm"}, "2.0": {"cn": "单体高压告警", "en": "Battery Cell High Voltage Alarm"}, "3.0": {"cn": "总压低压告警", "en": "Pile Low Voltage Alarm"}, "4.0": {"cn": "从错误中恢复", "en": "Recover From E<PERSON>r"}, "5.0": {"cn": "充电低温告警", "en": "Charge Low Temperature Alarm"}, "6.0": {"cn": "充电高温告警", "en": "Charge High Temperature Alarm"}, "7.0": {"cn": "放电低温告警", "en": "Discharge Low Temperature Alarm"}, "8.0": {"cn": "放电高温告警", "en": "Discharge High Temperature Alarm"}, "9.0": {"cn": "充电过流告警", "en": "Charge Over Current Alarm"}, "10.0": {"cn": "放电过流告警", "en": "Discharge Over Current Alarm"}, "11.0": {"cn": "BMS温度高告警", "en": "BMS High Temperature Alarm"}, "12.0": {"cn": "模块温度高告警", "en": "Module High Temperature Alarm"}, "13.0": {"cn": "模块低压告警", "en": "Module Low Voltage Alarm"}, "14.0": {"cn": "模块高压告警", "en": "Module High Voltage Alarm"}, "15.0": {"cn": "SOC低告警", "en": "SOC Under <PERSON><PERSON><PERSON><PERSON> Alarm"}, "16.0": {"cn": "Flash数据异常告警", "en": "Flash User Data Invalid Alarm"}, "17.0": {"cn": "预留", "en": "Reserve"}, "18.0": {"cn": "预留", "en": "Reserve"}, "19.0": {"cn": "预留", "en": "Reserve"}, "20.0": {"cn": "预留", "en": "Reserve"}, "21.0": {"cn": "预留", "en": "Reserve"}, "22.0": {"cn": "预留", "en": "Reserve"}, "23.0": {"cn": "预留", "en": "Reserve"}, "24.0": {"cn": "预留", "en": "Reserve"}, "25.0": {"cn": "单体低压保护", "en": "Battery Cell Under Voltage Protection"}, "26.0": {"cn": "单体高压保护", "en": "Battery Cell Over Voltage Protection"}, "27.0": {"cn": "总压低压保护", "en": "Pile Under Voltage Protection"}, "28.0": {"cn": "总压高压保护", "en": "Pile Over Voltage Protection"}, "29.0": {"cn": "充电低温保护", "en": "Charge Under Temperature Protection"}, "30.0": {"cn": "充电高温保护", "en": "Charge Over Temperature Protection"}, "31.0": {"cn": "放电低温保护", "en": "Discharge Under Temperature Protection"}, "32.0": {"cn": "放电高温保护", "en": "Discharge Over Temperature Protection"}, "33.0": {"cn": "充电过流保护", "en": "Charge Over Current Protection"}, "34.0": {"cn": "放电过流保护", "en": "Discharge Over Current Protection"}, "35.0": {"cn": "短路保护", "en": "Short Circuit Protection"}, "36.0": {"cn": "温度高保护", "en": "Over Temperature Protection"}, "37.0": {"cn": "模块低压保护", "en": "Module Under Voltage Protection"}, "38.0": {"cn": "模块高压保护", "en": "Module Over Voltage Protection"}, "39.0": {"cn": "电压传感器故障", "en": "Voltage Sensor Error"}, "40.0": {"cn": "温度采样异常", "en": "Temperature Sample Error"}, "41.0": {"cn": "内部通信故障", "en": "Internal Com Error"}, "42.0": {"cn": "输入过压故障", "en": "Input Over Voltage Error"}, "43.0": {"cn": "输入反接故障", "en": "Input Transposition Error"}, "44.0": {"cn": "继电器故障", "en": "<PERSON><PERSON>"}, "45.0": {"cn": "电池损坏故障", "en": "Battery Cell Error"}, "46.0": {"cn": "关机电路异常", "en": "Shutdown Circuit Error"}, "47.0": {"cn": "BMIC 异常", "en": "BMIC Error"}, "48.0": {"cn": "内部总线异常", "en": "Internal Bus Error"}, "49.0": {"cn": "初始化自检异常", "en": "System Init Test Error"}, "50.0": {"cn": "电量计过压", "en": "Gauge Under Voltage"}, "51.0": {"cn": "电量计欠压", "en": "Gauge Over Voltage"}, "52.0": {"cn": "电量计充电过流", "en": "Gauge Charge Over Current"}, "53.0": {"cn": "电量计放电过流", "en": "Gauge Discharge Over Current"}, "54.0": {"cn": "电量计硬件过压", "en": "Gauge Hardware Over Voltage"}, "55.0": {"cn": "电量计硬件欠压", "en": "Gauge Hardware Under Voltage"}, "56.0": {"cn": "平衡模块SYS故障", "en": "Balance Module SYS Error"}, "57.0": {"cn": "平衡模块DEV故障", "en": "Balance Module DEV Error"}, "58.0": {"cn": "单体硬件过压故障", "en": "Cell Hardware Over Voltage"}, "59.0": {"cn": "单体硬件欠压故障", "en": "Cell Hardware Under Voltage"}, "60.0": {"cn": "硬件充电过流故障", "en": "Hardware Charge Over Current Fault"}, "61.0": {"cn": "硬件放电过流故障", "en": "Hardware Discharge Over Current Fault"}, "62.0": {"cn": "硬件总电压过压故障", "en": "Hardware Pack Over Voltage Fault"}, "63.0": {"cn": "硬件总电压欠压故障或熔丝断故障", "en": "Hardware Pack Under Voltage Or Fuse Break Fault"}, "64.0": {"cn": "电池接线故障", "en": "Internal Cell Open Wire Fault"}, "65.0": {"cn": "与主机通信断开", "en": "Com Error Between Battery and Host"}, "66.0": {"cn": "ISO故障", "en": "ISO Fault"}, "67.0": {"cn": "直流接触器开路", "en": "DC Break Open"}, "68.0": {"cn": "直流接触器短路", "en": "DC Break Short"}, "69.0": {"cn": "电量计与平衡模块检测电压不匹配或熔丝断故障", "en": "Pack Voltage Mismatch Between Sensors Or Fuse Break Fault"}, "70.0": {"cn": "电池SOC低故障", "en": "SOC Under <PERSON><PERSON><PERSON><PERSON> Alarm"}, "71.0": {"cn": "异常放电故障", "en": "Discharge at Force Charge"}}, "event": {"0.0": {"cn": "等待电网模式", "en": "Waiting for <PERSON><PERSON>"}, "1.0": {"cn": "进入并网模式", "en": "Grid Connected"}, "2.0": {"cn": "进入EPS模式", "en": "<PERSON><PERSON> Disconnected, EPS"}, "3.0": {"cn": "进入故障模式", "en": "Fault Mode"}, "4.0": {"cn": "进入保留模式", "en": "Reserved Mode"}, "5.0": {"cn": "进入自检模式", "en": "Checking"}}, "version": {"0.0": {"cn": "20200916", "en": "20200916"}}}}