spring.profiles.active=${SPRING_PROFILES_ACTIVE:dev}
spring.application.name=EcosService
server.port=${SERVER_PORT:20200}
# custom
snowflake.work=${SNOWFLAKE_WORD:1}
snowflake.datacenter=${SNOWFLAKE_DADACENTER:1}
custom.device.config.path=com.weihengtech.pojo.dtos.device.config
# upload
upload.bin.wifi.name=magpi
upload.bin.max.size=8
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
# openfeign
feign.client.config.default.loggerLevel=full
feign.client.config.default.connectTimeout=10000
feign.client.config.default.readTimeout=20000
feign.hystrix.enabled=true
hystrix.command.default.execution.timeout.enabled=true
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=11000
hystrix.command.default.circuitBreaker.requestVolumeThreshold=30
hystrix.command.default.metrics.rollingStats.timeInMilliseconds=5000
hystrix.command.default.circuitBreaker.errorThresholdPercentage=60
hystrix.command.default.circuitBreaker.sleepWindowInMilliseconds=3000
# in-system
custom.auth.access-key=${CUSTOM_ACCESS_KEY:ICPAFMUWOBauFZZQrNpQ}
custom.auth.access-secret=${CUSTOM_ACCESS_SECRET:vQKDzs8EV4aqOzxXPAxU}
# 关闭discovery健康检查
spring.cloud.discovery.client.composite-indicator.enabled=false
# oss
custom.ali.oss.key=${CUSTOM_ALI_OSS_KEY:LTAI5tPMHfRiYbsVMRrWKbSo}
custom.ali.oss.secret=${CUSTOM_ALI_OSS_SECRET:******************************}
custom.ali.oss.endpoint=${CUSTOM_ALI_OSS_ENDPOINT:oss-accelerate.aliyuncs.com}
custom.ali.oss.bucket=${CUSTOM_ALI_OSS_BUCKET:ecos-bin-test}
custom.ali.oss.bucket.sign=${CUSTOM_ALI_OSS_BUCKET_SIGN:ecos-bin-test-sign}
# notify服务
custom.url.notify=${CUSTOM_URL_NOTIFY:http://notify.dev.weiheng-tech.com}
custom.notify.token=${CUSTOM_NOTIFY_TOKEN:9YD#=#gHJUas0&ibs-gCUtvoCHR%fSX06!z^3s#srRsk@^}
custom.notify.to-user=${CUSTOM_NOTIFY_TO_USER:jiajun.li}
# 工单服务
custom.url.order=${CUSTOM_URL_ORDER:http://localhost:8080}
custom.order.token=${CUSTOM_ORDER_TOKEN:123}
custom.order.project=${CUSTOM_ORDER_PROJECT:6424e1c8e4b03574a656a719}
# log配置
logging.config=classpath:log4j2-spring.xml
logging.level.org.springframework.boot.actuate.endpoint.EndpointId=ERROR
# 配置url
custom.url.config=${CUSTOM_URL_CONFIG:https://dcdn-config.weiheng-tech.com/test}
# 配置数据中心
custom.datacenter.name=${CUSTOM_DATACENTER_NAME:CN}
# iot sdk平台
sdk.iot.ecos.flag.elink=0
sdk.iot.ecos.flag.tuya=1
sdk.iot.ecos.flag.ocpp=2
sdk.iot.ecos.flag.wh=3
# auth配置
auth.center.app_id=1
auth.center.salt=phw3&AvNt!kVz%z@&^Fp
#auth.auto.open=false