# redis
spring.redis.host=${REDIS_HOST}
spring.redis.port=${REDIS_PORT}
spring.redis.database=${REDIS_DB}
spring.redis.password=${REDIS_PASSWORD}
spring.redis.timeout=2000
spring.redis.jedis.pool.max-wait=2000
spring.redis.jedis.pool.time-between-eviction-runs=20000
# mysql
spring.autoconfigure.exclude=com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.initial-size=10
spring.datasource.druid.min-idle=10
spring.datasource.druid.max-active=100
spring.datasource.druid.max-wait=3000
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=30
spring.datasource.druid.time-between-eviction-runs-millis=30000
spring.datasource.druid.min-evictable-idle-time-millis=30000
spring.datasource.druid.test-on-borrow=true
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-return=true
spring.datasource.druid.remove-abandoned=true
spring.datasource.druid.remove-abandoned-timeout=120
spring.datasource.druid.connection-error-retry-attempts=3
# mysql 数据源
spring.datasource.dynamic.primary=ecos-local
spring.datasource.dynamic.strict=true
spring.datasource.dynamic.datasource.ecos-admin.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.ecos-admin.url=${MYSQL_ECOS_ADMIN_URL}
spring.datasource.dynamic.datasource.ecos-admin.username=${MYSQL_ECOS_ADMIN_USERNAME}
spring.datasource.dynamic.datasource.ecos-admin.password=${MYSQL_ECOS_ADMIN_PASSWORD}
spring.datasource.dynamic.datasource.ecos-local.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.ecos-local.url=${MYSQL_ECOS_LOCAL_URL}
spring.datasource.dynamic.datasource.ecos-local.username=${MYSQL_ECOS_LOCAL_USERNAME}
spring.datasource.dynamic.datasource.ecos-local.password=${MYSQL_ECOS_LOCAL_PASSWORD}
spring.datasource.dynamic.datasource.ecos-event.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.ecos-event.url=${MYSQL_ECOS_EVENT_URL}
spring.datasource.dynamic.datasource.ecos-event.username=${MYSQL_ECOS_EVENT_USERNAME}
spring.datasource.dynamic.datasource.ecos-event.password=${MYSQL_ECOS_EVENT_PASSWORD}
spring.datasource.dynamic.datasource.ecos-xxl.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.ecos-xxl.url=${MYSQL_ECOS_XXL_URL}
spring.datasource.dynamic.datasource.ecos-xxl.username=${MYSQL_ECOS_XXL_USERNAME}
spring.datasource.dynamic.datasource.ecos-xxl.password=${MYSQL_ECOS_XXL_PASSWORD}
# mybatis-plus
mybatis-plus.mapper-locations=classpath:/mapper/**/*.xml
mybatis-plus.global-config.db-config.id-type=auto
# page helper
pagehelper.helper-dialect=mysql
pagehelper.reasonable=false
pagehelper.support-methods-arguments=true
pagehelper.params=count=countSql
# i18n
spring.messages.basename=messages
# log
logging.level.root=INFO
logging.level.com.weihengtech.dao=WARN
logging.level.com.alibaba.nacos.client.naming=WARN
logging.file.name=/usr/java/logs/ecos-service.log
logging.file.max-size=50MB
# mqtt
ali.cloud.access.key.id=${ALI_CLOUD_MQTT_ACCESS_KEY_ID}
ali.cloud.access.key.secret=${ALI_CLOUD_MQTT_ACCESS_KEY_SECRET}
ali.cloud.iot.endpoint=${ALI_CLOUD_MQTT_IOT_ENDPOINT}
ali.cloud.iot.region-id=${ALI_CLOUD_MQTT_IOT_REGION_ID}
ali.cloud.product.key=${ALI_CLOUD_MQTT_PRODUCT_KEY}
ali.cloud.instance-id=${ALI_CLOUD_INSTANCEID:}
# custom
upload.base.url=${CUSTOM_UPLOAD_BASE_URL}
upload.base.url.http=${CUSTOM_UPLOAD_BASE_URL_HTTP}
custom.timezone=${CUSTOM_TIMEZONE}
# lindorm
custom.lindorm.url=${CUSTOM_LINDORM_URL}
custom.lindorm.elink.url=${CUSTOM_LINDORM_ELINK_URL}
custom.lindorm.elink.database=${CUSTOM_LINDORM_ELINK_DATABASE}
custom.lindorm.elink.table=${CUSTOM_LINDORM_ELINK_TABLE}
custom.lindorm.tuya.database=${CUSTOM_LINDORM_TUYA_DATABASE}
custom.lindorm.tuya.table=${CUSTOM_LINDORM_TUYA_TABLE}
custom.lindorm.charger.url=${CUSTOM_LINDORM_CHARGER_URL}
custom.lindorm.charger.database=${CUSTOM_LINDORM_CHARGER_DATABASE}
custom.lindorm.charger.table=${CUSTOM_LINDORM_CHARGER_TABLE}
# admin
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true
management.metrics.export.prometheus.enabled=true
management.metrics.tags.application=${spring.application.name}
# xxl-job
### 调度中心部署根地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
xxl.job.admin.addresses=${XXL_JOB_ADMIN_ADDRESSES}
### 执行器通讯TOKEN [选填]：非空时启用；
xxl.job.accessToken=${XXL_JOB_ACCESS_TOKEN}
### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
xxl.job.executor.appname=${XXL_JOB_EXECUTOR_APPNAME}
### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
xxl.job.executor.ip=${XXL_JOB_EXECUTOR_IP}
### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
xxl.job.executor.port=${XXL_JOB_EXECUTOR_PORT_SERVICE}
### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
xxl.job.executor.logpath=${XXL_JOB_EXECUTOR_LOGPATH}
### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
xxl.job.executor.logretentiondays=${XXL_JOB_EXECUTOR_LOGRETENTIONDAYS}
### 是否注册调度器
custom.xxl.job.enable=${XXL_JOB_ENABLE}
# url
custom.url.account=${CUSTOM_URL_ACCOUNT}
custom.url.client=${CUSTOM_URL_CLIENT}
custom.url.elinkter=${CUSTOM_URL_ELINTER}
custom.url.tuya=${CUSTOM_URL_TUYA}
custom.url.ip=${CUSTOM_URL_IP}
custom.url.ieee=${CUSTOM_URL_IEEE}
custom.url.ieee-ausnet=${CUSTOM_URL_IEEE_AUSNET}
custom.url.ieee-synergy=${CUSTOM_URL_IEEE_SYNERGY}
sdk.iot.ecos.base-url=${SDK_IOT_ECOS_BASE_URL}
sdk.iot.ecos.read-timeout=${SDK_IOT_ECOS_READ_TIMEOUT}

# influxdb
influxdb.url=${CUSTOM_INFLUX_URL}
influxdb.elink-url=${INFLUXDB_ELINK_URL}
influxdb.charge-rurl=${INFLUXDB_CHARGER_URL}
influxdb.org=${CUSTOM_INFLUX_ORG}
influxdb.token=${CUSTOM_INFLUX_TOKEN}
influxdb.tuya.database=${INFLUXDB_TUYA_DATABASE}
influxdb.tuya.table=${INFLUXDB_TUYA_TABLE}
influxdb.elink.database=${INFLUXDB_ELINK_DATABASE}
influxdb.elink.table=${INFLUXDB_ELINK_TABLE}
influxdb.charger.database=${INFLUXDB_CHARGER_DATABASE}
influxdb.charger.table=${INFLUXDB_CHARGER_TABLE}
