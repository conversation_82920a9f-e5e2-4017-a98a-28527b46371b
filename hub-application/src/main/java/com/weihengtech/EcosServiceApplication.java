package com.weihengtech;

import com.weihengtech.utils.InitUtil;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
 * <AUTHOR>
 */
@SpringCloudApplication
@EnableTransactionManagement
@EnableWebMvc
@MapperScan(basePackages = "com.weihengtech.dao")
@EnableScheduling
@EnableRetry
@EnableFeignClients(basePackages = "com.weihengtech.api")
public class EcosServiceApplication {

	public static void main(String[] args) {
		InitUtil.init(SpringApplication.run(EcosServiceApplication.class, args));
	}
}
