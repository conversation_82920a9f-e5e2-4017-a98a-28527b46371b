package com.weihengtech.annotation.validator;

import com.weihengtech.validator.PasswordValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PasswordValidator.class)
public @interface PasswordValidate {

	/**
	 * 约束注解校验时的默认输出信息
	 */
	String message() default "密码格式错误";

	/**
	 * 约束直接在验证时所属的组别
	 */
	Class<?>[] groups() default {};

	/**
	 * 约束注解的有效负载
	 */
	Class<? extends Payload>[] payload() default {};
}
