package com.weihengtech.validator;

import cn.hutool.core.util.ReUtil;
import com.weihengtech.annotation.validator.IsLocalDateTime;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class IsLocalDateTimeValidator implements ConstraintValidator<IsLocalDateTime, String> {

	@Override
	public void initialize(IsLocalDateTime constraintAnnotation) {
		ConstraintValidator.super.initialize(constraintAnnotation);
	}

	@Override
	public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
		final String regex = "^[\\d]{4}-[\\d]{2}-[\\d]{2} [\\d]{2}:[\\d]{2}:[\\d]{2}$";
		String str = Optional.ofNullable(s).orElse("");
		return str.length() == 0 || ReUtil.isMatch(regex, str);
	}
}
