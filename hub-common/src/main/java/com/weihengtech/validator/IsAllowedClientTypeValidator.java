package com.weihengtech.validator;

import com.weihengtech.annotation.validator.IsAllowedClientType;
import com.weihengtech.enums.other.ClientTypeEnum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 */
public class IsAllowedClientTypeValidator implements ConstraintValidator<IsAllowedClientType, String> {

	@Override
	public boolean isValid(String clientType, ConstraintValidatorContext constraintValidatorContext) {
		for (ClientTypeEnum clientTypeEnum : ClientTypeEnum.values()) {
			if (clientTypeEnum.name().equals(clientType)) {
				return true;
			}
		}
		return false;
	}
}
