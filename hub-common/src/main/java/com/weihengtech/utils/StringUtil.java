package com.weihengtech.utils;

/**
 * <AUTHOR>
 */
public class StringUtil {

	public static String convertToMethodName(String typeName) {
		String[] stringArray = typeName.split("_");
		StringBuilder stringBuilder = new StringBuilder();
		for (int i = 0; i < stringArray.length; i++) {
			if (i == 0) {
				stringBuilder.append(stringArray[i].toLowerCase());
			} else {
				stringBuilder.append(stringArray[i].charAt(0));
				stringBuilder.append(stringArray[i].substring(1).toLowerCase());
			}
		}
		return stringBuilder.toString();
	}
}
