package com.weihengtech.utils;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletionService;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.Future;
import java.util.function.Function;

/**
 * 获取多线程异步结果工具类
 *
 * <AUTHOR>
 * @date 2023/9/27 19:25
 * @version 1.0
 */
@Slf4j
public class AsyncResultUtil {


    /**
     * 多线程获取值
     * @param needHandleParList 需要获取的入参
     * @param function 多线程获取的方法
     * @param executor 线程
     * @param <K> 入参
     * @param <V> 出参
     * @return 结果
     */
    public static  <K, V> List<V> multiThreadDone(List<K> needHandleParList, Function<K, V> function, Executor executor) {
        if (needHandleParList == null || needHandleParList.isEmpty()) {
            return Collections.emptyList();
        }
        ExecutorCompletionService<V> completionService = new ExecutorCompletionService<>(executor);
        for (K handlePar : needHandleParList) {
            completionService.submit(() -> function.apply(handlePar));
        }
        int size = needHandleParList.size();
        List<V> result = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            V v = takeAsyncResult(completionService);
            if (v != null) {
                result.add(v);
            }
        }
        return result;
    }

    /**
     * 多线程获取值，入参为list的
     * @param needHandleParList 需要获取的入参
     * @param function 多线程获取的方法
     * @param executor 线程
     * @param <K> 入参
     * @param <V> 出参
     * @return 结果
     */
    public static <K, V> List<V> multiThreadDoneList(List<K> needHandleParList, Function<List<K>, List<V>> function, Executor executor, int partNum) {
        if (needHandleParList == null || needHandleParList.isEmpty()) {
            return Collections.emptyList();
        }
        ExecutorCompletionService<List<V>> completionService = new ExecutorCompletionService<>(executor);
        List<List<K>> partitionNeedHandleParList = Lists.partition(needHandleParList, partNum);
        List<Future<List<V>>> futureList = new ArrayList<>(partitionNeedHandleParList.size());
        try {
            for (List<K> handlePar : partitionNeedHandleParList) {
                Future<List<V>> submit = completionService.submit(() -> function.apply(handlePar));
                futureList.add(submit);
            }
            int size = partitionNeedHandleParList.size();
            List<V> result = new ArrayList<>();
            for (int i = 0; i < size; i++) {
                List<V> partVal = takeAsyncResult(completionService);
                if (partVal != null) {
                    result.addAll(partVal);
                }
            }
            return result;
        } finally {
            for (Future<List<V>> listFuture : futureList) {
                listFuture.cancel(true);
            }
        }

    }

    /**
     * 从异步容器中获取结果
     *
     * @param completionService 异步容器
     * @param <T> 类型
     * @return 结果
     */
    private static  <T> T takeAsyncResult(CompletionService<T> completionService) {
        try {
            return completionService.take().get();
        } catch (ExecutionException | InterruptedException e) {
            log.error(e.getMessage(), e);
            throw new IllegalArgumentException(e.getMessage(), e);
        }
    }
}
