package com.weihengtech.utils;

import cn.hutool.json.JSONObject;
import lombok.val;

/**
 * <AUTHOR>
 */
public class ResultUtil {

	public static Boolean isRequestFail(JSONObject jsonObject) {
		val fail = "FAILURE";
		String state = jsonObject.getOrDefault("state", "FAILURE").toString();
		return fail.equals(state);
	}

	public static Boolean isRequestSuccess(JSONObject jsonObject) {
		val success = "SUCCESS";
		val pending = "PENDING";
		val failResult = "false";
		String state = jsonObject.getOrDefault("state", "FAILURE").toString();
		String result = jsonObject.getOrDefault("result", "false").toString();
		return pending.equals(state) || (success.equals(state) && !result.equals(failResult));
	}
}
