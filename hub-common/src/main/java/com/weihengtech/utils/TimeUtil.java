package com.weihengtech.utils;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Pair;
import lombok.val;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Calendar;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
public class TimeUtil {

	public static String localDateTimeToSerialString(LocalDateTime localDateTime) {
		return LocalDateTimeUtil.format(localDateTime, "yyyy-MM-dd HH:mm:ss");
	}

	public static String localDateTimeToSerialString(LocalDateTime localDateTime, String format) {
		return LocalDateTimeUtil.format(localDateTime, format);
	}

	public static String localDateTimeToSerialStringWithOffset(LocalDateTime localDateTime, String offset) {
		long timestamp = localDateTime.atZone(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
		return longTimestampToSerialString(timestamp, offset);
	}

	public static String localDateTimeToSerialStringWithOffset(
			LocalDateTime localDateTime, String offset,
			String format
	) {
		long timestamp = localDateTime.atZone(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
		return longTimestampToSerialString(timestamp, offset, format);
	}

	public static String longTimestampToSerialString(Long timestamp, String offset) {
		return localDateTimeToSerialString(LocalDateTimeUtil.of(timestamp + offsetToMillis(offset)));
	}

	public static String longTimestampToSerialStringOffsetGMT8(Long timestamp, String offset) {
		return localDateTimeToSerialString(LocalDateTimeUtil.of(timestamp + offsetToMillis(offset) - 8 * 3600 * 1000L));
	}

	public static String longTimestampToSerialStringOffsetGMT8(Long timestamp, String offset, String format) {
		return localDateTimeToSerialString(LocalDateTimeUtil.of(timestamp + offsetToMillis(offset) - 8 * 3600 * 1000L), format);
	}

	public static String longTimestampToSerialString(Long timestamp, String offset, String format) {
		return localDateTimeToSerialString(LocalDateTimeUtil.of(timestamp + offsetToMillis(offset)), format);
	}

	public static Long getLastDayStart(String offset) {
		return getDayStart(-1, offset);
	}

	public static Long getLastDayEnd(String offset) {
		return getDayEnd(-1, offset);
	}

	public static Long getDayStart(Integer dayOffset, String offset) {
		Calendar calendar = getCalendarAtTodayStartWithOffset(offset);
		calendar.add(Calendar.DATE, dayOffset);
		return calendar.getTime().getTime();
	}

	public static Long getDayEnd(Integer dayOffset, String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(System.currentTimeMillis());
		calendar.add(Calendar.DATE, dayOffset);
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.SECOND, 59);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.MILLISECOND, 999);
		return calendar.getTime().getTime();
	}

	public static Long getCurrentTime(String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(System.currentTimeMillis());
		return calendar.getTime().getTime();
	}

	public static Integer getDayOfWeekend(long timestamp, String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(timestamp);
		int weekDay = calendar.get(Calendar.DAY_OF_WEEK) - 1;
		return weekDay == 0 ? 7 : weekDay;
	}

	public static Integer getHourOfDay(long timestamp, String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(timestamp);
		return calendar.get(Calendar.HOUR_OF_DAY);
	}

	public static Integer getDayCountWithOffsetMonth(String offset, Integer offsetMonth) {
		Calendar calendar = getCalendarWithOffset("GMT" + offset);
		calendar.setTimeInMillis(System.currentTimeMillis());
		calendar.add(Calendar.MONTH, offsetMonth);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		long offsetMonthTimestamp = calendar.getTime().getTime();
		return getDaysInStartAndEnd(offsetMonthTimestamp, System.currentTimeMillis());
	}

	public static Pair<Long, Long> computeStartAndEndByOffsetDay(Integer offsetDay, String offset) {
		return Pair.of(getDayStart(offsetDay, offset), getDayEnd(0, offset));
	}

	public static Integer getDaysInStartAndEnd(long startTime, long endTime) {
		return Integer.parseInt(String.valueOf((endTime - startTime) / (1000 * 3600 * 24)));
	}

	public static Long getCurrentWeekStart(String offset) {
		Calendar calendar = getCalendarAtTodayStartWithOffset(offset);
		calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
		return calendar.getTime().getTime();
	}

	public static Long getCurrentMonthStart(String offset) {
		Calendar calendar = getCalendarAtTodayStartWithOffset(offset);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return calendar.getTime().getTime();
	}

	public static Long getCurrentSeasonStart(String offset) {
		Calendar calendar = getCalendarAtTodayStartWithOffset(offset);
		int month = calendar.get(Calendar.MONTH);
		if (Calendar.APRIL > month) {
			calendar.set(Calendar.MONTH, Calendar.JANUARY);
		} else if (Calendar.JULY > month) {
			calendar.set(Calendar.MONTH, Calendar.APRIL);
		} else if (Calendar.OCTOBER > month) {
			calendar.set(Calendar.MONTH, Calendar.JULY);
		} else {
			calendar.set(Calendar.MONTH, Calendar.OCTOBER);
		}
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return calendar.getTime().getTime();
	}

	public static Long getCurrentYearStart(String offset) {
		Calendar calendar = getCalendarAtTodayStartWithOffset(offset);
		calendar.set(Calendar.MONTH, Calendar.JANUARY);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return calendar.getTime().getTime();
	}

	private static Calendar getCalendarAtTodayStartWithOffset(String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(System.currentTimeMillis());
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar;
	}

	public static Long getAssignDayStart(Long startTime, String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(startTime == null ? System.currentTimeMillis() : startTime);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime().getTime();
	}

	public static Long getAssignMonthStart(Long startTime, String offset, Integer offsetMonth) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(startTime == null ? System.currentTimeMillis() : startTime);
		calendar.add(Calendar.MONTH, offsetMonth);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime().getTime();
	}

	public static Long getAssignMonthEnd(Long startTime, String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(startTime == null ? System.currentTimeMillis() : startTime);
		calendar.add(Calendar.MONTH, 1);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.add(Calendar.DAY_OF_MONTH, -1);
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		calendar.set(Calendar.MILLISECOND, 999);
		return calendar.getTime().getTime();
	}

	private static TimeZone offsetToTimeZone(String offset) {
		offset = getTimezoneCode(offset);
		offset = offset.replace("GMT", "");
		return TimeZone.getTimeZone("GMT" + offset);
	}

	private static Calendar getCalendarWithOffset(String offset) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeZone(offsetToTimeZone(offset));
		return calendar;
	}

	private static long offsetToMillis(String offset) {
		offset = getTimezoneCode(offset);
		offset = offset.replace("GMT", "");
		if (offset.length() > 3) {
			if (offset.startsWith("+")) {
				offset = offset.replace("+", "");
				String[] split = offset.split(":");
				return Long.parseLong(split[0]) * 60 * 60 * 1000 + Long.parseLong(split[1]) * 60 * 1000;
			} else {
				offset = offset.replace("-", "");
				String[] split = offset.split(":");
				return -(Long.parseLong(split[0]) * 60 * 60 * 1000 + Long.parseLong(split[1]) * 60 * 1000);
			}
		} else {
			if (offset.startsWith("+")) {
				offset = offset.replace("+", "");
				return Long.parseLong(offset) * 60 * 60 * 1000;
			} else {
				offset = offset.replace("-", "");
				return -(Long.parseLong(offset) * 60 * 60 * 1000);
			}
		}
	}

	public static Pair<Integer, Integer> offsetToNumber(String timezone) {
		String timezoneCode = getTimezoneCode(timezone);
		String time = timezoneCode.replaceAll("GMT", "");
		if (time.length() <= 3) {
			time = time + ":00";
		}
		if (time.startsWith("+")) {
			time = time.substring(1);
			String[] split = time.split(":");
			return Pair.of(Integer.parseInt(split[0]), Integer.parseInt(split[1]));
		}
		time = time.substring(1);
		String[] split = time.split(":");
		return Pair.of(-Integer.parseInt(split[0]), -Integer.parseInt(split[1]));
	}

	private static String getTimezoneCode(String timezone) {
		try {
			val instant = Instant.now();
			ZoneId zoneId = ZoneId.of(timezone);
			val timezoneStr = zoneId.getRules().getOffset(instant).toString();
			return "Z".equals(timezoneStr) ? "+00:00" : timezoneStr;
		} catch (Exception ignored) {
		}
		return timezone;
	}

	public static String getLightingTimezone(String timezone) {
		String timezoneCode = getTimezoneCode(timezone);
		timezoneCode = timezoneCode.replaceAll("GMT", "");
		return "GMT" + timezoneCode;
	}

	public static Integer convertMinuteBetweenTimestamp(Long small, Long large) {
		return new BigDecimal((large - small) / 60 / 1000).intValue();
	}
}
