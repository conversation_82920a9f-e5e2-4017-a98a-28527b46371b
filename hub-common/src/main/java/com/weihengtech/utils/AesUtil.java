package com.weihengtech.utils;

import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
public class AesUtil {

	private static final AES AES = new AES(Mode.CTS, Padding.PKCS5Padding, "0CoJUW8judm6Qyw8".getBytes(),
			"8878654531627389".getBytes()
	);

	public static String encode(String key) {
		return AES.encryptHex(key.getBytes(StandardCharsets.UTF_8));
	}

	public static String decode(String secret) {
		return AES.decryptStr(secret, StandardCharsets.UTF_8);
	}

	public static void main(String[] args) {
		System.out.println(AesUtil.encode("AcMeterType"));
	}
}
