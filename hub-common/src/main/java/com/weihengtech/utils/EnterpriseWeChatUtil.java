package com.weihengtech.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 */
@Slf4j
public class EnterpriseWeChatUtil {

	public static String hybridContent(HashMap<String, String> param) {
		// status firing  resolved
		String status = param.get("status");
		StringBuilder builder = StrUtil.builder();
		if ("firing".equals(status)) {
			builder.append(warning("告警")).append(" ").append(param.get("alertName")).append(lineBreak())
					.append(quote("时间: ")).append(comment(convertTime(param.get("startAt")))).append(lineBreak());
		} else {
			builder.append(info("恢复")).append(" ").append(param.get("alertName")).append(lineBreak())
					.append(quote("时间: ")).append(comment(convertTime(param.get("endAt")))).append(lineBreak());
		}
		return builder
				.append(quote("设备: ")).append(bold(param.get("deviceId"))).append(lineBreak())
				.append(quote("分组: ")).append(info(param.get("group"))).append(lineBreak())
				.append(quote("等级: ")).append(param.get("level")).append(lineBreak())
				.append(quote("监听: ")).append(param.get("monitor")).append(lineBreak()).append(lineBreak())
				.append(link("查看", param.get("generatorURL")))
				.toString();
	}

	private static String info(String data) {
		return font("info", data);
	}

	private static String warning(String data) {
		return font("warning", data);
	}

	private static String comment(String data) {
		return font("comment", data);
	}

	private static String font(String style, String data) {
		return "<font color=\"" + style + "\">" + data + "</font>";
	}

	private static String lineBreak() {
		return "\n";
	}

	private static String bold(String data) {
		return "**" + data + "**";
	}

	private static String link(String name, String data) {
		return "[" + name + "](" + data + ")";
	}

	private static String quote(String data) {
		return "> " + data;
	}

	private static String convertTime(String data) {
		data = data.replace("T", " ");
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
		Date parse;
		try {
			parse = simpleDateFormat.parse(data);
		} catch (ParseException e) {
			log.warn(e.getMessage());
			return "";
		}
		return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(parse);
	}
}
