package com.weihengtech.utils;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 */
public class UrlUtil {

	public static String httpsUrl(String url) {
		if (url.contains(":")) {
			return StrUtil.format("https:{}", StrUtil.subAfter(url, ':', false));
		}
		return StrUtil.format("https://{}", url);
	}

	public static String httpUrl(String url) {
		if (url.contains(":")) {
			return StrUtil.format("http:{}", StrUtil.subAfter(url, ':', false));
		}
		return StrUtil.format("http://{}", url);
	}
}
