package com.weihengtech.utils;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.enums.device.ImportTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class SpecialParseUtil {

	public static final String REGEX_352 = "^(L?.{3}-.{5}-.{2})$";
	public static final String REGEX_BRAND = "^(eCactus|GONEO|HERF|SEMOOKII|LEHO|CEEG|BLAUHOFF|EVANTRA)$";
	public static final String REGEX_FACTORY = "^WeiHeng$";
	public static final String REGEX_BMS_MANUFACTURER = "^(None|Dyness|WeiHeng|Pylon|Poweramp)$";
	public static final String REGEX_MODEL = "^SPHA(.*)$";
	public static final String REGEX_DSP_SUB = "^(\\d+)-(\\d+)$";
	public static final String REGEX_EMS_SUB = "^(.{5})-(.{3})R$";

	public static String parseDeviceSn(Integer type, String inputDeviceSn) {
		// 兼容充电桩
		if (ImportTypeEnum.charger.getCode().equals(type)) {
			if (inputDeviceSn.startsWith("SN") && inputDeviceSn.length() == 16) {
				return inputDeviceSn;
			} else {
				return "";
			}
		} else {
			try {
				return validSnDate(inputDeviceSn);
			} catch (Exception e) {
				log.warn("解析DeviceSn异常 " + inputDeviceSn);
				return "";
			}
		}
	}

	public static String parseBmsSn(String inputBmsSn) {
		return StrUtil.isBlank(inputBmsSn) ? "" : inputBmsSn;
	}

	private static String validSnDate(String sn) {
		char[] chars = sn.toCharArray();
		String stringBuilder = String.valueOf(chars[11]) + chars[12];
		int num = Integer.parseInt(stringBuilder);
		if (chars.length == 18 && num >= 19
				&& ((chars[13] >= '1' && chars[13] <= '9') || (chars[13] >= 'A' && chars[13] <= 'C'))
				&& ((chars[14] >= '0' && chars[14] <= '9') || (chars[14] >= 'A' && chars[14] <= 'V'))) {
			return sn;
		}
		log.warn("解析DeviceSn失败 " + sn);
		return "";
	}

	public static String matchTransform(String regex, String content, List<Boolean> matchList, String propName) {
		Boolean isMatch = ReUtil.isMatch(regex, content.trim());
		if (!isMatch) {
			log.warn("匹配正则失败 " + propName + " " + content);
		}
		matchList.add(isMatch);
		return isMatch ? content.trim() : "";
	}
}
