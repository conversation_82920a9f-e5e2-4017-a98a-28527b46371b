package com.weihengtech.utils;

import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.concurrent.TimeUnit;

import static cn.hutool.core.thread.ThreadUtil.sleep;

/**
 * 事务工具
 *
 * <AUTHOR>
 * @date 2023/11/24 13:44
 * @version 1.0
 */
public class TransactionUtils {

    /**
     * 事务结束事件
     * @param runnable 方法
     */
    public static void afterCompletion(Runnable runnable) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCompletion(int status) {
                    runnable.run();
                }
            });
        } else {
            runnable.run();
        }
    }

    /**
     * 注册事务回调
     * @param runnable 执行方法
     */
    public static void afterCommit(Runnable runnable) {
        afterCommit(0, TimeUnit.SECONDS, runnable);
    }


    /**
     * 注册事务回调
     * @param delay 延迟时间
     * @param timeUnit 时间戳
     * @param runnable 执行方法
     */
    public static void afterCommit(long delay, TimeUnit timeUnit, Runnable runnable) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    if (delay >0) {
                        sleep(delay, timeUnit);
                    }
                    runnable.run();
                }
            });
        } else {
            sleep(delay, timeUnit);
            runnable.run();
        }
    }
}
