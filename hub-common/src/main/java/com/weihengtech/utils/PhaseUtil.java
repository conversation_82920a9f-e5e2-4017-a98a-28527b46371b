package com.weihengtech.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import java.util.List;

/**
 * <AUTHOR>
 */
public class PhaseUtil {

	/**
	 * 单相机前缀
	 */
	private static final String S = "S";

	/**
	 * 三相机前缀
	 */
	private static final String T = "T";

	/**
	 * 北美机前缀
	 */
	private static final String SHD = "SHD";

	/**
	 * 是否单相机
	 *
	 * @param deviceSn 设备SN
	 * @return 是否单相
	 */
	public static boolean isSinglePhase(String deviceSn) {
		if (StrUtil.isBlank(deviceSn)) {
			return false;
		}
		return deviceSn.startsWith(S);
	}

	/**
	 * 是否三相机
	 *
	 * @param deviceSn 设备SN
	 * @return 是否三相
	 */
	public static boolean isThreePhase(String deviceSn) {
		if (StrUtil.isBlank(deviceSn)) {
			return false;
		}
		return deviceSn.startsWith(T);
	}

	/**
	 * 是否北美机
	 *
	 * @param deviceSn 设备SN
	 * @return 是否北美机
	 */
	public static boolean isUs(String deviceSn) {
		if (StrUtil.isBlank(deviceSn)) {
			return false;
		}
		return deviceSn.startsWith(SHD);
	}

	/**
	 * 是否单、三相混合
	 *
	 * @param deviceSnList 设备SN列表
	 * @return 是否单、三相混合
	 */
	public static boolean isAllSamePhase(List<String> deviceSnList) {
		if (CollUtil.isEmpty(deviceSnList)) {
			return true;
		}
		boolean allUs = deviceSnList.stream()
				.allMatch(PhaseUtil :: isUs);
		boolean anyUs = deviceSnList.stream()
				.anyMatch(PhaseUtil :: isUs);

		boolean allSingle = deviceSnList.stream()
				.allMatch(PhaseUtil::isSinglePhase);
		boolean allThree = deviceSnList.stream()
				.allMatch(PhaseUtil :: isThreePhase);

		return allUs || allThree || (allSingle && !anyUs);
	}

}
