package com.weihengtech.utils;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class SnowFlakeUtil {

	@Value("${snowflake.work}")
	private Long workId;

	@Value("${snowflake.datacenter}")
	private Long datacenterId;

	private volatile Snowflake snowflake;

	public long generateId() {
		singleInstance();
		return snowflake.nextId();
	}

	public String generateStringId() {
		singleInstance();
		return snowflake.nextIdStr();
	}

	private void singleInstance() {
		if (snowflake == null) {
			synchronized (SnowFlakeUtil.class) {
				if (snowflake == null) {
					snowflake = IdUtil.getSnowflake(workId, datacenterId);
				}
			}
		}
	}
}
