package com.weihengtech.enums.firmware;

import lombok.Getter;

/**
 * @author: jiahao.jin
 * @create: 2025-07-14 17:10
 * @description:
 */

@Getter
public enum UpgradeDeviceStatusEnum {

    // 升级状态：等待中/升级中/成功/失败
    WAITING("waiting"), UPGRADEING("upgradeing"), SUCCESS("success"), FAILURE("failure");

    private final String status;

    UpgradeDeviceStatusEnum(String status) {
        this.status = status;
    }
}
