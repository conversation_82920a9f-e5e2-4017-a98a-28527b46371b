package com.weihengtech.enums.firmware;

import cn.hutool.core.lang.Pair;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum UploadTypeEnum {

	/**
	 * 固件包类型
	 */
	WIFI(""), DSP1("FIRMWARE_START1"), DSP2("FIRMWARE_START2"), B<PERSON>("FIRMWARE_BMSFW,FIRMWARE_DCBFW"), EMS("FIRMWARE_HMI")
	, ARCDSP("");

	String prefix;

	UploadTypeEnum(String prefix) {
		this.prefix = prefix;
	}

	/**
	 * 根据固件包类型获取设备映射方法
	 *
	 * @param addressMap 固件包类型
	 * @return 固件包类型对应的方法
	 */
	public static Pair<String, String> getDeviceVersionMethod(String addressMap) {
		switch (addressMap) {
			case "WIFI":
				return Pair.of("setWifiVersion", "");
			case "DSP1":
				return Pair.of("setDsp1SoftwareVersion", "setDsp1SubVersion");
			case "DSP2":
				return Pair.of("setDsp2SoftwareVersion", "setDsp2SubVersion");
			case "BMS":
				return Pair.of("setBmsSoftwareVersion", "");
			case "EMS":
				return Pair.of("setEmsSoftwareVersion", "setEmsSubVersion");
			case "ARCDSP":
				return Pair.of("setArcDspSoftwareVersion", "setArcDspSubVersion");
			default:
				return Pair.of("", "");
		}
	}
}
