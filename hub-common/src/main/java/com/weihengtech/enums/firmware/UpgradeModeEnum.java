package com.weihengtech.enums.firmware;

import lombok.Getter;

/**
 * @author: jiahao.jin
 * @create: 2025-07-14 16:50
 * @description:
 */
@Getter
public enum UpgradeModeEnum {

    // 立即执行
    IMEDIATE_EXECUTION("immediate_execution"),
    // 定时执行
    SCHEDULED_EXECUTION("scheduled_execution");

    private final String modeName;

    UpgradeModeEnum(String modeName) {
        this.modeName = modeName;
    }
}
