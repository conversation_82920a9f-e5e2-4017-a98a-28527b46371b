package com.weihengtech.enums.firmware;

import lombok.Getter;

/**
 * @author: jiahao.jin
 * @create: 2025-07-14 16:55
 * @description:
 */
@Getter
public enum UpgradeTaskStatusEnum {

    // 任务状态：待执行/执行中/已结束/已终止
    WAITING("waiting"), RUNNING("running"), FINISHED("finished"), TERMINATED("terminated");

    private final String status;

    UpgradeTaskStatusEnum(String status) {
        this.status = status;
    }
}
