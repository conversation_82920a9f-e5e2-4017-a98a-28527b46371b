package com.weihengtech.enums.firmware;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 固件包适用机型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/25 10:55
 */
@AllArgsConstructor
@Getter
public enum FirmwareModelEnum {

    single(0, 101),
    three(1, 102),
    us(2, 103);

    final int model;
    final int seriesCode;

    public static boolean isMatchSeries(String model, int series) {
        return Arrays.stream(FirmwareModelEnum.values())
                .anyMatch(i -> model.equals(String.valueOf(i.getModel())) && series == i.getSeriesCode());
    }

}
