package com.weihengtech.enums.firmware;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum MqTagEnum {

	UPGRADE_FAILURE("upgradeFailure"),
	UPGRADE_POST("upgradePost"),
	UPGRADE_CHECK("upgradeCheck"),
	UPGRADE_POST_BATCH("upgradePostBatch"),
	UPGRADE_BATCH_CHECK("upgradeBatchCheck")

	;

	String methodName;

	MqTagEnum(String methodName) {
		this.methodName = methodName;
	}

	public interface TimeDelay {

		Long MILLISECOND = 1L;
		Long SECOND = MILLISECOND * 1000L;
		Long MINUTE = SECOND * 60L;
		Long HALF_HOUR = MINUTE * 30L;
		Long HOUR = HALF_HOUR * 2;
		// 十分钟
		Long TEN_MINUTE = MINUTE * 10L;
	}
}
