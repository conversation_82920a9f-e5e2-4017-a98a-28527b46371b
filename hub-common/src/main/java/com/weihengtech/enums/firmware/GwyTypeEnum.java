package com.weihengtech.enums.firmware;

import com.weihengtech.consts.CommonConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 固件包适用机型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/25 10:55
 */
@AllArgsConstructor
@Getter
public enum GwyTypeEnum {

    wifi("CSAWW", CommonConstants.ITEM_CODE_WIFI),
    eth("CSAEW", CommonConstants.ITEM_CODE_ETH),
    _4g("CSACW", CommonConstants.ITEM_CODE_4G);

    final String pre;
    final String dictItemCode;

    public static String getCodeByPre(String pre) {
        return Arrays.stream(GwyTypeEnum.values())
                .filter(i -> i.pre.equals(pre))
                .findFirst()
                .map(GwyTypeEnum::getDictItemCode)
                .orElse(null);
    }

}
