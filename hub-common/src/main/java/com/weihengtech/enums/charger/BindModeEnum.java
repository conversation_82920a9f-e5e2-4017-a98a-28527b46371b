package com.weihengtech.enums.charger;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 绑定模式
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/6 13:50
 */
@AllArgsConstructor
@Getter
public enum BindModeEnum {
    CHARGE_DERICT_BIND(0),
    REMOTE_BIND(1),
    REMOTE_WIRE_BIND(2),
    REMOTE_WIFI_BIND(3);

    final Integer code;

    public static boolean isNetBind(Integer bindMode) {
        return REMOTE_WIFI_BIND.getCode().equals(bindMode) ||
                REMOTE_WIRE_BIND.getCode().equals(bindMode);
    }

    public static boolean isFinishCurStep(Integer bindMode) {
        return CHARGE_DERICT_BIND.getCode().equals(bindMode) ||
                REMOTE_WIFI_BIND.getCode().equals(bindMode) ||
                REMOTE_WIRE_BIND.getCode().equals(bindMode);
    }

    public static boolean isDirectBind(Integer bindMode) {
        return CHARGE_DERICT_BIND.getCode().equals(bindMode);
    }
}
