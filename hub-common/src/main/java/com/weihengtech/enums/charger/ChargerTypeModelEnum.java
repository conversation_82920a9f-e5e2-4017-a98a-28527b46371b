package com.weihengtech.enums.charger;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 充电桩类型、型号枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/30 15:38
 */
@AllArgsConstructor
@Getter
public enum ChargerTypeModelEnum {

    SINGLE_0x30(48, 10, "Aloe SH", "WH-ECA-702EUA", "7.0KW", "32A", "270V", "140V"),
    SINGLE_0x31(49, 10, "Aloe SH", "WH-ECA-702EUB", "7.0KW", "32A","270V", "140V"),
    THREE_0x32(50, 11, "Aloe TH", "WH-ECA-113EUA", "11KW", "16A", "270V", "140V"),
    THREE_0x33(51, 11, "Aloe TH", "WH-ECA-113EUB", "11KW", "16A", "270V", "140V");

    final Integer typeCode;
    final Integer typeId;
    final String type;
    final String model;
    final String ratedPower;
    final String ratedCurrent;
    final String voltageUp;
    final String voltageDown;

    public static String getModelByCode(Integer code) {
        return Arrays.stream(ChargerTypeModelEnum.values())
                .filter(i -> i.typeCode.equals(code))
                .map(i -> i.model)
                .findFirst()
                .orElse(null);
    }

    public static Integer getTypeByCode(Integer code) {
        return Arrays.stream(ChargerTypeModelEnum.values())
                .filter(i -> i.typeCode.equals(code))
                .map(i -> i.typeId)
                .findFirst()
                .orElse(null);
    }

    public static Double getRatedPowerByModel(String model) {
        return Arrays.stream(ChargerTypeModelEnum.values())
                .filter(i -> i.model.equals(model))
                .map(i -> i.ratedPower.split("KW")[0])
                .map(Double::valueOf)
                .findFirst()
                .orElse(null);
    }

    public static ChargerTypeModelEnum getEnumById(Integer typeId) {
        return Arrays.stream(ChargerTypeModelEnum.values())
                .filter(i -> i.typeId.equals(typeId))
                .findFirst()
                .orElse(null);
    }

}
