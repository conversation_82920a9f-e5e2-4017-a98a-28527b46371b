package com.weihengtech.enums.charger;

import lombok.Getter;

/**
 * 充电桩状态枚举
 *
 * <AUTHOR>
 * @date 2024/2/1 19:06
 * @version 1.0
 */
@Getter
public enum ChargerStatusEnum {
	Available(6, "Available"),
	Preparing(7, "Preparing"),
	SuspendEV(8, "SuspendedEV"),
	Charging(9, "Charging"),
	SuspendEVSE(10, "SuspendedEVSE"),
	Finishing(11, "Finishing"),
	Fault(12, "Faulted");

	final int dbCode;
	final String status;

	ChargerStatusEnum(int dbCode, String status) {
		this.dbCode = dbCode;
		this.status = status;
	}

	public static Integer getCodeByStatus(String status) {
		for (ChargerStatusEnum chargerStatusEnum : ChargerStatusEnum.values()) {
			if (chargerStatusEnum.getStatus().equals(status)) {
				return chargerStatusEnum.getDbCode();
			}
		}
		return null;
	}
}
