package com.weihengtech.enums.other;

public enum DatacenterEnum {
    CN,
    US,
    AU,
    EU;

    public static String getDatacenterHost(DatacenterEnum datacenterEnum, String profile) {
        switch (datacenterEnum) {
            case AU:
                if ("prod".equals(profile)) {
                    return "https://api-ecos-au.weiheng-tech.com";
                } else {
                    return "http://api-ecos-au.dev.weiheng-tech.com";
                }
            case EU:
                if ("prod".equals(profile)) {
                    return "https://api-ecos-eu.weiheng-tech.com";
                } else {
                    return "http://api-ecos-eu.dev.weiheng-tech.com";
                }
            case US:
                if ("prod".equals(profile)) {
                    return "https://api-ecos-us.weiheng-tech.com";
                } else {
                    return "http://api-ecos-us.dev.weiheng-tech.com";
                }
            default:
                if ("prod".equals(profile)) {
                    return "https://api-ecos-cn.weiheng-tech.com";
                } else {
                    return "http://api-ecos-cn.dev.weiheng-tech.com";
                }
        }
    }

    public static Integer getDatacenterId(String datacenter) {
        switch (DatacenterEnum.valueOf(datacenter)) {
            case AU:
                return 183;
            case EU:
                return 171;
            case CN:
                return 226;
            case US:
                return 1;
            default:
                throw new RuntimeException("datacenter error");
        }
    }
}
