package com.weihengtech.enums.tsdb;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/12 17:02
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum TsdbSampleEnum {

	ONE_MINUTE_NEAR_POINT("1m", 1L),

	FIVE_MINUTE_NEAR_POINT( "5m", 5L),

	FIFTEEN_MINUTE_NONE("15m", 15L),

	ONE_HOUR_NONE_POINT("1h", 60L),

	TWO_HOUR_NONE_POINT("2h", 120L);

	final String delta;
	final Long sample;
}
