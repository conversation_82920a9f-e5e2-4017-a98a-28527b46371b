package com.weihengtech.enums.device;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum DeviceTypeNameEnum {

	/**
	 * 设备类型转换
	 */
	Ox11("17.0", "single-phase inverter"),
	<PERSON>x13("19.0", "three-phase inverter"),
	<PERSON>x21("33.0", "single phase storage-HV"),
	<PERSON>x22("34.0", "single phase hybrid-HV"),
	<PERSON><PERSON><PERSON>("35.0", "three phase storage-HV"),
	<PERSON><PERSON><PERSON>("37.0", "single phase hybrid-US/EU"),
	<PERSON>x<PERSON>("49.0", "single phase storage-LV"),
	<PERSON>x33("50.0", "three phase storage-LV");

	String code;
	String name;

	DeviceTypeNameEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}
}
