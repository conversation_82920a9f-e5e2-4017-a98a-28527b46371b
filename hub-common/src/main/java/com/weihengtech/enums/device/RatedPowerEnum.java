package com.weihengtech.enums.device;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 额定功率枚举
 *
 * <AUTHOR>
 * @date 2024/8/26 10:27
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum RatedPowerEnum {
	_362("362", "3600W"),
	_462("462", "4600W"),
	_502("502", "5000W"),
	_602("602", "6000W"),
	_762("762", "7600W"),
	_802("802", "8000W"),
	_962("962", "9600W"),
	_103("103", "10000W"),
	_113("113", "11400W"),
	_123("123", "12000W"),
	_133("133", "13000W")
	;

	final String part;
	final String ratedPower;

	/** 根据设备Sn获取额定功率 */
	public static String getPowerBySn(String deviceSn) {
		String part = deviceSn.substring(3, 6);
		return getPowerByPart(part);
	}

	/** 根据部分字符获取额定功率 */
	public static String getPowerByPart(String part) {
		return Arrays.stream(RatedPowerEnum.values()).filter(i -> part.equals(i.getPart()))
				.map(RatedPowerEnum::getRatedPower)
				.findFirst()
				.orElse(null);
	}

}
