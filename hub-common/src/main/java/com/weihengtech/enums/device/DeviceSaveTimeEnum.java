package com.weihengtech.enums.device;

import lombok.Getter;

/**
 * 安装商保留设备时长枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 18:35
 */
@Getter
public enum DeviceSaveTimeEnum {

    NEVER("不保留", -1),
    DAY_3("3天", 3),
    ALWAYS("永久", 999);



    /**
     * 时长
     */
    private final String time;

    /**
     * 编码
     */
    private final int code;

    DeviceSaveTimeEnum(String time, int code) {
        this.time = time;
        this.code = code;
    }

}
