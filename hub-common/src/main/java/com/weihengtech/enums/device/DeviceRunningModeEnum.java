package com.weihengtech.enums.device;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum DeviceRunningModeEnum {

	/**
	 * 设备运行状态枚举
	 */
	Ox00("0.0", "Waiting for the grid"), Ox01("1.0", "Online"), Ox02("2.0", "Offline"), Ox03(
			"3.0",
			"Fault Error occurred"
	), Ox04("4.0", "RESERVED"), Ox05("5.0", "Checking state");

	String code;
	String name;

	DeviceRunningModeEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}
}
