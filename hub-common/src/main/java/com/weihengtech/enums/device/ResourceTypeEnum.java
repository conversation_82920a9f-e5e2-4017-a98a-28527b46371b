package com.weihengtech.enums.device;

import lombok.Getter;

import java.util.Arrays;

/**
 * 平台资源类型枚举
 *
 * <AUTHOR>
 * @date 2023/11/24 13:10
 * @version 1.0
 */
@Getter
public enum ResourceTypeEnum {
	CHA("CHA", 5, "Agave SH"),
	SHA("SHA", 5, "Agave SH"),
	SSC("SSC", 6, "Agave SH Plus"),
	SHC("SHC", 7, "Copia SH"),
	THA("THA", 8, "Copia TH"),
	SGA("SGA", 5, "Agave SH"),
	SSH("SSH", 5, "Agave SH"),
	TGA("TGA", 8, "Copia TH"),
	SHE("SHE", 5, "Agave SH"),
	SHD("SHD", 9, "Copia SH-US"),
	TIA("TIA", 20, "Agave TH"),
	SIA("SIA", 22, "Agave SH Ultra");

	private final String preSn;
	private final Integer id;
	private final String code;

	ResourceTypeEnum(String preSn, Integer id, String code) {
		this.preSn = preSn;
		this.id = id;
		this.code = code;
	}

	/**
	 * 根据设备SN映射平台的资源类型
	 *
	 * @param deviceSn 设备SN
	 * @return 资源类型
	 */
	public static int getIdByPreSn(String deviceSn) {
		ResourceTypeEnum[] values = ResourceTypeEnum.values();
		return Arrays.stream(values)
				.filter(i -> deviceSn.startsWith(i.preSn))
				.findFirst()
				.map(ResourceTypeEnum::getId)
				// 其他的都归到-------Copia TH
				.orElse(8);
	}

	/**
	 * 判断是否是AgaveSH
	 *
	 * @param deviceSn 设备SN
	 * @return
	 */
	public static Boolean isAgaveSh(String deviceSn) {
		return deviceSn.startsWith(CHA.preSn) ||
				deviceSn.startsWith(SHA.preSn) ||
				deviceSn.startsWith(SGA.preSn) ||
				deviceSn.startsWith(SSH.preSn) ||
				deviceSn.startsWith(SHE.preSn);
	}
}
