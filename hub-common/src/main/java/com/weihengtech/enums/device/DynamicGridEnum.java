package com.weihengtech.enums.device;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 系列枚举
 *
 * <AUTHOR>
 * @date 2023/11/8 19:34
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum DynamicGridEnum {
	SA_POWER_NETWORKS(0, "SA Power Networks", "00062011"),
	JEMENA(1, "Jemena", "00062011"),
	CITIPOWER(2, "CitiPower", "00062011"),
	AUSNET(3, "AusNet", "00062011"),
	SYNERGY(4, "Synergy", "00062011"),
	;

	final Integer code;
	final String name;
	final String lfdiCode;

	public static String getLfdiCodeByCode(Integer code) {
		return Arrays.stream(DynamicGridEnum.values())
				.filter(i -> i.getCode().equals(code))
				.findFirst()
				.map(DynamicGridEnum::getLfdiCode)
				.orElse(null);
	}
}
