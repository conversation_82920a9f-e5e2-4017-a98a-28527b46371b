package com.weihengtech.enums.socket;

import lombok.Getter;

/**
 * 单插状态枚举
 *
 * <AUTHOR>
 * @date 2023/11/24 13:10
 * @version 1.0
 */
@Getter
public enum SocketStatusEnum {
	TURN_ON(true, 13),
	TURN_OFF(false, 14);

	private final Boolean code;
	private final Integer dbCode;

	SocketStatusEnum(Boolean code, Integer dbCode) {
		this.code = code;
		this.dbCode = dbCode;
	}

	public static Integer getIdByCode(Boolean code) {
		for (SocketStatusEnum socketStatusEnum : SocketStatusEnum.values()) {
			if (socketStatusEnum.getCode().equals(code)) {
				return socketStatusEnum.getDbCode();
			}
		}
		return null;
	}
}
