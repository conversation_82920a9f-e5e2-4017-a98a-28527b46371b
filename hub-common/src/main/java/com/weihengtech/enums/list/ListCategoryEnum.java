package com.weihengtech.enums.list;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: jiahao.jin
 * @create: 2025-07-21 16:21
 * @description:
 */
@AllArgsConstructor
@Getter
public enum ListCategoryEnum {

    DEVICE_SN(1, "DEVICE_SN"),
    EMS_VERSION(2, "EMS_VERSION"),
    EMS_SUBVERSION(3, "EMS_SUBVERSION"),
    DSP1_VERSION(4, "DSP1_VERSION"),
    DSP1_SUBVERSION(5, "DSP1_SUBVERSION"),
    DSP2_VERSION(6, "DSP2_VERSION"),
    DSP2_SUBVERSION(7, "DSP2_SUBVERSION"),
    ARCDSP_VERSION(8, "ARCDSP_VERSION"),
    ARCDSP_SUBVERSION(9, "ARCDSP_SUBVERSION"),
    BMS_VERSION(10, "BMS_VERSION"),
    ;

    private final Integer id;
    private final String code;

    public static String getCodeById(Integer id) {
        for (ListCategoryEnum listCategoryEnum : ListCategoryEnum.values()) {
            if (listCategoryEnum.getId().equals(id)) {
                return listCategoryEnum.getCode();
            }
        }
        return null;
    }

}
