package com.weihengtech.consts.parser.graph;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;

/**
 * 故障表通用错误码
 *
 * <AUTHOR>
 * @date 2024/4/30 10:47
 * @version 1.0
 */
public interface ErrorCodeConstants {

	// ---------------------------------------arm bit----------------------------------------------------------------------
	List<String> ARM_BIT_CN_PARSE = Lists.newArrayList("逆变器信息丢失", "逆变器数据丢失", "BMS通信异常",
			"AC电表通信异常", "PV电表通信异常", "输出超限故障", "找不到外部Flash", "找不到EEPROM",
			"主机通信故障", "RTC不工作", "电表类型不匹配", "柴油发电机启动失败",
			"PCU继电器粘连故障",
			"预留", "EPS并机故障", "预留", "预留", "预留",
			"预留", "预留", "预留", "预留", "预留",
			"预留", "预留", "预留", "预留", "预留",
			"预留", "预留", "预留", "预留", "预留"
	);

	List<String> ARM_BIT_EN_PARSE = Lists.newArrayList("Inverter Information Loss",
			"Inverter Data Loss", "BMS Communication Loss",
			"AC Meter Communication Loss",
			"PV Meter Communication Loss",
			"Meter Hard Export Out of Range",
			"Can't Find The Extern Flash",
			"Can't Find The Eeporm",
			"Master Can't Communit With Slave",
			"RTC Don't Work", "ACR Meter Missmatch",
			"Error Of Start Diesel Generator",
			"Error ofPCU Relay Adhesion",
			"Reserve", "EPS Parallel Error", "Reserve", "Reserve", "Reserve",
			"Reserve", "Reserve", "Reserve", "Reserve", "Reserve",
			"Reserve", "Reserve", "Reserve", "Reserve", "Reserve",
			"Reserve", "Reserve", "Reserve", "Reserve", "Reserve"
	);

	// ---------------------------------------bms bit error----------------------------------------------------------------------
	List<String> BATTERY_ERROR_CN_PARSE = Lists.newArrayList("充电时过温故障", "充电时欠温故障或温度采样断开", "放电时过温故障",
			"放电时欠温故障或温度采样断开", "温度过高", "单体硬件过压二级故障/硬件过温故障", "单体硬件过压故障",
			"单体硬件欠压二级故障/硬件欠温故障", "单体硬件欠压故障",
			"硬件充电过流故障", "硬件放电过流故障",
			"硬件总电压过压故障/预留", "硬件总电压欠压故障或熔丝断故障/预留", "电池接线故障", "与主机通信断开",
			"ISO故障", "主控芯片温度高/预留",
			"直流接触器开路", "直流接触器短路",
			"电量计与平衡模块检测电压不匹配或熔丝断故障", "电池SOC低故障/预留", "异常放电故障",
			"系统初始化故障", "455级联通信故障/堆栈故障", "氢气泄漏故障/预留",
			"预留/输出短路故障", "预留/温度接线故障", "预留/累加总压与采样总压不匹配故障", "预留/预充接触器开路",
			"预留/预充接触器短路", "充电中", "放电中"
	);

	List<String> BATTERY_ERROR_EN_PARSE = Lists.newArrayList("Over temperature in charging Fault",
			"under temperature in charging Fault or temperature sensor wire break",
			"Over temperature in discharging Fault",
			"under temperature in discharging Fault or temperature sensor wire break",
			"Over temperature",
			"Cell Hardware Over Voltage LV2/hardware over temperature fault",
			"Cell Hardware Over Voltage",
			"Cell Hardware Under Voltage LV2/hardware under temperature fault",
			"Cell Hardware Under Voltage",
			"Hardware Charge Over Current Fault",
			"Hardware DisCharge Over Current Fault",
			"Hardware Pack Over Voltage Fault/reserve",
			"Hardware Pack Under Voltage Fault or fuse break/reserve",
			"internal Cell open wire",
			"com Error between Pack and host", "ISO Fault",
			"chip Over temperature/reserve",
			"dc break open", "dc break short",
			"pack Voltage mismatch between sensors",
			"SOC_under_threshold_alarm/reserve",
			"DisCharge_at_force_Charge_mode",
			"system init Error", "455 stack fault/stack fault", "H2 fault/reserve",
			"Reserved/output short circuit fault", "Reserved/internal temperature sensor open wire",
			"Reserved/internal volatge mismatch", "Reserved/precharge break open",
			"Reserved/precharge break short", "charging", "discharging"
	);

	// ---------------------------------------bms bit alarm----------------------------------------------------------------------
	List<String> BATTERY_ALARM_CN_PARSE = Lists.newArrayList("单体过压告警", "单体欠压告警", "充电过流告警", "放电过流告警",
			"总电压过压告警", "总电压欠压告警", "充电时过温告警", "充电时欠温告警", "放电时过温告警",
			"放电时欠温告警", "温度过高或温度采样断开",
			"SOC低告警/预留", "flash数据异常告警",
			"预留/单个NTC断线警告", "预留/充电结束", "预留/接近充电结束"
	);

	List<String> BATTERY_ALARM_EN_PARSE = Lists.newArrayList("Cell Over Voltage alarm",
			"Cell Under Voltage alarm", "Cell Over Current alarm",
			"DisCharge Over Current alarm",
			"pack Over Voltage alarm", "pack Under Voltage alarm",
			"Over temperature in charging alarm",
			"under temperature in charging alarm",
			"Over temperature in discharging alarm",
			"under temperature in discharging alarm",
			"Over temperature or temperature sensor wire break",
			"SOC_under_threshold_alarm/reserve",
			"flash_userdata_invalid_alarm", "Reserved/single ntc openwire alarm",
			"Reserved/end of charge", "Reserved/near end of charge"
	);

	// ---------------------------------------bms bit fault----------------------------------------------------------------------
	List<String> BATTERY_FAULT_CN_PARSE = Lists.newArrayList(
			"电量计通信故障/加热继电器粘连",
			"电量计过压/采样总压过压",
			"电量计欠压/采样总压欠压",
			"电量计充电过流/采样电流充电过流",
			"电量计放电过流/采样电流放电过流",
			"电量计硬件过压/电流采样异常",
			"电量计硬件欠压/温差故障",
			"平衡模块SYS故障",
			"平衡模块DEV故障",
			"平衡模块通信故障",
			"单体过压故障",
			"单体欠压故障",
			"充电过流故障",
			"放电过流故障",
			"总电压过压故障",
			"总电压欠压故障"
	);

	List<String> BATTERY_FAULT_EN_PARSE = Lists.newArrayList(
			"Gauge com Error/heater relay short",
			"Gauge Over Voltage/sample over voltage",
			"Gauge Under Voltage/sample under voltage",
			"Gauge Charge Over Current/sample over charge current",
			"Gauge DisCharge Over Current/sample over discharge current",
			"Gauge Hardware Over Voltage/sample current mismatch",
			"Gauge Hardware Under Voltage/temperature difference error",
			"Balance Module SYS Error",
			"Balance Module DEV Error", "Balance Module com Error",
			"Cell Over Voltage Fault",
			"Cell Under Voltage Fault", "Cell Over Current Fault",
			"DisCharge Over Current Fault",
			"pack Over Voltage Fault", "pack Under Voltage Fault"
	);

	// ---------------------------------------dsp decimal----------------------------------------------------------------------
	Map<String, String> DSP_DECIMAL_CN = MapUtil.<String, String>builder()
			.put("1001", "逆变器上线")
			.put("1002", "逆变器离线")
			.put("0", "从错误中恢复")
			.put("1", "PV1过压")
			.put("2", "PV1过流")
			.put("3", "PV2过压")
			.put("4", "PV2过流")
			.put("5", "Reserve")
			.put("6", "Reserve")
			.put("7", "Reserve")
			.put("8", "Reserve")
			.put("9", "BUS过压故障")
			.put("10", "无市电")
			.put("11", "电网电压故障")
			.put("12", "电网电流故障")
			.put("13", "电网频率故障")
			.put("14", "逆变器温度故障")
			.put("15", "DCI过流")
			.put("16", "漏电流故障")
			.put("17", "绝缘阻抗过低")
			.put("18", "Reserve")
			.put("19", "Reserve")
			.put("20", "Reserve")
			.put("21", "Reserve")
			.put("22", "Reserve")
			.put("23", "Reserve")
			.put("24", "Reserve")
			.put("25", "Reserve")
			.put("26", "电池电压故障")
			.put("27", "电池电流故障")
			.put("28", "电池温度故障")
			.put("29", "电池Boost电路故障")
			.put("30", "电池继电器故障")
			.put("31", "电池Buck电路故障")
			.put("32", "Reserve")
			.put("33", "Reserve")
			.put("34", "Reserve")
			.put("35", "Reserve")
			.put("36", "Reserve")
			.put("37", "负载电压故障")
			.put("38", "负载电流故障")
			.put("39", "过载故障")
			.put("40", "负载输出短路故障")
			.put("41", "N-PE之间电压高")
			.put("42", "Reserve")
			.put("43", "LVRT")
			.put("44", "风扇故障")
			.put("45", "烟感告警")
			.put("46", "降额运行告警")
			.put("47", "Reserve")
			.put("48", "Relay故障")
			.put("49", "Relay故障")
			.put("50", "Relay故障")
			.put("51", "存储器故障")
			.put("52", "通信故障")
			.put("53", "孤岛")
			.put("54", "环境温度低")
			.put("55", "软件版本主副不一致")
			.put("56", "Reserve")
			.put("57", "Reserve")
			.put("58", "Reserve")
			.put("59", "Reserve")
			.put("60", "Reserve")
			.build();
	Map<String, String> DSP_DECIMAL_EN = MapUtil.<String, String>builder()
			.put("1001", "Inverter Online")
			.put("1002", "Inverter Offline")
			.put("0", "Recovery from error")
			.put("1", "PV1 Voltage Fault")
			.put("2", "PV1 Current Fault")
			.put("3", "PV2 Voltage Fault")
			.put("4", "PV2 Current Fault")
			.put("5", "Reserve")
			.put("6", "Reserve")
			.put("7", "Reserve")
			.put("8", "Reserve")
			.put("9", "Bus Voltage Overtrip Fault")
			.put("10", "No Utility Fault")
			.put("11", "Grid Voltage Fault")
			.put("12", "Grid Current Overtrip Fault")
			.put("13", "Grid Frequency Fault")
			.put("14", "Inverter Temperature Fault")
			.put("15", "DC Injection Current Overtrip")
			.put("16", "Leakage Current Overtrip")
			.put("17", "Insulation Resistance Low")
			.put("18", "Reserve")
			.put("19", "Reserve")
			.put("20", "Reserve")
			.put("21", "Reserve")
			.put("22", "Reserve")
			.put("23", "Reserve")
			.put("24", "Reserve")
			.put("25", "Reserve")
			.put("26", "Battery Voltage Fault")
			.put("27", "Battery Current Overtrip Fault")
			.put("28", "Battery Temperature Fault")
			.put("29", "Battery Boost Circuit Fault")
			.put("30", "Battery Relay Fault")
			.put("31", "Battery Buck Circuit Fault")
			.put("32", "Reserve")
			.put("33", "Reserve")
			.put("34", "Reserve")
			.put("35", "Reserve")
			.put("36", "Reserve")
			.put("37", "Load Voltage Overtrip Fault")
			.put("38", "Load Current Overtrip Fault")
			.put("39", "Overload Fault")
			.put("40", "Output Short Circuit Fault")
			.put("41", "The Voltage from Grid Line N to PE is High")
			.put("42", "Reserve")
			.put("43", "LVRT_Fault_Warning")
			.put("44", "Fan Warning")
			.put("45", "Smoke Detect Warning")
			.put("46", "Power Derate Warning")
			.put("47", "Reserve")
			.put("48", "Grid-Tied Relay Fault")
			.put("49", "EPS Relay Fault")
			.put("50", "Bypass Relay Fault")
			.put("51", "Storage IC Fault")
			.put("52", "Communication Fault")
			.put("53", "Island Fault")
			.put("54", "Ambient Temperature Low Fault")
			.put("55", "Software Version Inconsistent Fault")
			.put("56", "Reserve")
			.put("57", "Reserve")
			.put("58", "Reserve")
			.put("59", "Reserve")
			.put("60", "Reserve")
			.build();

	// ---------------------------------------bms decimal----------------------------------------------------------------------
	Map<String, String> BMS_DECIMAL_CN = MapUtil.<String, String>builder()
			.put("1", "单体低压告警")
			.put("2", "单体高压告警")
			.put("3", "总压低压告警")
			.put("4", "总压高压告警")
			.put("5", "充电低温告警")
			.put("6", "充电高温告警")
			.put("7", "放电低温告警")
			.put("8", "放电高温告警")
			.put("9", "充电过流告警")
			.put("10", "放电过流告警")
			.put("11", "BMS温度高告警")
			.put("12", "模块温度高告警")
			.put("13", "模块低压告警")
			.put("14", "模块高压告警")
			.put("15", "SOC低告警")
			.put("16", "flash数据异常告警")
			.put("17", "Reserve")
			.put("18", "Reserve")
			.put("19", "Reserve")
			.put("20", "Reserve")
			.put("21", "Reserve")
			.put("22", "Reserve")
			.put("23", "Reserve")
			.put("24", "Reserve")
			.put("25", "单体低压保护")
			.put("26", "单体高压保护")
			.put("27", "总压低压保护")
			.put("28", "总压高压保护")
			.put("29", "充电低温保护")
			.put("30", "充电高温保护")
			.put("31", "放电低温保护")
			.put("32", "放电高温保护")
			.put("33", "充电过流保护")
			.put("34", "放电过流保护")
			.put("35", "短路保护")
			.put("36", "温度高保护")
			.put("37", "模块低压保护")
			.put("38", "模块高压保护")
			.put("39", "电压传感器故障")
			.put("40", "温度采样异常")
			.put("41", "内部通信故障")
			.put("42", "输入过压故障")
			.put("43", "输入反接故障")
			.put("44", "继电器故障")
			.put("45", "电池损坏故障")
			.put("46", "关机电路异常")
			.put("47", "BMIC 异常")
			.put("48", "内部总线异常")
			.put("49", "初始化自检异常")
			.put("50", "电量计过压")
			.put("51", "电量计欠压")
			.put("52", "电量计充电过流")
			.put("53", "电量计放电过流")
			.put("54", "电量计硬件过压")
			.put("55", "电量计硬件欠压")
			.put("56", "平衡模块SYS故障")
			.put("57", "平衡模块DEV故障")
			.put("58", "单体硬件过压故障")
			.put("59", "单体硬件欠压故障")
			.put("60", "硬件充电过流故障")
			.put("61", "硬件放电过流故障")
			.put("62", "硬件总电压过压故障")
			.put("63", "硬件总电压欠压故障或熔丝断故障")
			.put("64", "电池接线故障")
			.put("65", "与主机通信断开")
			.put("66", "ISO故障")
			.put("67", "直流接触器开路")
			.put("68", "直流接触器短路")
			.put("69", "电量计与平衡模块检测电压不匹配或熔丝断故障")
			.put("70", "电池SOC低故障")
			.put("71", "异常放电故障")
			.put("72", "其他故障")
			.put("73", "455级联通信故障")
			.put("74", "氢气泄漏故障")
			.build();
	Map<String, String> BMS_DECIMAL_EN = MapUtil.<String, String>builder()
			.put("1", "Battery Cell Low Voltage Alarm")
			.put("2", "Battery Cell High Voltage Alarm")
			.put("3", "Pile Low Voltage Alarm")
			.put("4", "Pile High Voltage Alarm")
			.put("5", "Charge Low Temperature Alarm")
			.put("6", "Charge High Temperature Alarm")
			.put("7", "DisCharge Low Temperature Alarm")
			.put("8", "DisCharge High Temperature Alarm")
			.put("9", "Charge Over Current Alarm")
			.put("10", "DisCharge Over Current Alarm")
			.put("11", "BMS High Temperature Alarm")
			.put("12", "Module High Temperature Alarm")
			.put("13", "Module Low Voltage Alarm")
			.put("14", "Module High Voltage Alarm")
			.put("15", "SOC_under_threshold_alarm")
			.put("16", "flash_userdata_invalid_alarm")
			.put("17", "Reserve")
			.put("18", "Reserve")
			.put("19", "Reserve")
			.put("20", "Reserve")
			.put("21", "Reserve")
			.put("22", "Reserve")
			.put("23", "Reserve")
			.put("24", "Reserve")
			.put("25", "Battery Cell Under Voltage Protection")
			.put("26", "Battery Cell Over Voltage Protection")
			.put("27", "Pile Under Voltage Protection")
			.put("28", "Pile Over Voltage Protection")
			.put("29", "Charge Under Temperature Protection")
			.put("30", "Charge Over Temperature Protection")
			.put("31", "DisCharge Under Temperature Protection")
			.put("32", "DisCharge Over Temperature Protection")
			.put("33", "Charge Over Current Protection")
			.put("34", "DisCharge Over Current Protection")
			.put("35", "Short Circuit Protection")
			.put("36", "Over Temperature Protection")
			.put("37", "Module Under Voltage Protection")
			.put("38", "Module Over Voltage Protection")
			.put("39", "Voltage Sensor Error")
			.put("40", "Temperature Sample Error")
			.put("41", "Internal Communication Error")
			.put("42", "Input Over Voltage Error")
			.put("43", "Input Transposition Error")
			.put("44", "Relay Error")
			.put("45", "Battery Cell Error")
			.put("46", "Shutdown Circuit Error")
			.put("47", "BMIC Error")
			.put("48", "Internal Bus Error")
			.put("49", "System Init Test Error")
			.put("50", "Gauge Over Voltage")
			.put("51", "Gauge Under Voltage")
			.put("52", "Gauge Charge Over Current")
			.put("53", "Gauge DisCharge Over Current")
			.put("54", "Gauge Hardware Over Voltage")
			.put("55", "Gauge Hardware Under Voltage")
			.put("56", "Balance Module SYS Error")
			.put("57", "Balance Module DEV Error")
			.put("58", "Cell Hardware Over Voltage")
			.put("59", "Cell Hardware Under Voltage")
			.put("60", "Hardware Charge Over Current Fault")
			.put("61", "Hardware DisCharge Over Current Fault")
			.put("62", "Hardware Pack Over Voltage Fault")
			.put("63", "Hardware Pack Under Voltage Fault or Fuse Break")
			.put("64", "Internal Cell Open Wire")
			.put("65", "Com Error between Pack and host")
			.put("66", "ISO Fault")
			.put("67", "DC Break Open")
			.put("68", "DC Break Short")
			.put("69", "Pack Voltage Mismatch between Sensors")
			.put("70", "SOC Under Threshold Alarm")
			.put("71", "DisCharge at Force Charge mode Fault")
			.put("72", "Other Error")
			.put("73", "455 stack fault")
			.put("74", "H2 fault")
			.build();

	// ---------------------------------------weiheng battery---------------------------------------------------------------
	List<String> WEIHENG_BATTERY_EN_PARSE = Lists.newArrayList("cell over voltage alarm",
			"cell under voltage alarm", "cell over current alarm",
			"discharge over current alarm",
			"pack over voltage alarm", "pack under voltage alarm",
			"over temperature in charging alarm",
			"under temperature in charging alarm",
			"over temperature in discharging alarm",
			"under temperature in discharging alarm",
			"over temperature",
			"pack SOC under threshold",
			"FLASH data reading invalid",
			"bq76pl455 state transition failed due to communication failure",
			"end of charge", "near end of charge",
			"gauge com error", "gauge over voltage",
			"gauge under voltage",
			"gauge charge over current",
			"gauge discharge over current",
			"gauge hardware over voltage",
			"gauge hardware under voltage",
			"balance module SYS error",
			"balance module DEV error",
			"balance module com error", "cell over voltage fault",
			"cell under voltage fault",
			"cell over current fault",
			"discharge over current fault",
			"pack over voltage fault",
			"pack under voltage fault",
			"over temperature in charging fault",
			"under temperature in charging fault or temperature sensor wire break",
			"over temperature in discharging fault",
			"under temperature in discharging fault or temperature sensor wire break",
			"over temperature",
			"cell hardware over voltage LV2",
			"cell hardware over voltage",
			"cell hardware under voltage LV2",
			"cell hardware under voltage",
			"hardware charge over current fault",
			"hardware discharge over current fault",
			"hardware pack over voltage fault",
			"hardware pack under voltage fault or fuse break",
			"internal cell open wire",
			"com error between pack and host", "ISO fault",
			"chip over temperature",
			"dc break open", "dc break short",
			"pack voltage mismatch between sensors",
			"pack SOC stays under threshold for a period",
			"battery discharge under force charge mode",
			"system init error",
			"battery module stacking fault",
			"hydrogen detected",
			"fault from BQ76PL455 or fault IO of BQ76PL455 disconnected",
			"internal cell sensor open wire",
			"internal gauge voltage sensor fault",
			"reverse", "reverse", "charging",
			"discharging", "reverse", "reverse", "reverse",
			"reverse", "reverse",
			"reverse", "reverse", "reverse",
			"reverse", "reverse", "reverse", "reverse", "reverse",
			"reverse", "reverse",
			"reverse", "reverse",
			"reverse", "reverse", "reverse", "reverse", "reverse",
			"reverse", "reverse",
			"reverse", "reverse",
			"reverse", "reverse", "reverse", "reverse", "reverse",
			"reverse", "reverse",
			"reverse", "reverse",
			"reverse", "reverse", "reverse", "reverse", "reverse",
			"reverse", "reverse",
			"reverse", "reverse",
			"reverse", "reverse", "reverse", "reverse", "reverse",
			"reverse", "reverse",
			"reverse", "reverse",
			"reverse", "reverse", "reverse", "reverse", "reverse",
			"reverse", "reverse",
			"reverse", "reverse",
			"reverse", "reverse"
	);

	List<String> WEIHENG_BATTERY_CN_PARSE = Lists.newArrayList("单体过压告警", "单体欠压告警", "充电过流告警",
			"放电过流告警", "总电压过压告警", "总电压欠压告警", "充电时过温告警", "充电时欠温告警",
			"放电时过温告警", "放电时欠温告警",
			"温度过高", "SOC低于阈值告警",
			"FLASH数据读取无效", "455芯片状态转移无效", "充电结束", "接近充电结束",
			"电量计通信故障", "电量计过压", "电量计欠压",
			"电量计充电过流", "电量计放电过流",
			"电量计硬件过压", "电量计硬件欠压", "平衡模块SYS故障", "平衡模块DEV故障",
			"平衡模块通信故障", "单体过压故障", "单体欠压故障",
			"充电过流故障", "放电过流故障",
			"总电压过压故障", "总电压欠压故障", "充电时过温故障", "充电时欠温故障或温度采样断开",
			"放电时过温故障", "放电时欠温故障或温度采样断开",
			"温度过高", "单体硬件过压二级故障",
			"单体硬件过压故障", "单位硬件欠压二级故障", "单体硬件欠压故障", "硬件充电过流故障",
			"硬件放电过流故障", "硬件总电压过压故障",
			"硬件总电压欠压故障或熔丝断故障", "电池接线故障",
			"与主机通信断开", "ISO故障", "主控芯片温度过高", "直流接触器开路", "直流接触器短路",
			"电量计与平衡模块检测电压不匹配或熔丝断故障或模组间bq76pl455存在通信故障",
			"SOC持续低于阈值故障", "强充模式下放电报故障", "系统初始化故障",
			"叠放故障-可能是455电路板故障导致通信质量较差导致", "氢气释放故障",
			"BQ76PL455芯片报错或者硬件故障IO连线断开",
			"温度采样接线故障", "Z100电压通道故障", "预留", "预留", "充电状态", "放电状态",
			"预留", "预留", "预留", "预留",
			"预留", "预留", "预留", "预留", "预留",
			"预留", "预留", "预留", "预留", "预留", "预留", "预留", "预留", "预留",
			"预留", "预留", "预留", "预留",
			"预留", "预留", "预留", "预留", "预留",
			"预留", "预留", "预留", "预留", "预留", "预留", "预留", "预留", "预留",
			"预留", "预留", "预留", "预留",
			"预留", "预留", "预留", "预留", "预留",
			"预留", "预留", "预留", "预留", "预留", "预留", "预留", "预留", "预留",
			"预留", "预留", "预留", "预留",
			"预留", "预留", "预留", "预留", "预留",
			"预留"
	);
}
