package com.weihengtech.consts.parser.graph;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 17:27
 */
public interface CatlErrorConstants extends ErrorCodeConstants {

    // ---------------------------------------bms bit error-------------------------------------------------------------
    List<String> CATL_BATTERY_ERROR_CN_PARSE = Lists.newArrayList("单体过压1级",
            "单体过压2级", "单体过压3级", "单体欠压1级", "单体欠压2级", "单体欠压3级", "单体极限过压", "单体极限欠压", "单柜电压过高",
            "单柜电压过高", "单柜电压过低", "单柜电压过低", "电芯压差过大", "电芯电压无效", "单体欠压报换故障", "单体过压报换故障",
            "低压防过放预警", "低压防过放极限预警", "放电电流过流1级", "放电电流过流2级", "放电电流过流3级", "放电电流过流4级",
            "充电电流过流1级", "充电电流过流2级", "充电电流过流3级", "充电电流过流4级", "电流传感器故障", "预留", "预留", "预留",
            "电芯温度过高1级", "电芯温度过高2级"
    );

    List<String> CATL_BATTERY_ERROR_EN_PARSE = Lists.newArrayList(
            "Single cell over-voltage warning level 1",
            "Single cell over-voltage warning level 2",
            "Single cell over-voltage warning level 3",
            "Single cell under-voltage warning level 1",
            "Single cell under-voltage warning level 2",
            "Single cell under-voltage warning level 3",
            "Single cell extreme over-voltage warning",
            "Single cell extreme under-voltage warning",
            "Single rack over-voltage warning level 1",
            "Single rack over-voltage warning level 2",
            "Single rack under-voltage warning level 1",
            "Single rack under-voltage warning level 2",
            "Big voltage difference between cells warning level1",
            "Invalid cell voltage warning",
            "Replacement required due to single cell under-voltage warning",
            "Replacement required due to single cell over-voltage warning",
            "LowSOC_OverDischrg_Warning",
            "LowSOC_OverDischrg_Fault",
            "Discharge over-current warning level 1",
            "Discharge over-current warning level 2",
            "Discharge over-current warning level 3",
            "Discharge over-current warning level 4",
            "Charge over-current warning level 1",
            "Charge over-current warning level 2",
            "Charge over-current warning level 3",
            "Charge over-current warning level 4",
            "Current sensor failed warning",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Single cell over-temperature warning level 1",
            "Single cell over-temperature warning level 2");
    
    // ---------------------------------------bms bit error2-------------------------------------------------------------
    List<String> CATL_BATTERY_ERROR_2_CN_PARSE = Lists.newArrayList("电芯温度过高3级", "电芯温度过高4级", "电芯温度过低1级", "电芯温度过低2级",
            "电芯温度过低3级", "电芯温差过大1级", "电芯温差过大2级", "电芯温差过大3级",
            "SBMU主控箱箱体过温1级", "SBMU主控箱箱体过温2级", "SBMU主控箱箱体过温3级", "SBMU主控箱箱体过温4级", "SBMU主控箱箱体低温1级",
            "SBMU主控箱箱体低温2级", "SBMU主控箱箱体低温3级", "单体温度采样故障（单点）", "单体温度采样故障（多点）", "预留", "预留", "预留",
            "预留", "主正继电器驱动短电源故障", "主正继电器驱动短地故障", "主负继电器驱动短电源故障", "主负继电器驱动短地故障", "预充继电器驱动短电源故障",
            "预充继电器驱动短地故障", "CSC供电驱动短电源故障", "CSC供电驱动短地故障", "CSC编码驱动短电源故障", "预留", "预留");

    List<String> CATL_BATTERY_ERROR_2_EN_PARSE = Lists.newArrayList("Single cell over-temperature warning level 3",
            "Single cell over-temperature warning level 4",
            "Single cell under-temperature warning level 1",
            "Single cell under-temperature warning level 2",
            "Single cell under-temperature warning level 3",
            "Big temperature difference between cells warning level 1",
            "Big temperature difference between cells warning level 2",
            "Big temperature difference between cells warning level 3",
            "SBMU main control box over-temperature warning level 1",
            "SBMU main control box over-temperature warning level 2",
            "SBMU main control box over-temperature warning level 3",
            "SBMU main control box over-temperature warning level 4",
            "SBMU main control box under-temperature warning level 1",
            "SBMU main control box under-temperature warning level 2",
            "SBMU main control box under-temperature warning level 3",
            "Single temperature sampling abnormal warning",
            "Multiple temperature sampling abnormal warning",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Main positive relay driver short to power supply warning",
            "Main positive relay driver short to ground warning",
            "Main negative relay driver short to power supply warning",
            "Main negative relay driver short to ground warning",
            "Pre-charge relay driver short to power supply warning",
            "Pre-charge relay driver short to ground warning",
            "CSC power supply driver short to power supply warning",
            "CSC power supply driver short to ground warning",
            "CSC coding driver short to power supply warning",
            "Platform Reserved signal","Platform Reserved signal");

    // ---------------------------------------bms bit error3-------------------------------------------------------------
    List<String> CATL_BATTERY_ERROR_3_CN_PARSE = Lists.newArrayList("预留",
            "预留", "预留", "预留", "预留", "预留", "预留", "电流传感器使能前供电异常故障", "电流传感器使能后供电异常故障",
            "CSC 24V供电异常故障", "SBMU 24V供电异常故障", "SBMU 24V供电异常告警", "预留", "预留", "预留", "电柜FUSE故障",
            "电柜隔离开关故障", "主正继电器粘连故障", "主负继电器粘连故障", "主正和主负继电器同时粘连", "主正继电器开路故障", "主负继电器开路故障",
            "电柜门禁（行程开关）故障", "主控箱风机故障", "预留", "电柜消防一级故障（气感）", "电柜消防一级故障（烟感）", "电柜消防二级故障（温感）",
            "预留", "预留", "气溶胶触发告警", "预留");

    List<String> CATL_BATTERY_ERROR_3_EN_PARSE = Lists.newArrayList("Platform Reserved signal",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Abnormal power supply warning before current sensor is enabled",
            "Abnormal power supply warning after current sensor is enabled",
            "CSC 24V power supply abnormal fault",
            "SBMU 24V power supply abnormal fault",
            "SBMU 24V power supply abnormal warning",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Rack fuse  fault",
            "Rack isolate switch  fault",
            "Main positive relay sticking  fault",
            "Main negative relay sticking  fault",
            "Both main positive relay and main negative relay sticking fault",
            "Main positive relay open circuit  fault",
            "Main negative open circuit  fault",
            "Battery rack door (travel switch) fault",
            "Main control box fan  fault",
            "Platform Reserved signal ",
            "Rack fire system level 1 fault ( gas sensor)",
            "Rack fire system level 1 fault ( smoke sensor)",
            "Rack fire system level 2 fault ( temperature sensor)",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Aerosol eruption state warning",
            "Platform Reserved signal");

    // ---------------------------------------bms bit error4-------------------------------------------------------------
    List<String> CATL_BATTERY_ERROR_4_CN_PARSE = Lists.newArrayList("预留", "电柜消防二级故障（气感&烟感）", "预留", "预留", "预留", "预留", "CCAN通讯故障",
            "电流报文丢失故障", "MCAN MBMU通信故障", "预留", "CSC菊花链通讯故障", "预留", "均衡回路故障", "预留", "SOC过低1级", "SOC过低2级",
            "高压回路断路故障", "预留", "预留", "预留", "预充失败2次", "预充过流", "电芯电压采样线掉线", "CSC采样回路漏电流", "CSC NTC开路告警",
            "CSC NTC短路告警", "预留", "预留", "预留", "预留", "CSC的MCU1级故障", "CSC的MCU2级故障", "预留");

    List<String> CATL_BATTERY_ERROR_4_EN_PARSE = Lists.newArrayList("Rack fire system level 2 fault ( gas & smoke sensors)",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Inner communication warning (CCAN)",
            "Inner communication warning (SCAN)",
            "Inner communication warning (MCAN)",
            "Platform Reserved signal ",
            "CSC daisy chain communication failure",
            "Platform Reserved signal",
            "Balancing circuit fault",
            "Platform Reserved signal",
            "SOC low warning level 1",
            "SOC low warning level 2",
            "HV circuit open circuit fault",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Pre-charging failed twice fault",
            "Pre-charge overcurrent fault",
            "Cell voltage sampling wire disconnected fault",
            "Sampling loop leakage current fault",
            "CSC NTC open circuit warning",
            "CSC NTC short circuit warning",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "Platform Reserved signal",
            "CSC MCU level 1 fault",
            "CSC MCU level 2 fault",
            "Platform Reserved signal");

    // ---------------------------------------bms bit error5-------------------------------------------------------------
    List<String> CATL_BATTERY_ERROR_5_CN_PARSE = Lists.newArrayList("热失控火灾故障",
            "主控箱 NTC 开路告警", "主控箱 NTC 短路告警", "预留", "CSC个数配置错误故障", "高压回路（Fuse）断路故障", "电池模块电压过高1级",
            "电池模块电压过高2级", "电池模块电压过高4级", "电池模块电压过低1级", "电池模块电压过低2级", "电池模块电压过低4级", "电池簇充电电压过压",
            "电池簇放电电压欠压", "电芯压差过大2级", "电芯压差过大4级", "电池模块压差过大1级", "电池模块压差过大2级", "电池模块压差过大4级",
            "电池单体温升速率越限故障", "电池单体温升速率越限故障", "电池单体温升速率越限故障", "SOH过低告警", "SOH过低故障", "预留");

    List<String> CATL_BATTERY_ERROR_5_EN_PARSE = Lists.newArrayList("Thermal runaway caused fire fault",
            "SBMU NTC open circuit warning",
            "SBMU NTC short circuit warning",
            "Platform Reserved signal",
            "CSC Number Config Error",
            "HV circuit（Fuse） open circuit fault",
            "Bat module  over-voltage warning level 1",
            "Bat modulel  over-voltage warning level 2",
            "Bat module  over-voltage warning level 3",
            "Bat module  under-voltage warning level 1",
            "Bat module  under-voltage warning level 2",
            "Bat module under-voltage warning level 3",
            "Bat cluster  over-voltage warning",
            "Bat cluster under-voltage warning",
            "Big voltage difference between cells warning level2",
            "Big voltage difference between cells warning level4",
            "Big voltage difference between modules warning level1",
            "Big voltage difference between modules warning level2",
            "Big voltage difference between modules warning level4",
            "Cell temperature rise exceeding limit level1",
            "Cell temperature rise exceeding limit level2",
            "Cell temperature rise exceeding limit level4",
            "PackSOHLowWarning",
            "PackSOHLowFault",
            "Platform Reserved signal");
}
