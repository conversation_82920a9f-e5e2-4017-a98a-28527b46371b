package com.weihengtech.consts.parser.graph;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 故障表-北美机
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 17:27
 */
public interface UsErrorConstants extends ErrorCodeConstants {

    // ---------------------------------------dsp1----------------------------------------------------------------------
    List<String> DSP_1_US_CN_BIT_PARSE = Lists.newArrayList("机型错误",
            "CPU1版本不匹配",
            "CPU2版本不匹配",
            "ARM版本不匹配",
            "远程关机",
            "电网不匹配",
            "接地故障",
            "霍尔传感器故障",
            "ISO故障",
            "GFCI故障",
            "AFCI故障",
            "外部flash安规故障",
            "外部flash警告",
            "ARM通讯故障",
            "副DSP通信故障",
            "EPRM读写失败",
            "CAN失败",
            "INV过温",
            "BuckBoost过温",
            "Boost过温",
            "低温告警",
            "过频降载",
            "过温降载",
            "PV高于BUS故障",
            "电网软起BUS失败",
            "PV软起BUS失败",
            "电池软起BUS失败",
            "预留",
            "预留",
            "预留",
            "预留",
            "警告中恢复");

    List<String> DSP_1_US_EN_BIT_PARSE = Lists.newArrayList("ALM_ID_PARA_CFG_ERR",
            "ALM_ID_CPU1_VERN_UNMATCH",
            "ALM_ID_CPU2_VERN_UNMATCH",
            "ALM_ID_ARM_VERN_UNMATCH",
            "ALM_ID_REMOTE_TURN_OFF",
            "ALM_ID_GRID_UNMATCH",
            "ALM_ID_GND_CONNET_FLT",
            "ALM_ID_HCT_FLT",
            "ALM_ID_ISO_FAIL",
            "ALM_ID_GFCI_DEV_FAIL",
            "ALM_ID_AFCI_FAIL",
            "ALM_ID_EXFLASH_SAFETY_FLT",
            "ALM_ID_EXFLASH_WARN",
            "ALM_ID_ARM_COM_FLT",
            "ALM_ID_SLAVE_COM_FLT",
            "ALM_ID_EPRM_RW_FAIL",
            "ALM_ID_CAN_FAIL",
            "ALM_ID_INV_HTSK_OTP",
            "ALM_ID_BUCKBST_HTSK_OTP",
            "ALM_ID_BST_HTSK_OTP",
            "ALM_ID_HTSK_UTP",
            "ALM_ID_PWRLMT_OFP",
            "ALM_ID_PWRLMT_OTP",
            "ALM_ID_PV_ABOVE_BUS",
            "ALM_ID_DCBUS_GRID_SS_FAIL",
            "ALM_ID_DCBUS_PV_SS_FAIL",
            "ALM_ID_DCBUS_BATT_SS_FAIL",
            "RESERVE",
            "RESERVE",
            "RESERVE",
            "RESERVE",
            "ALM_ID_REC_FROM_WARN");

    // ---------------------------------------dsp2----------------------------------------------------------------------
    List<String> DSP_2_US_CN_BIT_PARSE = Lists.newArrayList("PV欠压",
            "太阳光弱",
            "PV1反接",
            "PV2反接",
            "PV3反接",
            "PV4反接",
            "PV1过压",
            "PV2过压",
            "PV3过压",
            "PV4过压",
            "Boost1硬件过流",
            "Boost2硬件过流",
            "Boost3硬件过流",
            "Boost4硬件过流",
            "Boost1软件过流",
            "Boost2软件过流",
            "Boost3软件过流",
            "Boost4软件过流",
            "预充Relay故障",
            "电池反接",
            "电池过压",
            "电池欠压",
            "BuckBoost硬件过流",
            "充电电流高",
            "放电电流高",
            "DcBus硬件过压",
            "DcBus软件过压",
            "DcBus软件欠压",
            "DcBus不平衡",
            "DcBus平衡过流",
            "预留",
            "预留");

    List<String> DSP_2_US_EN_BIT_PARSE = Lists.newArrayList("ALM_ID_PV_UVP",
            "ALM_ID_SUNPWR_WEAK",
            "ALM_ID_PV01_REVERSE",
            "ALM_ID_PV02_REVERSE",
            "ALM_ID_PV03_REVERSE",
            "ALM_ID_PV04_REVERSE",
            "ALM_ID_PV01_OVP",
            "ALM_ID_PV02_OVP",
            "ALM_ID_PV03_OVP",
            "ALM_ID_PV04_OVP",
            "ALM_ID_BST1_HW_OCP",
            "ALM_ID_BST2_HW_OCP",
            "ALM_ID_BST3_HW_OCP",
            "ALM_ID_BST4_HW_OCP",
            "ALM_ID_BST1_SW_OCP",
            "ALM_ID_BST2_SW_OCP",
            "ALM_ID_BST3_SW_OCP",
            "ALM_ID_BST4_SW_OCP",
            "ALM_ID_PRECHG_RLY_FLT",
            "ALM_ID_BATT_REVERSE",
            "ALM_ID_BATT_OVP",
            "ALM_ID_BATT_UVP",
            "ALM_ID_BUCKBST_HW_OCP",
            "ALM_ID_BUCKBST_CHG_SW_OCP",
            "ALM_ID_BUCKBST_DSCHG_SW_OCP",
            "ALM_ID_DCBUS_HW_OVP",
            "ALM_ID_DCBUS_SW_OVP",
            "ALM_ID_DCBUS_SW_UVP",
            "ALM_ID_DCBUS_UNBALAN",
            "ALM_ID_DCBUS_UNBALAN_OCP",
            "RESERVE",
            "RESERVE");

    // ---------------------------------------dsp3----------------------------------------------------------------------
    List<String> DSP_3_US_CN_BIT_PARSE = Lists.newArrayList("电网丢失",
            "电网一级过压",
            "电网二级过压",
            "电网一级欠压",
            "电网二级欠压",
            "电网1.1倍过压",
            "电网十分钟过压",
            "电网瞬间过压",
            "电网一级过频",
            "电网二级过频",
            "电网一级欠频",
            "电网二级欠频",
            "电网穿越故障",
            "电网RLY检测失败",
            "电网RLYON失败",
            "电网RLY强制吸合失败",
            "EPS RLY失败",
            "逆变软件过流",
            "电网电流过流",
            "逆变硬件逐波限流",
            "逆变DCI保护",
            "逆变短路",
            "GFCI保护",
            "EPS过压",
            "EPS过载",
            "EPS二倍过载",
            "EPS DCV保护",
            "孤岛",
            "预留",
            "预留",
            "预留",
            "预留");

    List<String> DSP_3_US_EN_BIT_PARSE = Lists.newArrayList("ALM_ID_GRID_LOSS",
            "ALM_ID_GRID_OVP1",
            "ALM_ID_GRID_OVP2",
            "ALM_ID_GRID_UVP1",
            "ALM_ID_GRID_UVP2",
            "ALM_ID_GRID_OVP_110",
            "ALM_ID_GRID_10MIN_OVP",
            "ALM_ID_GRID_INST_OVP",
            "ALM_ID_GRID_OFP1",
            "ALM_ID_GRID_OFP2",
            "ALM_ID_GRID_UFP1",
            "ALM_ID_GRID_UFP2",
            "ALM_ID_GRID_FRT",
            "ALM_ID_GRID_RLY_FLT",
            "ALM_ID_GRID_RLYON_FLT",
            "ALM_ID_GRID_RLYON_FORCE_FLT",
            "ALM_ID_EPS_RLY_FLT",
            "ALM_ID_INV_SW_OCP",
            "ALM_ID_GRID_SW_OCP",
            "ALM_ID_INV_HW_WAVE_OCP",
            "ALM_ID_INV_DCI_PROT",
            "ALM_ID_INV_SC",
            "ALM_ID_GFCI_PROT",
            "ALM_ID_EPS_VOLT_PROT",
            "ALM_ID_EPS_OLP",
            "ALM_ID_EPS_OLP_2PN",
            "ALM_ID_EPS_DCV_PROT",
            "ALM_ID_ISLAND_FAIL",
            "RESERVE",
            "RESERVE",
            "RESERVE",
            "RESERVE");
}
