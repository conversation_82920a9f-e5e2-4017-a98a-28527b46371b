package com.weihengtech.consts.parser.graph;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 故障表-三相机
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 17:27
 */
public interface ThreeErrorConstants extends ErrorCodeConstants {

    // ---------------------------------------dsp1----------------------------------------------------------------------
    List<String> DSP_1_CN_BIT_PARSE = Lists.newArrayList("GFCI硬件故障",
            "HCT硬件故障",
            "ISO硬件故障",
            "EEPROM读写故障",
            "Bus中点过压",
            "Bus不平衡",
            "相序错误",
            "一致性永久故障",
            "EPSN和GridN未连接",
            "ARM通讯故障",
            "外部flash安规故障",
            "EPS电压不稳",
            "PE未接故障(N-PE电压高)",
            "GFCI故障",
            "ISO故障",
            "DCI故障",
            "电池温度故障",
            "逆变器温度高",
            "PV温度高",
            "环境温度高",
            "Bus电压高（软件）",
            "Bus电压高（硬件）",
            "逆变Relay故障",
            "负载Relay故障",
            "预充Relay故障",
            "网侧继电器粘连故障",
            "无市电",
            "电网电压故障",
            "电网频率故障",
            "电网电压110%过压故障",
            "输出过载（是否保留）",
            "软件版本主副不一致"
    );

    List<String> DSP_1_EN_BIT_PARSE = Lists.newArrayList("GfciDeviceFault",
            "HctDeviceFault",
            "IsoDeviceFault",
            "EepromRWFault",
            "BusMVoltHighSW",
            "BusVoltUnbalanceSW",
            "GridPhaseSeqFault",
            "ConsistentFault",
            "EpsN_GridN_NoConnect",
            "ArmComFault",
            "ExFlashSafetyFault",
            "EpsGridSwapFault",
            "PENNoConnectFault",
            "GfciFault",
            "IsolationFault",
            "DciOutRange",
            "BattHeatSinkTempHighFault",
            "InvHeatSinkTempHighFault",
            "PVHeatSinkTempHighFault",
            "AmbientTempHighFault",
            "BusVoltHighSW",
            "BusVoltHighHW",
            "InvRelayFault",
            "EpsRelayFault",
            "PrechargeRelayFault",
            "GridRelayFault",
            "NoUtility",
            "GridVoltFault",
            "GridFreqFault",
            "GridAvgOver110",
            "OutputOverLoad",
            "Software Version Inconsistent Fault"
    );

    // ---------------------------------------dsp2----------------------------------------------------------------------
    List<String> DSP_2_CN_BIT_PARSE = Lists.newArrayList("警告中恢复",
            "PV1过压",
            "PV2过压",
            "PV3过压",
            "PV1过流（软件）",
            "PV1过流（硬件）",
            "PV2过流（软件）",
            "PV2过流（硬件）",
            "PV3过流（软件）",
            "PV3过流（硬件）",
            "PV电压低",
            "BUS电压低",
            "Bus过压（硬件）",
            "风扇堵转",
            "电网瞬时过压",
            "开环电压故障",
            "保留",
            "保留",
            "保留",
            "电网电流过流（软件）",
            "电网电流过流（硬件）",
            "逆变过流",
            "逆变硬件过流",
            "输出过载",
            "LVRT",
            "孤岛",
            "过频降载",
            "过温降载",
            "输入过流降载",
            "输出过流降载",
            "低温警告（环境温度低）",
            "外部flash警告"
    );

    List<String> DSP_2_EN_BIT_PARSE = Lists.newArrayList("RecFromWarning",
            "Pv1VoltHigh",
            "Pv2VoltHigh",
            "Pv3VoltHigh",
            "Pv1CurrOutRangeSW",
            "Pv1CurrOutRangeHW",
            "Pv2CurrOutRangeSW",
            "Pv2CurrOutRangeHW",
            "Pv3CurrOutRangeSW",
            "Pv3CurrOutRangeHW",
            "PvVoltLow",
            "BusVoltLow",
            "BusVoltHighHW",
            "FanLockFault",
            "GridVoltInstOver",
            "OpenLoopVoltage",
            "reserve",
            "reserve",
            "reserve",
            "GridCurrOutRangeSW",
            "GridCurrOutRangeHW",
            "InvCurrOutRangeSW",
            "InvCurrOutRangeHW",
            "OutputOverLoad",
            "LVRT",
            "IslandingOccur",
            "ReducePbyOverF",
            "ReducePbyOverT",
            "ReducePbyLimitInI",
            "ReducePbyLimitOutI",
            "LowTempWarn",
            "ExterFlashWarn"
    );

    // ---------------------------------------dsp3----------------------------------------------------------------------
    List<String> DSP_3_CN_BIT_PARSE = Lists.newArrayList("电池电压高（软件）",
            "电池电压低（软件）",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "BuckBst电流高（硬件）",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留",
            "保留"
    );

    List<String> DSP_3_EN_BIT_PARSE = Lists.newArrayList("BattVoltHighSW",
            "BattVoltLOWSW",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "BuckBstCurrHighHW",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve",
            "reserve"
    );
}
