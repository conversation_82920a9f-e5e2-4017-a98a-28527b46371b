package com.weihengtech.consts;

/**
 * <AUTHOR>
 */
public class RecordContentConstants {

	// 设备列表
	public static final String DEVICE_LIST = "频繁请求设备列表";

	// 启动设备
	public static final String START_DEVICE = "启动了[{deviceNameList}]";

	// 设备停机
	public static final String DOWN_DEVICE = "停机了[{deviceNameList}]";

	// 重启
	public static final String RESTART_DEVICE = "重启了[{deviceNameList}]";

	// 修改备注
	public static final String UPDATE_DEVICE_ALIAS = "将设备[{deviceName}]备注[{beforeAlias}]修改为[{afterAlias}]";

	// 修改描述
	public static final String UPDATE_DEVICE_DESC = "将设备[{deviceName}]描述修改为[{afterDesc}]";

	// 刷新设备状态
	public static final String REFRESH_DEVICE_STATE = "刷新了[{deviceName}]的设备状态";

	// 查询设备信息页
	public static final String QUERY_DEVICE_INFO = "查询了[{deviceName}]的[{pageName}]信息页";

	// 设备配置页
	public static final String SET_DEVICE_CONFIG = "将设备[{deviceName}]配置[{pageName}]页面的[{paramNameList}]修改为[{paramValueList}]";

	// 历史曲线
	public static final String QUERY_TSDB = "搜索了[{deviceName}]设备[{fromGMT8}]到[toGMT8]时间段的[{metricList}]数据";

	// 事件
	public static final String QUERY_EVENT = "搜索了[{deviceName}]设备的事件";

	// 修改设备维护记录
	public static final String DEVICE_MAINTENANCE_UPDATE = "修改了[{id}]的维护记录[{info}]";

	// 新增软件版本
	public static final String INSERT_SOFTWARE_VERSION = "新增软件版本[{softwareVersionName}],类型:[{hardwareTypeId}],关联硬件版本id:[{hardwareVersionIdList}]";

	// 修改软件版本
	public static final String UPDATE_SOFTWARE_VERSION = "修改[{softwareVersionId}]软件版本名字为{softwareVersionName}],关联硬件版本id:[{hardwareVersionIdList}]";

	// 删除软件版本
	public static final String DELETE_SOFTWARE_VERSION = "删除软件版本[{softwareVersionIdList}]";

	// 新增硬件版本
	public static final String INSERT_HARDWARE_VERSION = "新增硬件版本[{hardwareVersionName}],类型:[{typeId}],关联软件版本id:[{softwareVersionIdList}]";

	// 修改硬件版本
	public static final String UPDATE_HARDWARE_VERSION = "修改[{hardwareVersionId}]硬件版本名字为{hardwareVersionName}],关联软件版本id:[{softwareVersionIdList}]";

	// 删除硬件版本
	public static final String DELETE_HARDWARE_VERSION = "删除硬件版本[{hardwareVersionIdList}]";

	// 固件升级
	public static final String UPGRADE_FIRMWARE = "给[{deviceIdList}]升级固件包[{firmwareIdList}]";

	// 上传固件包
	public static final String UPLOAD_FIRMWARE = "上传固件包[{firmwareId}]-[downloadUrl]-[size]";

	// 修改固件包
	public static final String UPDATE_FIRMWARE = "修改固件包[{firmwareId}]-[{firmwareBean}]";

	// 输入sn升级固件
	public static final String UPGRADE_FIRMWARE_SN = "给[{deviceNameList}]升级了固件包[{firmwareIdList}]";

	// 新增hub版本号
	public static final String INSERT_HUB_VERSION = "新增了Hub的版本号: android[{androidVersionName}]-ios[{iosVersionName}]";

	// 修改Hub版本号
	public static final String UPDATE_HUB_VERSION = "修改了Hub的版本号[{versionId}]: android[{androidVersionName}]-ios[{iosVersionName}]";

	// 删除Hub版本号
	public static final String DELETE_HUB_VERSION = "删除了Hub的版本号[{versionId}]";

	// 经销商绑设备
	public static final String AGENT_BIND_DEVICE = "给经销商[{agentEmail}]绑定了设备[[{deviceNameList}]";

	// 给经销商绑定失败的设备
	public static final String AGENT_BIND_DEVICE_FAILURE = "给经销商[{agentEmail}]绑定失败的设备[{deviceNameList}]";

	// 解绑设备
	public static final String AGENT_UNBIND_DEVICE = "解绑设备[{deviceNameList}]";

	// 解绑失败的设备
	public static final String AGENT_UNBIND_DEVICE_FAILURE = "解绑失败的设备[{deviceNameList}]";

	// 新增重点关注设备
	public static final String INSERT_FOCUS_DEVICE = "新增了重点关注设备[{deviceName}]";

	// 删除重点关注设备
	public static final String DELETE_FOCUS_DEVICE = "删除了重点关注设备[{focusDeviceName}]";

	// 新增高频采样
	public static final String INSERT_HIGH_FREQUENCY_SAMPLING = "新增高频采样[{deviceName}]";

	// 删除高频采样
	public static final String DELETE_HIGH_FREQUENCY_SAMPLING = "删除高频采样[{deviceNameList}]";

	// 新增配置文件
	public static final String SAVE_CONFIG_FILE = "保存了配置文件[{fileName}]";

	// 修改详细地址
	public static final String UPDATE_DEVICE_ADDRESS = "将设备[{deviceName}]的详细地址由[{beforeAddress}]修改为[{afterAddress}]";
}
