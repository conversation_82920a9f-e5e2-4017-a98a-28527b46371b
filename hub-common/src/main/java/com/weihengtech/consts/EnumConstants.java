package com.weihengtech.consts;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import lombok.Getter;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class EnumConstants {

	@Getter
	public enum RecordModule {
		ACCOUNT(0, "账号相关"), DEVICE_OPERATION(1, "设备操作"), DEVICE_INFO(2, "设备信息"), DEVICE_CONFIG(3, "设备配置"), CURVES(
				4,
				"历史曲线"
		), DEVICE_EVENT(5, "设备事件"), FIRMWARE_MAINTAIN(6, "固件维护"), VERSION_MAINTAIN(
				7,
				"版本维护"
		), DEVICE_BIND(8, "设备绑定"), DEVICE_FOCUS(9, "重点关注"), HIGH_FREQUENCY_SAMPLING(10, "高频采样"), CONFIG_FILE(11, "配置文件");

		int code;
		String name;

		RecordModule(int code, String name) {
			this.code = code;
			this.name = name;
		}

		private static List<Dict> MODULE_LIST;
		private static Map<Integer, String> MODULE_MAP;

		public static List<Dict> recordModuleList() {
			if (CollUtil.isEmpty(MODULE_LIST)) {
				MODULE_LIST = Arrays.stream(RecordModule.values())
						.sorted(Comparator.comparingInt(RecordModule::getCode)).map(recordModule -> {
							Dict dict = new Dict();
							dict.put("id", recordModule.getCode());
							dict.put("name", recordModule.getName());
							return dict;
						}).collect(Collectors.toList());
			}
			return MODULE_LIST;
		}

		public static String getModuleName(Integer code) {
			if (CollUtil.isEmpty(MODULE_MAP)) {
				MODULE_MAP = Arrays.stream(RecordModule.values())
						.collect(Collectors.toMap(RecordModule::getCode, RecordModule::getName));
			}
			return MODULE_MAP.get(code);
		}
	}
}
