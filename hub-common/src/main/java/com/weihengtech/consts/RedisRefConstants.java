package com.weihengtech.consts;

/**
 * <AUTHOR>
 */
public class RedisRefConstants {

	public static final String SPEEDUP = "SPEEDUP:";
	public static final String SPEEDUP_LIKE = "SPEEDUP:*";
	public static final Long SPEEDUP_TUYA_TIMEOUT = 5 * 60L;
	public static final String SPEEDUP_TUYA_FLAG = "11";
	public static final Long SPEEDUP_ELINK_TIMEOUT = 30L;
	public static final String SPEEDUP_ELINK_FLAG = "00";

	public static final Long SPEEDUP_WH_TIMEOUT = 5 * 60L;
	public static final String SPEEDUP_WH_FLAG = "22";

	public static final String VPP_MODE_VALID_TIME = "vpp-mode:";

	public static String buildVppModeKey(String deviceSn) {
		return VPP_MODE_VALID_TIME + deviceSn;
	}

	public static String buildSpeedupLikeKey(String datacenter) {
		return datacenter + ":" + SPEEDUP_LIKE;
	}

	public static String buildSpeedupRedisKey(String deviceFlag, String datacenter) {
		return datacenter + ":" + SPEEDUP + deviceFlag;
	}

	private static final String EMAIL_FOCUSED_DEVICE_OFFLINE = "EMAIL:FOCUSED:";

	public static String buildFocusedDeviceOfflineKey(Integer focusedId) {
		return EMAIL_FOCUSED_DEVICE_OFFLINE + focusedId;
	}

	public static final String DATACENTER_PREFIX = "DATACENTER:SYNC:";

	public static String buildDatacenterKey(String datacenter) {
		return DATACENTER_PREFIX + datacenter;
	}
}
