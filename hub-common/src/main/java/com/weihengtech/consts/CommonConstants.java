package com.weihengtech.consts;

import cn.hutool.core.collection.ListUtil;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface CommonConstants {

	Integer TAG_DELETE = 0;
	Integer TAG_ACTIVE = 1;
	Integer VERSION_BIND_OFF = 0;
	Integer VERSION_BIND_ON = 1;
	Integer DEVICE_UPGRADE_ING = 2;
	Integer DEVICE_UPGRADE_FINISH = 1;
	Integer DEVICE_UPGRADE_FAILURE = 0;
	Integer DEVICE_STATE_ON = 0;
	Integer DEVICE_STATE_OFF = 1;
	Integer DEVICE_STATE_RESTART = 2;
	int PERIOD_CUSTOM = 0;
	int PERIOD_WEEK = 1;
	int PERIOD_MONTH = 2;
	int PERIOD_SEASON = 3;
	int PERIOD_YEAR = 4;

	List<String> METRIC_LIST = ListUtil.toLinkedList(
			// 今日发电量
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV,
			// 电池今日充电
			TsdbMetricsConstants.BAT_E_TOTAL_CHARGE,
			// 电池今日放电
			TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE,
			// 电网今日卖电量
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID,
			// 电网今日买电量
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID
	);

	List<String> HOME_ENERGY = ListUtil.toLinkedList(
			// 今日发电量
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV,
			// 电网今日卖电量
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID,
			// 电网今日买电量
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID
	);

	List<String> TH_REALTIME_POWER = ListUtil.toLinkedList(
			// 光伏功率
			TsdbMetricsConstants.METER_P_PV,
			// pv电表功率
			TsdbMetricsConstants.METER_PV_P,
			// 电网功率
			TsdbMetricsConstants.AC_P,
			// 电池功率
			TsdbMetricsConstants.BAT_P,
			// 仪表功率
			TsdbMetricsConstants.METER_P,
			// 应急电源有功功率1
			TsdbMetricsConstants.EPS_P,
			// 应急电源有功功率2
			TsdbMetricsConstants.EPS_PS,
			// 应急电源有功功率3
			TsdbMetricsConstants.EPS_PT,
			// 电池剩余电量
			TsdbMetricsConstants.BAT_SOC,
			// 系统功率配置
			TsdbMetricsConstants.SYS_POWER_CONFIG
	);

	List<String> NA_REALTIME_POWER = ListUtil.toLinkedList(
			// 光伏功率
			TsdbMetricsConstants.METER_P_PV,
			// pv电表功率
			TsdbMetricsConstants.METER_PV_P,
			// 电网功率
			TsdbMetricsConstants.AC_P,
			// 电池功率
			TsdbMetricsConstants.BAT_P,
			// 仪表功率
			TsdbMetricsConstants.METER_P,
			// 应急电源有功功率1
			TsdbMetricsConstants.EPS_P,
			// 应急电源有功功率2
			TsdbMetricsConstants.EPS_PS,
			// 电池剩余电量
			TsdbMetricsConstants.BAT_SOC,
			// 系统功率配置
			TsdbMetricsConstants.SYS_POWER_CONFIG
	);

	List<String> SP_REALTIME_POWER = ListUtil.toLinkedList(
			// 光伏功率
			TsdbMetricsConstants.METER_P_PV,
			// pv电表功率
			TsdbMetricsConstants.METER_PV_P,
			// 电网功率
			TsdbMetricsConstants.AC_P,
			// 电池功率
			TsdbMetricsConstants.BAT_P,
			// 仪表功率
			TsdbMetricsConstants.METER_P,
			// EPS功率
			TsdbMetricsConstants.EPS_P_1,
			// 电池剩余电量
			TsdbMetricsConstants.BAT_SOC,
			TsdbMetricsConstants.SYS_POWER_CONFIG
	);

	LinkedList<String> CHARGE_NOW_RUN_DATA = ListUtil.toLinkedList(
			TsdbMetricsConstants.VOLTAGE_L1,
			TsdbMetricsConstants.VOLTAGE_L2,
			TsdbMetricsConstants.VOLTAGE_L3,
			TsdbMetricsConstants.CURRENT_IMPORT_L1,
			TsdbMetricsConstants.CURRENT_IMPORT_L2,
			TsdbMetricsConstants.CURRENT_IMPORT_L3,
			TsdbMetricsConstants.POWER_ACTIVE_IMPORT
	);

	int GRAPH_HOME = 0;
	int GRAPH_SOLAR = 1;
	int GRAPH_BATTERY = 2;
	int GRAPH_GRID = 3;
	int HOME_DEVICE_PERIOD_TODAY = 1;
	int HOME_DEVICE_PERIOD_YESTERDAY = 2;
	int HOME_DEVICE_PERIOD_WEEK = 3;
	int HOME_DEVICE_PERIOD_MONTH = 4;

	int DEVICE_ELINK = 0;
	int DEVICE_TUYA = 1;

	String DICT_CODE_TOP_VERSION = "GWY_TOP_VERSION";
	String DICT_CODE_CHECK_VERSION = "GWY_CHECK_VERSION";
	// 逆变器固件配置
	String DICT_CODE_INVERTER_FIRMWARE = "GWY_INVERTER_FIRMWARE";
	String ITEM_CODE_WIFI = "ESP32-WiFi";
	String ITEM_CODE_ETH = "ESP32-ETH";
	String ITEM_CODE_4G = "ESP32-4G";
	// EMS最新版本号
	String ITEM_CODE_EMS = "EMS";
	// DSP1最新版本号
	String ITEM_CODE_DSP1 = "DSP1";
	// DSP2最新版本号
	String ITEM_CODE_DSP2 = "DSP2";
	// BMS最新版本号
	String ITEM_CODE_BMS = "BMS";
}
