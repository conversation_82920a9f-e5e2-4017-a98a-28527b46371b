package com.weihengtech.consts;

import java.util.Arrays;
import java.util.List;

/**
 * 内置角色ID常量
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 10:30
 */
public interface RoleConstants {

    /** 超级管理员 */
    String ROLE_ADMIN = "1";

    /** 销售管理 */
    String ROLE_SALE_ADMIN = "2";

    /** 售后管理 */
    String ROLE_AFTER_SALE_ADMIN = "3";

    /** 研发管理 */
    String ROLE_DEV_ADMIN = "4";

    /** 售后 */
    String ROLE_AFTER_SALE = "5";

    /** 研发(通用) */
    String ROLE_DEV = "6";

    /** 研发(维护固件) */
    String ROLE_DEV_FIRMWARE = "7";

    /** 研发(版本更新) */
    String ROLE_DEV_VERSION = "8";

    /** 销售 */
    String ROLE_SALE = "9";

    /** 经销商 */
    String ROLE_AGENT = "10";

    /** 分销商 */
    String ROLE_DEALER = "11";

    /** 零售商 */
    String ROLE_RETAILER = "12";

    /** 安装商 */
    String ROLE_INSTALLER = "13";

    /** 内部系统角色 */
    String ROLE_SYSTEM = "ROLE_SYSTEM";

    /**
     * 需要判断权限的角色
     */
    List<String> NEED_FILTER_ROLE = Arrays.asList(ROLE_SALE, ROLE_AGENT,
            ROLE_DEALER, ROLE_RETAILER, ROLE_INSTALLER);

    /**
     * 代理商角色
     */
    List<String> AGENT_ROLE = Arrays.asList(ROLE_AGENT, ROLE_DEALER, ROLE_RETAILER);
}
