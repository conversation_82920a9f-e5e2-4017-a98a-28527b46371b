package com.weihengtech.intercepters;

import com.weihengtech.containers.ProcessHandlerContainer;
import com.weihengtech.handlers.ICustomProcessHandler;
import com.weihengtech.interfaces.IAroundProcessHandler;
import com.weihengtech.interfaces.IGlobalProcessHandler;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.lang.NonNull;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class AuthorizationInterceptor implements HandlerInterceptor, ApplicationContextAware {

	private ApplicationContext applicationContext;

	@Override
	public boolean preHandle(
			@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
			@NonNull Object handler
	) throws Exception {
		ProcessHandlerContainer processHandlerContainer = applicationContext
				.getBeansOfType(ProcessHandlerContainer.class).get(ProcessHandlerContainer.class.getSimpleName());
		HandlerInterceptor.super.preHandle(request, response, handler);
		if (processHandlerContainer == null) {
			return true;
		}
		List<IGlobalProcessHandler> globalProcessHandlerList = processHandlerContainer.getGlobalProcessHandlerList();
		List<IAroundProcessHandler> aroundProcessHandlerList = processHandlerContainer.getAroundProcessHandlerList();
		List<ICustomProcessHandler> customProcessHandlerList = processHandlerContainer.getCustomProcessHandlerList();
		globalProcessHandlerList.forEach(IGlobalProcessHandler::preHandle);
		aroundProcessHandlerList.forEach(IAroundProcessHandler::preHandle);
		customProcessHandlerList.forEach(authorityProcessHandler ->
				authorityProcessHandler.preHandle(request, handler));
		return true;
	}

	@Override
	public void postHandle(
			@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
			@NonNull Object handler, ModelAndView modelAndView
	) throws Exception {
		ProcessHandlerContainer processHandlerContainer = applicationContext
				.getBeansOfType(ProcessHandlerContainer.class).get(ProcessHandlerContainer.class.getSimpleName());
		HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
		if (processHandlerContainer == null) {
			return;
		}
		processHandlerContainer.getCustomProcessHandlerList().forEach(
				customProcessHandler -> customProcessHandler.postHandle(request, response, handler, modelAndView));
		processHandlerContainer.getAroundProcessHandlerList().forEach(IAroundProcessHandler::postHandle);
		processHandlerContainer.getGlobalProcessHandlerList().forEach(IGlobalProcessHandler::postHandle);
	}

	@Override
	public void afterCompletion(
			@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
			@NonNull Object handler, Exception ex
	) throws Exception {
		ProcessHandlerContainer processHandlerContainer = applicationContext
				.getBeansOfType(ProcessHandlerContainer.class).get(ProcessHandlerContainer.class.getSimpleName());
		HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
		if (processHandlerContainer != null) {
			processHandlerContainer.getCustomProcessHandlerList().forEach(ICustomProcessHandler::afterCompletion);
			processHandlerContainer.getAroundProcessHandlerList().forEach(IAroundProcessHandler::afterCompletion);
			processHandlerContainer.getGlobalProcessHandlerList().forEach(IGlobalProcessHandler::afterCompletion);
		}
		SecurityContextHolder.clearContext();
	}

	@Override
	public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}
}
