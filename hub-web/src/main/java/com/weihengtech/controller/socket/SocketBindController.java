package com.weihengtech.controller.socket;

import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.dtos.device.NetBindDeviceDTO;
import com.weihengtech.pojo.vos.socket.NetBindSocketVO;
import com.weihengtech.service.socket.SocketBoundService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 充电桩绑定模块
 *
 * <AUTHOR>
 * @date 2024/1/25 13:56
 * @version 1.0
 */
@RestController
@RequestMapping("/smart-plug/device/bound")
@Api(tags = "23_设备绑定模块")
public class SocketBindController {

	@Resource
	private SocketBoundService socketBoundService;

	@PostMapping("/net/bind_account")
	@ApiOperation("配网流程给设备绑定账号")
	public DataResponse<NetBindDeviceDTO> netBindAccount(@RequestBody @Valid NetBindSocketVO param) {
		return DataResponse.success(socketBoundService.netDeviceBind(param));
	}

}
