package com.weihengtech.controller.socket;

import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.dtos.device.DeviceListPageDTO;
import com.weihengtech.pojo.vos.device.DeviceFlagVO;
import com.weihengtech.service.socket.SocketDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 充电桩设备信息
 *
 * <AUTHOR>
 * @date 2024/1/25 10:48
 * @version 1.0
 */
@RestController
@RequestMapping("/smart-plug/device/info")
@Api(tags = "02_设备信息")
public class SocketInfoController {

	@Resource
	private SocketDetailService socketDetailService;

	@PostMapping("/basic")
	@ApiOperation(value = "设备信息 DeviceInformation 数据")
	public DataResponse<DeviceListPageDTO> queryDeviceInformation(@RequestBody @Valid DeviceFlagVO deviceFlagVO) {
		return DataResponse.success(socketDetailService.queryDeviceInformation(deviceFlagVO.getDeviceName()));
	}
}
