package com.weihengtech.controller.socket;

import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.vos.socket.SocketRecordQueryVO;
import com.weihengtech.sdk.iot.ecos.model.tuya.response.TuyaDeviceLog;
import com.weihengtech.service.socket.SocketRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 单插设备开关记录
 *
 * <AUTHOR>
 * @date 2024/1/25 10:48
 * @version 1.0
 */
@RestController
@RequestMapping("/smart-plug/device/record")
@Api(tags = "01_开关记录")
public class SocketRecordController {

	@Resource
	private SocketRecordService socketRecordService;

	@PostMapping("/list")
	@ApiOperation(value = "单插开关记录")
	public DataResponse<List<TuyaDeviceLog>> queryChargeRecord(@RequestBody @Valid SocketRecordQueryVO req) {
		return DataResponse.success(socketRecordService.queryChargeRecord(req));
	}
}
