package com.weihengtech.controller.charger;

import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dtos.charger.ChargeStatusDTO;
import com.weihengtech.service.charger.ChargeRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 充电桩设备信息
 *
 * <AUTHOR>
 * @date 2024/1/25 10:48
 * @version 1.0
 */
@RestController
@RequestMapping("/ac-charger/device/callback")
@Api(tags = "05_充电桩主动上报回调接口")
public class ChargerCallbackController {

	@Resource
	private ChargeRecordService chargeRecordService;

	@PostMapping("/charge")
	@ApiOperation(value = "ocpp上报的充电状态")
	public EmptyResponse queryChargeRecord(@RequestBody ChargeStatusDTO req) {
		chargeRecordService.handleStatus(req);
		return EmptyResponse.success();
	}

}
