package com.weihengtech.controller.charger;

import com.weihengtech.service.charger.ChargerInfoService;
import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.dtos.device.info.ChargerDeviceInfoDTO;
import com.weihengtech.pojo.vos.device.DeviceSyncStateVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 设备列表
 *
 * <AUTHOR>
 * @date 2024/1/24 19:17
 * @version 1.0
 */
@RestController
@RequestMapping("/ac-charger/device/list")
@Api(tags = "01_设备列表")
public class ChargerListController {

	@Resource
	private ChargerInfoService chargerInfoService;

	@GetMapping("detail")
	@ApiOperation(value = "设备信息详情")
	public DataResponse<ChargerDeviceInfoDTO> chargerDetail(@RequestParam Long id) {
		return DataResponse.success(chargerInfoService.queryByDeviceId(id));
	}

	@PostMapping("sync_state")
	@ApiOperation(value = "同步单设备状态")
	public DataResponse<Integer> syncDeviceState(@RequestBody @Valid DeviceSyncStateVo deviceSyncStateVo) {
		return DataResponse.success(chargerInfoService.syncDeviceState(deviceSyncStateVo));
	}
}
