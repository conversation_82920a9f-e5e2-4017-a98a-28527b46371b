package com.weihengtech.controller.charger;

import com.weihengtech.service.charger.ChargeRecordService;
import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.dos.charger.ChargeRecordDO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.charger.RecordPageVO;
import com.weihengtech.pojo.vos.charger.RecordQueryVO;
import com.weihengtech.pojo.vos.charger.RecordSaveVO;
import com.weihengtech.pojo.vos.charger.RecordUpdVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 充电桩设备信息
 *
 * <AUTHOR>
 * @date 2024/1/25 10:48
 * @version 1.0
 */
@RestController
@RequestMapping("/ac-charger/device/record")
@Api(tags = "04_充电记录")
public class ChargerRecordController {

	@Resource
	private ChargeRecordService chargeRecordService;


	@PostMapping("/list")
	@ApiOperation(value = "设备信息 充电记录 数据")
	public DataResponse<List<ChargeRecordDO>> queryChargeRecord(@RequestBody @Valid RecordQueryVO req) {
		return DataResponse.success(chargeRecordService.queryChargeRecord(req));
	}

	@GetMapping("/last_one")
	@ApiOperation(value = "设备信息 最近一次充电记录 数据")
	public DataResponse<ChargeRecordDO> queryLastRecord(@RequestParam String deviceName) {
		return DataResponse.success(chargeRecordService.queryLastRecord(deviceName));
	}


	// -------------ecos api----------------------
	@PostMapping("/page")
	@ApiOperation(value = "设备信息 充电记录 数据(分页)")
	public DataResponse<PageInfoDTO<ChargeRecordDO>> pageChargeRecord(@RequestBody @Valid RecordPageVO req) {
		return DataResponse.success(chargeRecordService.pageChargeRecord(req));
	}

	@GetMapping("/last")
	@ApiOperation(value = "设备信息 最近一次充电记录 数据")
	public DataResponse<ChargeRecordDO> queryLastRecord(@RequestParam Long deviceId,
														@RequestParam Boolean isFilterCharing) {
		return DataResponse.success(chargeRecordService.queryLastRecord(deviceId, isFilterCharing));
	}

	@PostMapping("/save")
	@ApiOperation(value = "保存充电记录")
	public DataResponse<Void> saveChargeRecord(@RequestBody @Valid RecordSaveVO req) {
		chargeRecordService.saveChargeRecord(req);
		return DataResponse.success(null);
	}

	@PutMapping("/upd")
	@ApiOperation(value = "更新充电记录")
	public DataResponse<Void> updChargeRecord(@RequestBody @Valid RecordUpdVO req) {
		chargeRecordService.updChargeRecord(req);
		return DataResponse.success(null);
	}
}
