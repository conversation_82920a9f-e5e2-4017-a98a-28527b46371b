package com.weihengtech.controller.charger;

import com.weihengtech.pojo.dtos.charger.ChargerRunDataDTO;
import com.weihengtech.service.charger.ChargerDetailService;
import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.dtos.device.info.ChargerDeviceInfoDTO;
import com.weihengtech.pojo.vos.device.DeviceFlagVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 充电桩设备信息
 *
 * <AUTHOR>
 * @date 2024/1/25 10:48
 * @version 1.0
 */
@RestController
@RequestMapping("/ac-charger/device/info")
@Api(tags = "02_设备信息")
public class ChargerInfoController {

	@Resource
	private ChargerDetailService chargerDeviceDetailService;

	@PostMapping("/basic")
	@ApiOperation(value = "设备信息 DeviceInformation 数据")
	public DataResponse<ChargerDeviceInfoDTO> queryDeviceInformation(@RequestBody @Valid DeviceFlagVO deviceFlagVO) {
		return DataResponse
				.success(chargerDeviceDetailService.queryDeviceInformation(deviceFlagVO.getDeviceName()));
	}

	@GetMapping("/run_data")
	@ApiOperation(value = "充电桩充电时充电数据")
	public DataResponse<ChargerRunDataDTO> runData(@RequestParam("deviceId") String deviceId) {
		return DataResponse.success(chargerDeviceDetailService.runData(deviceId));
	}
}
