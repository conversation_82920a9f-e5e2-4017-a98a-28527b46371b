package com.weihengtech.controller.charger;

import com.weihengtech.service.charger.ChargerBoundService;
import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.dtos.charger.NetBindChargerDTO;
import com.weihengtech.pojo.vos.charger.NetBindChargerVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 充电桩绑定模块
 *
 * <AUTHOR>
 * @date 2024/1/25 13:56
 * @version 1.0
 */
@RestController
@RequestMapping("/ac-charger/device/bound")
@Api(tags = "23_设备绑定模块")
public class ChargerBindController {

	@Resource
	private ChargerBoundService chargerBoundService;

	@PostMapping("/net/bind_account")
	@ApiOperation("配网流程给设备绑定账号")
	public DataResponse<NetBindChargerDTO> netBindAccount(@RequestBody @Valid NetBindChargerVO netBindChargerVO) {
		return DataResponse.success(chargerBoundService.netDeviceBind(netBindChargerVO));
	}

}
