package com.weihengtech.controller.singlephase;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.pojo.dtos.device.DeviceDefaultConfigDto;
import com.weihengtech.pojo.vos.device.DeviceConfigConfigureVO;
import com.weihengtech.pojo.vos.device.DeviceConfigQueryVO;
import com.weihengtech.pojo.vos.device.DeviceSnVo;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.service.singlephase.SpDeviceConfigService;
import com.weihengtech.utils.AesUtil;
import com.weihengtech.utils.DeviceConfigFilterUtil;
import com.weihengtech.utils.OperatingRecordUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/single-phase/device/config")
@Api(tags = "03_设备配置")
public class SpDeviceConfigController {

	@Resource
	private SpDeviceConfigService spDeviceConfigService;

	@Resource
	private TuyaDatacenterService tuyaDatacenterService;

	@PostMapping("/query")
	@ApiOperation(value = "设备配置 查询接口")
	public DataResponse<Object> query(
			@RequestBody @Valid DeviceConfigQueryVO deviceConfigQueryVO
	) {
		return DataResponse.success(spDeviceConfigService.query(deviceConfigQueryVO.getDeviceName(),
				deviceConfigQueryVO.getCode()
		));
	}

	@PostMapping("/queryDefault")
	@ApiOperation(value = "设备配置 查询设备预设配置接口")
	public DataResponse<DeviceDefaultConfigDto> queryDefault(
			@RequestBody @Valid DeviceSnVo deviceSnVo
    ) {
        return DataResponse.success(spDeviceConfigService.queryDefault(deviceSnVo.getDeviceName()));
	}

	@GetMapping("/queryInfo")
	@ApiOperation(value = "设备配置 序列码")
	public DataResponse<JSONObject> queryInfo() {
		return DataResponse.success(spDeviceConfigService.queryInfo());
	}

	@PostMapping("/configure")
	@ApiOperation(value = "设备配置 配置接口")
	public EmptyResponse configure(@RequestBody @Valid DeviceConfigConfigureVO deviceConfigConfigureVO) {
		DeviceConfigFilterUtil.filter(deviceConfigConfigureVO);
		String userId = UserInfoUtil.currentUser().getId();
		spDeviceConfigService.configureDeviceConfig(deviceConfigConfigureVO, deviceConfigConfigureVO.getDeviceName());
		// tag
		OperatingRecordUtil.log(Long.parseLong(userId), RecordContentConstants.SET_DEVICE_CONFIG,
				MapUtil.<String, String>builder().put("deviceName", deviceConfigConfigureVO.getDeviceName())
						.put("pageName", AesUtil.decode(deviceConfigConfigureVO.getCode()))
						.put("paramNameList", JSONUtil.toJsonStr(deviceConfigConfigureVO.getPropNameList()))
						.put("paramValueList", JSONUtil.toJsonStr(deviceConfigConfigureVO.getPropValueList())).build(),
				EnumConstants.RecordModule.DEVICE_CONFIG.getCode()
		);
		return EmptyResponse.success();
	}

	@PostMapping("/restartBatSps")
	@ApiOperation(value = "重启电池sps")
	public EmptyResponse restartBatSps(@RequestBody @Valid DeviceSnVo deviceSnVo) {
		String userId = UserInfoUtil.currentUser().getId();
		spDeviceConfigService.restartBatSps(deviceSnVo.getDeviceName());
		OperatingRecordUtil.log(Long.parseLong(userId), RecordContentConstants.SET_DEVICE_CONFIG,
				MapUtil.<String, String>builder().put("deviceName", deviceSnVo.getDeviceName())
						.put("pageName", "Execute").put("paramNameList", JSONUtil.toJsonStr(ListUtil.toList("重启电池Sps")))
						.put("paramValueList", JSONUtil.toJsonStr(ListUtil.toList("1"))).build(),
				EnumConstants.RecordModule.DEVICE_CONFIG.getCode()
		);
		return EmptyResponse.success();
	}

	@PostMapping("/checkSelfTestState")
	@ApiOperation(value = "检查自检状态")
	public DataResponse<String> checkSelfTestState(@RequestBody @Valid DeviceSnVo deviceSnVo) {
		return DataResponse.success(spDeviceConfigService.checkSelfTestState(deviceSnVo.getDeviceName()));
	}
}
