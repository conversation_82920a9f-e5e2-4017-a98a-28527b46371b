package com.weihengtech.controller.common.device;

import com.aliyun.lindorm.tsdb.client.model.Result;
import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.dtos.parser.ParseErrorDTO;
import com.weihengtech.pojo.vos.tsdb.TsdbParseErrorVo;
import com.weihengtech.pojo.vos.tsdb.TsdbQueryVO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.device.TsdbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("tsdb")
@Api(tags = "04_TSDB")
@Slf4j
public class TsdbController {

	@Resource
	private DeviceListService deviceListService;
	@Resource
	private TsdbService tsdbService;

	@PostMapping("query")
	@ApiOperation(value = "tsdb查询")
    public DataResponse<Map<String, LinkedHashMap<Long, Object>>> queryTsdb(
			@RequestBody @Valid TsdbQueryVO tsdbQueryVO
	) {
		return DataResponse.success(tsdbService.query(tsdbQueryVO));
	}

	@PostMapping("queryList")
	@ApiOperation(value = "tsdb查询List版本")
    public DataResponse<Result> queryTsdbList(
			@RequestBody @Valid TsdbQueryVO tsdbQueryVO) {
		return DataResponse.success(tsdbService.queryList(tsdbQueryVO));
	}

	@PostMapping("parseError")
	@ApiOperation(value = "解析错误码")
	public DataResponse<ParseErrorDTO> parseError(@RequestBody @Valid TsdbParseErrorVo tsdbParseErrorVo) {
		return DataResponse.success(deviceListService.parseError(tsdbParseErrorVo));
	}

	@PostMapping("export")
	@ApiOperation(value = "导出")
	public void export(@RequestBody @Valid TsdbQueryVO param, HttpServletResponse response) {
		tsdbService.export(param, response);
	}
}
