package com.weihengtech.controller.common.other;

import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.dtos.other.DatacenterListDto;
import com.weihengtech.service.other.TuyaDatacenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/23 10:12
 * @version 1.0
 */
@RestController
@RequestMapping("datacenter")
@Api(tags = "11_数据中心")
public class DatacenterController {

	@Resource
	private TuyaDatacenterService tuyaDatacenterService;

	@GetMapping("list")
	@ApiOperation(value = "查询数据中心列表")
	public DataResponse<List<DatacenterListDto>> listDatacenter(HttpServletRequest request) {
		return DataResponse.success(tuyaDatacenterService.listDatacenter(request));
	}
}
