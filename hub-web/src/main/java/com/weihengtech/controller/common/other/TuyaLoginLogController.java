package com.weihengtech.controller.common.other;


import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dos.other.TuyaLoginLogDO;
import com.weihengtech.service.other.TuyaLoginLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p>
 * 系统信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@RestController
@RequestMapping("/log")
@Api(tags = "13_日志埋点")
public class TuyaLoginLogController {

    @Resource
    private TuyaLoginLogService tuyaLoginLogService;

    @PostMapping("/tuya/login")
    @ApiOperation(value = "保存tuya登录日志")
    public EmptyResponse saveTuyaLoginLog(@RequestBody TuyaLoginLogDO tuyaLoginLogDO) {
        tuyaLoginLogDO.setAccount(UserInfoUtil.currentUserEmail());
        tuyaLoginLogDO.setLoginTime(new Date());
        tuyaLoginLogService.save(tuyaLoginLogDO);
        return EmptyResponse.success();
    }

}

