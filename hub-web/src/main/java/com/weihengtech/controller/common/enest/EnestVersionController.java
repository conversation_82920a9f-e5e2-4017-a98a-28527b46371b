package com.weihengtech.controller.common.enest;

import cn.hutool.extra.cglib.CglibUtil;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.pojo.dos.enest.EnestVersionDo;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.dtos.enest.EnestVersionPageDto;
import com.weihengtech.pojo.vos.enest.EnestVersionAddVo;
import com.weihengtech.pojo.vos.enest.EnestVersionIdVo;
import com.weihengtech.pojo.vos.enest.EnestVersionPageVo;
import com.weihengtech.pojo.vos.enest.EnestVersionUpdateVo;
import com.weihengtech.service.enest.EnestVersionService;
import com.weihengtech.utils.ActionFlagUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("enest/version")
@Api(tags = "05_ENest客户端配置")
public class EnestVersionController {

	@Resource
	private EnestVersionService enestVersionService;

	@PostMapping("add")
	@ApiOperation(value = "新增enest版本号")
	public EmptyResponse addClientVersion(@RequestBody @Valid EnestVersionAddVo enestVersionAddVo) {
		enestVersionService.addEnestVersion(enestVersionAddVo);
		return EmptyResponse.success();
	}

	@PostMapping("del")
	@ApiOperation(value = "删除enest版本号")
	public EmptyResponse delClientVersion(@RequestBody @Valid EnestVersionIdVo enestVersionIdVo) {
		ActionFlagUtil.assertTrue(enestVersionService.removeById(enestVersionIdVo.getId()));
		return EmptyResponse.success();
	}

	@PostMapping("update")
	@ApiOperation(value = "修改enest版本号")
	
	public EmptyResponse updateClientVersion(@RequestBody @Valid EnestVersionUpdateVo enestVersionUpdateVo) {
		enestVersionService.updateEnestVersion(enestVersionUpdateVo);
		return EmptyResponse.success();
	}

	@PostMapping("page")
	@ApiOperation(value = "分页查询enest版本号")
	public DataResponse<PageInfoDTO<EnestVersionPageDto>> pageEnestVersion(
			@RequestBody @Valid EnestVersionPageVo enestVersionPageVo
	) {
		enestVersionPageVo.checkParams();
		return DataResponse.success(enestVersionService.pageClientVersion(
				enestVersionPageVo.getPageNum(),
				enestVersionPageVo.getPageSize()
		));
	}

	@PostMapping("get")
	@ApiOperation(value = "查询指定id的Enest版本号")
	public DataResponse<EnestVersionPageDto> getEnestVersion(@RequestBody @Valid EnestVersionIdVo enestVersionIdVo) {
		EnestVersionDo enestVersionDo = Optional.ofNullable(enestVersionService.getById(enestVersionIdVo.getId()))
				.orElseThrow(() -> new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION));
		EnestVersionPageDto enestVersionPageDto = new EnestVersionPageDto();
		CglibUtil.copy(enestVersionDo, enestVersionPageDto);
		return DataResponse.success(enestVersionPageDto);
	}
}
