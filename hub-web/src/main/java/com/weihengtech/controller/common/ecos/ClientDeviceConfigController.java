package com.weihengtech.controller.common.ecos;

import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.ecos.ClientDeviceConfigDo;
import com.weihengtech.pojo.vos.ecos.EcosDeviceConfigQueryVo;
import com.weihengtech.pojo.vos.ecos.EcosDeviceConfigUpdateVo;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.ecos.ClientDeviceConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("ecos/config")
@Api(tags = "02_Ecos设备相关")
public class ClientDeviceConfigController {

	@Resource
	private ClientDeviceConfigService clientDeviceConfigService;
	@Resource
	private DeviceListService deviceListService;

	@PostMapping("/update")
	@ApiOperation(value = "修改ecos设备相关配置")
	public EmptyResponse updateDeviceConfig(@RequestBody @Valid EcosDeviceConfigUpdateVo ecosDeviceConfigUpdateVo) {
		clientDeviceConfigService.updateOrInsertDeviceConfig(ecosDeviceConfigUpdateVo);
		return EmptyResponse.success();
	}

	@PostMapping("/query")
	@ApiOperation(value = "查询指定设备的配置")
	public DataResponse<ClientDeviceConfigDo> queryDeviceConfig(@RequestBody @Valid EcosDeviceConfigQueryVo ecosDeviceConfigQueryVo) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(ecosDeviceConfigQueryVo.getDeviceSn());
		if (null == deviceListDO) {
			throw new CustomException(ExceptionEnum.DEVICE_NOT_EXIST);
		}
		ClientDeviceConfigDo clientDeviceConfigDo = clientDeviceConfigService.getById(deviceListDO.getId());
		return DataResponse.success(
				clientDeviceConfigDo == null ? new ClientDeviceConfigDo().withId(deviceListDO.getId()).withFeedback(false)
						: clientDeviceConfigDo
		);
	}

}
