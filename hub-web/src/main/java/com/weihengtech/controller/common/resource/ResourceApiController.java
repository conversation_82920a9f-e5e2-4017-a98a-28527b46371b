package com.weihengtech.controller.common.resource;

import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.enums.device.ImportTypeEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.service.device.DeviceListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 提供给资源平台相关API
 *
 * <AUTHOR>
 * @date 2023/12/6 17:13
 * @version 1.0
 */
@RestController
@RequestMapping("resource")
@Api(tags = "25_资源平台相关API")
public class ResourceApiController {

	@Resource
	private DeviceListService deviceListService;

	@PostMapping("/save")
	@ApiOperation(value = "保存新的设备")
	public DataResponse<Long> saveNewDevice(@RequestBody DeviceListDO item) {
		DeviceListDO existsItem = deviceListService.lambdaQuery().eq(DeviceListDO::getDeviceSn, item.getDeviceSn()).one();
		if (existsItem != null) {
			return DataResponse.success(existsItem.getId());
		}
		item.initImportNewDevice(ImportTypeEnum.storage.getCode(), null, item.getDeviceSn(), null);
		deviceListService.save(item);
        return DataResponse.success(item.getId());
    }
}
