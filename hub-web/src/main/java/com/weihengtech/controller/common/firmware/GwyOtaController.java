package com.weihengtech.controller.common.firmware;

import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.vos.firmware.GwyConfigVersionVO;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotGwyFirmwareResponse;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotGwyHistoryResponse;
import com.weihengtech.service.firmware.GwyOtaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/18 19:45
 * @version 1.0
 */
@RestController
@RequestMapping("/gwy")
@Api(tags = "30_自研棒子ota")
@Slf4j
public class GwyOtaController {

	@Resource
	private GwyOtaService gwyOtaService;

	@PostMapping("/upgrade")
	@ApiOperation(value = "ota升级")
    public DataResponse<String> upgradeGwy(@RequestParam String wifiSn) {
		return DataResponse.success(gwyOtaService.upgradeGwy(wifiSn));
	}

	@GetMapping("/query/status")
	@ApiOperation(value = "查询升级状态")
    public DataResponse<String> queryGwyStatus(@RequestParam String deviceSn,
											  @RequestParam String taskId) {
		return DataResponse.success(gwyOtaService.queryGwyStatus(deviceSn, taskId));
	}

	@GetMapping("/query/history")
	@ApiOperation(value = "查询升级记录")
	public DataResponse<List<IotGwyHistoryResponse>> queryGwyHistory(@RequestParam String deviceSn) {
		return DataResponse.success(gwyOtaService.queryGwyHistory(deviceSn));
	}

	@GetMapping("/query/firmware")
	@ApiOperation(value = "查询固件列表")
	public DataResponse<List<IotGwyFirmwareResponse>> queryGwyFirmwareList(@RequestParam String productName) {
		return DataResponse.success(gwyOtaService.queryGwyFirmwareList(productName));
	}

	@PostMapping("/query/config")
	@ApiOperation(value = "查询置顶固件包和校验版本的配置")
	public DataResponse<GwyConfigVersionVO> queryGwyConfigVersion() {
		GwyConfigVersionVO res = gwyOtaService.queryGwyConfigVersion();
		return DataResponse.success(res);
	}

	@PostMapping("/save/config")
	@ApiOperation(value = "保存置顶固件包和校验版本的配置")
	public EmptyResponse saveGwyConfigVersion(@RequestBody GwyConfigVersionVO param) {
		gwyOtaService.saveGwyConfigVersion(param);
		return EmptyResponse.success();
	}
}
