package com.weihengtech.controller.common.list;

import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dtos.list.ListItemDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.list.ListItemAddVO;
import com.weihengtech.pojo.vos.list.ListItemPageVO;
import com.weihengtech.service.list.ListItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @author: jiahao.jin
 * @create: 2025-07-21 16:02
 * @description:
 */
@RestController
@RequestMapping("/list/item")
@Api(tags = "33_名单内容")
public class ListItemController {

    @Resource
    private ListItemService listItemService;

    // 添加名单
    @PostMapping("/add")
    @ApiOperation(value = "添加名单")
    public EmptyResponse addListItem(@RequestBody @Valid ListItemAddVO listItemAddVO) {
        listItemService.addListItem(listItemAddVO);
        return EmptyResponse.success();
    }

    // 移除名单
    @GetMapping("/remove")
    @ApiOperation(value = "移除名单")
    public EmptyResponse removeListItem(@RequestParam Long id) {
        listItemService.removeListItem(id);
        return EmptyResponse.success();
    }

    // 分页查询名单
    @PostMapping("/page")
    @ApiOperation(value = "分页查询名单")
    public DataResponse<PageInfoDTO<ListItemDTO>> pageListItem(@RequestBody @Valid ListItemPageVO listItemPageVO) {
        listItemPageVO.checkParams();
        return DataResponse.success(listItemService.pageListItem(listItemPageVO));
    }
}
