package com.weihengtech.controller.common.device;

import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dos.device.DeviceRelationDO;
import com.weihengtech.pojo.vos.relation.DeviceRelSaveVO;
import com.weihengtech.service.device.DeviceRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/23 9:23
 * @version 1.0
 */
@RestController
@RequestMapping("device/rel")
@Api(tags = "26_设备关联关系")
public class DeviceRelationController {

	@Resource
	private DeviceRelationService deviceRelationService;

	@GetMapping("/list")
	@ApiOperation(value = "获取关联关系列表")
	public DataResponse<List<DeviceRelationDO>> list(@RequestParam String deviceName) {
		return DataResponse.success(deviceRelationService.listRelation(deviceName));
	}

	@PostMapping("/save")
	@ApiOperation(value = "保存关联关系列表")
	public EmptyResponse save(@RequestBody @Valid DeviceRelSaveVO param) {
		deviceRelationService.saveRelation(param);
		return EmptyResponse.success();
	}

	@DeleteMapping("/del")
	@ApiOperation(value = "清除关联关系列表")
	public EmptyResponse del(@RequestParam String deviceName) {
		deviceRelationService.delRelation(deviceName);
		return EmptyResponse.success();
	}
}
