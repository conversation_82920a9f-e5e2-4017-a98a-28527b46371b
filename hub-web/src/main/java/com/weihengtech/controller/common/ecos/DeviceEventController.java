package com.weihengtech.controller.common.ecos;

import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.dtos.ecos.DeviceEventPageDTO;
import com.weihengtech.pojo.dtos.ecos.DeviceEventStatisticsDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.ecos.DeviceEventVO;
import com.weihengtech.service.ecos.DeviceEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("ecos/device")
@Api(tags = "04_设备事件")
public class DeviceEventController {

	@Resource
	private DeviceEventService deviceEventService;

	@PostMapping("event/page")
	@ApiOperation(value = "设备事件分页")
	public DataResponse<PageInfoDTO<DeviceEventPageDTO>> pageDeviceEvent(@RequestBody @Valid DeviceEventVO deviceEventVO) {
		return DataResponse.success(deviceEventService.pageDeviceEvent(deviceEventVO));
	}

	@PostMapping("event/page/v2")
	@ApiOperation(value = "设备事件分页")
	public DataResponse<PageInfoDTO<DeviceEventPageDTO>> pageDeviceEventV2(@RequestBody @Valid DeviceEventVO deviceEventVO) {
		return DataResponse.success(deviceEventService.pageDeviceEventV2(deviceEventVO));
	}

	@GetMapping("event/statistics")
	@ApiOperation(value = "设备事件统计")
	public DataResponse<DeviceEventStatisticsDTO> statisticsDeviceEvent() {
		return DataResponse.success(deviceEventService.statisticsDeviceEvent());
	}
}
