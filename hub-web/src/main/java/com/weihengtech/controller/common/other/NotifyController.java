package com.weihengtech.controller.common.other;

import cn.hutool.json.JSONUtil;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.vos.other.NotifyHybridPrometheusVo;
import com.weihengtech.service.other.NotifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("notify")
@Api(tags = "21_设备事件通知")
@Slf4j
// @ApiIgnore
public class NotifyController {

	@Resource
	private NotifyService notifyService;
	@Resource
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;

	@PostMapping("hybrid")
	@ApiOperation(value = "Hybrid设备事件通知")
	public EmptyResponse hybridNotify(@RequestBody NotifyHybridPrometheusVo notifyHybridPrometheusVo) {
		log.info(JSONUtil.toJsonStr(notifyHybridPrometheusVo));
		threadPoolTaskExecutor.execute(() -> notifyService.hybridNotify(notifyHybridPrometheusVo));
		return EmptyResponse.success();
	}
}
