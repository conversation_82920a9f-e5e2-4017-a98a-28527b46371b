package com.weihengtech.controller.common.device;

import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.pojo.dos.device.DeviceFocusDo;
import com.weihengtech.pojo.dtos.device.FocusDeviceDto;
import com.weihengtech.pojo.dtos.device.FocusDevicePageDto;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.device.DeviceFocusIdVo;
import com.weihengtech.pojo.vos.device.DeviceFocusUpdateVo;
import com.weihengtech.pojo.vos.device.DeviceFocusVo;
import com.weihengtech.pojo.vos.parent.PageInfoVO;
import com.weihengtech.service.device.DeviceFocusService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.OperatingRecordUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("device/focus")
@Api(tags = "12_重点关注设备模块")
public class DeviceFocusController {

	@Resource
	private DeviceFocusService deviceFocusService;

	@PostMapping("add")
	@ApiOperation(value = "新增重点关注设备")
	public EmptyResponse addFocusDevice(@RequestBody @Valid DeviceFocusVo deviceFocusVo) {
		deviceFocusVo.checkParams();
		deviceFocusService.addFocusDevice(deviceFocusVo, Long.parseLong(UserInfoUtil.currentUserId()));
		// tag
		OperatingRecordUtil.log(
				RecordContentConstants.INSERT_FOCUS_DEVICE,
				MapUtil.<String, String>builder().put("deviceName", deviceFocusVo.getDeviceFlag()).build(),
				EnumConstants.RecordModule.DEVICE_FOCUS.getCode()
		);

		return EmptyResponse.success();
	}

	@PostMapping("update")
	@ApiOperation(value = "更新重点关注设备")
	public EmptyResponse updateFocusDevice(
			@RequestBody @Valid DeviceFocusUpdateVo deviceFocusUpdateVo
	) {
		deviceFocusUpdateVo.checkParams();
		deviceFocusService.updateFocusDevice(deviceFocusUpdateVo, Long.parseLong(UserInfoUtil.currentUserId()));
		return EmptyResponse.success();
	}

	@PostMapping("del")
	@ApiOperation(value = "删除重点关注设备")
	public EmptyResponse delFocusDevice(@RequestBody @Valid DeviceFocusIdVo deviceFocusIdVo) {
		DeviceFocusDo deviceFocusDo = deviceFocusService.getById(deviceFocusIdVo.getId());
		if (null == deviceFocusDo) {
			throw new CustomException(ExceptionEnum.ACTION_NOT_SUCCESS_ERROR);
		}
		ActionFlagUtil.assertTrue(deviceFocusService.removeById(deviceFocusDo.getId()));
		// tag
		OperatingRecordUtil.log(
				RecordContentConstants.DELETE_FOCUS_DEVICE,
				MapUtil.<String, String>builder().put("focusDeviceName", deviceFocusDo.getDeviceFlag()).build(),
				EnumConstants.RecordModule.DEVICE_FOCUS.getCode()
		);

		return EmptyResponse.success();
	}

	@PostMapping("get")
	@ApiOperation(value = "查询指定重点关注设备")
	public DataResponse<FocusDeviceDto> getFocusDevice(@RequestBody @Valid DeviceFocusIdVo deviceFocusIdVo) {
		DeviceFocusDo deviceFocusDo = deviceFocusService.getById(deviceFocusIdVo.getId());
		BeanUtil.assertNotNull(deviceFocusDo);
		FocusDeviceDto focusDeviceDto = new FocusDeviceDto();
		CglibUtil.copy(deviceFocusDo, focusDeviceDto);
		return DataResponse.success(focusDeviceDto);
	}

	@PostMapping("page")
	@ApiOperation(value = "分页查询重点关注设备")
	public DataResponse<PageInfoDTO<FocusDevicePageDto>> pageFocusDevice(@RequestBody @Valid PageInfoVO pageInfoVO) {
		pageInfoVO.checkParams();
		return DataResponse
				.success(deviceFocusService.pageFocusDevice(pageInfoVO.getPageNum(), pageInfoVO.getPageSize()));
	}
}
