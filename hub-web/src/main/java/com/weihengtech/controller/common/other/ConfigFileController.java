package com.weihengtech.controller.common.other;

import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.vos.other.ConfigUploadVO;
import com.weihengtech.service.other.ConfigFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/config")
@Api(tags = "28_配置文件")
public class ConfigFileController {

	@Resource
	private ConfigFileService configFileService;

	@PostMapping("/upload")
	@ApiOperation(value = "配置文件上传")
	public EmptyResponse uploadConfig(@RequestBody @Valid ConfigUploadVO param) {
		configFileService.uploadConfig(param.getContent());
		return EmptyResponse.success();
	}
}
