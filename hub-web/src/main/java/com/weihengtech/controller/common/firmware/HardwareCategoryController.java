package com.weihengtech.controller.common.firmware;

import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.dtos.firmware.HardwareCategoryDTO;
import com.weihengtech.service.firmware.HardwareCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("hardware/category")
@Api(tags = "18_硬件总分类")
public class HardwareCategoryController {

	@Resource
	private HardwareCategoryService hardwareCategoryService;

	@GetMapping("all")
	@ApiOperation(value = "查询所有的硬件分类")
	public DataResponse<List<HardwareCategoryDTO>> queryAllHardwareCategory() {
		return DataResponse.success(hardwareCategoryService.queryAllHardwareCategory());
	}
}
