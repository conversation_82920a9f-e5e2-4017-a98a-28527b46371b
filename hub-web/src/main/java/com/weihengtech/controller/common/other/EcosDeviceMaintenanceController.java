package com.weihengtech.controller.common.other;

import cn.hutool.core.map.MapUtil;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.pojo.dtos.other.EcosDeviceMaintenanceInfoDto;
import com.weihengtech.pojo.vos.other.EcosDeviceMaintenanceInfoUpdateVo;
import com.weihengtech.service.other.EcosDeviceMaintenanceService;
import com.weihengtech.utils.OperatingRecordUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("device/maintenance")
@Api(tags = "24_设备维护信息")
public class EcosDeviceMaintenanceController {

	@Resource
	private EcosDeviceMaintenanceService ecosDeviceMaintenanceService;

	@GetMapping("info")
	@ApiOperation(value = "获取设备维护信息")
	public DataResponse<EcosDeviceMaintenanceInfoDto> getDeviceMaintenanceInfo(@RequestParam("deviceId") Long deviceId) {
		if (null == deviceId) throw new CustomException(ExceptionEnum.DEVICE_NOT_EXIST);
		return DataResponse.success(ecosDeviceMaintenanceService.getInfo(deviceId));
	}

	@PostMapping("update")
	@ApiOperation(value = "更新设备维护信息")
	public EmptyResponse updateDeviceMaintenanceInfo(@RequestBody @Valid EcosDeviceMaintenanceInfoUpdateVo ecosDeviceMaintenanceInfoUpdateVo) {
		ecosDeviceMaintenanceService.updateInfo(ecosDeviceMaintenanceInfoUpdateVo);
		String userId = UserInfoUtil.currentUserId();
		OperatingRecordUtil.log(Long.parseLong(userId), RecordContentConstants.DEVICE_MAINTENANCE_UPDATE,
				MapUtil.<String, String>builder()
						.put("id", ecosDeviceMaintenanceInfoUpdateVo.getId())
						.put("info", ecosDeviceMaintenanceInfoUpdateVo.getRecord())
						.build(),
				EnumConstants.RecordModule.DEVICE_INFO.getCode()
		);
		return EmptyResponse.success();
	}

}
