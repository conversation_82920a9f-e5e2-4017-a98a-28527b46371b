package com.weihengtech.controller.common.firmware;

import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dtos.firmware.HardwareTypeDTO;
import com.weihengtech.pojo.dtos.firmware.HardwareTypeLinkCategoryDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.firmware.HardwareTypeInsertVO;
import com.weihengtech.pojo.vos.firmware.HardwareTypeQueryVO;
import com.weihengtech.pojo.vos.firmware.HardwareTypeUpdateVO;
import com.weihengtech.service.firmware.HardwareTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("hardware/type")
@Api(tags = "20_硬件类型")
public class HardwareTypeController {

	@Resource
	private HardwareTypeService hardwareTypeService;

	@PostMapping("insert")
	@ApiOperation(value = "新增硬件类型")
	public EmptyResponse insertHardwareType(
			@RequestBody @Valid HardwareTypeInsertVO hardwareTypeInsertVO
	) {
		hardwareTypeService.insertHardwareType(hardwareTypeInsertVO);
		return EmptyResponse.success();
	}

	@PostMapping("update")
	@ApiOperation(value = "更新硬件类型")
	public EmptyResponse updateHardwareType(
			@RequestBody @Valid HardwareTypeUpdateVO hardwareTypeUpdateVO
	) {
		hardwareTypeUpdateVO.checkParams();
		hardwareTypeService.updateHardwareType(hardwareTypeUpdateVO);
		return EmptyResponse.success();
	}

	@PostMapping("page")
	@ApiOperation(value = "分页查询硬件类型")
	public DataResponse<PageInfoDTO<HardwareTypeDTO>> pageHardwareType(
			@RequestBody @Valid HardwareTypeQueryVO hardwareTypeQueryVO
	) {
		hardwareTypeQueryVO.checkParams();
		return DataResponse.success(hardwareTypeService.pageHardwareType(hardwareTypeQueryVO));
	}

	@PostMapping("allWithCategory")
	@ApiOperation(value = "查询硬件分页与硬件厂商映射关系")
	public DataResponse<List<HardwareTypeLinkCategoryDTO>> allWithCategory() {
		return DataResponse.success(hardwareTypeService.allWithCategory());
	}
}
