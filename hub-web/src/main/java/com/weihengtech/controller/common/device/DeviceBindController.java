package com.weihengtech.controller.common.device;

import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dtos.device.DeviceBoundBindAccountDto;
import com.weihengtech.pojo.dtos.device.DeviceBoundPageDto;
import com.weihengtech.pojo.dtos.device.DeviceBoundUnBindAccountDto;
import com.weihengtech.pojo.dtos.device.NetBindDeviceDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.device.DeviceBoundBindAccountVo;
import com.weihengtech.pojo.vos.device.DeviceBoundBindCountryVo;
import com.weihengtech.pojo.vos.device.DeviceBoundPageVo;
import com.weihengtech.pojo.vos.device.DeviceShareQrVo;
import com.weihengtech.pojo.vos.device.DeviceWifiSnVO;
import com.weihengtech.pojo.vos.device.NetBindDeviceVO;
import com.weihengtech.pojo.vos.device.NetBindWhVO;
import com.weihengtech.service.device.DeviceBoundService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("device/bound")
@Api(tags = "23_设备绑定模块")
public class DeviceBindController {

	@Resource
	private DeviceBoundService deviceBoundService;

	@PostMapping("/bindCountry")
	@ApiOperation("设备绑定国家")
	public EmptyResponse deviceUnBindAccount(@RequestBody @Valid DeviceBoundBindCountryVo deviceBoundBindCountryVo) {
		deviceBoundService.deviceBindCountry(deviceBoundBindCountryVo);
		return EmptyResponse.success();
	}

	@PostMapping("/net/bind_account")
	@ApiOperation("配网流程给设备绑定账号")
	public DataResponse<NetBindDeviceDTO> netBindAccount(@RequestBody @Valid NetBindDeviceVO netBindDeviceVo) {
		return DataResponse.success(deviceBoundService.netDeviceBind(netBindDeviceVo));
	}

	@PostMapping("/share/installer")
	@ApiOperation(value = "设备分享给安装商")
	public EmptyResponse deviceShareInstaller(@RequestBody @Valid DeviceShareQrVo req) {
		deviceBoundService.deviceShareInstaller(req.getCode());
		return EmptyResponse.success();
	}

	@PostMapping("/agent/bind")
	@ApiOperation("给vpp指定用户批量绑定设备")
	public DataResponse<DeviceBoundBindAccountDto> deviceBindAgent(@RequestBody DeviceBoundBindAccountVo deviceBoundBindAccountVo) {
		return DataResponse.success(deviceBoundService.deviceBindAgent(deviceBoundBindAccountVo));
	}

	@PostMapping("/agent/unbind")
	@ApiOperation("给vpp指定用户批量解绑设备")
	public DataResponse<DeviceBoundUnBindAccountDto> deviceUnBindAgent(@RequestBody DeviceBoundBindAccountVo deviceBoundBindAccountVo) {
		return DataResponse.success(deviceBoundService.deviceUnBindAgent(deviceBoundBindAccountVo));
	}

	@PostMapping("/iot/net/bind_account")
	@ApiOperation("配网流程给设备绑定账号")
	public DataResponse<NetBindDeviceDTO> iotNetBindAccount(@RequestBody @Valid NetBindWhVO netBindDeviceVo) {
		return DataResponse.success(deviceBoundService.iotNetDeviceBind(netBindDeviceVo));
	}

	@GetMapping("/iot/is_online")
	@ApiOperation("配网流程判断设备是否在线")
	public DataResponse<Boolean> iotIsOnline(@RequestParam String wifiSn) {
		return DataResponse.success(deviceBoundService.iotIsOnline(wifiSn));
	}

	@PostMapping("/iot/reset")
	@ApiOperation("配网过程中iot平台解绑设备")
	public EmptyResponse iotResetDevice(@RequestBody DeviceWifiSnVO param) {
		deviceBoundService.iotResetDevice(param.getWifiSn());
		return EmptyResponse.success();
	}

}
