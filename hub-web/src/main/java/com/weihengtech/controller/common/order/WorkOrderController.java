package com.weihengtech.controller.common.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.api.pojo.dtos.WorkOrderDetailDto;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.order.PageWorkOrderVo;
import com.weihengtech.pojo.vos.order.ReplyWorkOrderVo;
import com.weihengtech.service.order.IWorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("order")
@Api(tags = "07_工单模块")
public class WorkOrderController {

	@Resource
	private IWorkOrderService workOrderService;

	@PostMapping("page")
	@ApiOperation(value = "设备工单分页")
	public DataResponse<PageInfoDTO<WorkOrderDetailDto>> pageWorkOrder(@RequestBody PageWorkOrderVo pageWorkOrderVo) {
		pageWorkOrderVo.checkParams();
		return DataResponse.success(workOrderService.pageWorkOrder(pageWorkOrderVo));
	}


	@PostMapping("reply")
	@ApiOperation(value = "工单回复")
	public EmptyResponse replyWorkOrder(@RequestBody @Valid ReplyWorkOrderVo replyWorkOrderVo) {
		if (CollUtil.isNotEmpty(replyWorkOrderVo.getPicList()) || StrUtil.isNotBlank(replyWorkOrderVo.getContent())) {
			workOrderService.replyWorkOrder(replyWorkOrderVo);
		}
		return EmptyResponse.success();
	}

	@GetMapping("detail")
	@ApiOperation(value = "工单详情")
	public DataResponse<WorkOrderDetailDto> detailWorkOrder(@RequestParam("workOrderId") String workOrderId) {
		if (StrUtil.isBlank(workOrderId)) throw new CustomException(ExceptionEnum.PARAM_MISS_EXCEPTION);
		return DataResponse.success(workOrderService.detailWorkOrder(workOrderId));
	}
}
