package com.weihengtech.controller.common.device;

import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dtos.device.DynamicDesignDTO;
import com.weihengtech.pojo.dtos.device.DynamicExportDTO;
import com.weihengtech.pojo.dtos.device.DynamicTestDTO;
import com.weihengtech.pojo.vos.device.DynamicSaveVO;
import com.weihengtech.pojo.vos.device.DynamicSwitchVO;
import com.weihengtech.service.device.StorageEnergyInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023/11/23 9:23
 * @version 1.0
 */
@RestController
@RequestMapping("device/storage")
@Api(tags = "27_储能设备信息")
public class StorageInfoController {

	@Resource
	private StorageEnergyInfoService storageEnergyInfoService;

	@GetMapping("/export")
	@ApiOperation(value = "获取动态输出信息")
	public DataResponse<DynamicExportDTO> dynamicExport(@RequestParam String deviceName) {
		return DataResponse.success(storageEnergyInfoService.dynamicExport(deviceName));
	}

	@GetMapping("/max_power")
	@ApiOperation(value = "获取最大输出功率")
	public DataResponse<Integer> maxExportPower(@RequestParam String deviceName) {
		Integer power = storageEnergyInfoService.maxExportPower(deviceName);
		return DataResponse.success(power * 10);
	}

	@GetMapping("/design")
	@ApiOperation(value = "获取设备设计相关信息")
	public DataResponse<DynamicDesignDTO> designInfo(@RequestParam String deviceName) {
		DynamicDesignDTO res = storageEnergyInfoService.designInfo(deviceName);
		return DataResponse.success(res);
	}

	@PostMapping("/save")
	@ApiOperation(value = "保存动态输出信息")
	public EmptyResponse dynamicSave(@RequestBody @Valid DynamicSaveVO param) {
		storageEnergyInfoService.dynamicSave(param);
		return EmptyResponse.success();
	}

	@GetMapping("/test")
	@ApiOperation(value = "动态输出生效校验")
	public DataResponse<DynamicTestDTO> dynamicTest(@RequestParam String deviceName) {
		return DataResponse.success(storageEnergyInfoService.dynamicTest(deviceName));
	}

	@PostMapping("/switch")
	@ApiOperation(value = "动态输出使能开关")
	public EmptyResponse dynamicSwitch(@RequestBody @Valid DynamicSwitchVO param) {
		storageEnergyInfoService.dynamicSwitch(param);
		return EmptyResponse.success();
	}
}
