package com.weihengtech.controller.common.ecos;

import com.weihengtech.api.EcosClientApi;
import com.weihengtech.api.pojo.dtos.AccountBindInfoDTO;
import com.weihengtech.api.pojo.dtos.EcosAccountDto;
import com.weihengtech.api.pojo.vos.ClientBindAccountVo;
import com.weihengtech.api.pojo.vos.ClientUnBindAccountVo;
import com.weihengtech.auth.dto.BindInfoDTO;
import com.weihengtech.auth.dto.BindQueryReqDTO;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.enums.device.SeriesEnum;
import com.weihengtech.pojo.dos.charger.ChargerInfoDO;
import com.weihengtech.service.charger.ChargerInfoService;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.RoleConstants;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.ecos.GlobalEnestLatestVersionDto;
import com.weihengtech.pojo.dtos.ecos.GlobalVersionDto;
import com.weihengtech.pojo.dtos.ecos.InstallBoundDTO;
import com.weihengtech.pojo.dtos.ecos.InstallBoundInfoDTO;
import com.weihengtech.pojo.vos.charger.ChargerSaveVO;
import com.weihengtech.pojo.vos.ecos.EcosBindAccountVo;
import com.weihengtech.service.device.DeviceBoundService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.ecos.ClientGlobalVersionService;
import com.weihengtech.service.enest.EnestVersionService;
import com.weihengtech.service.ext.ExtCommonService;
import com.weihengtech.service.socket.SocketInfoService;
import com.weihengtech.service.specific.StrategyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("ecos")
@Api(tags = "01_Ecos相关API")
public class ClientApiController {

	@Resource
	private DeviceListService deviceListService;
	@Resource
	private EcosClientApi ecosClientApi;
	@Resource
	private ClientGlobalVersionService clientGlobalVersionService;
	@Resource
	private EnestVersionService enestVersionService;
	@Resource
	private AuthCenterResClient	authCenterResClient;
	@Resource
	private ChargerInfoService chargerInfoService;
	@Resource
	private SocketInfoService socketInfoService;
	@Resource
	private DeviceBoundService deviceBoundService;
	@Resource
	private StrategyService strategyService;

	@PostMapping("/account/device/bindClientAccount")
	@ApiOperation(value = "用户绑定主账号")
	public EmptyResponse bindClientAccount(@RequestBody @Valid EcosBindAccountVo ecosBindAccountVo) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(ecosBindAccountVo.getDeviceName());
		DataResponse<Integer> response = ecosClientApi.bindAccount(
				ClientBindAccountVo.builder()
						.device(deviceListDO)
						.username(ecosBindAccountVo.getEmail())
						.build()
		);
		if (HttpStatus.OK.value() == response.getCode()) {
			switch (response.getData()) {
				case 0:
				case 2:
					throw new CustomException(ExceptionEnum.DEVICE_ALREADY_BIND);
				case 1:
					return EmptyResponse.success();
				case 3:
					throw new CustomException(ExceptionEnum.ACCOUNT_NOT_EXIST);
				case 4:
					throw new CustomException(ExceptionEnum.INVALID_DATACENTER);
				default:
			}
		}
		return EmptyResponse.fail();
	}

	@GetMapping("/device/queryByDeviceName")
	@ApiOperation(value = "通过deviceName查询设备")
	@ApiIgnore
	public DataResponse<DeviceListDO> queryByDeviceName(@RequestParam("deviceName") String deviceName) {
        return DataResponse.success(deviceListService.getDeviceInfoByDeviceName(deviceName));
	}

	@GetMapping("/device/queryById")
	@ApiOperation(value = "通过deviceId查询设备")
	@ApiIgnore
	public DataResponse<DeviceListDO> queryById(@RequestParam("deviceId") Long deviceId) {
		return DataResponse.success(deviceListService.getDeviceById(deviceId));
	}

	@PostMapping("/device/batch_query")
	@ApiOperation(value = "通过deviceId批量查询设备")
	@ApiIgnore
	public DataResponse<Collection<DeviceListDO>> queryBatchByIds(@RequestParam(required = false) Boolean isNeedExt,
																  @RequestBody List<Long> deviceIds) {
		return DataResponse.success(deviceListService.getDeviceByIds(deviceIds.stream()
				.map(String :: valueOf).collect(Collectors.toList()), isNeedExt));
	}

	@PostMapping("/device/saveNewDevice")
	@ApiOperation(value = "保存新的设备")
	@ApiIgnore
	public EmptyResponse saveNewDevice(@RequestBody DeviceListDO deviceListDO) {
		deviceListService.saveNewDevice(deviceListDO);
        return DataResponse.success();
    }

	@PostMapping("/device/updateById")
	@ApiOperation(value = "更新设备")
	@ApiIgnore
	public EmptyResponse updateById(@RequestBody DeviceListDO deviceListDO) {
		deviceListService.updDevice(deviceListDO);
		return EmptyResponse.success();
	}

	@GetMapping("/device/nowBindDeviceList")
	@ApiOperation(value = "现在绑定的设备列表")
	@ApiIgnore
	public DataResponse<List<DeviceListDO>> nowBindDeviceList(
			@RequestParam("wifiSn") String wifiSn
	) {
        return DataResponse.success(deviceListService.getNowBindDeviceList(wifiSn));
    }

	@GetMapping("/device/listOtherBindDevice")
	@ApiOperation(value = "其他绑定的设备列表")
	@ApiIgnore
	public DataResponse<List<DeviceListDO>> listOtherBindDevice(
			@RequestParam("wifiSn") String wifiSn,
			@RequestParam("deviceName") String deviceName
	) {
		return DataResponse.success(deviceListService.getOtherBindDeviceList(wifiSn, deviceName));
	}

	@GetMapping("/version")
	@ApiOperation(value = "ecos 版本信息")
	@ApiIgnore
	public DataResponse<GlobalVersionDto> ecosLatestVersion(@RequestParam("language") String language) {
		return DataResponse.success(clientGlobalVersionService.latestVersion(language));
	}

	@GetMapping("/enestVersion")
	@ApiOperation(value = "enest 版本信息")
	@ApiIgnore
	public DataResponse<GlobalEnestLatestVersionDto> enestLatestVersion(@RequestParam("language") String language) {
		return DataResponse.success(enestVersionService.latestVersion(language));
	}

	@GetMapping("/account/list")
	@ApiOperation(value = "主子账号列表")
	public DataResponse<EcosAccountDto> accountList(@RequestParam("deviceSn") String deviceSn) {
		DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(deviceSn);
		return ecosClientApi.getDeviceMasterAndSubAccount(deviceInfo.getId());
	}

	@PutMapping("/device/unbind")
	@ApiOperation(value = "解绑设备")
	public EmptyResponse unbindDevice(@RequestParam("deviceSn") String deviceSn) {
		DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(deviceSn);
		return ecosClientApi.unbindAccount(new ClientUnBindAccountVo()
				.withDeviceIdList(Collections.singletonList(deviceInfo.getId())));
	}

	@PostMapping("/device/agent")
	@ApiOperation(value = "批量查询设备绑定的经销商")
	@ApiIgnore
	public DataResponse<List<BindInfoDTO>> batchBindInfo(@RequestBody List<Long> deviceIds) {
		BindQueryReqDTO req = BindQueryReqDTO.builder()
				.resourceIds(deviceIds.stream().map(String :: valueOf).collect(Collectors.toList()))
				.roleCategory(RoleConstants.ROLE_AGENT)
				.build();
		List<BindInfoDTO> batchBindInfo = authCenterResClient.getBatchBindInfo(req);
		return DataResponse.success(batchBindInfo);
	}

	@PostMapping("/charger/save")
	@ApiOperation(value = "充电桩增加新设备")
	public EmptyResponse saveCharger(@RequestBody ChargerSaveVO chargerSaveVO) {
		chargerInfoService.saveNewCharger(chargerSaveVO);
		return DataResponse.success();
	}

	@PostMapping("/charger/upd")
	@ApiOperation(value = "充电桩更新设备")
	public EmptyResponse updCharger(@RequestBody ChargerSaveVO chargerSaveVO) {
		chargerInfoService.updCharger(chargerSaveVO);
		return DataResponse.success();
	}

	@PostMapping("/charger/ext/upd")
	@ApiOperation(value = "更新充电桩扩展信息")
	public EmptyResponse updChargerExt(@RequestBody ChargerInfoDO param) {
		// todo 改为通用ext扩展接口
		ExtCommonService<ChargerInfoDO> extCommonService = strategyService.chooseExtServ(SeriesEnum.AC_Charger.getId());
		extCommonService.updExtByDeviceId(param);
		return DataResponse.success();
	}

	@PostMapping("/socket/save")
	@ApiOperation(value = "单插增加新设备")
	public EmptyResponse saveSocket(@RequestBody DeviceListDO deviceInfo) {
		socketInfoService.saveNewSocket(deviceInfo);
		return DataResponse.success();
	}

	@PostMapping("/socket/upd")
	@ApiOperation(value = "单插更新设备")
	public EmptyResponse updSocket(@RequestBody DeviceListDO deviceInfo) {
		socketInfoService.updSocket(deviceInfo);
		return DataResponse.success();
	}

	@PostMapping("/install/bound")
	@ApiOperation(value = "安装商绑定或解绑")
	public EmptyResponse boundInstall(@RequestBody @Valid InstallBoundDTO item) {
		deviceBoundService.boundInstall(item);
		return DataResponse.success();
	}

	@GetMapping("/account/device/list")
	@ApiOperation(value = "各类型设备列表")
	public DataResponse<AccountBindInfoDTO> getDevicesByAccount(@RequestParam("account") String account) {
		return DataResponse.success(deviceBoundService.getDevicesByAccount(account));
	}

	@GetMapping("/install/info")
	@ApiOperation("查询设备绑定安装商信息")
	public DataResponse<InstallBoundInfoDTO> getBindInstallInfo(@RequestParam String deviceId) {
		return DataResponse.success(deviceBoundService.getBindInfo(deviceId, RoleConstants.ROLE_INSTALLER));
	}

}
