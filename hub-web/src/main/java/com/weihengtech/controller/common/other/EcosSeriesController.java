package com.weihengtech.controller.common.other;

import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dtos.other.EcosSeriesDTO;
import com.weihengtech.pojo.vos.ecosseries.SeriesInsertVO;
import com.weihengtech.pojo.vos.ecosseries.SeriesUpdateVO;
import com.weihengtech.service.other.EcosSeriesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("series")
@Api(tags = "15_产品系列")
public class EcosSeriesController {

	@Resource
	private EcosSeriesService ecosSeriesService;

	@ApiIgnore
	@PostMapping("insert")
	@ApiOperation(value = "新增系列")
	public EmptyResponse insertSeries(@RequestBody @Valid SeriesInsertVO seriesInsertVO) {
		ecosSeriesService.insertSeries(seriesInsertVO);
		return EmptyResponse.success();
	}

	@ApiIgnore
	@PostMapping("update")
	@ApiOperation(value = "修改指定系列数据")
	public EmptyResponse updateSeries(@RequestBody @Valid SeriesUpdateVO seriesUpdateVO) {
		seriesUpdateVO.checkParams();
		ecosSeriesService.updateSeries(seriesUpdateVO);
		return EmptyResponse.success();
	}

	@GetMapping("query")
	@ApiOperation(value = "查询系列列表")
	public DataResponse<List<EcosSeriesDTO>> querySeries() {
		return DataResponse.success(ecosSeriesService.querySeries());
	}
}
