package com.weihengtech.controller.common.ecos;

import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.pojo.dos.ecos.ClientAgreementVersionDo;
import com.weihengtech.pojo.dos.ecos.ClientGlobalConfigDo;
import com.weihengtech.pojo.dos.ecos.ClientGlobalVersionDo;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.dtos.ecos.EcosClientGlobalVersionDto;
import com.weihengtech.pojo.vos.parent.PageInfoVO;
import com.weihengtech.pojo.vos.ecos.EcosGlobalAgreementVersionAddVo;
import com.weihengtech.pojo.vos.ecos.EcosGlobalAgreementVersionDelVo;
import com.weihengtech.pojo.vos.ecos.EcosGlobalConfigVo;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionAddVo;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionIdVo;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionPageVo;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionUpdateVo;
import com.weihengtech.service.ecos.ClientAgreementVersionService;
import com.weihengtech.service.ecos.ClientGlobalConfigService;
import com.weihengtech.service.ecos.ClientGlobalVersionService;
import com.weihengtech.utils.ActionFlagUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("ecos/global")
@Api(tags = "03_Ecos客户端配置")
public class ClientGlobalConfigController {

	@Resource
	private ClientGlobalConfigService clientGlobalConfigService;

	@Resource
	private ClientGlobalVersionService clientGlobalVersionService;

	@Resource
	private ClientAgreementVersionService clientAgreementVersionService;

	@PostMapping("config")
	@ApiOperation(value = "修改ecos客户端全局配置")
	public EmptyResponse configureGlobalConfig(@RequestBody @Valid EcosGlobalConfigVo ecosGlobalConfigVo) {
		ClientGlobalConfigDo clientGlobalConfigDo = clientGlobalConfigService.getById(1);
		CglibUtil.copy(ecosGlobalConfigVo, clientGlobalConfigDo);
		ActionFlagUtil.assertTrue(clientGlobalConfigService.updateById(clientGlobalConfigDo));
		return EmptyResponse.success();
	}

	@PostMapping("version/add")
	@ApiOperation(value = "新增ecos版本号")
	public EmptyResponse addClientVersion(@RequestBody @Valid EcosGlobalVersionAddVo ecosGlobalVersionAddVo) {
		clientGlobalVersionService.addClientVersion(ecosGlobalVersionAddVo);
		return EmptyResponse.success();
	}

	@PostMapping("version/del")
	@ApiOperation(value = "删除ecos版本号")
	public EmptyResponse delClientVersion(@RequestBody @Valid EcosGlobalVersionIdVo ecosGlobalVersionIdVo) {
		ActionFlagUtil.assertTrue(clientGlobalVersionService.removeById(ecosGlobalVersionIdVo.getId()));
		return EmptyResponse.success();
	}

	@PostMapping("version/update")
	@ApiOperation(value = "修改ecos版本号")
	public EmptyResponse updateClientVersion(@RequestBody @Valid EcosGlobalVersionUpdateVo ecosGlobalVersionUpdateVo) {
		clientGlobalVersionService.updateClientVersion(ecosGlobalVersionUpdateVo);
		return EmptyResponse.success();
	}

	@PostMapping("version/page")
	@ApiOperation(value = "分页查询ecos版本号")
	public DataResponse<PageInfoDTO<EcosClientGlobalVersionDto>> pageClientVersion(
			@RequestBody @Valid EcosGlobalVersionPageVo ecosGlobalVersionPageVo
	) {
		ecosGlobalVersionPageVo.checkParams();
		return DataResponse.success(clientGlobalVersionService.pageClientVersion(
				ecosGlobalVersionPageVo.getPageNum(),
				ecosGlobalVersionPageVo.getPageSize()
		));
	}

	@PostMapping("version/get")
	@ApiOperation(value = "查询指定id的ecos版本号")
	public DataResponse<EcosClientGlobalVersionDto> getClientVersion(
			@RequestBody @Valid EcosGlobalVersionIdVo ecosGlobalVersionIdVo
	) {
		ClientGlobalVersionDo clientGlobalVersionDo = Optional
				.ofNullable(clientGlobalVersionService.getById(ecosGlobalVersionIdVo.getId()))
				.orElseThrow(() -> new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION));
		EcosClientGlobalVersionDto ecosClientGlobalVersionDto = new EcosClientGlobalVersionDto();
		CglibUtil.copy(clientGlobalVersionDo, ecosClientGlobalVersionDto);
		return DataResponse.success(ecosClientGlobalVersionDto);
	}

	@PostMapping("agreement/add")
	@ApiOperation(value = "新增agreement版本号")
	public EmptyResponse addAgreementVersion(@RequestBody @Valid EcosGlobalAgreementVersionAddVo ecosGlobalAgreementVersionAddVo) {
		ClientAgreementVersionDo clientAgreementVersionDo = new ClientAgreementVersionDo();
		CglibUtil.copy(ecosGlobalAgreementVersionAddVo, clientAgreementVersionDo);
		ActionFlagUtil.assertTrue(clientAgreementVersionService.save(clientAgreementVersionDo));
		return EmptyResponse.success();
	}

	@PostMapping("agreement/del")
	@ApiOperation(value = "删除agreement版本号")
	public EmptyResponse delAgreementVersion(@RequestBody @Valid EcosGlobalAgreementVersionDelVo ecosGlobalAgreementVersionDelVo) {
		ActionFlagUtil.assertTrue(clientAgreementVersionService.removeById(ecosGlobalAgreementVersionDelVo.getId()));
		return EmptyResponse.success();
	}

	@PostMapping("agreement/page")
	@ApiOperation(value = "分页查询agreement版本号")
	public DataResponse<PageInfoDTO<ClientAgreementVersionDo>> pageAgreementVersion(
			@RequestBody @Valid PageInfoVO pageInfoVO
	) {
		pageInfoVO.checkParams();
		return DataResponse.success(clientAgreementVersionService.pageAgreementVersion(
				pageInfoVO.getPageNum(),
				pageInfoVO.getPageSize()
		));
	}

	@GetMapping("agreement/latest")
	@ApiOperation(value = "查询最新的agreement版本号")
	public DataResponse<ClientAgreementVersionDo> getAgreementVersion() {
		return DataResponse.success(clientAgreementVersionService.getOne(
				Wrappers.<ClientAgreementVersionDo>lambdaQuery().orderByDesc(ClientAgreementVersionDo::getId).last("LIMIT 1"))
		);
	}
}
