package com.weihengtech.controller.common.firmware;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.pojo.dtos.firmware.HardwareCategoryTypeMapDTO;
import com.weihengtech.pojo.dtos.firmware.HardwareVersionAllDTO;
import com.weihengtech.pojo.dtos.firmware.HardwareVersionPageDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.dtos.firmware.SoftwareVersionAllDTO;
import com.weihengtech.pojo.dtos.firmware.SoftwareVersionPageDTO;
import com.weihengtech.pojo.vos.firmware.HardwareVersionInsertVO;
import com.weihengtech.pojo.vos.firmware.HardwareVersionPageVO;
import com.weihengtech.pojo.vos.firmware.HardwareVersionUpdateVO;
import com.weihengtech.pojo.vos.firmware.RemarksVo;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionInsertVO;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionPageVO;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionUpdateVO;
import com.weihengtech.pojo.vos.firmware.VersionBaseVO;
import com.weihengtech.pojo.vos.firmware.VersionBatchDeleteVO;
import com.weihengtech.service.firmware.HardwareSoftwareVersionService;
import com.weihengtech.utils.OperatingRecordUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("version")
@Api(tags = "19_软硬件版本")
public class HardwareSoftwareVersionController {

	@Resource
	private HardwareSoftwareVersionService hardwareSoftwareVersionService;

	@PostMapping("software/insert")
	@ApiOperation(value = "新增软件版本")
	public EmptyResponse insertSoftwareVersion(
			@RequestBody @Valid SoftwareVersionInsertVO softwareVersionInsertVO
	) {
		hardwareSoftwareVersionService.insertSoftwareVersion(softwareVersionInsertVO);
		// tag
		OperatingRecordUtil.log(
				RecordContentConstants.INSERT_SOFTWARE_VERSION,
				MapUtil.<String, String>builder()
						.put("softwareVersionName", softwareVersionInsertVO.getSoftwareVersion())
						.put(
								"hardwareTypeId",
								JSONUtil.toJsonStr(StrUtil.isBlank(softwareVersionInsertVO.getHardwareTypeId())
										? ""
										: softwareVersionInsertVO.getHardwareTypeId())
						)
						.put(
								"hardwareVersionIdList",
								JSONUtil.toJsonStr(CollUtil.isEmpty(softwareVersionInsertVO.getHardwareVersionList())
										? ListUtil.empty()
										: softwareVersionInsertVO.getHardwareVersionList())
						)
						.build(),
				EnumConstants.RecordModule.FIRMWARE_MAINTAIN.getCode()
		);
		return EmptyResponse.success();
	}

	@PostMapping("hardware/insert")
	@ApiOperation(value = "新增硬件版本")
	public EmptyResponse insertHardwareVersion(
			@RequestBody @Valid HardwareVersionInsertVO hardwareVersionInsertVO
	) {
		hardwareSoftwareVersionService.insertHardwareVersion(hardwareVersionInsertVO);
		// tag
		OperatingRecordUtil.log(
				RecordContentConstants.INSERT_HARDWARE_VERSION,
				MapUtil.<String, String>builder()
						.put("hardwareVersionName", hardwareVersionInsertVO.getHardwareVersion())
						.put(
								"typeId",
								JSONUtil.toJsonStr(StrUtil.isBlank(hardwareVersionInsertVO.getTypeId())
										? ""
										: hardwareVersionInsertVO.getTypeId())
						)
						.put(
								"softwareVersionIdList",
								JSONUtil.toJsonStr(CollUtil.isEmpty(hardwareVersionInsertVO.getSoftwareVersionList())
										? ListUtil.empty()
										: hardwareVersionInsertVO.getSoftwareVersionList())
						)
						.build(),
				EnumConstants.RecordModule.FIRMWARE_MAINTAIN.getCode()
		);
		return EmptyResponse.success();
	}

	@PostMapping("software/update")
	@ApiOperation(value = "更新软件版本")
	public EmptyResponse updateSoftwareVersion(
			@RequestBody @Valid SoftwareVersionUpdateVO softwareVersionUpdateVO
	) {
		hardwareSoftwareVersionService.updateSoftwareVersion(softwareVersionUpdateVO);
		// tag
		OperatingRecordUtil.log(
				RecordContentConstants.UPDATE_SOFTWARE_VERSION,
				MapUtil.<String, String>builder()
						.put("softwareVersionId", softwareVersionUpdateVO.getSoftwareVersionId())
						.put(
								"softwareVersionName",
								JSONUtil.toJsonStr(StrUtil.isBlank(softwareVersionUpdateVO.getSoftwareVersion())
										? ""
										: softwareVersionUpdateVO.getSoftwareVersion())
						)
						.put(
								"hardwareVersionIdList",
								JSONUtil.toJsonStr(CollUtil.isEmpty(softwareVersionUpdateVO.getHardwareVersionList())
										? ListUtil.empty()
										: softwareVersionUpdateVO.getHardwareVersionList())
						)
						.build(),
				EnumConstants.RecordModule.FIRMWARE_MAINTAIN.getCode()
		);
		return EmptyResponse.success();
	}

	@PostMapping("hardware/update")
	@ApiOperation(value = "更新硬件版本")
	public EmptyResponse updateHardwareVersion(
			@RequestBody @Valid HardwareVersionUpdateVO hardwareVersionUpdateVO
	) {
		hardwareSoftwareVersionService.updateHardwareVersion(hardwareVersionUpdateVO);
		// tag
		OperatingRecordUtil.log(
				RecordContentConstants.UPDATE_HARDWARE_VERSION,
				MapUtil.<String, String>builder()
						.put("hardwareVersionId", hardwareVersionUpdateVO.getHardwareVersionId())
						.put(
								"hardwareVersionName",
								JSONUtil.toJsonStr(StrUtil.isBlank(hardwareVersionUpdateVO.getHardwareVersion())
										? ""
										: hardwareVersionUpdateVO.getHardwareVersion())
						)
						.put(
								"softwareVersionIdList",
								JSONUtil.toJsonStr(CollUtil.isEmpty(hardwareVersionUpdateVO.getSoftwareVersionList())
										? ListUtil.empty()
										: hardwareVersionUpdateVO.getSoftwareVersionList())
						)
						.build(),
				EnumConstants.RecordModule.FIRMWARE_MAINTAIN.getCode()
		);
		return EmptyResponse.success();
	}

	@PostMapping("software/remarks")
	@ApiOperation(value = "软件版本添加备注")
	public EmptyResponse softwareRemarks(@RequestBody @Valid RemarksVo remarksVo) {
		hardwareSoftwareVersionService.softwareRemarks(remarksVo);
		return EmptyResponse.success();
	}

	@PostMapping("hardware/remarks")
	@ApiOperation(value = "硬件版本添加备注")
	public EmptyResponse hardwareRemarks(@RequestBody @Valid RemarksVo remarksVo) {
		hardwareSoftwareVersionService.hardwareRemarks(remarksVo);
		return EmptyResponse.success();
	}

	@PostMapping("hardware/all")
	@ApiOperation(value = "软件版本新增和编辑页面显示信息")
	public DataResponse<HardwareVersionAllDTO> queryAllHardwareVersion(@RequestBody @Valid VersionBaseVO versionBaseVo) {
		return DataResponse.success(hardwareSoftwareVersionService.queryAllHardwareVersion(versionBaseVo));
	}

	@PostMapping("software/all")
	@ApiOperation(value = "硬件版本新增和编辑页面显示信息")
	public DataResponse<SoftwareVersionAllDTO> queryAllSoftwareVersion(
			@RequestBody @Valid VersionBaseVO versionBaseVo
	) {
		return DataResponse.success(hardwareSoftwareVersionService.queryAllSoftwareVersion(versionBaseVo));
	}

	@PostMapping("software/delete")
	@ApiOperation(value = "删除指定软件版本, 已绑定的无法删除")
	public EmptyResponse batchDeleteSoftwareVersion(
			@RequestBody @Valid VersionBatchDeleteVO versionBatchDeleteVO
	) {
		hardwareSoftwareVersionService.batchDeleteSoftwareVersion(versionBatchDeleteVO);
		// tag
		OperatingRecordUtil.log(Long.parseLong(UserInfoUtil.currentUserId()), RecordContentConstants.DELETE_SOFTWARE_VERSION,
				MapUtil.<String, String>builder()
						.put("softwareVersionIdList", JSONUtil.toJsonStr(versionBatchDeleteVO.getVersionIdList()))
						.build(),
				EnumConstants.RecordModule.FIRMWARE_MAINTAIN.getCode()
		);
		return EmptyResponse.success();
	}

	@PostMapping("hardware/delete")
	@ApiOperation(value = "删除指定硬件版本, 已绑定的无法删除")
	public EmptyResponse batchDeleteHardwareVersion(
			@RequestBody @Valid VersionBatchDeleteVO versionBatchDeleteVO
	) {
		hardwareSoftwareVersionService.batchDeleteHardwareVersion(versionBatchDeleteVO);
		// tag
		OperatingRecordUtil.log(Long.parseLong(UserInfoUtil.currentUserId()), RecordContentConstants.DELETE_HARDWARE_VERSION,
				MapUtil.<String, String>builder()
						.put("hardwareVersionIdList", JSONUtil.toJsonStr(versionBatchDeleteVO.getVersionIdList()))
						.build(),
				EnumConstants.RecordModule.FIRMWARE_MAINTAIN.getCode()
		);
		return EmptyResponse.success();
	}

	@PostMapping("hardware/page")
	@ApiOperation(value = "硬件版本分页")
	public DataResponse<PageInfoDTO<HardwareVersionPageDTO>> pageHardwareVersion(
			@RequestBody HardwareVersionPageVO hardwareVersionPageVO
	) {
		hardwareVersionPageVO.checkParams();
		return DataResponse.success(hardwareSoftwareVersionService.pageHardwareVersion(hardwareVersionPageVO));
	}

	@PostMapping("software/page")
	@ApiOperation(value = "软件版本分页")
	public DataResponse<PageInfoDTO<SoftwareVersionPageDTO>> pageSoftwareVersion(
			@RequestBody SoftwareVersionPageVO softwareVersionPageVO
	) {
		softwareVersionPageVO.checkParams();
		return DataResponse.success(hardwareSoftwareVersionService.pageSoftwareVersion(softwareVersionPageVO));
	}

	@GetMapping("hardware/map")
	@ApiOperation(value = "硬件分页与厂商映射")
	public DataResponse<List<HardwareCategoryTypeMapDTO>> mapHardwareCategoryType() {
		return DataResponse.success(hardwareSoftwareVersionService.mapHardwareCategoryType());
	}
}
