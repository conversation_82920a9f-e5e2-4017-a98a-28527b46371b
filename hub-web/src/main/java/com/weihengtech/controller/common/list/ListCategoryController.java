package com.weihengtech.controller.common.list;

import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.dos.list.ListCategoryDO;
import com.weihengtech.service.list.ListCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: jiahao.jin
 * @create: 2025-07-15 16:00
 * @description:
 */
@RestController
@RequestMapping("/list/category")
@Api(tags = "32_名单分类")
public class ListCategoryController {

    @Resource
    private ListCategoryService listCategoryService;

    // 查询所有分类
    @GetMapping("/all")
    @ApiOperation(value = "查询所有分类")
    public DataResponse<List<ListCategoryDO>> listAll() {
        return DataResponse.success(listCategoryService.listAll());
    }
}
