package com.weihengtech.controller.common.other;

import com.weihengtech.auth.model.dto.CountryDTO;
import com.weihengtech.common.DataResponse;
import com.weihengtech.service.other.EcosCountryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("country")
@Api(tags = "13_国家地区信息")
public class EcosCountryController {

	@Resource
	private EcosCountryService ecosCountryService;

	@GetMapping("query")
	@ApiOperation(value = "获取国家列表")
	public DataResponse<List<CountryDTO>> queryCountryList() {
		return DataResponse.success(ecosCountryService.queryCountryList());
	}

	@PostMapping("ip")
	@ApiOperation(value = "批量翻译IP为归属地")
	public DataResponse<Map<String, String>> queryIpList(@RequestBody List<String> ipList) {
		return DataResponse.success(ecosCountryService.queryIpList(ipList));
	}
}
