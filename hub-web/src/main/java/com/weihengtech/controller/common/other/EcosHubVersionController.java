package com.weihengtech.controller.common.other;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.pojo.dos.other.EcosGlobalVersionDo;
import com.weihengtech.pojo.dtos.other.EcosGlobalVersionDto;
import com.weihengtech.pojo.dtos.other.EcosHubGlobalVersionDto;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.dtos.ecos.EcosClientGlobalVersionDto;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionAddVo;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionIdVo;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionPageVo;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionUpdateVo;
import com.weihengtech.service.other.EcosGlobalVersionService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.LocaleUtil;
import com.weihengtech.utils.OperatingRecordUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("global")
@Api(tags = "14_Hub版本")
public class EcosHubVersionController {

	@Resource
	private EcosGlobalVersionService ecosGlobalVersionService;

	@GetMapping("version")
	@ApiOperation(value = "最新hubApp版本")
	public DataResponse<EcosGlobalVersionDto> getLatestAppVersion(HttpServletRequest request) {
		EcosGlobalVersionDo latestVersion = ecosGlobalVersionService.getOne(
				Wrappers.<EcosGlobalVersionDo>lambdaQuery().orderByDesc(EcosGlobalVersionDo::getId).last("LIMIT 1"));
		EcosGlobalVersionDto ecosGlobalVersionDto = new EcosGlobalVersionDto();

		if (null == latestVersion) {
			return DataResponse.success(ecosGlobalVersionDto);
		}
		CglibUtil.copy(latestVersion, ecosGlobalVersionDto);

		EcosGlobalVersionDo lastForceUpdateVersion = ecosGlobalVersionService
				.getOne(Wrappers.<EcosGlobalVersionDo>lambdaQuery().orderByDesc(EcosGlobalVersionDo::getId)
						.eq(EcosGlobalVersionDo::getFlag, 2).last("LIMIT 1"));
		if (null == lastForceUpdateVersion) {
			ecosGlobalVersionDto.setPreForceAndroidVersion("");
			ecosGlobalVersionDto.setPreForceIosVersion("");
		} else {
			ecosGlobalVersionDto.setPreForceAndroidVersion(lastForceUpdateVersion.getAndroidVersion());
			ecosGlobalVersionDto.setPreForceIosVersion(lastForceUpdateVersion.getIosVersion());
		}
		try {
			String methodName = LocaleUtil.mapLocaleToDatabaseGetMethod(request);
			String content = ReflectUtil.invoke(latestVersion, methodName).toString();
			ecosGlobalVersionDto.setContent(content);
		} catch (Exception e) {
			ecosGlobalVersionDto.setContent(latestVersion.getEnUs());
		}
		return DataResponse.success(ecosGlobalVersionDto);
	}

	@PostMapping("version/add")
	@ApiOperation(value = "新增hub版本号")
	public EmptyResponse addHubVersion(@RequestBody @Valid EcosGlobalVersionAddVo ecosGlobalVersionAddVo) {
		ecosGlobalVersionService.addHubVersion(ecosGlobalVersionAddVo);
		return EmptyResponse.success();
	}

	@PostMapping("version/del")
	@ApiOperation(value = "删除hub版本号")
	public EmptyResponse delClientVersion(@RequestBody @Valid EcosGlobalVersionIdVo ecosGlobalVersionIdVo) {
		ActionFlagUtil.assertTrue(ecosGlobalVersionService.removeById(ecosGlobalVersionIdVo.getId()));
		// tag
		OperatingRecordUtil.log(
				RecordContentConstants.DELETE_HUB_VERSION,
				MapUtil.<String, String>builder().put("versionId", ecosGlobalVersionIdVo.getId()).build(),
				EnumConstants.RecordModule.VERSION_MAINTAIN.getCode()
		);
		return EmptyResponse.success();
	}

	@PostMapping("version/update")
	@ApiOperation(value = "修改hub版本号")
	public EmptyResponse updateHubVersion(@RequestBody @Valid EcosGlobalVersionUpdateVo ecosGlobalVersionUpdateVo) {
		ecosGlobalVersionService.updateHubVersion(ecosGlobalVersionUpdateVo);
		return EmptyResponse.success();
	}

	@PostMapping("version/page")
	@ApiOperation(value = "分页查询hub版本号")
	public DataResponse<PageInfoDTO<EcosHubGlobalVersionDto>> pageClientVersion(
			@RequestBody @Valid EcosGlobalVersionPageVo ecosGlobalVersionPageVo
	) {
		ecosGlobalVersionPageVo.checkParams();
		return DataResponse.success(ecosGlobalVersionService.pageHubVersion(
				ecosGlobalVersionPageVo.getPageNum(),
				ecosGlobalVersionPageVo.getPageSize()
		));
	}

	@PostMapping("version/get")
	@ApiOperation(value = "查询指定id的hub版本号")
	public DataResponse<EcosClientGlobalVersionDto> getClientVersion(
			@RequestBody @Valid EcosGlobalVersionIdVo ecosGlobalVersionIdVo
	) {
		EcosGlobalVersionDo ecosGlobalVersionDo = Optional
				.ofNullable(ecosGlobalVersionService.getById(ecosGlobalVersionIdVo.getId()))
				.orElseThrow(() -> new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION));
		EcosClientGlobalVersionDto ecosClientGlobalVersionDto = new EcosClientGlobalVersionDto();
		CglibUtil.copy(ecosGlobalVersionDo, ecosClientGlobalVersionDto);
		return DataResponse.success(ecosClientGlobalVersionDto);
	}
}
