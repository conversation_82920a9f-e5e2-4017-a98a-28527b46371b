package com.weihengtech.controller.common.device;

import com.weihengtech.auth.dto.AvailableUserDTO;
import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.bos.device.AccountWithRoleBo;
import com.weihengtech.service.device.AvailableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 获取用户接口
 *
 * <AUTHOR>
 * @date 2023/11/23 9:23
 * @version 1.0
 */
@RestController
@RequestMapping("available")
@Api(tags = "09_可用账号")
public class AvailableController {

	@Resource
	private AvailableService availableService;

	@GetMapping("/top_user")
	@ApiOperation(value = "一级账号列表")
	public DataResponse<List<AvailableUserDTO>> topAccountList() {
		return DataResponse.success(availableService.topAccountList());
	}

	@GetMapping("/sec_user")
	@ApiOperation(value = "二级账号列表")
	public DataResponse<List<AvailableUserDTO>> secAccountList() {
		return DataResponse.success(availableService.secAccountList());
	}

	@GetMapping("/role/{roleId}")
	@ApiOperation(value = "指定角色账号")
	public DataResponse<List<AccountWithRoleBo>> roleUserList(@PathVariable("roleId") String roleId) {
		return DataResponse.success(availableService.getRoleUserList(roleId));
	}
}
