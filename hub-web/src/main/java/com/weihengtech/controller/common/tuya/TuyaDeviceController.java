package com.weihengtech.controller.common.tuya;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.vos.device.DeviceSyncStateVo;
import com.weihengtech.pojo.vos.tuya.TuyaDeviceEventQueryVo;
import com.weihengtech.pojo.vos.tuya.TuyaDeviceEventVo;
import com.weihengtech.pojo.vos.tuya.TuyaSyncDatacenterVo;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.ecos.DeviceEventService;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.utils.SnowFlakeUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("tuya/device")
@Api(tags = "08_涂鸦模块相关")
@ApiIgnore
@Slf4j
public class TuyaDeviceController {

    @Resource
    private DeviceEventService deviceEventService;
    @Resource
    private DeviceListService deviceListService;
    @Resource
    private SnowFlakeUtil snowFlakeUtil;
    @Resource
    private TuyaDatacenterService tuyaDatacenterService;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @PostMapping("/saveEvent")
    @ApiOperation("保存设备事件")
    public EmptyResponse saveDeviceEvent(@RequestBody TuyaDeviceEventVo tuyaDeviceEventVo) {
        deviceEventService.save(tuyaDeviceEventVo);
        return EmptyResponse.success();
    }

    @PostMapping("/existEvent")
    @ApiOperation("是否存在事件")
    public DataResponse<Boolean> existDeviceEvent(@RequestBody TuyaDeviceEventQueryVo tuyaDeviceEventQueryVo) {
        return DataResponse.success(deviceEventService.exist(tuyaDeviceEventQueryVo));
    }

    @GetMapping("/getDeviceBeanByWifiSn")
    @ApiOperation("通过wifiSn获取设备")
    public DataResponse<DeviceListDO> getDeviceBeanByWifiSn(@RequestParam("wifiSn") String wifiSn) {
        return DataResponse.success(deviceListService.getDeviceBeanByWifiSn(wifiSn));
    }

    @GetMapping("/getDeviceBeanByDeviceName")
    @ApiOperation("通过deviceName获取设备")
    public DataResponse<DeviceListDO> getDeviceBeanByDeviceName(@RequestParam("deviceName") String deviceName) {
        return DataResponse.success(deviceListService.getDeviceInfoByDeviceName(deviceName));
    }

    @GetMapping("/getAuDeviceList")
    @ApiOperation("获取AU的DeviceList")
    public DataResponse<List<String>> getAuDeviceSnList() {
        return DataResponse.success(deviceListService.listAuDeviceSn());
    }

    @PostMapping("/saveNewDevice")
    @ApiOperation("涂鸦保存新设备")
    public EmptyResponse saveNewDevice(@RequestBody DeviceListDO deviceListDO) {
        if (null != deviceListDO && StrUtil.isNotBlank(deviceListDO.getDeviceName())) {
            deviceListDO.setId(snowFlakeUtil.generateId());
            deviceListService.saveNewDevice(deviceListDO);
        }
        return EmptyResponse.success();
    }

    @PostMapping("/updateDevice")
    @ApiOperation("涂鸦更新设备信息")
    public EmptyResponse updateDevice(@RequestBody DeviceListDO deviceListDO) {
        if (null != deviceListDO && null != deviceListDO.getId()) {
            Long deviceId = deviceListDO.getId();
            DeviceSyncStateVo deviceSyncStateVo = new DeviceSyncStateVo().withDeviceId(String.valueOf(deviceId));
            threadPoolTaskExecutor.execute(() -> {
                try {
                    log.info("tuya updateDevice: {}", deviceId);
                    Integer integer = deviceListService.syncDeviceState(deviceSyncStateVo);
                    if (integer >= 0) {
                        DeviceListDO currentDevice = deviceListService.getDeviceById(deviceId);
                        if (Objects.equals(currentDevice.getWifiSn(), deviceListDO.getWifiSn())) {
                            deviceListService.updateById(deviceListDO);
                        }
                    }
                } catch (Exception e) {
                    log.warn("Tuya updateDevice error: {} {}", deviceListDO.getDeviceName(), e.getMessage());
                }
            });
        }
        return EmptyResponse.success();
    }

    @PostMapping("/syncDatacenter")
    @ApiOperation("涂鸦同步数据中心")
    public EmptyResponse syncDatacenter(@RequestBody @Valid TuyaSyncDatacenterVo tuyaSyncDatacenterVo) {
        if (CollUtil.isNotEmpty(tuyaSyncDatacenterVo.getWifiSnList())) {
            tuyaDatacenterService.syncDatacenter(tuyaSyncDatacenterVo.getWifiSnList());
        }
        return EmptyResponse.success();
    }
}
