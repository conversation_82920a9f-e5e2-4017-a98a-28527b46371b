package com.weihengtech.controller.common.systeminfo;


import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dos.systeminfo.SystemInfoDO;
import com.weihengtech.pojo.vos.systeminfo.SettingTransferVO;
import com.weihengtech.pojo.vos.systeminfo.SystemInfoSaveResVO;
import com.weihengtech.pojo.vos.systeminfo.SystemInfoSaveVO;
import com.weihengtech.pojo.vos.systeminfo.SystemInfoUpdVO;
import com.weihengtech.service.systeminfo.SystemInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 系统信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@RestController
@RequestMapping("/system_info")
@Api(tags = "13_配网系统模块")
public class SystemInfoController {

    @Autowired
    private SystemInfoService systemInfoService;

    @PostMapping("")
    @ApiOperation(value = "保存系统详情")
    public DataResponse<SystemInfoSaveResVO> saveSystemInfo(
            @RequestBody @Valid SystemInfoSaveVO saveVO
    ) {
        SystemInfoSaveResVO res = systemInfoService.saveInfo(saveVO);
        return DataResponse.success(res);
    }

    @GetMapping("/query/{id}")
    @ApiOperation(value = "查看系统详情")
    public DataResponse<SystemInfoDO> getSystemInfo(@PathVariable Long id) {
        SystemInfoDO res = systemInfoService.getSysInfo(id);
        return DataResponse.success(res);
    }

    @PutMapping("")
    @ApiOperation(value = "更新系统详情")
    public EmptyResponse updSystemInfo(@RequestBody @Valid SystemInfoUpdVO updVO) {
        systemInfoService.updById(updVO);
        return EmptyResponse.success();
    }

    @GetMapping("/list")
    @ApiOperation(value = "获取系统列表")
    public DataResponse<List<SystemInfoDO>> getSystemInfo() {
        List<SystemInfoDO> resList = systemInfoService.listByAccount(Long.parseLong(UserInfoUtil.currentUserId()));
        return DataResponse.success(resList);
    }

    @PostMapping("/qr_code")
    @ApiOperation(value = "生成二维码需要密文")
    public DataResponse<String> qrCodeEncryption(@RequestBody @Valid SettingTransferVO dto) {
        return DataResponse.success(systemInfoService.qrCodeEncryption(dto));
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除未完成转移的系统")
    public EmptyResponse deleteSystem(@PathVariable Long id) {
        systemInfoService.removeUnFinishedSystem(id);
        return EmptyResponse.success();
    }

}

