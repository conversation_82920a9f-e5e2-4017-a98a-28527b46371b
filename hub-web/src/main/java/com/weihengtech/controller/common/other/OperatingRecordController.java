package com.weihengtech.controller.common.other;

import cn.hutool.core.lang.Dict;
import com.weihengtech.common.DataResponse;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.pojo.dtos.other.OperatingRecordPageDto;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.other.OperatingRecordPageVo;
import com.weihengtech.service.other.OperatingRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("operating")
@Api(tags = "22_操作记录")
public class OperatingRecordController {

	@Resource
	private OperatingRecordService operatingRecordService;

	@GetMapping("typeList")
	@ApiOperation(value = "操作记录类型列表")
	public DataResponse<List<Dict>> operatingTypeList() {
		return DataResponse.success(EnumConstants.RecordModule.recordModuleList());
	}

	@PostMapping("page")
	@ApiOperation(value = "操作记录分页查询")
	public DataResponse<PageInfoDTO<OperatingRecordPageDto>> pageOperatingRecord(
			@RequestBody @Valid OperatingRecordPageVo operatingRecordPageVo
	) {
		operatingRecordPageVo.checkParams();
		return DataResponse.success(operatingRecordService.pageOperatingRecord(operatingRecordPageVo));
	}
}
