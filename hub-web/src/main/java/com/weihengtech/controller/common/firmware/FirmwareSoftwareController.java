package com.weihengtech.controller.common.firmware;

import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dtos.firmware.FirmwareAllDTO;
import com.weihengtech.pojo.dtos.firmware.FirmwarePageDTO;
import com.weihengtech.pojo.dtos.firmware.FirmwareUpgradeRecordPageDTO;
import com.weihengtech.pojo.dtos.firmware.FirmwareUploadAllDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.firmware.FirmwareDelVO;
import com.weihengtech.pojo.vos.firmware.FirmwarePageVO;
import com.weihengtech.pojo.vos.firmware.FirmwareTopVO;
import com.weihengtech.pojo.vos.firmware.FirmwareUpgradeRecordPageVO;
import com.weihengtech.pojo.vos.firmware.RemarksVo;
import com.weihengtech.pojo.vos.firmware.VersionBaseVO;
import com.weihengtech.service.firmware.FirmwareSoftwareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("firmware")
@Api(tags = "17_固件包维护")
public class FirmwareSoftwareController {

	@Resource
	private FirmwareSoftwareService firmwareSoftwareService;

	@PostMapping("/software/all")
	@ApiOperation(value = "获取固件包新增编译页面需要的软件版本信息")
	public DataResponse<List<FirmwareUploadAllDTO>> queryAllSoftwareVersion(@RequestBody VersionBaseVO versionBaseVo) {
		return DataResponse.success(firmwareSoftwareService.queryAllSoftwareVersion(versionBaseVo));
	}

	@PostMapping("/page")
	@ApiOperation(value = "固件包分页")
	public DataResponse<PageInfoDTO<FirmwarePageDTO>> pageFirmware(@RequestBody FirmwarePageVO firmwarePageVO) {
		firmwarePageVO.checkParams();
		return DataResponse.success(firmwareSoftwareService.pageFirmware(firmwarePageVO));
	}

	@PostMapping("/del")
	@ApiOperation(value = "删除指定固件")
	public EmptyResponse delFirmware(@RequestBody @Valid FirmwareDelVO firmwareDelVO) {
		firmwareSoftwareService.delFirmware(firmwareDelVO);
		return EmptyResponse.success();
	}

	@PostMapping("upgrade/record/page")
	@ApiOperation(value = "固件软件版本升级记录")
	public DataResponse<PageInfoDTO<FirmwareUpgradeRecordPageDTO>> pageUpgradeRecord(
			@RequestBody @Valid FirmwareUpgradeRecordPageVO firmwareUpgradeRecordPageVO
	) {
		firmwareUpgradeRecordPageVO.checkParams();
		return DataResponse.success(firmwareSoftwareService.pageUpgradeRecord(firmwareUpgradeRecordPageVO));
	}

	@GetMapping("all")
	@ApiOperation(value = "固件包选择列表")
	public DataResponse<List<FirmwareAllDTO>> allFirmware(@RequestParam(required = false) String model) {
		return DataResponse.success(firmwareSoftwareService.allFirmware(model));
	}

	@PostMapping("remarks")
	@ApiOperation(value = "固件包添加备注")
	public EmptyResponse remarks(@RequestBody @Valid RemarksVo remarksVo) {
		firmwareSoftwareService.remarks(remarksVo);
		return EmptyResponse.success();
	}

	@PostMapping("top")
	@ApiOperation(value = "置顶")
	public EmptyResponse top(@RequestBody @Valid FirmwareTopVO topVO) {
		firmwareSoftwareService.top(topVO);
		return EmptyResponse.success();
	}
}
