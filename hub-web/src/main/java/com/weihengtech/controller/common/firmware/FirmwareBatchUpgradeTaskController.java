package com.weihengtech.controller.common.firmware;

import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dos.firmware.DeviceUpgradeDetailDO;
import com.weihengtech.pojo.dtos.device.DeviceListPageDTO;
import com.weihengtech.pojo.dtos.firmware.FirmwareBatchUpgradeTaskDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.firmware.DeviceUpgradeDetailPageVo;
import com.weihengtech.pojo.vos.firmware.FirmwareBatchUpgradeDevicePageVo;
import com.weihengtech.pojo.vos.firmware.FirmwareBatchUpgradeTaskPageVo;
import com.weihengtech.pojo.vos.firmware.FirmwareBatchUpgradeTaskVo;
import com.weihengtech.service.firmware.DeviceUpgradeDetailService;
import com.weihengtech.service.firmware.FirmwareBatchUpgradeTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @author: jiahao.jin
 * @create: 2025-07-15 16:00
 * @description:
 */
@RestController
@RequestMapping("batchUpgrade")
@Api(tags = "31_批量升级")
public class FirmwareBatchUpgradeTaskController {

    @Resource
    private FirmwareBatchUpgradeTaskService firmwareBatchUpgradeTaskService;
    @Resource
    private DeviceUpgradeDetailService deviceUpgradeDetailService;

    @PostMapping("/add")
    @ApiOperation(value = "添加批量升级任务")
    public EmptyResponse addBatchUpgradeTask(@RequestBody @Valid FirmwareBatchUpgradeTaskVo firmwareBatchUpgradeTaskVo) {
        firmwareBatchUpgradeTaskService.addBatchUpgradeTask(firmwareBatchUpgradeTaskVo);
        return EmptyResponse.success();
    }

    // 批量升级任务分页查询接口
    @PostMapping("/page")
    @ApiOperation(value = "批量升级任务分页查询")
    public DataResponse<PageInfoDTO<FirmwareBatchUpgradeTaskDTO>> pageBatchUpgradeTask(@RequestBody FirmwareBatchUpgradeTaskPageVo firmwareBatchUpgradeTaskPageVo) {
        return DataResponse.success(firmwareBatchUpgradeTaskService.pageBatchUpgradeTask(firmwareBatchUpgradeTaskPageVo));
    }

    // 根据筛选条件查询要批量升级的设备的接口
    @PostMapping("/device/page")
    @ApiOperation(value = "根据筛选条件查询要批量升级的设备")
    public DataResponse<PageInfoDTO<DeviceListPageDTO>> pageBatchUpgradeDevice(@RequestBody FirmwareBatchUpgradeDevicePageVo firmwareBatchUpgradeDevicePageVo) {
        firmwareBatchUpgradeDevicePageVo.checkParams();
        return DataResponse.success(firmwareBatchUpgradeTaskService.pageBatchUpgradeDevice(firmwareBatchUpgradeDevicePageVo));
    }

    // 根据任务ID查询设备升级详情列表
    @PostMapping("/device/detail/page")
    @ApiOperation(value = "分页查询设备升级详情列表")
    public DataResponse<PageInfoDTO<DeviceUpgradeDetailDO>> pageDeviceUpgradeDetailByTaskId(@RequestBody DeviceUpgradeDetailPageVo deviceUpgradeDetailPageVo) {
        deviceUpgradeDetailPageVo.checkParams();
        return DataResponse.success(deviceUpgradeDetailService.pageDeviceUpgradeDetail(deviceUpgradeDetailPageVo));
    }

    // 终止批量升级任务
    @GetMapping("/stop")
    @ApiOperation(value = "终止批量升级任务")
    public EmptyResponse stopBatchUpgradeTask(@RequestParam String taskId) {
        firmwareBatchUpgradeTaskService.stopBatchUpgradeTask(taskId);
        return EmptyResponse.success();
    }
}
