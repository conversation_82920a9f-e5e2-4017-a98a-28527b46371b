package com.weihengtech.controller.common.mes;

import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.vos.mes.MesDeviceUnbindVo;
import com.weihengtech.service.mes.MesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("mes")
@Api(tags = "06_Mes模块相关")
public class MesController {

    @Resource
    private MesService mesService;

    @PostMapping("/device/unbind")
    @ApiOperation(value = "设备无状态解绑")
    public EmptyResponse mesDeviceUnbind(@RequestBody @Valid MesDeviceUnbindVo mesDeviceUnbindVo) {
        boolean success = mesService.deviceUnbind(mesDeviceUnbindVo.getDeviceSnList());
        if (success) {
            return EmptyResponse.success();
        }
        return EmptyResponse.fail();
    }
}
