package com.weihengtech.controller.common.device;

import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dos.device.RippleTemplateDO;
import com.weihengtech.pojo.dtos.device.RippleControlDTO;
import com.weihengtech.pojo.vos.device.DeviceSnVo;
import com.weihengtech.service.device.RippleTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/24 14:27
 * @version 1.0
 */
@RestController
@RequestMapping("/ripple")
@Api(tags = "29_脉冲控制")
public class RippleConfigController {

    @Resource
    private RippleTemplateService rippleTemplateService;

    @PostMapping("/query")
    @ApiOperation(value = "脉冲控制查询")
    public DataResponse<RippleControlDTO> rippleControlQuery(@RequestBody @Valid DeviceSnVo deviceSnVo) {
        return DataResponse.success(rippleTemplateService.rippleControlQuery(deviceSnVo.getDeviceName()));
    }

    @PostMapping("/config")
    @ApiOperation(value = "脉冲控制配置")
    public EmptyResponse rippleControlConfig(@RequestBody @Valid RippleControlDTO param) {
        rippleTemplateService.rippleControlConfig(param);
        return EmptyResponse.success();
    }

    @PostMapping("/template/save")
    @ApiOperation(value = "保存脉冲控制模板")
    public EmptyResponse saveRippleTemplate(@RequestBody RippleTemplateDO param) {
        rippleTemplateService.saveRippleTemplate(param);
        return EmptyResponse.success();
    }

    @GetMapping("/template/list")
    @ApiOperation(value = "获取脉冲控制模板")
    public DataResponse<List<RippleTemplateDO>> listRippleTemplate() {
        return DataResponse.success(rippleTemplateService.listRippleTemplate());
    }

    @DeleteMapping("/template/del")
    @ApiOperation(value = "删除脉冲控制")
    public EmptyResponse delRippleTemplate(@RequestParam Long id) {
        rippleTemplateService.removeById(id);
        return EmptyResponse.success();
    }
}
