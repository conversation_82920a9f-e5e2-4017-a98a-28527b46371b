package com.weihengtech.controller.common.other;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.dos.other.DictDO;
import com.weihengtech.pojo.dos.other.DictItemDO;
import com.weihengtech.pojo.dtos.other.DictItemDTO;
import com.weihengtech.service.other.DictItemService;
import com.weihengtech.service.other.DictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 固定电价api
 *
 * <AUTHOR>
 * @date 2025/4/28 16:16
 * @version 1.0
 */
@RestController
@RequestMapping("/dict")
@Api(tags = {"23_数据字典"})
public class DictController {
    @Resource
    private DictItemService dictItemService;

    @GetMapping("/item")
    @ApiOperation(value = "数据字典查询")
    public DataResponse<List<DictItemDTO>> query(@RequestParam String code,
                                                 @RequestParam Boolean isTranslate,
                                                 HttpServletRequest request) {
        List<DictItemDTO> resList = dictItemService.queryDictItemsByDictCode(code, isTranslate,
                request.getHeader("Language"));
        return DataResponse.success(resList);
    }
}
