package com.weihengtech.controller.common.firmware;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Enums;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.enums.firmware.UploadTypeEnum;
import com.weihengtech.service.firmware.FileUploadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("file")
@Api(tags = "16_文件上传")
public class FileUploadController {

	@Resource
	private FileUploadService fileUploadService;

	@PostMapping("/bin")
	@ApiOperation(value = "固件上传接口")
	public DataResponse<String> uploadHardwareBin(
			@RequestPart(value = "multipartFile") MultipartFile multipartFile,
			@RequestParam("softwareId") String softwareIdStr,
			@RequestParam("firmwareType") String firmwareType,
			@RequestParam("firmwareId") String firmwareId,
			@RequestParam("model") String model
	) {
		if (multipartFile == null || StrUtil.isBlank(softwareIdStr) || StrUtil.isBlank(firmwareType)) {
			throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
		}
		final String s = ",";
		String[] firmwareTypeArray = firmwareType.split(s);
		String[] softwareIdArray = softwareIdStr.split(s);
		if (softwareIdArray.length != firmwareTypeArray.length) {
			throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
		}
		List<UploadTypeEnum> typeList = Arrays.stream(firmwareTypeArray)
				.filter(type -> Enums.getIfPresent(UploadTypeEnum.class, type).isPresent())
				.map(type -> Enums.getIfPresent(UploadTypeEnum.class, type).or(UploadTypeEnum.WIFI)).distinct()
				.collect(Collectors.toList());

		if (typeList.contains(UploadTypeEnum.WIFI) && typeList.size() > 1) {
			throw new CustomException(ExceptionEnum.WIFI_UPLOAD_ONLY_ERROR);
		}

		if (firmwareTypeArray.length == typeList.size()) {
			Map<Long, String> idForPrefixMap = new HashMap<>(typeList.size());
			for (int i = 0; i < typeList.size(); i++) {
				idForPrefixMap.put(Long.parseLong(softwareIdArray[i]), typeList.get(i).getPrefix());
			}
			Pair<String, Long> downloadUrlAndSize = fileUploadService.uploadSingleBinFile(multipartFile, idForPrefixMap,
					firmwareId, model
			);
			return DataResponse.success(downloadUrlAndSize.getKey());
		}
		throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
	}
}
