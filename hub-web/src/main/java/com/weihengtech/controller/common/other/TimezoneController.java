package com.weihengtech.controller.common.other;

import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.dtos.other.TimezoneDto;
import com.weihengtech.service.other.TimezoneService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("timezone")
@Api(tags = "14_时区")
public class TimezoneController {

	@Resource
	private TimezoneService timezoneService;

	@GetMapping("list")
	@ApiOperation(value = "时区列表")
	public DataResponse<List<TimezoneDto>> listTimezone() {
		return DataResponse.success(timezoneService.allTimezoneWithLocale());
	}
}
