package com.weihengtech.controller.common.device;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.api.pojo.vos.ReadDeviceWithFuncCodeVO;
import com.weihengtech.api.pojo.vos.WriteDeviceVO;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.event.CustomEvent;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.device.DeviceSpeedUpDo;
import com.weihengtech.pojo.vos.device.DeviceSpeedUpVO;
import com.weihengtech.pojo.vos.device.SpecificSpeedupVo;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.device.DeviceSpeedUpService;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.utils.InitUtil;
import com.weihengtech.utils.OperatingRecordUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("specific")
@Api(tags = "05_特定服务控制")
@Slf4j
public class SpecificServController {

	@Resource
	private DeviceListService deviceListService;
	@Resource
	private StrategyService strategyService;
	@Resource
	private DeviceSpeedUpService deviceSpeedUpService;
	@Resource
	private TuyaDatacenterService tuyaDatacenterService;
	@PostMapping("/speedup/once/{deviceId}")
	@ApiOperation(value = "加速短周期采样")
	public EmptyResponse speedupOnce(@PathVariable("deviceId") String deviceId) {
		DeviceListDO deviceListDO = deviceListService.getDeviceById(Long.parseLong(deviceId));
		if (deviceListDO == null) {
			return EmptyResponse.fail();
		}
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		try {
			specificServService.speedupOnce(deviceListDO.getDeviceSn());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new CustomException(ExceptionEnum.SPEED_UP_FAILURE);
		}
		operateLog(deviceListDO.getDeviceSn());
		return EmptyResponse.success();
	}

	@PostMapping("/speedup/keep/{deviceId}")
	@ApiOperation(value = "加速持续采样")
	public EmptyResponse speedupKeep(@RequestBody DeviceSpeedUpVO param) {
		Assert.notNull(param.getDeviceId(), "deviceId is null");
		DeviceListDO deviceListDO = deviceListService.querySameDatacenterDevice(param.getDeviceId());
		return speedup(deviceListDO, param);
	}

	@PostMapping("/speedup/keepWithDeviceName/{deviceName}")
	@ApiOperation(value = "使用deviceName加速持续采样")
	public EmptyResponse speedupKeepWithDeviceName(@RequestBody DeviceSpeedUpVO param) {
		Assert.hasLength(param.getDeviceFlag(), "deviceSn is blank");
		DeviceListDO deviceListDO = deviceListService
				.getOne(Wrappers.<DeviceListDO>lambdaQuery().eq(DeviceListDO::getDeviceName, param.getDeviceFlag()));
		return speedup(deviceListDO, param);
	}

	@GetMapping("/speedup/list")
	@ApiOperation(value = "加速采集设备列表")
	public DataResponse<List<DeviceSpeedUpDo>> speedupList() {
		List<DeviceSpeedUpDo> list = deviceSpeedUpService.getAll();
		list.forEach(i -> i.setState(i.isValid()));
		return DataResponse.success(list);
	}

	@PostMapping("/speedup/stop")
	@ApiOperation(value = "停止指定设备的加速采集")
	public EmptyResponse speedupOff(@RequestBody @Valid SpecificSpeedupVo specificSpeedupVo) {
		deviceSpeedUpService.turnOffSpeedup(specificSpeedupVo.getDeviceNameList());
		return EmptyResponse.success();
	}

	@PostMapping("/device/read")
	@ApiOperation(value = "透传读")
	public DataResponse<List<Integer>> readDevice(@RequestBody @Valid ReadDeviceWithFuncCodeVO param) {
		String deviceSn = param.getDeviceId();
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceSn);
		Integer state = deviceListDO.getState();
		if (null != state && state >= 0) {
			SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
			List<Integer> res = specificServService.sendReadCommand(deviceListDO.getWifiSn(), param.getSlaveId(),
					param.getStartAddress(), param.getLen());
			return DataResponse.success(res);
		}
		log.info("SpecificServController#readDevice  {}", JSONUtil.toJsonStr(param));
		throw new CustomException(ExceptionEnum.DEVICE_OFFLINE_ERROR);
	}

	@PostMapping("/device/write")
	@ApiOperation(value = "透传写")
	public DataResponse<Boolean> writeDevice(@RequestBody @Valid WriteDeviceVO param) {
		String deviceSn = param.getDeviceId();
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceSn);
		Integer state = deviceListDO.getState();
		if (null != state && state >= 0) {
			SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
			Boolean res = specificServService.sendWriteCommand(deviceListDO.getWifiSn(), param.getSlaveId(),
					param.getStartAddress(), param.getLen(), param.getValues());
			if (res) {
				InitUtil.APPLICATION_CONTEXT
						.publishEvent(new CustomEvent(CustomEvent.Type.SEND_WRITE_COMMAND, param));
			}
			return DataResponse.success(res);
		}
		log.info("SpecificServController#writeDevice {}", JSONUtil.toJsonStr(param));
		throw new CustomException(ExceptionEnum.DEVICE_OFFLINE_ERROR);
	}

	/** 永久加速 */
	private EmptyResponse speedup(DeviceListDO deviceListDO, DeviceSpeedUpVO param) {
		if (null == deviceListDO) {
			throw new CustomException(ExceptionEnum.DEVICE_NOT_EXIST);
		}
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		try {
			specificServService.speedupKeep(param.getDeviceFlag(), param);
		} catch (Exception e) {
			throw new CustomException(ExceptionEnum.SPEED_UP_FAILURE);
		}
		operateLog(param.getDeviceFlag());
		return EmptyResponse.success();
	}

	/** 记录操作日志 */
	private void operateLog(String deviceSn) {
		OperatingRecordUtil.log(
				RecordContentConstants.INSERT_HIGH_FREQUENCY_SAMPLING,
				MapUtil.<String, String>builder().put("deviceName", deviceSn).build(),
				EnumConstants.RecordModule.HIGH_FREQUENCY_SAMPLING.getCode()
		);
	}
}
