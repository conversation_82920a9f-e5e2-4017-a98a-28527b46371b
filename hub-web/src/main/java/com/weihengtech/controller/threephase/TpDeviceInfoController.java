package com.weihengtech.controller.threephase;

import cn.hutool.core.map.MapUtil;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.pojo.dtos.device.info.BatteryDataDTO;
import com.weihengtech.pojo.dtos.device.info.DeviceInformationDTO;
import com.weihengtech.pojo.dtos.device.info.EspParameterDTO;
import com.weihengtech.pojo.dtos.device.info.InverterInformationDTO;
import com.weihengtech.pojo.dtos.device.info.MeterInformationDTO;
import com.weihengtech.pojo.dtos.device.info.ParallelInfoDTO;
import com.weihengtech.pojo.dtos.device.info.PowerFlowDTO;
import com.weihengtech.pojo.dtos.device.info.WeiHengBatteryInfoDTO;
import com.weihengtech.pojo.dtos.device.info.WeiHengBatteryInternalDTO;
import com.weihengtech.pojo.dtos.device.info.WeiHengBatterySingleCellDTO;
import com.weihengtech.pojo.vos.device.DeviceFlagVO;
import com.weihengtech.service.device.DeviceBoundService;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.service.threephase.TpDeviceDetailService;
import com.weihengtech.utils.OperatingRecordUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/three-phase/device/info")
@Api(tags = "02_设备信息")
public class TpDeviceInfoController {

	@Resource
	private TpDeviceDetailService tpDeviceDetailService;
	@Resource
	private DeviceBoundService deviceBoundService;
	@Resource
	private TuyaDatacenterService tuyaDatacenterService;

	@PostMapping("/power")
	@ApiOperation(value = "设备信息 PowerFlow 数据")
	public DataResponse<PowerFlowDTO> queryPowerFlow(@RequestBody @Valid DeviceFlagVO deviceFlagVO) {

		deviceBoundService.onlyProcessBoundDevice(deviceFlagVO.getDeviceName());
		return DataResponse
				.success(tpDeviceDetailService.queryPowerFlow(deviceFlagVO.getDeviceName()));
	}

	@PostMapping("/basic")
	@ApiOperation(value = "设备信息 DeviceInformation 数据")
	public DataResponse<DeviceInformationDTO> queryDeviceInformation(@RequestBody @Valid DeviceFlagVO deviceFlagVO) {
		deviceBoundService.onlyProcessBoundDevice(deviceFlagVO.getDeviceName());
		DeviceInformationDTO deviceInformation = tpDeviceDetailService.queryDeviceInformation(deviceFlagVO.getDeviceName(), Boolean.TRUE);
		if (2 != UserInfoUtil.currentUser().getSource()) {
			deviceInformation.buildDefaultVersion();
		}
		return DataResponse.success(deviceInformation);
	}

	@PostMapping("/inverter")
	@ApiOperation(value = "设备信息 InverterInformation 数据")
	public DataResponse<InverterInformationDTO> queryInverterInformation(
			@RequestBody @Valid DeviceFlagVO deviceFlagVO
	) {
		deviceBoundService.onlyProcessBoundDevice(deviceFlagVO.getDeviceName());
		return DataResponse.success(tpDeviceDetailService.queryInverterInformation(deviceFlagVO.getDeviceName()));
	}

	@PostMapping("/meter")
	@ApiOperation(value = "设备信息 MeterInformation 数据")
	public DataResponse<MeterInformationDTO> queryMeterInformation(@RequestBody @Valid DeviceFlagVO deviceFlagVO) {
		deviceBoundService.onlyProcessBoundDevice(deviceFlagVO.getDeviceName());
		return DataResponse.success(tpDeviceDetailService.queryMeterInformation(deviceFlagVO.getDeviceName()));
	}

	@PostMapping("/esp")
	@ApiOperation(value = "设备信息 ESP Parameter 数据")
	public DataResponse<EspParameterDTO> queryEspParameter(@RequestBody @Valid DeviceFlagVO deviceFlagVO) {
		deviceBoundService.onlyProcessBoundDevice(deviceFlagVO.getDeviceName());
		return DataResponse.success(tpDeviceDetailService.queryEspParameter(deviceFlagVO.getDeviceName()));
	}

	@PostMapping("/battery")
	@ApiOperation(value = "设备信息 Battery Data 数据")
	public DataResponse<BatteryDataDTO> queryBatteryData(@RequestBody @Valid DeviceFlagVO deviceFlagVO) {
		deviceBoundService.onlyProcessBoundDevice(deviceFlagVO.getDeviceName());
		return DataResponse.success(tpDeviceDetailService.queryBatteryData(deviceFlagVO.getDeviceName(), deviceFlagVO.getBatteryIdx()));
	}

	@PostMapping("/weiHengBattery")
	@ApiOperation(value = "为恒电池页")
	public DataResponse<WeiHengBatteryInfoDTO> queryWeiHengBatteryInfo(@RequestBody @Valid DeviceFlagVO deviceFlagVO) {
		return DataResponse.success(tpDeviceDetailService.queryWeiHengBatteryInfo(deviceFlagVO.getDeviceName(), deviceFlagVO.getBatteryIdx()));
	}

	@PostMapping("/weiHengBatteryInternal")
	@ApiOperation(value = "为恒电池页Internal")
	public DataResponse<WeiHengBatteryInternalDTO> queryWeiHengBatteryInternal(
			@RequestBody @Valid DeviceFlagVO deviceFlagVO
	) {
		return DataResponse.success(tpDeviceDetailService.queryWeiHengBatteryInternal(deviceFlagVO.getDeviceName()));
	}

	@PostMapping("/weiHengBatterySingleCell")
	@ApiOperation(value = "为恒电池页SingleCell")
	public DataResponse<WeiHengBatterySingleCellDTO> queryWeiHengBatterySingleCell(
			@RequestBody @Valid DeviceFlagVO deviceFlagVO
	) {
		return DataResponse.success(tpDeviceDetailService.queryWeiHengBatterySingleCell(deviceFlagVO.getDeviceName()));
	}

	@PostMapping("/isWeiHengBattery")
	@ApiOperation(value = "是否为为恒电池")
	public DataResponse<Boolean> isWeiHengBattery(@RequestBody @Valid DeviceFlagVO deviceFlagVO) {
		return DataResponse.success(tpDeviceDetailService.isWeiHengBattery(deviceFlagVO.getDeviceName()));
	}

	@PostMapping("/parallel")
	@ApiOperation(value = "并机运行模式和并机数量")
	public DataResponse<ParallelInfoDTO> parallelInfo(@RequestBody @Valid DeviceFlagVO deviceFlagVO) {
		deviceBoundService.onlyProcessBoundDevice(deviceFlagVO.getDeviceName());
		return DataResponse.success(tpDeviceDetailService.queryParallelInfo(deviceFlagVO.getDeviceName()));
	}
}
