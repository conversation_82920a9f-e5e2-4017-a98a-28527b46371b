package com.weihengtech.controller.threephase;

import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.device.DeviceStatusAndSocDTO;
import com.weihengtech.pojo.vos.device.DeviceSyncStateVo;
import com.weihengtech.service.threephase.TpDeviceListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/three-phase/device/list")
@Api(tags = "01_设备列表")
public class TpDeviceListController {

	@Resource
	private TpDeviceListService tpDeviceListService;

	@GetMapping("/detail")
	@ApiOperation(value = "查询储能机扩展详情")
	public DataResponse<DeviceListDO> deviceDetail(@RequestParam Long id) {
		DeviceListDO deviceInfo = tpDeviceListService.getById(id);
		if (2 != UserInfoUtil.currentUser().getSource()) {
			deviceInfo.buildDefaultVersion();
		}
		return DataResponse.success(deviceInfo);
	}

	@PostMapping("sync_state")
	@ApiOperation(value = "同步单设备状态")
	public DataResponse<Integer> syncDeviceState(@RequestBody @Valid DeviceSyncStateVo deviceSyncStateVo) {
		return DataResponse.success(tpDeviceListService.syncDeviceState(deviceSyncStateVo));
	}

	@PostMapping("sync_state_and_soc")
	@ApiOperation(value = "同步单设备状态和soc")
	public DataResponse<DeviceStatusAndSocDTO> syncDeviceStateAndSoc(@RequestBody @Valid DeviceSyncStateVo deviceSyncStateVo) {
		return DataResponse.success(tpDeviceListService.syncDeviceStateAndSoc(deviceSyncStateVo));
	}
}
