package com.weihengtech.config;

import com.weihengtech.auth.common.Constants;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ApiKey;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.Collections;

/**
 * <AUTHOR>
 */
@EnableSwagger2
@Configuration
public class SwaggerConfig implements EnvironmentAware {

	private Environment environment;

	@Bean(value = "hubApiCommon")
	public Docket hubApiCommon() {
		return docket("common API", "com.weihengtech.controller.common");
	}

	@Bean(value = "hubApiV1")
	public Docket hubApiV1() {
		return docket("v1 API", "com.weihengtech.controller.v1");
	}

	@Bean(value = "hubApiSp")
	public Docket hubApiSp() {
		return docket("single_phase API", "com.weihengtech.controller.singlephase");
	}

	@Bean(value = "hubApiTp")
	public Docket hubApiTp() {
		return docket("three_phase API", "com.weihengtech.controller.threephase");
	}

	@Bean(value = "hubApiAuth")
	public Docket hubApiAuth() {
		return docket("auth API", "com.weihengtech.auth.controller");
	}

	@Bean(value = "hubApiCharger")
	public Docket hubApiCharger() {
		return docket("charger API", "com.weihengtech.controller.charger");
	}

	@Bean(value = "hubApiSocket")
	public Docket hubApiSocket() {
		return docket("socket API", "com.weihengtech.controller.socket");
	}

	private ApiInfo apiInfo() {
		return new ApiInfoBuilder().title("Ecos Hub Server文档")
				.description("ecos-hub接口文档")
				.termsOfServiceUrl("http://localhost:20200")
				.version("1.0.0")
				.build();
	}

	private Docket docket(String groupName, String basePackage) {
		return new Docket(DocumentationType.SWAGGER_2)
				.enable("dev".equals(environment.getProperty("spring.profiles.active", "prod")))
				.apiInfo(apiInfo())
				.groupName(groupName).select()
				.apis(RequestHandlerSelectors.basePackage(basePackage))
				.paths(PathSelectors.any()).build()
				.securitySchemes(Collections.singletonList(new ApiKey(Constants.AUTHORIZATION, Constants.AUTHORIZATION, "header")));
	}

	@Override
	public void setEnvironment(@NotNull Environment environment) {
		this.environment = environment;
	}
}
