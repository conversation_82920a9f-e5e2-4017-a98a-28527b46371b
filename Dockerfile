FROM library/konajdk/kona-with-font-jdk:8

ARG JAR_FILE
ARG ACTIVE
ADD ${JAR_FILE} ecos-service.jar
RUN sh -c 'touch /ecos-service.jar'
ENV JAVA_OPTS="-Xms1280m -Xmx1280m"
ENV SPRING_PROFILES_ACTIVE=${ACTIVE}
ENV SNOWFLAKE_WORD=1
ENV SNOWFLAKE_DADACENTER=1
ENV SERVER_PORT=20200
ENV XXL_JOB_ACCESS_TOKEN=Qz4&iwdaNkeVLQh&cL
ENV XXL_JOB_EXECUTOR_APPNAME=ecos-service
ENV XXL_JOB_EXECUTOR_LOGPATH=/usr/java/logs/xxl/service
ENV XXL_JOB_EXECUTOR_LOGRETENTIONDAYS=3
ENV ALI_CLOUD_TSDB_ENABLE=false
ENV CUSTOM_TIMEZONE=GMT+8
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone
CMD java $JAVA_OPTS -jar /ecos-service.jar