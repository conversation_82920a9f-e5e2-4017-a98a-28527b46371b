<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weihengtech.dao.systeminfo.SystemDeviceRelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weihengtech.pojo.dos.systeminfo.SystemDeviceRelDO">
        <id column="id" property="id" />
        <result column="system_id" property="systemId" />
        <result column="device_sn" property="deviceSn" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        r.id,
        r.system_id,
        r.device_id,
        r.is_check,
        r.create_time
    </sql>

    <select id="getRelByUserAndDevice" resultType="java.lang.Long">
        SELECT r.id
        from system_info si
                 join system_device_rel r
                      on r.system_id = si.id
        where si.installer_id = #{installerId} and r.device_id = #{deviceId}
    </select>

    <select id="listBySystemId" resultType="com.weihengtech.pojo.dos.systeminfo.SystemDeviceRelDO">
        SELECT <include refid="Base_Column_List"/>, h.device_sn
        from system_info si
                 join system_device_rel r
                      on r.system_id = si.id
                 join hybrid_single_phase h
                           on h.id = r.device_id
        where si.id = #{systemId}
    </select>

</mapper>
