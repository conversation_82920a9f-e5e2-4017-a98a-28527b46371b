<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.weihengtech.dao.ecos.DeviceEventMapper">
    <insert id="save">
        INSERT INTO `device_event` (`device_name`,`subsystem`,`code`,`level`,`event_version`,`english`,`chinese`,`upload_time`)
        VALUES (#{deviceName},#{subsystem},#{code},#{level},#{eventVersion},#{english},#{chinese},#{uploadTime})
    </insert>
    <select id="selectCondition" resultType="com.weihengtech.pojo.bos.ecos.DeviceEventPageBO">
        SELECT
        `device_name`, `code`, `chinese`, `english`, `level`, `upload_time`, `subsystem`
        FROM
        `device_event`
        <where>
            <if test="deviceName != null and deviceName != ''">
                AND device_name = #{deviceName}
            </if>
            <if test="startTime != null and startTime != ''">
                AND upload_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND upload_time &lt;= #{endTime}
            </if>
            <if test="level != null and level != ''">
                AND `level` = #{level}
            </if>
            <if test="content != null and content != ''">
                <choose>
                    <when test="isChinese == true">
                        and chinese like concat('%', #{content}, '%')
                    </when>
                    <otherwise>
                        and english like concat('%', #{content}, '%')
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY `upload_time` DESC
    </select>

    <select id="selectConditionAgentAndEngineer"
            resultType="com.weihengtech.pojo.bos.ecos.DeviceEventPageBO">
        SELECT
        `device_name`, `code`, `chinese`, `english`, `level`, `upload_time`, `subsystem`
        FROM
        `device_event`
        <where>
            `device_name` in
            <foreach item="item" index="index" collection="deviceNameList" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
            <if test="deviceEventVO.startTime != null and deviceEventVO.startTime != ''">
                AND upload_time &gt;= #{deviceEventVO.startTime}
            </if>
            <if test="deviceEventVO.endTime != null and deviceEventVO.endTime != ''">
                AND upload_time &lt;= #{deviceEventVO.endTime}
            </if>
            <if test="deviceEventVO.level != null and deviceEventVO.level != ''">
                AND `level` = #{deviceEventVO.level}
            </if>
        </where>
        ORDER BY `upload_time` DESC
    </select>

    <select id="selectLevelCount" resultType="java.lang.Integer">
        SELECT
        count(id)
        FROM
        `device_event`
        <where>
            `level` = #{english}
            <if test="deviceNameList != null and deviceNameList.size() > 0">
                AND `device_name` in
                <foreach item="item" index="index" collection="deviceNameList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY `upload_time` DESC
    </select>
    <select id="selectOne" resultType="com.weihengtech.pojo.bos.ecos.DeviceEventPageBO">
        SELECT
            `device_name`, `code`, `chinese`, `english`, `level`, `upload_time`, `subsystem`
        FROM
            `device_event`
        <where>
            `device_name` = #{deviceName} and
            `upload_time` = #{uploadTime} and
            `subsystem` = #{subsystem} and
            `code` = #{code}
        </where>
    </select>
</mapper>