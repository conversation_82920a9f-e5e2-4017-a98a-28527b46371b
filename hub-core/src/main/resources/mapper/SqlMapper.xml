<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.weihengtech.dao.other.SqlMapper">

    <select id="queryDeviceSnByType" resultType="java.lang.String">
        SELECT sp.`device_flag`
        FROM `device_speed_up` AS sp
                 LEFT JOIN `hybrid_single_phase` AS device on device.`device_name` = sp.`device_flag`
        WHERE device.`data_source` = #{type}
          and (sp.expire_time is null or sp.expire_time > unix_timestamp() * 1000)
    </select>

    <select id="queryDeviceInHomePage" resultType="com.weihengtech.pojo.dtos.device.DeviceListPageDTO">
        SELECT
        device.id as id,
        device.device_name as deviceName,
        device.device_sn as deviceSn,
        device.device_model as deviceModel,
        device.brand as brand,
        device.factory as factory,
        device.power_board_hardware_version as powerBoardHardwareVersion,
        device.dsp1_software_version as dsp1SoftwareVersion,
        device.dsp2_software_version as dsp2SoftwareVersion,
        device.dsp1_sub_version as dsp1SubVersion,
        device.dsp2_sub_version as dsp2SubVersion,
        device.arc_dsp_software_version as arcDspSoftwareVersion,
        device.arc_dsp_sub_version as arcDspSubVersion,
        device.arc_dsp_boot_loader_software_version as arcDspBootLoaderSoftwareVersion,
        device.ems_sub_version as emsSubVersion,
        device.ems_software_version as emsSoftwareVersion,
        device.ems_hardware_version as emsHardwareVersion,
        device.wifi_sn as wifiSn,
        device.wifi_hardware_version as wifiHardwareVersion,
        device.wifi_software_version as wifiSoftwareVersion,
        device.bms_gauge_version as bmsGaugeVersion,
        device.bms_sn as bmsSn,
        device.bms_vendor as bmsVendor,
        device.bms_software_version as bmsSoftwareVersion,
        device.bms_hardware_version as bmsHardwareVersion,
        device.state as state,
        device.alias as alias,
        device.first_install as setupTime,
        device.update_time as updTime,
        device.ip installCountry,
        device.category,
        device.model,
        device.data_source as dataSource,
        device.parallel_number as parallelNumber,
        device.rated_power as ratedPower,
        device.safety_standard as safetyStandard,
        device.address,
        CASE
        WHEN device.state >= 0 THEN device.bat_soc
        ELSE NULL
        END as batSoc
        FROM `hybrid_single_phase` AS device
        <where>
            <if test="wifiSn != null and wifiSn != ''">
                AND device.`wifi_sn` like concat('%', #{wifiSn}, '%')
            </if>
            <if test="condition != null and condition != ''">
                AND (device.`wifi_sn` like concat('%', #{condition}, '%') OR
                device.`device_name` like concat('%', #{condition}, '%') OR
                device.`alias` like concat('%', #{condition}, '%'))
            </if>
            <if test="state != null">
                AND
                case #{state} when 20 then device.`state` &gt;= 0
                when 21 then device.`state` in (-3,-4)
                else device.`state` = #{state} end
            </if>
            <if test="stateList != null and stateList.size() > 0">
                AND device.`state` in
                <foreach collection="stateList" item="state" open="(" close=")" separator=",">
                    #{state}
                </foreach>
            </if>
            <if test="deviceIdList != null and deviceIdList.size() > 0">
                AND device.`id` in
                <foreach collection="deviceIdList" item="deviceId" open="(" close=")" separator=",">
                    #{deviceId}
                </foreach>
            </if>
            <if test="installCountry != null and installCountry != ''.toString() and installCountry != '0'.toString()">
                and device.ip like concat('%', #{installCountry}, '%')
            </if>
            <if test="installCountry != null and installCountry == '0'.toString()">
                and device.ip is null
            </if>
            <if test="batSocState != null">
                AND device.`state` >= 0
                AND
                case #{batSocState}
                when 0 then device.`bat_soc` &gt;= 0 and device.`bat_soc` &lt; 0.10 end
            </if>
            <if test="address != null and address != ''">
                AND device.`address` like concat('%', #{address}, '%')
            </if>
        </where>
        ORDER BY case
        when device.`state`=3 then 0
        when device.`state`=0 then 1
        when device.`state`=5 then 2
        when device.`state`=2 then 3
        when device.`state`=1 then 4
        when device.`state`=4 then 5
        when device.`state`=6 then 6
        when device.`state`=7 then 7
        when device.`state`=8 then 8
        when device.`state`=9 then 9
        when device.`state`=10 then 10
        when device.`state`=11 then 11
        when device.`state`=12 then 12
        when device.`state`=13 then 13
        when device.`state`=14 then 14
        when device.`state`=-2 then 15
        when device.`state`=-1 then 16
        when device.`state`=-3 then 17
        when device.`state`=-4 then 18
        when device.`state`=-5 then 19 end,
        device.`first_install` DESC
    </select>

    <select id="queryDeviceByLikeWifiSn" resultType="java.lang.String">
        SELECT
        device.`wifi_sn`
        FROM
        `hybrid_single_phase` as device
        <where>
            <if test="prop != null and prop != ''">
                AND device.`wifi_sn` like CONCAT('%', #{prop},'%')
            </if>
            <if test="deviceIdList != null and deviceIdList.size() > 0">
                AND device.`id` in
                <foreach collection="deviceIdList" item="deviceId" open="(" close=")" separator=",">
                    #{deviceId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryCurrentToDoUpgradeDevice" resultType="com.weihengtech.pojo.dos.firmware.FirmwareUpgradeRecordDO">
        SELECT
            r.`id`,
            r.`device_id`,
            r.`device_name`,
            r.`firmware_id`,
            r.`upload_url`,
            r.`address_map`,
            r.`status`,
            r.`create_time`,
            r.`update_time`,
            r.`series_table`,
            r.`source_version`,
            r.`finish_version`,
            r.`size`
        FROM
            `firmware_upgrade_record` AS r
        LEFT JOIN `hybrid_single_phase` as d on r.`device_id` = d.`id`
        WHERE r.`status` > 1 and (r.`finish_version` IS NULL or r.`finish_version` = '')
        AND d.id in
        <foreach collection="deviceIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryFocusDevice" resultType="com.weihengtech.pojo.dtos.device.FocusDevicePageDto">
        SELECT
            f.*, d.id deviceId, d.state
        FROM
            `device_focus` as f
        LEFT JOIN `hybrid_single_phase` as d on f.`device_flag` = d.`device_name`
        <where>
            <if test="userId != null">
                AND f.`user_id` = #{userId}
            </if>
            <if test="deviceIdList != null and deviceIdList.size() > 0">
                AND d.id in
                <foreach collection="deviceIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY f.`create_time` DESC
    </select>

    <select id="queryDeviceSpeedupList" resultType="com.weihengtech.pojo.dos.device.DeviceSpeedUpDo">
        SELECT
        *
        FROM
        `device_speed_up` as ds
        LEFT JOIN `hybrid_single_phase` as d on ds.`device_flag` = d.`device_name`
        <where>
            AND d.id in
            <foreach collection="deviceIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        ORDER BY ds.`create_time` DESC
    </select>

    <select id="queryDeviceStatus" resultType="com.weihengtech.pojo.dtos.device.DeviceStatusDTO">
        select h.id deviceId, h.state, h.first_install setUpTime, h.bat_soc batSoc, h.bms_software_version bmsSoftwareVersion
        from hybrid_single_phase h
        where h.id in
        <foreach collection="deviceIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryFirmwareBatchUpgradeDeviceList" resultType="com.weihengtech.pojo.dtos.device.DeviceListPageDTO">
        SELECT device.id as id, device.device_name as deviceName, device.device_sn as deviceSn, device.device_model as deviceModel, device.brand as brand, device.factory as factory, device.power_board_hardware_version as powerBoardHardwareVersion, device.dsp1_software_version as dsp1SoftwareVersion, device.dsp2_software_version as dsp2SoftwareVersion, device.dsp1_sub_version as dsp1SubVersion, device.dsp2_sub_version as dsp2SubVersion, device.arc_dsp_software_version as arcDspSoftwareVersion, device.arc_dsp_sub_version as arcDspSubVersion, device.arc_dsp_boot_loader_software_version as arcDspBootLoaderSoftwareVersion, device.ems_sub_version as emsSubVersion, device.ems_software_version as emsSoftwareVersion, device.ems_hardware_version as emsHardwareVersion, device.wifi_sn as wifiSn, device.wifi_hardware_version as wifiHardwareVersion, device.wifi_software_version as wifiSoftwareVersion, device.bms_gauge_version as bmsGaugeVersion, device.bms_sn as bmsSn, device.bms_vendor as bmsVendor, device.bms_software_version as bmsSoftwareVersion, device.bms_hardware_version as bmsHardwareVersion, device.state as state, device.alias as alias, device.first_install as setupTime, device.update_time as updTime, device.ip installCountry, device.category, device.model, device.data_source as dataSource, device.parallel_number as parallelNumber, device.rated_power as ratedPower, device.safety_standard as safetyStandard, device.bat_soc as batSoc
        FROM `hybrid_single_phase` AS device
        <where>
            <!-- 必须是在线设备 -->
            AND device.`state` &gt;= 0
            <!-- 设备ID列表 -->
            <if test="deviceIdList != null and deviceIdList.size() > 0">
                AND device.`id` in
                <foreach collection="deviceIdList" item="deviceId" open="(" close=")" separator=",">
                    #{deviceId}
                </foreach>
            </if>
            <if test="deviceSn != null and deviceSn != ''">
                AND device.`device_sn` like concat('%', #{deviceSn}, '%')
            </if>
            <!-- 根据固件类型进行版本筛选 -->
            <if test="firmwareType != null">
                <!-- 如果firmwareAllWhiteListVo为null直接什么也查不到 -->
                <if test="firmwareAllWhiteListVo == null">
                    AND 1 = 0
                </if>
                <choose>
                    <!-- EMS固件类型 -->
                    <when test="firmwareType == 'EMS'">
                        <!-- EMS软件版本筛选 -->
                        <if test="emsSoftwareCondition != null">
                            <!-- 优先检查子版本号（只支持等于） -->
                            <if test="emsSoftwareCondition.subVersion != null and emsSoftwareCondition.subVersion != ''">
                                AND device.ems_sub_version = #{emsSoftwareCondition.subVersion}
                            </if>
                            <!-- 当没有子版本号时，检查主版本号（支持小于等于，比较最后一块） -->
                            <if test="(emsSoftwareCondition.subVersion == null or emsSoftwareCondition.subVersion == '') and emsSoftwareCondition.version != null and emsSoftwareCondition.version != ''">
                                <choose>
                                    <when test='emsSoftwareCondition.operator == &quot;=&quot;'>
                                        AND SUBSTRING_INDEX(device.ems_software_version, '-', -1) = SUBSTRING_INDEX(#{emsSoftwareCondition.version}, '-', -1)
                                    </when>
                                    <when test='emsSoftwareCondition.operator == &quot;&lt;=&quot;'>
                                        AND CAST(SUBSTRING_INDEX(device.ems_software_version, '-', -1) AS UNSIGNED) &lt;= CAST(SUBSTRING_INDEX(#{emsSoftwareCondition.version}, '-', -1) AS UNSIGNED)
                                    </when>
                                    <when test='emsSoftwareCondition.operator == &quot;&lt;&quot;'>
                                        AND CAST(SUBSTRING_INDEX(device.ems_software_version, '-', -1) AS UNSIGNED) &lt; CAST(SUBSTRING_INDEX(#{emsSoftwareCondition.version}, '-', -1) AS UNSIGNED)
                                    </when>
                                </choose>
                            </if>
                        </if>
                        <!-- EMS硬件版本筛选 -->
                        <if test="emsHardwareCondition != null and emsHardwareCondition.version != null and emsHardwareCondition.version != ''">
                            <choose>
                                <when test='emsHardwareCondition.operator == &quot;=&quot;'>
                                    AND SUBSTRING_INDEX(device.ems_hardware_version, '-', -1) = SUBSTRING_INDEX(#{emsHardwareCondition.version}, '-', -1)
                                </when>
                                <when test='emsHardwareCondition.operator == &quot;&lt;=&quot;'>
                                    AND CAST(SUBSTRING_INDEX(device.ems_hardware_version, '-', -1) AS UNSIGNED) &lt;= CAST(SUBSTRING_INDEX(#{emsHardwareCondition.version}, '-', -1) AS UNSIGNED)
                                </when>
                                <when test='emsHardwareCondition.operator == &quot;&lt;&quot;'>
                                    AND CAST(SUBSTRING_INDEX(device.ems_hardware_version, '-', -1) AS UNSIGNED) &lt; CAST(SUBSTRING_INDEX(#{emsHardwareCondition.version}, '-', -1) AS UNSIGNED)
                                </when>
                            </choose>
                        </if>
                        <!-- EMS固件白名单筛选 -->
                        <if test="firmwareAllWhiteListVo != null">
                            <!-- 如果EMS软件版本白名单为空，则查不到任何数据 -->
                            <if test="firmwareAllWhiteListVo.emsSoftwareWhiteList == null or firmwareAllWhiteListVo.emsSoftwareWhiteList.size() == 0">
                                AND 1 = 0
                            </if>
                            <if test="firmwareAllWhiteListVo.emsSoftwareWhiteList != null and firmwareAllWhiteListVo.emsSoftwareWhiteList.size() > 0">
                                AND device.ems_software_version IN
                                <foreach collection="firmwareAllWhiteListVo.emsSoftwareWhiteList" item="emsVersion" open="(" close=")" separator=",">
                                    #{emsVersion}
                                </foreach>
                            </if>
                            <!-- 子版本号白名单（如果有子版本号条件才检查） -->
                            <if test="emsSoftwareCondition != null and emsSoftwareCondition.subVersion != null and emsSoftwareCondition.subVersion != ''">
                                <if test="firmwareAllWhiteListVo.emsSubWhiteList == null or firmwareAllWhiteListVo.emsSubWhiteList.size() == 0">
                                    AND 1 = 0
                                </if>
                                <if test="firmwareAllWhiteListVo.emsSubWhiteList != null and firmwareAllWhiteListVo.emsSubWhiteList.size() > 0">
                                    AND device.ems_sub_version IN
                                    <foreach collection="firmwareAllWhiteListVo.emsSubWhiteList" item="emsSubVersion" open="(" close=")" separator=",">
                                        #{emsSubVersion}
                                    </foreach>
                                </if>
                            </if>
                        </if>
                    </when>
                    <!-- DSP1固件类型 -->
                    <when test="firmwareType == 'DSP1'">
                        <!-- DSP1软件版本筛选 -->
                        <if test="dsp1SoftwareCondition != null">
                            <!-- 优先检查子版本号（只支持等于） -->
                            <if test="dsp1SoftwareCondition.subVersion != null and dsp1SoftwareCondition.subVersion != ''">
                                AND device.dsp1_sub_version = #{dsp1SoftwareCondition.subVersion}
                            </if>
                            <!-- 当没有子版本号时，检查主版本号（支持小于等于，比较最后一块） -->
                            <if test="(dsp1SoftwareCondition.subVersion == null or dsp1SoftwareCondition.subVersion == '') and dsp1SoftwareCondition.version != null and dsp1SoftwareCondition.version != ''">
                                <choose>
                                    <when test='dsp1SoftwareCondition.operator == &quot;=&quot;'>
                                        AND SUBSTRING_INDEX(device.dsp1_software_version, '-', -1) = SUBSTRING_INDEX(#{dsp1SoftwareCondition.version}, '-', -1)
                                    </when>
                                    <when test='dsp1SoftwareCondition.operator == &quot;&lt;=&quot;'>
                                        AND CAST(SUBSTRING_INDEX(device.dsp1_software_version, '-', -1) AS UNSIGNED) &lt;= CAST(SUBSTRING_INDEX(#{dsp1SoftwareCondition.version}, '-', -1) AS UNSIGNED)
                                    </when>
                                    <when test='dsp1SoftwareCondition.operator == &quot;&lt;&quot;'>
                                        AND CAST(SUBSTRING_INDEX(device.dsp1_software_version, '-', -1) AS UNSIGNED) &lt; CAST(SUBSTRING_INDEX(#{dsp1SoftwareCondition.version}, '-', -1) AS UNSIGNED)
                                    </when>
                                </choose>
                            </if>
                        </if>
                        <!-- DSP1固件白名单筛选 -->
                        <if test="firmwareAllWhiteListVo != null">
                            <!-- 如果DSP1软件版本白名单为空，则查不到任何数据 -->
                            <if test="firmwareAllWhiteListVo.dsp1SoftwareWhiteList == null or firmwareAllWhiteListVo.dsp1SoftwareWhiteList.size() == 0">
                                AND 1 = 0
                            </if>
                            <if test="firmwareAllWhiteListVo.dsp1SoftwareWhiteList != null and firmwareAllWhiteListVo.dsp1SoftwareWhiteList.size() > 0">
                                AND device.dsp1_software_version IN
                                <foreach collection="firmwareAllWhiteListVo.dsp1SoftwareWhiteList" item="dsp1Version" open="(" close=")" separator=",">
                                    #{dsp1Version}
                                </foreach>
                            </if>
                            <!-- 子版本号白名单（如果有子版本号条件才检查） -->
                            <if test="dsp1SoftwareCondition != null and dsp1SoftwareCondition.subVersion != null and dsp1SoftwareCondition.subVersion != ''">
                                <if test="firmwareAllWhiteListVo.dsp1SubWhiteList == null or firmwareAllWhiteListVo.dsp1SubWhiteList.size() == 0">
                                    AND 1 = 0
                                </if>
                                <if test="firmwareAllWhiteListVo.dsp1SubWhiteList != null and firmwareAllWhiteListVo.dsp1SubWhiteList.size() > 0">
                                    AND device.dsp1_sub_version IN
                                    <foreach collection="firmwareAllWhiteListVo.dsp1SubWhiteList" item="dsp1SubVersion" open="(" close=")" separator=",">
                                        #{dsp1SubVersion}
                                    </foreach>
                                </if>
                            </if>
                        </if>
                    </when>
                    <!-- DSP2固件类型 -->
                    <when test="firmwareType == 'DSP2'">
                        <!-- DSP2软件版本筛选 -->
                        <if test="dsp2SoftwareCondition != null">
                            <!-- 优先检查子版本号（只支持等于） -->
                            <if test="dsp2SoftwareCondition.subVersion != null and dsp2SoftwareCondition.subVersion != ''">
                                AND device.dsp2_sub_version = #{dsp2SoftwareCondition.subVersion}
                            </if>
                            <!-- 当没有子版本号时，检查主版本号（支持小于等于，比较最后一块） -->
                            <if test="(dsp2SoftwareCondition.subVersion == null or dsp2SoftwareCondition.subVersion == '') and dsp2SoftwareCondition.version != null and dsp2SoftwareCondition.version != ''">
                                <choose>
                                    <when test='dsp2SoftwareCondition.operator == &quot;=&quot;'>
                                        AND SUBSTRING_INDEX(device.dsp2_software_version, '-', -1) = SUBSTRING_INDEX(#{dsp2SoftwareCondition.version}, '-', -1)
                                    </when>
                                    <when test='dsp2SoftwareCondition.operator == &quot;&lt;=&quot;'>
                                        AND CAST(SUBSTRING_INDEX(device.dsp2_software_version, '-', -1) AS UNSIGNED) &lt;= CAST(SUBSTRING_INDEX(#{dsp2SoftwareCondition.version}, '-', -1) AS UNSIGNED)
                                    </when>
                                    <when test='dsp2SoftwareCondition.operator == &quot;&lt;&quot;'>
                                        AND CAST(SUBSTRING_INDEX(device.dsp2_software_version, '-', -1) AS UNSIGNED) &lt; CAST(SUBSTRING_INDEX(#{dsp2SoftwareCondition.version}, '-', -1) AS UNSIGNED)
                                    </when>
                                </choose>
                            </if>
                        </if>
                        <!-- DSP2固件白名单筛选 -->
                        <if test="firmwareAllWhiteListVo != null">
                            <!-- 如果DSP2软件版本白名单为空，则查不到任何数据 -->
                            <if test="firmwareAllWhiteListVo.dsp2SoftwareWhiteList == null or firmwareAllWhiteListVo.dsp2SoftwareWhiteList.size() == 0">
                                AND 1 = 0
                            </if>
                            <if test="firmwareAllWhiteListVo.dsp2SoftwareWhiteList != null and firmwareAllWhiteListVo.dsp2SoftwareWhiteList.size() > 0">
                                AND device.dsp2_software_version IN
                                <foreach collection="firmwareAllWhiteListVo.dsp2SoftwareWhiteList" item="dsp2Version" open="(" close=")" separator=",">
                                    #{dsp2Version}
                                </foreach>
                            </if>
                            <!-- 子版本号白名单（如果有子版本号条件才检查） -->
                            <if test="dsp2SoftwareCondition != null and dsp2SoftwareCondition.subVersion != null and dsp2SoftwareCondition.subVersion != ''">
                                <if test="firmwareAllWhiteListVo.dsp2SubWhiteList == null or firmwareAllWhiteListVo.dsp2SubWhiteList.size() == 0">
                                    AND 1 = 0
                                </if>
                                <if test="firmwareAllWhiteListVo.dsp2SubWhiteList != null and firmwareAllWhiteListVo.dsp2SubWhiteList.size() > 0">
                                    AND device.dsp2_sub_version IN
                                    <foreach collection="firmwareAllWhiteListVo.dsp2SubWhiteList" item="dsp2SubVersion" open="(" close=")" separator=",">
                                        #{dsp2SubVersion}
                                    </foreach>
                                </if>
                            </if>
                        </if>
                    </when>
                    <!-- ARCDSP固件类型 -->
                    <when test="firmwareType == 'ARCDSP'">
                        <!-- ARCDSP软件版本筛选 -->
                        <if test="arcdspSoftwareCondition != null">
                            <!-- 优先检查子版本号（只支持等于） -->
                            <if test="arcdspSoftwareCondition.subVersion != null and arcdspSoftwareCondition.subVersion != ''">
                                AND device.arc_dsp_sub_version = #{arcdspSoftwareCondition.subVersion}
                            </if>
                            <!-- 当没有子版本号时，检查主版本号（支持小于等于，比较最后一块） -->
                            <if test="(arcdspSoftwareCondition.subVersion == null or arcdspSoftwareCondition.subVersion == '') and arcdspSoftwareCondition.version != null and arcdspSoftwareCondition.version != ''">
                                <choose>
                                    <when test='arcdspSoftwareCondition.operator == &quot;=&quot;'>
                                        AND SUBSTRING_INDEX(device.arc_dsp_software_version, '-', -1) = SUBSTRING_INDEX(#{arcdspSoftwareCondition.version}, '-', -1)
                                    </when>
                                    <when test='arcdspSoftwareCondition.operator == &quot;&lt;=&quot;'>
                                        AND CAST(SUBSTRING_INDEX(device.arc_dsp_software_version, '-', -1) AS UNSIGNED) &lt;= CAST(SUBSTRING_INDEX(#{arcdspSoftwareCondition.version}, '-', -1) AS UNSIGNED)
                                    </when>
                                    <when test='arcdspSoftwareCondition.operator == &quot;&lt;&quot;'>
                                        AND CAST(SUBSTRING_INDEX(device.arc_dsp_software_version, '-', -1) AS UNSIGNED) &lt; CAST(SUBSTRING_INDEX(#{arcdspSoftwareCondition.version}, '-', -1) AS UNSIGNED)
                                    </when>
                                </choose>
                            </if>
                        </if>
                        <!-- ARCDSP固件白名单筛选 -->
                        <if test="firmwareAllWhiteListVo != null">
                            <!-- 如果ARCDSP软件版本白名单为空，则查不到任何数据 -->
                            <if test="firmwareAllWhiteListVo.arcDspSoftwareWhiteList == null or firmwareAllWhiteListVo.arcDspSoftwareWhiteList.size() == 0">
                                AND 1 = 0
                            </if>
                            <if test="firmwareAllWhiteListVo.arcDspSoftwareWhiteList != null and firmwareAllWhiteListVo.arcDspSoftwareWhiteList.size() > 0">
                                AND device.arc_dsp_software_version IN
                                <foreach collection="firmwareAllWhiteListVo.arcDspSoftwareWhiteList" item="arcDspVersion" open="(" close=")" separator=",">
                                    #{arcDspVersion}
                                </foreach>
                            </if>
                            <!-- 子版本号白名单（如果有子版本号条件才检查） -->
                            <if test="arcdspSoftwareCondition != null and arcdspSoftwareCondition.subVersion != null and arcdspSoftwareCondition.subVersion != ''">
                                <if test="firmwareAllWhiteListVo.arcDspSubWhiteList == null or firmwareAllWhiteListVo.arcDspSubWhiteList.size() == 0">
                                    AND 1 = 0
                                </if>
                                <if test="firmwareAllWhiteListVo.arcDspSubWhiteList != null and firmwareAllWhiteListVo.arcDspSubWhiteList.size() > 0">
                                    AND device.arc_dsp_sub_version IN
                                    <foreach collection="firmwareAllWhiteListVo.arcDspSubWhiteList" item="arcDspSubVersion" open="(" close=")" separator=",">
                                        #{arcDspSubVersion}
                                    </foreach>
                                </if>
                            </if>
                        </if>
                    </when>
                    <!-- BMS固件类型 -->
                    <when test="firmwareType == 'BMS'">
                        <!-- BMS软件版本筛选 -->
                        <if test="bmsSoftwareCondition != null">
                            <!-- 优先检查子版本号（只支持等于） -->
                            <if test="bmsSoftwareCondition.subVersion != null and bmsSoftwareCondition.subVersion != ''">
                                AND device.bms_software_version = #{bmsSoftwareCondition.subVersion}
                            </if>
                            <!-- 当没有子版本号时，检查主版本号（支持小于等于，比较最后一块） -->
                            <if test="(bmsSoftwareCondition.subVersion == null or bmsSoftwareCondition.subVersion == '') and bmsSoftwareCondition.version != null and bmsSoftwareCondition.version != ''">
                                <choose>
                                    <when test='bmsSoftwareCondition.operator == &quot;=&quot;'>
                                        AND SUBSTRING_INDEX(device.bms_software_version, '-', -1) = SUBSTRING_INDEX(#{bmsSoftwareCondition.version}, '-', -1)
                                    </when>
                                    <when test='bmsSoftwareCondition.operator == &quot;&lt;=&quot;'>
                                        AND CAST(SUBSTRING_INDEX(device.bms_software_version, '-', -1) AS UNSIGNED) &lt;= CAST(SUBSTRING_INDEX(#{bmsSoftwareCondition.version}, '-', -1) AS UNSIGNED)
                                    </when>
                                    <when test='bmsSoftwareCondition.operator == &quot;&lt;&quot;'>
                                        AND CAST(SUBSTRING_INDEX(device.bms_software_version, '-', -1) AS UNSIGNED) &lt; CAST(SUBSTRING_INDEX(#{bmsSoftwareCondition.version}, '-', -1) AS UNSIGNED)
                                    </when>
                                </choose>
                            </if>
                        </if>
                        <!-- BMS硬件版本筛选 -->
                        <if test="bmsHardwareCondition != null and bmsHardwareCondition.version != null and bmsHardwareCondition.version != ''">
                            <choose>
                                <when test='bmsHardwareCondition.operator == &quot;=&quot;'>
                                    AND SUBSTRING_INDEX(device.bms_hardware_version, '-', -1) = SUBSTRING_INDEX(#{bmsHardwareCondition.version}, '-', -1)
                                </when>
                                <when test='bmsHardwareCondition.operator == &quot;&lt;=&quot;'>
                                    AND CAST(SUBSTRING_INDEX(device.bms_hardware_version, '-', -1) AS UNSIGNED) &lt;= CAST(SUBSTRING_INDEX(#{bmsHardwareCondition.version}, '-', -1) AS UNSIGNED)
                                </when>
                                <when test='bmsHardwareCondition.operator == &quot;&lt;&quot;'>
                                    AND CAST(SUBSTRING_INDEX(device.bms_hardware_version, '-', -1) AS UNSIGNED) &lt; CAST(SUBSTRING_INDEX(#{bmsHardwareCondition.version}, '-', -1) AS UNSIGNED)
                                </when>
                            </choose>
                        </if>
                        <!-- BMS固件白名单筛选 -->
                        <if test="firmwareAllWhiteListVo != null">
                            <!-- 如果BMS软件版本白名单为空，则查不到任何数据 -->
                            <if test="firmwareAllWhiteListVo.bmsSoftwareWhiteList == null or firmwareAllWhiteListVo.bmsSoftwareWhiteList.size() == 0">
                                AND 1 = 0
                            </if>
                            <if test="firmwareAllWhiteListVo.bmsSoftwareWhiteList != null and firmwareAllWhiteListVo.bmsSoftwareWhiteList.size() > 0">
                                AND device.bms_software_version IN
                                <foreach collection="firmwareAllWhiteListVo.bmsSoftwareWhiteList" item="bmsVersion" open="(" close=")" separator=",">
                                    #{bmsVersion}
                                </foreach>
                            </if>
                        </if>
                    </when>
                </choose>
            </if>
            <!-- 安规筛选 -->
            <if test="safetyStandard != null and safetyStandard != ''">
                AND device.safety_standard = #{safetyStandard}
            </if>
            <!-- 发货地区筛选 -->
            <if test="countryId != null">
                AND device.country_id = #{countryId}
            </if>
            <!-- 安装地区筛选 -->
            <if test="installCountry != null and installCountry != ''.toString() and installCountry != '0'.toString()">
                and device.ip like concat('%', #{installCountry}, '%')
            </if>
            <if test="installCountry != null and installCountry == '0'.toString()">
                and device.ip is null
            </if>
            <!-- 电量状态筛选 -->
            <if test="batSocState != null">
                AND CASE #{batSocState}
                WHEN '0' THEN device.bat_soc &gt;= 0 AND device.bat_soc &lt; 0.10
                WHEN '1' THEN device.bat_soc &gt;= 0.10 OR device.bat_soc IS NULL
                END
            </if>
        </where>
        ORDER BY case when device.`state`=3 then 0 when device.`state`=0 then 1 when device.`state`=5 then 2 when device.`state`=2 then 3 when device.`state`=1 then 4 when device.`state`=4 then 5 when device.`state`=6 then 6 when device.`state`=7 then 7 when device.`state`=8 then 8 when device.`state`=9 then 9 when device.`state`=10 then 10 when device.`state`=11 then 11 when device.`state`=12 then 12 when device.`state`=13 then 13 when device.`state`=14 then 14 when device.`state`=-2 then 15 when device.`state`=-1 then 16 when device.`state`=-3 then 17 when device.`state`=-4 then 18 when device.`state`=-5 then 19 end, device.`first_install` DESC
    </select>

</mapper>