<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weihengtech.dao.systeminfo.SystemInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weihengtech.pojo.dos.systeminfo.SystemInfoDO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="datacenter_id" property="datacenterId" />
        <result column="owner_first_name" property="ownerFirstName" />
        <result column="owner_last_name" property="ownerLastName" />
        <result column="email" property="email" />
        <result column="phone" property="phone" />
        <result column="install_date" property="installDate" />
        <result column="installer_id" property="installerId" />
        <result column="transfer_time" property="transferTime" />
        <result column="current_step" property="currentStep" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <collection property="deviceList" ofType="com.weihengtech.pojo.dos.systeminfo.SystemDeviceRelDO">
            <result column="rid" property="id" javaType="java.lang.Long"/>
            <result column="device_id" property="deviceId" javaType="java.lang.Long"/>
            <result column="is_check" property="isCheck" javaType="java.lang.Integer"/>
            <result column="device_sn" property="deviceSn" javaType="java.lang.String"/>
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        si.id,
        si.name,
        si.datacenter_id,
        si.owner_first_name,
        si.owner_last_name,
        si.email,
        si.phone,
        si.install_date,
        si.installer_id,
        si.transfer_time,
        si.current_step,
        si.create_time,
        si.update_time
    </sql>

    <select id="getById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>, r.id rid, r.device_id, r.is_check, h.device_sn
        from system_info si
        left join system_device_rel r
        on r.system_id = si.id
        left join hybrid_single_phase h
        on h.id = r.device_id
        where si.id = #{id}
    </select>

    <select id="listByCondition" parameterType="com.weihengtech.pojo.vos.systeminfo.SystemInfoQueryVO" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>, r.id rid, r.device_id, r.is_check, h.device_sn
        from system_info si
        left join system_device_rel r
        on r.system_id = si.id
        left join hybrid_single_phase h
        on h.id = r.device_id
        where 1=1
        <if test="item.installerId != null and item.installerId != ''">
            and si.installer_id = #{item.installerId}
        </if>
    </select>

    <select id="getSystemByUserAndDevice" resultType="com.weihengtech.pojo.dos.systeminfo.SystemInfoDO">
        SELECT <include refid="Base_Column_List"></include>
        from system_info si
                 join system_device_rel r
                      on r.system_id = si.id
        where si.installer_id = #{installerId}
          and r.device_id = #{deviceId}
        and si.transfer_time is NULL
    </select>
</mapper>
