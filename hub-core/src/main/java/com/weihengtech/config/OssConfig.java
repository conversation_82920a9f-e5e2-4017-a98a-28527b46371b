package com.weihengtech.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class OssConfig {

	@Value("${custom.ali.oss.key}")
	private String key;

	@Value("${custom.ali.oss.secret}")
	private String secret;

	@Value("${custom.ali.oss.endpoint}")
	private String endpoint;

	@Bean
	public OSS ossClient() {
		return new OSSClientBuilder().build(endpoint, key, secret);
	}
}
