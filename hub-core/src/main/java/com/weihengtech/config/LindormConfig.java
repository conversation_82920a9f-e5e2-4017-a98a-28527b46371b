package com.weihengtech.config;

import com.aliyun.lindorm.tsdb.client.ClientOptions;
import com.aliyun.lindorm.tsdb.client.LindormTSDBClient;
import com.aliyun.lindorm.tsdb.client.LindormTSDBFactory;
import com.aliyun.lindorm.tsdb.client.SchemaPolicy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class LindormConfig {

	@Value("${custom.lindorm.url}")
	private String tuyaLindormUrl;

	@Value("${custom.lindorm.elink.url}")
	private String elinterLindormUrl;

	@Value("${custom.lindorm.charger.url}")
	private String chargerLindormUrl;

	@Bean("tuyaLindormClient")
	public LindormTSDBClient tuyaLindormClient() {
		ClientOptions options = ClientOptions.newBuilder(tuyaLindormUrl)
				// 规约
				.setSchemaPolicy(SchemaPolicy.WEAK)
				// 写入线程池大小
				.setNumBatchThreads(3)
				// 最大重试次数
				.setMaxRetries(3)
				// 重试等待时间
				.setRetryBackoffMs(1000)
				// 请求超时时间
				.setRequestTimeoutMs(15000)
				// 连接超时时间
				.setConnectTimeoutMs(15000)
				// 攒批最长等待时间
				.setMaxWaitTimeMs(3000).build();
		return LindormTSDBFactory.connect(options);
	}

	@Bean("elinterLindormClient")
	public LindormTSDBClient elinterLindormClient() {
		ClientOptions options = ClientOptions.newBuilder(elinterLindormUrl)
				// 规约
				.setSchemaPolicy(SchemaPolicy.WEAK)
				// 写入线程池大小
				.setNumBatchThreads(3)
				// 最大重试次数
				.setMaxRetries(3)
				// 重试等待时间
				.setRetryBackoffMs(1000)
				// 请求超时时间
				.setRequestTimeoutMs(15000)
				// 连接超时时间
				.setConnectTimeoutMs(15000)
				// 攒批最长等待时间
				.setMaxWaitTimeMs(3000).build();
		return LindormTSDBFactory.connect(options);
	}

	@Bean("chargerLindormClient")
	public LindormTSDBClient chargerLindormClient() {
		ClientOptions options = ClientOptions.newBuilder(chargerLindormUrl)
				// 规约
				.setSchemaPolicy(SchemaPolicy.WEAK)
				// 写入线程池大小
				.setNumBatchThreads(3)
				// 最大重试次数
				.setMaxRetries(3)
				// 重试等待时间
				.setRetryBackoffMs(1000)
				// 请求超时时间
				.setRequestTimeoutMs(15000)
				// 连接超时时间
				.setConnectTimeoutMs(15000)
				// 攒批最长等待时间
				.setMaxWaitTimeMs(3000).build();
		return LindormTSDBFactory.connect(options);
	}
}
