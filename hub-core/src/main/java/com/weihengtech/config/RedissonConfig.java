package com.weihengtech.config;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * <AUTHOR>
 */
@Configuration
@Profile({"dev", "test", "prod"})
@RequiredArgsConstructor
public class RedissonConfig {

	@Value("${spring.redis.host}")
	private String host;
	@Value("${spring.redis.port}")
	private String port;
	@Value("${spring.redis.database}")
	private String db;
	@Value("${spring.redis.password}")
	private String password;

	@Bean
	public RedissonClient redissonClient() {
		Config config = new Config();
		if (StrUtil.isBlank(password)) {
			config.useSingleServer().setAddress("redis://" + host + ":" + port)
					.setDatabase(Integer.parseInt(db));
		} else {
			config.useSingleServer().setAddress("redis://" + host + ":" + port)
					.setPassword(password)
					.setDatabase(Integer.parseInt(db));
		}
		return Redisson.create(config);
	}
}
