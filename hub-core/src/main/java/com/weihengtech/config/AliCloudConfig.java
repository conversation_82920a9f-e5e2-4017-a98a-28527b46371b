package com.weihengtech.config;

import com.aliyun.iot20180120.Client;
import com.aliyun.teaopenapi.models.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class AliCloudConfig {

	@Value("${ali.cloud.access.key.id}")
	private String mqttAccessKeyId;

	@Value("${ali.cloud.access.key.secret}")
	private String mqttAccessKeySecret;

	@Value("${ali.cloud.iot.region-id}")
	private String mqttRegionId;

	@Bean
	public Client mqttClient() throws Exception {
		Config config = new Config();
		config.setAccessKeyId(mqttAccessKeyId);
		config.setAccessKeySecret(mqttAccessKeySecret);
		config.setRegionId(mqttRegionId);
		return new Client(config);
	}
}
