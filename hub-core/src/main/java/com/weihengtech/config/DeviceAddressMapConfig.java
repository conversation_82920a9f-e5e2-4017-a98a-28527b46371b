package com.weihengtech.config;

import cn.hutool.core.collection.ListUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.enums.firmware.UploadTypeEnum;
import com.weihengtech.pojo.bos.device.DeviceConfigBO;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class DeviceAddressMapConfig {

	@Resource
	private DeviceConfigBO deviceConfig;

	private Map<String, List<Integer>> addressMap;

	@PostConstruct
	private void initialize() {
		addressMap = new HashMap<>(8);
		Map<String, Map<String, String>> deviceInformation = deviceConfig.getConfig().get("DeviceInformation");
		Map<String, String> dsp1Version = deviceInformation.get("DSP1_Version");
		Map<String, String> dsp2Version = deviceInformation.get("DSP2_Version");
		Map<String, Map<String, String>> armInformation = deviceConfig.getConfig().get("ARMInformation");
		Map<String, String> emsVersion = armInformation.get("EMS_Version");
		Map<String, String> emsSubVersion = armInformation.get("EmsSubVersion");
		Map<String, String> bmsVersion = armInformation.get("BMS_Software_Version");
		Map<String, String> dsp1SubVersion1 = armInformation.get("Dsp1SubVersion1");
		Map<String, String> dsp1SubVersion2 = armInformation.get("Dsp1SubVersion2");
		Map<String, String> dsp2SubVersion1 = armInformation.get("Dsp2SubVersion1");
		Map<String, String> dsp2SubVersion2 = armInformation.get("Dsp2SubVersion2");
		Map<String, Map<String, String>> usArmInformation = deviceConfig.getConfig().get("US_ARMInformation");
		Map<String, String> arcDspVersion = usArmInformation.get("ArcDspSoftwareVersion");
		Map<String, String> arcSubVersion = usArmInformation.get("ArcDspSubVersion");
		addressMap.put(
				UploadTypeEnum.DSP1.name(),
				ListUtil.toList(Integer.parseInt(dsp1Version.get("start")), Integer.parseInt(dsp1Version.get("len")))
		);
		addressMap.put(
				UploadTypeEnum.DSP1.name() + "_SUB1",
				ListUtil.toList(Integer.parseInt(dsp1SubVersion1.get("start")), Integer.parseInt(dsp1SubVersion1.get("len")))
		);
		addressMap.put(
				UploadTypeEnum.DSP1.name() + "_SUB2",
				ListUtil.toList(Integer.parseInt(dsp1SubVersion2.get("start")), Integer.parseInt(dsp1SubVersion2.get("len")))
		);
		addressMap.put(
				UploadTypeEnum.DSP2.name(),
				ListUtil.toList(Integer.parseInt(dsp2Version.get("start")), Integer.parseInt(dsp2Version.get("len")))
		);
		addressMap.put(
				UploadTypeEnum.DSP2.name() + "_SUB1",
				ListUtil.toList(Integer.parseInt(dsp2SubVersion1.get("start")), Integer.parseInt(dsp2SubVersion1.get("len")))
		);
		addressMap.put(
				UploadTypeEnum.DSP2.name() + "_SUB2",
				ListUtil.toList(Integer.parseInt(dsp2SubVersion2.get("start")), Integer.parseInt(dsp2SubVersion2.get("len")))
		);
		addressMap.put(
				UploadTypeEnum.EMS.name(),
				ListUtil.toList(Integer.parseInt(emsVersion.get("start")), Integer.parseInt(emsVersion.get("len")))
		);
		addressMap.put(
				UploadTypeEnum.EMS.name() + "_SUB",
				ListUtil.toList(Integer.parseInt(emsSubVersion.get("start")), Integer.parseInt(emsSubVersion.get("len")))
		);
		addressMap.put(
				UploadTypeEnum.BMS.name(),
				ListUtil.toList(Integer.parseInt(bmsVersion.get("start")), Integer.parseInt(bmsVersion.get("len")))
		);
		addressMap.put(
				UploadTypeEnum.ARCDSP.name(),
				ListUtil.toList(Integer.parseInt(arcDspVersion.get("start")), Integer.parseInt(arcDspVersion.get("len")))
		);
		addressMap.put(
				UploadTypeEnum.ARCDSP.name() + "_SUB",
				ListUtil.toList(Integer.parseInt(arcSubVersion.get("start")), Integer.parseInt(arcSubVersion.get("len")))
		);
	}

	public List<Integer> getAddressList(String mapKey) {
		if (addressMap.containsKey(mapKey)) {
			return addressMap.get(mapKey);
		}
		throw new CustomException(ExceptionEnum.CODE_LOGIC_EXCEPTION);
	}
}
