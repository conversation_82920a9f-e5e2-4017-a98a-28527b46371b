package com.weihengtech.config;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.pojo.bos.device.DeviceConfigBO;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class FileResourceConfig {

	@Bean
	public DeviceConfigBO deviceConfig() {
		String deviceJsonStr = ResourceUtil.readUtf8Str("device.json");
		return JSONUtil.toBean(deviceJsonStr, DeviceConfigBO.class);
	}
}
