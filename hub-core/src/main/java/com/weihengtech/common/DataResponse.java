package com.weihengtech.common;

import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.utils.LocaleUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
@ApiModel(value = "数据回参类")
public class DataResponse<T> extends EmptyResponse {

	@ApiModelProperty(name = "data", value = "数据体")
	private T data;

	/**
	 * 默认携带数据体回参
	 *
	 * @param data 数据体
	 * @param <T>  数据体类型
	 * @return DataResponse
	 */
	public static <T> DataResponse<T> success(T data) {
		DataResponse<T> dataResponse = new DataResponse<>();
		dataResponse.setData(data);
		dataResponse.setCode(200);
		dataResponse.setMsg("成功");
		return dataResponse;
	}

	/**
	 * 自定义数据回参
	 *
	 * @param code 自定义状态码
	 * @param msg  自定义描述
	 * @param data 自定义数据体
	 * @param <T>  数据体类型
	 * @return DataResponse
	 */
	public static <T> DataResponse<T> success(Integer code, String msg, T data) {
		DataResponse<T> dataResponse = new DataResponse<>();
		dataResponse.setData(data);
		dataResponse.setCode(code);
		dataResponse.setMsg(msg);
		return dataResponse;
	}

	/**
	 * 默认带数据体失败回参
	 *
	 * @param data 自定义失败数据体
	 * @param <T>  数据体类型
	 * @return DataResponse
	 */
	public static <T> DataResponse<T> fail(T data) {
		DataResponse<T> dataResponse = new DataResponse<>();
		dataResponse.setData(data);
		dataResponse.setCode(500);
		dataResponse.setMsg("服务器发生了错误");
		return dataResponse;
	}

	/**
	 * 自定义数据体失败回参
	 *
	 * @param code 自定义失败状态码
	 * @param msg  自定义失败描述信息
	 * @param data 自定义失败数据体
	 * @param <T>  数据体类型
	 * @return DataResponse
	 */
	public static <T> DataResponse<T> fail(Integer code, String msg, T data) {
		DataResponse<T> dataResponse = new DataResponse<>();
		dataResponse.setData(data);
		dataResponse.setCode(code);
		dataResponse.setMsg(msg);
		return dataResponse;
	}

	/**
	 * 异常枚举类带数据回参
	 *
	 * @param customException 异常类
	 * @param data            数据体
	 * @param <T>             数据体类型
	 * @return DataResponse
	 */
	public static <T> DataResponse<T> fail(CustomException customException, T data) {
		DataResponse<T> dataResponse = new DataResponse<>();
		dataResponse.setData(data);
		dataResponse.setCode(customException.getCode());
		dataResponse.setMsg(LocaleUtil.getMessage(customException.getMessage()));
		return dataResponse;
	}

	/**
	 * 异常枚举自定义信息带数据回参
	 *
	 * @param customException 自定义异常类
	 * @param msg             自定义错误信息
	 * @param data            错误数据体
	 * @param <T>             数据体类型
	 * @return DataResponse
	 */
	public static <T> DataResponse<T> fail(CustomException customException, String msg, T data) {
		DataResponse<T> dataResponse = new DataResponse<>();
		dataResponse.setData(data);
		dataResponse.setCode(customException.getCode());
		dataResponse.setMsg(msg);
		return dataResponse;
	}

	public static  <T> T parseData(DataResponse<T> res) {
		if (HttpStatus.OK.value() != res.getCode()) {
			log.warn("HubService request failed, resp:{}", res);
			throw new CustomException(ExceptionEnum.DATA_ASSERT_EXCEPTION);
		}
		return res.getData();
	}
}
