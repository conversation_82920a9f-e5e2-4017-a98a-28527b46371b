package com.weihengtech.common.exceptions;

import com.weihengtech.utils.LocaleUtil;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class CustomException extends RuntimeException {

	private final Integer code;

	public CustomException(Integer code, String msg) {
		super(msg);
		this.code = code;
	}

	public CustomException(ExceptionEnum exceptionEnum) {
		super(LocaleUtil.getMessage(exceptionEnum.getMsg()));
		this.code = exceptionEnum.getCode();
	}

	public CustomException(ExceptionEnum exceptionEnum, String param) {
		super(String.format("%s: %s", LocaleUtil.getMessage(exceptionEnum.getMsg()), param));
		this.code = exceptionEnum.getCode();
	}

}
