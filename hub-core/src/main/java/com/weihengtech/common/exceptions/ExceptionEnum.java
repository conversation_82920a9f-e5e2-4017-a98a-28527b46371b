package com.weihengtech.common.exceptions;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum ExceptionEnum {

	/**
	 * 未知异常， 用于处理非人为抛出的异常
	 */
	UNKNOWN_EXCEPTION(9999, "err.unknown.exception"),
	PARAM_VALID_EXCEPTION(10000, "err.param.valid.exception"),

	TOKEN_ILLEGAL_EXCEPTION(10001, "err.token.illegal.exception"),
	TOKEN_EXPIRED_EXCEPTION(10002, "err.token.expired.exception"),
	TOKEN_ERROR_EXCEPTION(10003, "err.token.error.exception"),
	LOGIN_EXPIRED_EXCEPTION(10004, "err.login.expired.exception"),
	CONFIG_PARSE_EXCEPTION(10005, "err.config.parse.exception"),
	DATA_ASSERT_EXCEPTION(10006, "err.data.assert.exception"),
	ACTION_ASSERT_NOT_FIT_EXCEPTION(10007, "err.action.assert.not.fit.exception"),
	CODE_LOGIC_EXCEPTION(10008, "err.code.logic.exception"),
	ROLE_NOT_ALLOWED(10009, "err.role.not.allowed"),
	TWICE_PASSWORD_MATCHING_EXCEPTION(10100, "err.twice.password.matching.exception"),
	TERMINAL_TYPE_EXCEPTION(10101, "err.terminal.type.exception"),
	LOGIN_INFO_EXCEPTION(10102, "err.login.info.exception"),
	PARAM_MISS_EXCEPTION(10103, "err.param.miss.exception"),
	EMAIL_SEND_LIMIT_EXCEPTION(10104, "err.email.send.limit.exception"),
	EMAIL_SEND_EXCEPTION(10105, "err.email.send.exception"),
	EMAIL_CODE_FIT_EXCEPTION(10106, "err.email.code.fit.exception"),
	SMS_PARAM_ERROR(10107, "err.sms.param"),
	SMS_SEND_ERROR(10108, "err.sms.send"),

	DEVICE_STATUS_CHECK_ERROR(10202, "err.device.status.check.error"),
	DEVICE_STATISTICS_CHECK_ERROR(10203, "err.device.statistics.check.error"),
	DEVICE_STATUS_INFO_QUERY_ERROR(10204, "err.device.status.info.error"),
	DEVICE_STATUS_NOT_FIT_ERROR(10205, "err.device.status.not.fit.error"),
	DEVICE_MULTI_QUERY_ERROR(10206, "err.device.status.multi.query.error"),
	DEVICE_QUERY_DATA_ERROR(10207, "err.device.query.data.error"),
	DEVICE_FLAG_EMPTY_ERROR(10208, "err.device.flag.empty.error"),
	DEVICE_CONFIG_CHANGE_ERROR(10209, "err.device.change.error"),
	DEVICE_CONFIG_NOT_EXIST(10210, "err.config.not.exist"),
	DEVICE_CONFIG_LENGTH_ERROR(10211, "err.config.length.error"),
	ACTION_NOT_SUCCESS_ERROR(10212, "err.action.not.success.error"),
	LINKED_VERSION_DELETE_ERROR(10213, "err.linked.version.delete.error"),
	DEVICE_REMOTE_ERROR(10214, "err.device.remote.error"),
	DEVICE_RESULT_PARSE_ERROR(10215, "err.device.result.parse.error"),
	DEVICE_CONFIG_FILE_ERROR(10216, "err.config.file.error"),
	HARDWARE_TYPE_NOT_SINGLE_ERROR(10217, "err.hardware.type.not.single.error"),
	DUPLICATE_KEY_EXCEPTION(10300, "err.duplicate.key.exception"),

	TIME_CANNOT_CROSS(20201, "err.time.cannot.cross"),
	SUFFIX_NOT_SUPPORT_EXCEPTION(20400, "err.suffix.not.support.exception"),
	FILE_UPLOAD_ERROR(20401, "err.file.upload.error"),
	FILE_SIZE_LIMIT_ERROR(20402, "err.file.size.limit.error"),
	WIFI_UPLOAD_ONLY_ERROR(20403, "err.wifi.upload.only.error"),
	FIRMWARE_VERSION_VALID_ERROR(20404, "err.firmware.version.valid.error"),
	SOFTWARE_VERSION_NOT_BIND_HARDWARE_TYPE_ERROR(20405, "err.software.version.not.bind.hardware.type.error"),
	SOFTWARE_VERSION_NOT_FIT_CHOOSE_TYPE_ERROR(20406, "err.software.version.not.fit.choose.type.error"),
	FIRMWARE_NOT_BIND_SOFTWARE(20407, "err.firmware.not.bind.software.error"),
	DEVICE_OFFLINE_ERROR(20408, "err.device.offline.error"),
	DEVICE_UPGRADE_ERROR(20409, "err.device.upgrade.error"),
	TSDB_OVER_TIME_SPAN(20410, "err.tsdb.over.time.span.error"),
	ONE_SOFTWARE_BIND_ONE_FIRMWARE(20411, "err.one.software.bind.one.firmware"),
	TUYA_SERVICE_READ_ERROR(20412, "err.tuya.service.read.error"),
	DEVICE_NO_WIFISN(20413, "err.device.no.wifisn"),
	DEVICE_NOT_EXIST(20414, "err.device.not.exist"),
	SPEED_UP_FAILURE(20415, "err.speed.up.failure"),
	DEVICE_ALREADY_BIND(20416, "err.device.already.bind"),
	ACCOUNT_NOT_EXIST(20417, "err.account.not.exist"),
	INVALID_ACTION_TO_DEVICE(20424, "err.invalid.action.to.device"),
	INVALID_DATACENTER(20438, "err.invalid.datacenter"),
	DEVICE_BIND_FAILURE(20432, "err.device.bind.failure"),
	DEVICE_BIND_ING(20433, "err.device.bind.ing"),
	BEFORE_INSTALL_DATE(20434, "err.before.install.date"),
	BLANK_MODEL(20435, "err.blank.model"),
	MIX_PHASE_UPGRADE_FAILED(20436, "err.phase.upgrade.failed"),
	TOP_FAILED_EXCEPTION(20437, "err.top.failed"),
	DEVICE_ALREADY_SLAVE_OTHER(20438, "err.device.already.slave.other"),
	DEVICE_ALREADY_MAX_SLAVE(20440, "err.device.already.max.slave"),
	DEVICE_REL_SELF_ERROR(20441, "err.device.rel.self"),
	DEVICE_MODE_NOT_MATCHED(20442, "err.device.mode.not.matched"),
	DATA_ALREADY_EXISTED(20443, "err.data.already.existed"),
	DEVICE_UPGRADE_SOC_LIMIT(20444, "err.device.upgrade.soc.limit"),
    CHOLLA_VERSION_TOO_OLD(20445, "err.cholla.version.old"),
	// 任务已在执行中，终止失败
	TASK_TERMINATE_FAILED(20446, "err.task.terminate.failed"),
	// 请先选择系列/类型
	SELECT_SERIES_TYPE_FIRST(20447, "err.select.series.type.first"),
	DEVICE_NOT_BIND_CHOLLA_GWY(20448, "err.device.not.bind.cholla.gwy"),
	;

	private final int code;
	private final String msg;

	ExceptionEnum(int code, String msg) {
		this.code = code;
		this.msg = msg;
	}
}
