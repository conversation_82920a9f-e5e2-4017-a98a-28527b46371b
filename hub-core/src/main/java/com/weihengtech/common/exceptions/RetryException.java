package com.weihengtech.common.exceptions;

import com.weihengtech.utils.LocaleUtil;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class RetryException extends RuntimeException {

	private final Integer code;

	public RetryException(Integer code, String msg) {
		super(msg);
		this.code = code;
	}

	public RetryException(ExceptionEnum exceptionEnum) {
		super(LocaleUtil.getMessage(exceptionEnum.getMsg()));
		this.code = exceptionEnum.getCode();
	}

	public RetryException(ExceptionEnum exceptionEnum, String msg) {
		super(msg);
		this.code = exceptionEnum.getCode();
	}

	@Override
	public Throwable fillInStackTrace() {
		return this;
	}
}
