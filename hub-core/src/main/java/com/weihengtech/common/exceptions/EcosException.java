package com.weihengtech.common.exceptions;

import com.weihengtech.utils.LocaleUtil;
import lombok.Getter;

/**
 * <AUTHOR>
 * @program ecos-admin-backend
 * @description
 * @create 2023-08-14 15:42
 **/
@Getter
public class EcosException extends RuntimeException {

    private final Integer code;

    public EcosException(Integer code, String msg) {
        super(msg);
        this.code = code;
    }

    public EcosException(ExceptionEnum exceptionEnum) {
        super(exceptionEnum.getMsg());
        this.code = exceptionEnum.getCode();
    }

    public EcosException(ExceptionEnum ecosExceptionEnum, String msg) {
        super(msg);
        this.code = ecosExceptionEnum.getCode();
    }

    @Override
    public String toString() {
        return LocaleUtil.getMessage(this.getMessage());
    }

    @Override
    public Throwable fillInStackTrace() {
        return this;
    }
}
