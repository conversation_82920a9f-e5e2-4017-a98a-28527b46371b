package com.weihengtech.common;

import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.utils.LocaleUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "空数据回参类")
public class EmptyResponse {

	@ApiModelProperty(name = "code", value = "状态码")
	private Integer code;

	@ApiModelProperty(name = "msg", value = "描述信息")
	private String msg;

	/**
	 * 默认成功回参
	 *
	 * @return EmptyResponse
	 */
	public static EmptyResponse success() {
		EmptyResponse emptyResponse = new EmptyResponse();
		emptyResponse.setCode(200);
		emptyResponse.setMsg("成功");
		return emptyResponse;
	}

	/**
	 * 自定义成功回参
	 *
	 * @param code 自定义状态码
	 * @param msg  自定义描述
	 * @return EmptyResponse
	 */
	public static EmptyResponse success(Integer code, String msg) {
		EmptyResponse emptyResponse = new EmptyResponse();
		emptyResponse.setCode(code);
		emptyResponse.setMsg(msg);
		return emptyResponse;
	}

	/**
	 * 默认失败回参
	 *
	 * @return EmptyResponse
	 */
	public static EmptyResponse fail() {
		EmptyResponse emptyResponse = new EmptyResponse();
		emptyResponse.setCode(500);
		emptyResponse.setMsg("服务器出现了错误");
		return emptyResponse;
	}

	/**
	 * 自定义失败回参
	 *
	 * @param code 自定义状态码
	 * @param msg  自定义错误信息
	 * @return EmptyResponse
	 */
	public static EmptyResponse fail(Integer code, String msg) {
		EmptyResponse emptyResponse = new EmptyResponse();
		emptyResponse.setCode(code);
		emptyResponse.setMsg(msg);
		return emptyResponse;
	}

	/**
	 * 自定义失败回参 异常枚举
	 *
	 * @param exceptionEnum 异常枚举类
	 * @return EmptyResponse
	 */
	public static EmptyResponse fail(ExceptionEnum exceptionEnum) {
		EmptyResponse emptyResponse = new EmptyResponse();
		emptyResponse.setCode(exceptionEnum.getCode());
		emptyResponse.setMsg(LocaleUtil.getMessage(exceptionEnum.getMsg()));
		return emptyResponse;
	}

	/**
	 * 自定义异常描述回参
	 *
	 * @param exceptionEnum 异常枚举类
	 * @param msg           自定义异常描述
	 * @return EmptyResponse
	 */
	public static EmptyResponse fail(ExceptionEnum exceptionEnum, String msg) {
		EmptyResponse emptyResponse = new EmptyResponse();
		emptyResponse.setCode(exceptionEnum.getCode());
		emptyResponse.setMsg(msg);
		return emptyResponse;
	}

	/**
	 * 未认证回参
	 *
	 * @return EmptyResponse
	 */
	public static EmptyResponse unauthorized() {
		EmptyResponse emptyResponse = new EmptyResponse();
		emptyResponse.setCode(HttpStatus.UNAUTHORIZED.value());
		emptyResponse.setMsg(HttpStatus.UNAUTHORIZED.getReasonPhrase());
		return emptyResponse;
	}
}
