package com.weihengtech.service.enest.impl;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.weihengtech.dao.enest.EnestVersionMapper;
import com.weihengtech.pojo.dos.enest.EnestVersionDo;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.dtos.ecos.GlobalEnestLatestVersionDto;
import com.weihengtech.pojo.dtos.enest.EnestVersionPageDto;
import com.weihengtech.pojo.vos.enest.EnestVersionAddVo;
import com.weihengtech.pojo.vos.enest.EnestVersionUpdateVo;
import com.weihengtech.service.enest.EnestVersionService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EnestVersionServiceImpl extends ServiceImpl<EnestVersionMapper, EnestVersionDo>
		implements
		EnestVersionService {

	@Override
	public void addEnestVersion(EnestVersionAddVo enestVersionAddVo) {
		EnestVersionDo enestVersionDo = new EnestVersionDo();
		enestVersionDo.setCreateTime(System.currentTimeMillis());

		CglibUtil.copy(enestVersionAddVo, enestVersionDo);
		ActionFlagUtil.assertTrue(this.save(enestVersionDo));
	}

	@Override
	public void updateEnestVersion(EnestVersionUpdateVo enestVersionUpdateVo) {
		EnestVersionDo enestVersionDo = this.getById(enestVersionUpdateVo.getId());
		BeanUtil.assertNotNull(enestVersionDo);
		CglibUtil.copy(enestVersionUpdateVo, enestVersionDo);
		ActionFlagUtil.assertTrue(this.updateById(enestVersionDo));
	}

	@Override
	public PageInfoDTO<EnestVersionPageDto> pageClientVersion(Integer pageNum, Integer pageSize) {

		PageHelper.startPage(pageNum, pageSize);
		List<EnestVersionDo> doList = this.list();
		PageInfo<EnestVersionDo> pageInfo = new PageInfo<>(doList);

		PageInfoDTO<EnestVersionPageDto> pageInfoDto = new PageInfoDTO<>();
		pageInfoDto.setTotalPages(pageInfo.getPages());
		pageInfoDto.setTotalCount(pageInfo.getTotal());
		pageInfoDto.setData(doList.stream().map(versionDo -> {
			EnestVersionPageDto enestVersionPageDto = new EnestVersionPageDto();
			CglibUtil.copy(versionDo, enestVersionPageDto);
			return enestVersionPageDto;
		}).collect(Collectors.toList()));

		return pageInfoDto;
	}

    @Override
    public GlobalEnestLatestVersionDto latestVersion(String language) {
		GlobalEnestLatestVersionDto globalEnestLatestVersionDto = new GlobalEnestLatestVersionDto();
		EnestVersionDo enestVersionDo = lambdaQuery().orderByDesc(EnestVersionDo::getId).last("LIMIT 1").one();
		BeanUtil.assertNotNull(enestVersionDo);
		EnestVersionDo enestForceVersionDo = lambdaQuery().orderByDesc(EnestVersionDo::getId).eq(EnestVersionDo::getFlag, 2).last("LIMIT 1").one();
		if (null != enestForceVersionDo) {
			globalEnestLatestVersionDto.setPreForceAndroidVersion(enestForceVersionDo.getAndroidVersion());
			globalEnestLatestVersionDto.setPreForceIosVersion(enestForceVersionDo.getIosVersion());
		} else {
			globalEnestLatestVersionDto.setPreForceAndroidVersion("");
			globalEnestLatestVersionDto.setPreForceIosVersion("");
		}
		CglibUtil.copy(enestVersionDo, globalEnestLatestVersionDto);
		try {
			String methodName = LocaleUtil.mapLocaleToDatabaseGetMethod(language);
			String content = ReflectUtil.invoke(enestVersionDo, "getZhCn".equals(methodName) ? methodName : "getEnUs").toString();
			globalEnestLatestVersionDto.setContent(content);
		} catch (Exception e) {
			globalEnestLatestVersionDto.setContent(enestVersionDo.getEnUs());
		}
		return globalEnestLatestVersionDto;
    }
}
