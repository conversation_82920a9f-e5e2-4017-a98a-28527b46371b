package com.weihengtech.service.enest;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.enest.EnestVersionDo;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.dtos.ecos.GlobalEnestLatestVersionDto;
import com.weihengtech.pojo.dtos.enest.EnestVersionPageDto;
import com.weihengtech.pojo.vos.enest.EnestVersionAddVo;
import com.weihengtech.pojo.vos.enest.EnestVersionUpdateVo;

/**
 * <AUTHOR>
 */
public interface EnestVersionService extends IService<EnestVersionDo> {

	/**
	 * 新增enest 版本信息
	 *
	 * @param enestVersionAddVo 新增版本信息入参
	 */
	void addEnestVersion(EnestVersionAddVo enestVersionAddVo);

	/**
	 * 更新enest版本信息
	 *
	 * @param enestVersionUpdateVo 需要更新的版本信息
	 */
	void updateEnestVersion(EnestVersionUpdateVo enestVersionUpdateVo);

	/**
	 * enest 版本分页
	 *
	 * @param pageNum  页码
	 * @param pageSize 每页数量
	 * @return 分页数据
	 */
	PageInfoDTO<EnestVersionPageDto> pageClientVersion(Integer pageNum, Integer pageSize);

	/**
	 * enest 最新版本信息
	 */
	GlobalEnestLatestVersionDto latestVersion(String language);
}
