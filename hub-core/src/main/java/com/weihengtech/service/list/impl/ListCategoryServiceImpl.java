package com.weihengtech.service.list.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.dao.list.ListCategoryMapper;
import com.weihengtech.pojo.dos.list.ListCategoryDO;
import com.weihengtech.service.list.ListCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: jiahao.jin
 * @create: 2025-07-21
 * @description: 名单分类Service实现类
 */
@Slf4j
@Service
public class ListCategoryServiceImpl extends ServiceImpl<ListCategoryMapper, ListCategoryDO>
        implements ListCategoryService {
    @Override
    public List<ListCategoryDO> listAll() {
        return list();
    }
}