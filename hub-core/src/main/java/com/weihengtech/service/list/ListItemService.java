package com.weihengtech.service.list;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.list.ListItemDO;
import com.weihengtech.pojo.dtos.list.ListItemDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.list.ListItemAddVO;
import com.weihengtech.pojo.vos.list.ListItemPageVO;

import java.util.List;

/**
 * @author: jiahao.jin
 * @create: 2025-07-21
 * @description: 名单内容Service
 */
public interface ListItemService extends IService<ListItemDO> {

    // 增加名单
    void addListItem(ListItemAddVO listItemAddVO);

    // 移除名单
    void removeListItem(Long id);

    // 分页查询名单
    PageInfoDTO<ListItemDTO> pageListItem(ListItemPageVO listItemPageVO);

    // 根据名单类型、分类ID查询所有名单
    List<ListItemDTO> listAll(Integer listType, String categoryCode);
}