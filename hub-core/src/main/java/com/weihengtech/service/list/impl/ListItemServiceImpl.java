package com.weihengtech.service.list.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.dao.list.ListItemMapper;
import com.weihengtech.pojo.dos.list.ListCategoryDO;
import com.weihengtech.pojo.dos.list.ListItemDO;
import com.weihengtech.pojo.dtos.list.ListItemDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.list.ListItemAddVO;
import com.weihengtech.pojo.vos.list.ListItemPageVO;
import com.weihengtech.service.list.ListCategoryService;
import com.weihengtech.service.list.ListItemService;
import com.weihengtech.utils.ActionFlagUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: jiahao.jin
 * @create: 2025-07-21
 * @description: 名单内容Service实现类
 */
@Slf4j
@Service
public class ListItemServiceImpl extends ServiceImpl<ListItemMapper, ListItemDO>
        implements ListItemService {

    @Resource
    private ListCategoryService listCategoryService;

    @Resource
    private ListItemMapper listItemMapper;

    @Override
    public void addListItem(ListItemAddVO listItemAddVO) {
        // 校验categoryId
        List<ListCategoryDO> listCategoryDOS = listCategoryService.listAll();
        if (listCategoryDOS.stream().noneMatch(i -> i.getId().equals(listItemAddVO.getCategoryId()))) {
            log.warn("分类不存在");
            throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
        }

        // 校验listType是否为0或者1
        if (listItemAddVO.getListType() != 0 && listItemAddVO.getListType() != 1) {
            log.warn("名单类型不正确");
            throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
        }

        // 查询是否已有
        if (listItemMapper.selectOne(Wrappers.<ListItemDO>lambdaQuery()
                .eq(ListItemDO::getCategoryId, listItemAddVO.getCategoryId())
                .eq(ListItemDO::getListType, listItemAddVO.getListType())
                .eq(ListItemDO::getContent, listItemAddVO.getContent())) != null) {
            log.warn("名单已存在");
            throw new CustomException(ExceptionEnum.DATA_ALREADY_EXISTED);
        }

        ListItemDO listItemDO = new ListItemDO();
        listItemDO.setListType(listItemAddVO.getListType());
        listItemDO.setCategoryId(listItemAddVO.getCategoryId());
        listItemDO.setContent(listItemAddVO.getContent());
        LocalDateTime now = LocalDateTime.now();
        listItemDO.setCreateTime(now);
        listItemDO.setUpdateTime(now);
        ActionFlagUtil.assertTrue(save(listItemDO));
    }

    @Override
    public void removeListItem(Long id) {
        ActionFlagUtil.assertTrue(removeById(id));
    }

    @Override
    public PageInfoDTO<ListItemDTO> pageListItem(ListItemPageVO listItemPageVO) {

        List<ListCategoryDO> listCategoryDOS = listCategoryService.listAll();
        if (listCategoryDOS.stream().noneMatch(i -> i.getId().equals(listItemPageVO.getCategoryId()))) {
            log.warn("分类不存在");
            throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
        }
        if (listItemPageVO.getListType() != 0 && listItemPageVO.getListType() != 1) {
            log.warn("名单类型不正确");
            throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
        }
        ListCategoryDO listCategoryDO = listCategoryDOS.stream().filter(i -> i.getId().equals(listItemPageVO.getCategoryId()))
                .findFirst().orElse(new ListCategoryDO());

        PageHelper.startPage(listItemPageVO.getPageNum(), listItemPageVO.getPageSize());
        PageInfo<ListItemDO> pageInfo = new PageInfo<>(listItemMapper.selectList(
                Wrappers.<ListItemDO>lambdaQuery()
                        .eq(ListItemDO::getCategoryId, listItemPageVO.getCategoryId())
                        .eq(ListItemDO::getListType, listItemPageVO.getListType())
                        .like(StringUtils.isNotBlank(listItemPageVO.getContent()), ListItemDO::getContent, listItemPageVO.getContent())
                        .orderByDesc(ListItemDO::getCreateTime)
        ));

        // 构建返回结果
        PageInfoDTO<ListItemDTO> pageInfoDTO = new PageInfoDTO<>();
        pageInfoDTO.setTotalPages(pageInfo.getPages());
        pageInfoDTO.setTotalCount(pageInfo.getTotal());
        pageInfoDTO.setData(pageInfo.getList().stream()
                .map(i -> ListItemDTO.builder()
                        .id(i.getId())
                        .listType(i.getListType())
                        .content(i.getContent())
                        .categoryId(i.getCategoryId())
                        .categoryName(listCategoryDO.getCategoryName())
                        .categoryCode(listCategoryDO.getCategoryCode())
                        .build())
                .collect(Collectors.toList()));

        return pageInfoDTO;
    }

    @Override
    public List<ListItemDTO> listAll(Integer listType, String categoryCode) {
        List<ListCategoryDO> listCategoryDOS = listCategoryService.listAll();
        if (listCategoryDOS.stream().noneMatch(i -> i.getCategoryCode().equals(categoryCode))) {
            log.warn("分类不存在");
            throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
        }
        if (listType != 0 && listType != 1) {
            log.warn("名单类型不正确");
            throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
        }
        ListCategoryDO listCategoryDO = listCategoryDOS.stream().filter(i -> i.getCategoryCode().equals(categoryCode))
                .findFirst().orElse(new ListCategoryDO());

        return listItemMapper.selectList(
                Wrappers.<ListItemDO>lambdaQuery()
                        .eq(ListItemDO::getCategoryId, listCategoryDO.getId())
                        .eq(ListItemDO::getListType, listType)
        ).stream()
                .map(i -> ListItemDTO.builder()
                        .id(i.getId())
                        .listType(i.getListType())
                        .content(i.getContent())
                        .categoryId(i.getCategoryId())
                        .categoryName(listCategoryDO.getCategoryName())
                        .categoryCode(listCategoryDO.getCategoryCode())
                        .build())
                .collect(Collectors.toList());
    }
}