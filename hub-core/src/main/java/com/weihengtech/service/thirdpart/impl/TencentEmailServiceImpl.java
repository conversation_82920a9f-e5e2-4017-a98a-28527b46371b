package com.weihengtech.service.thirdpart.impl;

import cn.hutool.core.collection.ListUtil;
import com.weihengtech.api.NotificationCenterApi;
import com.weihengtech.api.pojo.base.NotificationResponse;
import com.weihengtech.api.pojo.vos.NotificationBatchMailVo;
import com.weihengtech.consts.MailModelConstants;
import com.weihengtech.enums.other.MailModelEnum;
import com.weihengtech.service.thirdpart.TencentEmailService;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class TencentEmailServiceImpl implements TencentEmailService {

	@Resource
	private NotificationCenterApi notificationCenterApi;

	@Override
	public boolean sendEmail(String toEmail, MailModelEnum mailModelEnum, String... params) {
		MailModelConstants.MailInfoBO mailInfo = MailModelConstants.getMailInfo(mailModelEnum, params);
		return sendMail(mailInfo, ListUtil.toList(toEmail));
	}

	private boolean sendMail(MailModelConstants.MailInfoBO mailInfo, List<String> mailList) {
		try {
			NotificationBatchMailVo notificationBatchMailVo = new NotificationBatchMailVo();
			notificationBatchMailVo.setContent(mailInfo.getContent());
			notificationBatchMailVo.setIsHtml(true);
			notificationBatchMailVo.setSubject(mailInfo.getSubject());
			notificationBatchMailVo.setTo(String.join(",", mailList));
			NotificationResponse<String> notificationResponse = notificationCenterApi.v1AsyncMail(notificationBatchMailVo);
			if (notificationResponse == null || !notificationResponse.getSuccess() || notificationResponse.getCode() != HttpStatus.OK.value()) {
				return false;
			}
		} catch (Exception e) {
			return false;
		}
		return true;
	}
}
