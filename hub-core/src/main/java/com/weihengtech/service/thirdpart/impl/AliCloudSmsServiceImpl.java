package com.weihengtech.service.thirdpart.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.api.NotificationCenterApi;
import com.weihengtech.api.pojo.base.NotificationResponse;
import com.weihengtech.api.pojo.vos.NotificationBatchSmsVo;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.service.thirdpart.AliCloudSmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AliCloudSmsServiceImpl implements AliCloudSmsService {

	@Resource
	private NotificationCenterApi notificationCenterApi;

	@Override
	public boolean sendFocusDeviceOfflineSms(List<String> phoneList, String deviceName) {
		return sendBatchSms(phoneList, "SMS_243391265", MapUtil.<String, String>builder().put("name", deviceName).build());
	}

	@Override
	public boolean sendBatchSms(List<String> phoneList, String templateCode, Map<String, String> params) {
		List<Map<String, String>> listParam = new LinkedList<>();
		for (int i = 0; i < phoneList.size(); i++) {
			listParam.add(params);
		}
		return sendBatchSms(phoneList, templateCode, listParam);
	}

	@Override
	public boolean sendBatchSms(List<String> phoneList, String templateCode, List<Map<String, String>> params) {
		if (null == phoneList || null == params || phoneList.size() != params.size()) {
			throw new CustomException(ExceptionEnum.SMS_PARAM_ERROR);
		}
		return sendBatchSms(String.join(",", phoneList), templateCode, JSONUtil.toJsonStr(params));
	}

	@Override
	public boolean sendBatchSms(String phoneNumbers, String templateCode, String paramsJson) {
		try {
			NotificationBatchSmsVo notificationBatchSmsVo = new NotificationBatchSmsVo();
			notificationBatchSmsVo.setPhoneNumbers(phoneNumbers);
			notificationBatchSmsVo.setTemplateCode(templateCode);
			notificationBatchSmsVo.setTemplateParam(paramsJson);
			NotificationResponse<String> notificationResponse = notificationCenterApi.v1AsyncSms(notificationBatchSmsVo);
			if (null == notificationResponse || !notificationResponse.getSuccess() || notificationResponse.getCode() != HttpStatus.OK.value()) {
				return false;
			}
		} catch (Exception e) {
			log.error("Sms Send Error: {}-{}", phoneNumbers, paramsJson);
			return false;
		}
		return true;
	}
}
