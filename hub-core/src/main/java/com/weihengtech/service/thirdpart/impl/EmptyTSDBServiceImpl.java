package com.weihengtech.service.thirdpart.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import com.aliyun.lindorm.tsdb.client.model.Result;
import com.google.common.collect.Lists;
import com.weihengtech.pojo.dtos.tsdb.TsdbQueryDTO;
import com.weihengtech.service.thirdpart.TimeSeriesDatabaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service(value = "tsdbService9")
@Slf4j
public class EmptyTSDBServiceImpl implements TimeSeriesDatabaseService {

	@Override
	public Map<String, Object> lastPoint(
			String deviceFlag, List<String> metricList, long start,
			long end
	) {
		return MapUtil.empty();
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> withSampleQuery(TsdbQueryDTO param) {
		return MapUtil.empty();
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> withOutSampleQuery(TsdbQueryDTO param) {
		return MapUtil.empty();
	}

	@Override
	public Result withSampleQueryList(TsdbQueryDTO param) {
		Result result = new Result();
		result.setColumns(Lists.newArrayList());
		result.setMetadata(Lists.newArrayList());
		result.setRows(Lists.newArrayList());
		result.setError("");
		return result;
	}

	@Override
	public Result withOutSampleQueryList(TsdbQueryDTO param) {
		Result result = new Result();
		result.setColumns(Lists.newArrayList());
		result.setMetadata(Lists.newArrayList());
		result.setRows(Lists.newArrayList());
		result.setError("");
		return result;
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> deltaQuery(TsdbQueryDTO param) {
		return Collections.emptyMap();
	}

	@Override
	public Dict lastDataPoint(String deviceSn, LinkedList<String> metricList, long end) {
		return Dict.create();
	}
}
