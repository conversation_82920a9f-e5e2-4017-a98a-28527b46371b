package com.weihengtech.service.thirdpart.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.consts.RedisRefConstants;
import com.weihengtech.service.thirdpart.RedisService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class RedisServiceImpl implements RedisService {

	@Resource
	private StringRedisTemplate stringRedisTemplate;
	@Value("${custom.datacenter.name}")
	private String datacenter;

	@Override
	public void cacheSpeedupDevice(String deviceSn, String deviceSourceDateTypeFlag, long seconds) {
		Long expire = stringRedisTemplate.getExpire(RedisRefConstants.buildSpeedupRedisKey(deviceSn, datacenter));
		if ((null != expire && expire > 60L) || (null != expire && -1L == expire)) {
			return;
		}
		long timeMillis = System.currentTimeMillis();
		stringRedisTemplate.opsForValue().set(RedisRefConstants.buildSpeedupRedisKey(deviceSn, datacenter),
				deviceSourceDateTypeFlag + "," + timeMillis, seconds, TimeUnit.SECONDS
		);
	}

	@Override
	public Map<String, String> batchGetDataSourceTypeCachedDeviceFlag(String deviceSourceDateTypeFlag) {

		Set<String> keys = stringRedisTemplate.keys(RedisRefConstants.buildSpeedupLikeKey(datacenter));
		if (null == keys || CollUtil.isEmpty(keys)) {
			return MapUtil.empty();
		}
		return keys.stream().filter(key -> {
			String val = stringRedisTemplate.opsForValue().get(key);
			if (null == val || StrUtil.isBlank(val)) {
				return false;
			} else {
				try {
					return val.split(",")[0].equals(deviceSourceDateTypeFlag);
				} catch (Exception e) {
					return false;
				}
			}
		}).collect(Collectors.toMap(key -> key.split(":")[2], key -> {
			String val = stringRedisTemplate.opsForValue().get(key);
			assert val != null;
			return val.split(",")[1];
		}));
	}
}
