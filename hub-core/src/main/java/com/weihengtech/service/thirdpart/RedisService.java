package com.weihengtech.service.thirdpart;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface RedisService {

	/**
	 * 缓存需要加速采集的设备
	 *
	 * @param deviceSn                 设备标识
	 * @param deviceSourceDateTypeFlag 设备类型数值标识
	 * @param seconds                  缓存的时间
	 */
	void cacheSpeedupDevice(String deviceSn, String deviceSourceDateTypeFlag, long seconds);

	/**
	 * 查询所有缓存的设备标识
	 *
	 * @param deviceSourceDateTypeFlag 指定的设备类型数值
	 * @return 设备标识列表
	 */
	Map<String, String> batchGetDataSourceTypeCachedDeviceFlag(String deviceSourceDateTypeFlag);
}
