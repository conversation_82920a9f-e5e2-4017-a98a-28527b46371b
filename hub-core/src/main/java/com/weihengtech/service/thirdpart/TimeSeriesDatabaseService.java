package com.weihengtech.service.thirdpart;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.aliyun.lindorm.tsdb.client.LindormTSDBClient;
import com.aliyun.lindorm.tsdb.client.model.Result;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.pojo.dtos.tsdb.TsdbQueryDTO;
import com.weihengtech.service.thirdpart.impl.ElinkLindormServiceImpl;
import lombok.val;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public interface TimeSeriesDatabaseService {

	/**
	 * 最后的点位
	 *
	 * @param deviceFlag 设备标识
	 * @param metricList 属性名列表
	 * @param start      开始时间节点
	 * @param end        最后时间节点 毫秒时间戳
	 * @return 最后的点位
	 */
	Map<String, Object> lastPoint(
			String deviceFlag, List<String> metricList, long start,
			long end
	);

	/**
	 * 获取最后一点的数据，指定截止时间，获取截止时间前最后一帧数据
	 * 实现方式：截止时间5min内数据按时间倒排，取最新数据
	 *
	 * @param deviceSn 设备标识
	 * @param metricList 属性列表
	 * @param end 结束时间 毫秒时间戳
	 * @return 属性映射
	 */
	Dict lastDataPoint(String deviceSn, LinkedList<String> metricList, long end);

	/**
	 * 降采样，Map数据，提供app端解析
	 *
	 * @param param param
     * @return 点位数据
	 */
	Map<String, LinkedHashMap<Long, Object>> withSampleQuery(TsdbQueryDTO param);

	/**
	 * 不降采样，Map数据，提供app端解析
	 *
	 * @param param param
     * @return 点位数据
	 */
	Map<String, LinkedHashMap<Long, Object>> withOutSampleQuery(TsdbQueryDTO param);

	/**
	 * 降采样，标准格式，提供web端解析
	 *
	 * @param param param
     * @return 点位数据
	 */
	Result withSampleQueryList(TsdbQueryDTO param);

	/**
	 * 不降采样，标准格式，提供web端解析
	 *
	 * @param param param
     * @return 点位数据
	 */
	Result withOutSampleQueryList(TsdbQueryDTO param);

	/**
	 * 差值查询
	 *
	 * @param param
     * @return 差值映射结果
	 */
	Map<String, LinkedHashMap<Long, Object>> deltaQuery(TsdbQueryDTO param);

	/**
	 * 图表查询
	 *
	 * @param param param
     * @return 查询映射
	 */
	default Map<String, LinkedHashMap<Long, Object>> graphQuery(TsdbQueryDTO param) {
		val timeRange = param.getEndTime() - param.getStartTime();
		long oneHour = 3600L;
		long oneDay = oneHour * 24;
		long oneMonth = oneDay * 31;

		if (timeRange <= oneHour) {
			return withOutSampleQuery(param);
		} else if (timeRange < oneMonth) {
			long times = (timeRange / oneDay + 1) * 5;
			param.setTimes(times + "m");
			return withSampleQuery(param);
		} else {
			throw new CustomException(ExceptionEnum.TSDB_OVER_TIME_SPAN);
		}
	}

	/**
	 * 图表查询 List
	 *
	 * @param param param
     * @return 查询映射
	 */
	default Result graphQueryList(TsdbQueryDTO param) {
		val timeRange = param.getEndTime() - param.getStartTime();
		long oneHour = 3600L;
		long oneDay = oneHour * 24;
		long oneMonth = oneDay * 31;

		if (timeRange <= oneHour) {
			Result result = withOutSampleQueryList(param);
			if (this instanceof ElinkLindormServiceImpl) {
				List<List<Object>> rows = result.getRows();
				List<List<Object>> filterRows = rows.stream().filter(
						list -> CollUtil.count(list, Objects::isNull) <= (list.size() / 2)).collect(Collectors.toList()
				);
				result.setRows(filterRows);
			}
			return result;
		} else if (timeRange < oneMonth) {
			long times = (timeRange / oneDay + 1) * 5;
			param.setTimes(times + "m");
			return withSampleQueryList(param);
		} else {
			throw new CustomException(ExceptionEnum.TSDB_OVER_TIME_SPAN);
		}
	}
}
