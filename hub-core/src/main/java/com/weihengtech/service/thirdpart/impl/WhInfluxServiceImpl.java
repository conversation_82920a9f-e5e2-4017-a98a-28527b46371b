package com.weihengtech.service.thirdpart.impl;

import cn.hutool.core.lang.Dict;
import com.aliyun.lindorm.tsdb.client.model.Result;
import com.influxdb.query.FluxTable;
import com.weihengtech.influx.pojo.ResultWrapper;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.tsdb.TsdbQueryDTO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.iot.IotClientService;
import com.weihengtech.service.thirdpart.TimeSeriesDatabaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 为恒Iot平台tsdb数据操作实现类
 *
 * <AUTHOR>
 * @date 2024/5/20 17:17
 * @version 1.0
 */
@Service(value = "tsdbService4")
@Slf4j
public class WhInfluxServiceImpl implements TimeSeriesDatabaseService {

	@Resource
	private IotClientService whIotClient;

	@Override
	public Map<String, Object> lastPoint(
			String deviceFlag, List<String> metricList, long start,
			long end) {
		FluxTable fluxTable = whIotClient.queryIotLastPoint(TsdbQueryDTO.builder()
						.cloudId(deviceFlag)
						.startTime(start)
						.endTime(end)
						.metricList(metricList)
				.build());
		return ResultWrapper.convertFirstMapData(fluxTable);
	}

	@Override
	public Dict lastDataPoint(String deviceSn, LinkedList<String> metricList, long end) {
		Map<String, Object> map = lastPoint(deviceSn, metricList, end - 300, end);
		Dict dict = Dict.create();
		dict.putAll(map);
		return dict;
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> withSampleQuery(TsdbQueryDTO param) {
		FluxTable fluxTable = doSampleQuery(param);
		return ResultWrapper.convertMapData(fluxTable);
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> withOutSampleQuery(TsdbQueryDTO param) {
		FluxTable fluxTable = doWithoutSampleQuery(param);
		return ResultWrapper.convertMapData(fluxTable);
	}

	@Override
	public Result withSampleQueryList(TsdbQueryDTO param) {
		FluxTable fluxTable = doSampleQuery(param);
		com.weihengtech.influx.pojo.Result result = ResultWrapper.convertResultData(fluxTable);
		return new Result(result.getColumns(), result.getMetadata(), result.getRows());
	}

	@Override
	public Result withOutSampleQueryList(TsdbQueryDTO param) {
		FluxTable fluxTable = doWithoutSampleQuery(param);
		com.weihengtech.influx.pojo.Result result = ResultWrapper.convertResultData(fluxTable);
		return new Result(result.getColumns(), result.getMetadata(), result.getRows());
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> deltaQuery(TsdbQueryDTO param) {
		FluxTable fluxTable = whIotClient.queryIotDelta(TsdbQueryDTO.builder()
				.cloudId(param.getCloudId())
				.startTime(param.getStartTime())
				.endTime(param.getEndTime())
				.metricList(param.getMetricList())
				.times(param.getTsdbSampleEnum().getDelta())
				.build());
		return ResultWrapper.convertMapData(fluxTable);
	}

	/** 执行降采样查询 */
	private FluxTable doSampleQuery(TsdbQueryDTO param) {
		return whIotClient.queryIotWithSample(param);
	}

	/** 执行不降采样查询 */
	private FluxTable doWithoutSampleQuery(TsdbQueryDTO param) {
		return whIotClient.queryIotWithoutSample(param);
	}
}
