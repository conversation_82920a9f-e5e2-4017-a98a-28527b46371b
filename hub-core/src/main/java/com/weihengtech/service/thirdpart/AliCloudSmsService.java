package com.weihengtech.service.thirdpart;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AliCloudSmsService {

	/**
	 * 重点关注设备发送短信
	 *
	 * @param phoneList  电话列表
	 * @param deviceName 设备名
	 */
	boolean sendFocusDeviceOfflineSms(List<String> phoneList, String deviceName);

	/**
	 * 批量发送短信, 相同的参数
	 *
	 * @param phoneList    电话列表
	 * @param templateCode 模板
	 * @param params       使用相同的参数
	 */
	boolean sendBatchSms(List<String> phoneList, String templateCode, Map<String, String> params);

	/**
	 * 批量发送短信, 可以为不同的参数
	 *
	 * @param phoneList    电话列表
	 * @param templateCode 模板
	 * @param params       与phoneList相同长度的参数列表
	 */
	boolean sendBatchSms(List<String> phoneList, String templateCode, List<Map<String, String>> params);

	/**
	 * 批量发送短信, 序列化的JSON参数
	 *
	 * @param phoneNumbers 电话字符串,用逗号隔开
	 * @param templateCode 模板
	 * @param paramsJson   json参数
	 */
	boolean sendBatchSms(String phoneNumbers, String templateCode, String paramsJson);
}
