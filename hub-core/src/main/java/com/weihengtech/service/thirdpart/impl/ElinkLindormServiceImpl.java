package com.weihengtech.service.thirdpart.impl;

import cn.hutool.core.lang.Dict;
import com.aliyun.lindorm.tsdb.client.model.Result;
import com.weihengtech.influx.pojo.req.BasicParamDTO;
import com.weihengtech.influx.pojo.req.SampleParamDTO;
import com.weihengtech.influx.service.FluxQueryWrapper;
import com.weihengtech.pojo.dtos.tsdb.TsdbQueryDTO;
import com.weihengtech.service.thirdpart.TimeSeriesDatabaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service(value = "tsdbService2")
@Slf4j
public class ElinkLindormServiceImpl implements TimeSeriesDatabaseService {

	@Value("${influxdb.elink.database:default}")
	private String influxdbDatabase;

	@Value("${influxdb.elink.table:e_linter_by_sn_dev}")
	private String influxdbTable;

	@Resource
	private FluxQueryWrapper elinkFluxQueryWrapper;

	@Override
	public Map<String, Object> lastPoint(
			String deviceFlag, List<String> metricList, long start,
			long end) {
		return elinkFluxQueryWrapper.lastPoint(BasicParamDTO.builder()
				.database(influxdbDatabase)
				.table(influxdbTable)
				.deviceName(deviceFlag)
				.metricList(metricList)
				.start(start)
				.end(end)
				.build());
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> withSampleQuery(TsdbQueryDTO param) {
		return elinkFluxQueryWrapper.withSampleQuery(SampleParamDTO.builder()
				.database(influxdbDatabase)
				.table(influxdbTable)
				.deviceName(param.getCloudId())
				.metricList(param.getMetricList())
				.start(param.getStartTime())
				.end(param.getEndTime())
				.times(Long.parseLong(param.getTimes().substring(0, param.getTimes().length() - 1)))
				.isCreateEmpty(false)
				.build());
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> withOutSampleQuery(TsdbQueryDTO param) {
		return elinkFluxQueryWrapper.withoutSampleQuery(BasicParamDTO.builder()
				.database(influxdbDatabase)
				.table(influxdbTable)
				.deviceName(param.getCloudId())
				.metricList(param.getMetricList())
				.start(param.getStartTime())
				.end(param.getEndTime())
				.build());
	}

	@Override
	public Result withSampleQueryList(TsdbQueryDTO param) {
		com.weihengtech.influx.pojo.Result result = elinkFluxQueryWrapper.withSampleQueryList(SampleParamDTO.builder()
				.database(influxdbDatabase)
				.table(influxdbTable)
				.deviceName(param.getCloudId())
				.metricList(param.getMetricList())
				.start(param.getStartTime())
				.end(param.getEndTime())
				.times(Long.parseLong(param.getTimes().substring(0, param.getTimes().length() - 1)))
				.isCreateEmpty(false)
				.build());
		return new Result(result.getColumns(), result.getMetadata(), result.getRows());
	}

	@Override
	public Result withOutSampleQueryList(TsdbQueryDTO param) {
		com.weihengtech.influx.pojo.Result result = elinkFluxQueryWrapper.withoutSampleQueryList(BasicParamDTO.builder()
				.database(influxdbDatabase)
				.table(influxdbTable)
				.deviceName(param.getCloudId())
				.metricList(param.getMetricList())
				.start(param.getStartTime())
				.end(param.getEndTime())
				.build());
		return new Result(result.getColumns(), result.getMetadata(), result.getRows());
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> deltaQuery(TsdbQueryDTO param) {
		return elinkFluxQueryWrapper.deltaQuery(SampleParamDTO.builder()
				.database(influxdbDatabase)
				.table(influxdbTable)
				.deviceName(param.getCloudId())
				.metricList(param.getMetricList())
				.start(param.getStartTime())
				.end(param.getEndTime())
				.timesStr(param.getTsdbSampleEnum().getDelta())
				.isCreateEmpty(false)
				.build());
	}

	@Override
	public Dict lastDataPoint(String deviceSn, LinkedList<String> metricList, long end) {
		Map<String, Object> map = elinkFluxQueryWrapper.lastPoint(BasicParamDTO.builder()
				.database(influxdbDatabase)
				.table(influxdbTable)
				.deviceName(deviceSn)
				.metricList(metricList)
				.start(end - 60*5)
				.end(end)
				.build());
		Dict dict = Dict.create();
		dict.putAll(map);
		return dict;
	}
}
