package com.weihengtech.service.other.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.dao.other.DictItemMapper;
import com.weihengtech.pojo.dos.other.DictDO;
import com.weihengtech.pojo.dos.other.DictItemDO;
import com.weihengtech.pojo.dtos.other.DictItemDTO;
import com.weihengtech.service.other.DictItemService;
import com.weihengtech.service.other.DictService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
@DS("ecos-event")
public class DictItemServiceImpl extends ServiceImpl<DictItemMapper, DictItemDO> implements DictItemService {

    @Resource
    private DictService dictService;

    @Override
    public List<DictItemDTO> queryDictItemsByDictCode(String dictCode, Boolean isTranslate, String lang) {
        DictDO dict = dictService.getOne(Wrappers.<DictDO>lambdaQuery()
                .eq(DictDO::getCode, dictCode));
        if (dict == null) {
            return Collections.emptyList();
        }
        List<DictItemDO> itemList = list(Wrappers.<DictItemDO>lambdaQuery()
                .eq(DictItemDO::getDictId, dict.getId()));
        if (CollUtil.isEmpty(itemList)) {
            return Collections.emptyList();
        }
        String prop;
        if (isTranslate) {
            String[] s = lang.split("_");
            prop = s[0] + s[1];
        } else {
            prop = "zhCN";
        }
        return itemList.stream()
                .map(i -> DictItemDTO.builder()
                        .code(i.getCode())
                        .name(isTranslate ? BeanUtil.getFieldValue(i, prop).toString() : i.getZhCN())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDictItem(Long dictId, List<DictItemDO> dictItemList) {
        removeDictItem(dictId);
        dictItemList.forEach(dictItem -> baseMapper.insert(dictItem));
    }

    @Override
    public void removeDictItem(Long dictId) {
        baseMapper.delete(Wrappers.<DictItemDO>lambdaQuery()
                .eq(DictItemDO::getDictId, dictId));
    }
}
