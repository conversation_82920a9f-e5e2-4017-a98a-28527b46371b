package com.weihengtech.service.other.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.dao.other.EcosDeviceMaintenanceMapper;
import com.weihengtech.pojo.dos.other.EcosDeviceMaintenanceDo;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.other.EcosDeviceMaintenanceInfoDto;
import com.weihengtech.pojo.vos.other.EcosDeviceMaintenanceInfoUpdateVo;
import com.weihengtech.service.other.EcosDeviceMaintenanceService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.utils.ActionFlagUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EcosDeviceMaintenanceImpl extends ServiceImpl<EcosDeviceMaintenanceMapper, EcosDeviceMaintenanceDo> implements EcosDeviceMaintenanceService {

	@Resource
	private DeviceListService deviceListService;

	@Override
	@Transactional
	public void updateInfo(EcosDeviceMaintenanceInfoUpdateVo ecosDeviceMaintenanceInfoUpdateVo) {
		EcosDeviceMaintenanceDo ecosDeviceMaintenanceDo = getBaseMapper().selectById(ecosDeviceMaintenanceInfoUpdateVo.getId());
		if (null == ecosDeviceMaintenanceDo) throw new CustomException(ExceptionEnum.DEVICE_NOT_EXIST);
		String username = UserInfoUtil.currentUserEmail();
		Optional.ofNullable(ecosDeviceMaintenanceInfoUpdateVo.getRecord()).ifPresent(record -> {
			ecosDeviceMaintenanceDo.setRecord(record);
			ecosDeviceMaintenanceDo.setUpdateBy(username);
			ecosDeviceMaintenanceDo.setUpdateTime(System.currentTimeMillis());
			ActionFlagUtil.singleAction(getBaseMapper().updateById(ecosDeviceMaintenanceDo));
		});
	}

	@Override
	@Transactional
	public EcosDeviceMaintenanceInfoDto getInfo(Long deviceId) {
		DeviceListDO deviceListDO = deviceListService.getDeviceById(deviceId);
		if (null == deviceListDO) throw new CustomException(ExceptionEnum.DEVICE_NOT_EXIST);
		String username = UserInfoUtil.currentUserEmail();
		EcosDeviceMaintenanceDo ecosDeviceMaintenanceDo = getBaseMapper().selectById(deviceId);
		if (null == ecosDeviceMaintenanceDo) {
			ecosDeviceMaintenanceDo = new EcosDeviceMaintenanceDo();
			ecosDeviceMaintenanceDo.setId(deviceListDO.getId());
			ecosDeviceMaintenanceDo.setRecord("");
			ecosDeviceMaintenanceDo.setUpdateTime(System.currentTimeMillis());
			ecosDeviceMaintenanceDo.setUpdateBy(username);
			ActionFlagUtil.singleAction(getBaseMapper().insert(ecosDeviceMaintenanceDo));
		}
		return new EcosDeviceMaintenanceInfoDto()
				.setId(ecosDeviceMaintenanceDo.getId().toString())
				.setRecord(ecosDeviceMaintenanceDo.getRecord());
	}
}
