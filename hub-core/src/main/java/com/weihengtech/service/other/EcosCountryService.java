package com.weihengtech.service.other;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.auth.model.dto.CountryDTO;
import com.weihengtech.pojo.dos.other.EcosCountryDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface EcosCountryService extends IService<EcosCountryDO> {

	/**
	 * 查询国家列表
	 *
	 * @return 国家信息列表
	 */
	List<CountryDTO> queryCountryList();

	/**
	 * 是否存在国家id
	 *
	 * @param countryId 国家id
	 * @return 是否存在
	 */
	boolean exist(Integer countryId);

	/**
	 * 根据Ip批量翻译归属地
	 *
	 * @return Ip归属地列表
	 */
	Map<String, String> queryIpList(List<String> ipList);
}
