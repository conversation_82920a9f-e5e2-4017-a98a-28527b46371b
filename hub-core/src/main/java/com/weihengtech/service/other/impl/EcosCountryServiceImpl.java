package com.weihengtech.service.other.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.weihengtech.api.BaiduIpParserApi;
import com.weihengtech.api.WhIpParserApi;
import com.weihengtech.auth.model.dto.CountryDTO;
import com.weihengtech.auth.rpc.AuthCenterClient;
import com.weihengtech.common.DataResponse;
import com.weihengtech.dao.other.EcosCountryMapper;
import com.weihengtech.pojo.dos.other.EcosCountryDO;
import com.weihengtech.pojo.dtos.other.IpInfoDTO;
import com.weihengtech.service.other.EcosCountryService;
import com.weihengtech.utils.AsyncResultUtil;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
public class EcosCountryServiceImpl extends ServiceImpl<EcosCountryMapper, EcosCountryDO>
		implements
		EcosCountryService {

	@Resource
	private WhIpParserApi whIpParserApi;
	@Resource
	private BaiduIpParserApi baiduIpParserApi;
	@Resource
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;
	@Resource
	private AuthCenterClient authCenterClient;

	@Override
	public List<CountryDTO> queryCountryList() {
		List<CountryDTO> countryList = authCenterClient.listCountry();
		countryList.add(CountryDTO.builder().id(0).cnName("--").enName("--").name("--").build());
		return countryList;
	}

	@Override
	public boolean exist(Integer countryId) {
		return getById(countryId) != null;
	}

    @Override
    public Map<String, String> queryIpList(List<String> ipList) {
		if (CollUtil.isEmpty(ipList)) {
			return Collections.emptyMap();
		}
		try {
	//		return whParserIp(ipList);
			return baiduParserIp(ipList);
		} catch (Exception e) {
			log.error(String.format("query ip error, ip is: %s", ipList), e);
			return Collections.emptyMap();
		}
	}

	/**
	 * 百度服务解析Ip归属地
	 *
	 * @param ipList ip
	 * @return Ip归属地映射
	 */
	private Map<String, String> baiduParserIp(List<String> ipList) {
		if (CollUtil.isEmpty(ipList)) {
			return Collections.emptyMap();
		}
		if (ipList.size() == 1) {
			return parseBaiduIp(ipList.get(0));
		}
		Map<String, String> resMap = new HashMap<>(ipList.size());
		List<List<String>> pages = Lists.partition(ipList, 2);
		List<Map<String, String>> resList = new ArrayList<>(ipList.size());
		for (List<String> page : pages) {
			List<Map<String, String>> pageRes = AsyncResultUtil.multiThreadDone(page, this::parseBaiduIp, threadPoolTaskExecutor);
			resList.addAll(pageRes);
			// 接口QPS=2，间隔1s请求
			try {
				TimeUnit.MILLISECONDS.sleep(1000);
			} catch (InterruptedException e) {
				throw new RuntimeException(e);
			}
		}
		if (CollUtil.isNotEmpty(resList)) {
			resList.forEach(resMap::putAll);
		}
		return resMap;
	}

	private Map<String, String> parseBaiduIp(String ip) {
		JSONObject response = baiduIpParserApi.ip(ip);
		if (response == null || !response.containsKey("data") || !"200".equals(response.getString("code"))) {
			return Collections.emptyMap();
		}
		IpInfoDTO ipInfo = response.getObject("data", IpInfoDTO.class);
		return Collections.singletonMap(ip, ipInfo.getCountry());
	}

	/**
	 * 为恒Ip服务解析归属地
	 *
	 * @param ipList ip
	 * @return Ip归属地映射
	 */
	private Map<String, String> whParserIp(List<String> ipList) {
		if (CollUtil.isEmpty(ipList)) {
			return Collections.emptyMap();
		}
		if (ipList.size() == 1) {
			return parseIp(ipList);
		}
		Map<String, String> resMap = new HashMap<>(ipList.size());
		List<List<String>> pages = Lists.partition(ipList, 30);
		List<Map<String, String>> result = AsyncResultUtil.multiThreadDone(pages, this::parseIp, threadPoolTaskExecutor);
		if (CollUtil.isNotEmpty(result)) {
			result.forEach(resMap::putAll);
		}
		return resMap;
	}

	private Map<String, String> parseIp(List<String> ipList) {
		if (CollUtil.isEmpty(ipList)) {
			return Collections.emptyMap();
		}
		String ipStr = String.join(Constants.COMMA, ipList);
		DataResponse<Map<String, String>> response = whIpParserApi.loc(ipStr);
		if (response == null || response.getData() == null || CollUtil.isEmpty(response.getData())) {
			return Collections.emptyMap();
		}
		Map<String, String> ipDataMap = response.getData();
		ipDataMap.forEach((k, v) -> {
			if (StrUtil.isNotBlank(v)) {
				String content = v.substring(1, v.length() - 1);
				if (content.contains(" ")) {
					content = content.substring(0, content.indexOf(" "));
				}
				v = content;
				ipDataMap.put(k, v);
			}
		});
		return response.getData();
    }
}
