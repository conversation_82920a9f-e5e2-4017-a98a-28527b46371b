package com.weihengtech.service.other.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.weihengtech.auth.model.dto.SubUserResDTO;
import com.weihengtech.auth.model.dto.UserResDTO;
import com.weihengtech.auth.rpc.AuthCenterClient;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.dao.other.OperatingRecordMapper;
import com.weihengtech.pojo.dos.other.OperatingRecordDo;
import com.weihengtech.pojo.dtos.other.OperatingRecordPageDto;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.other.OperatingRecordPageVo;
import com.weihengtech.service.other.OperatingRecordService;
import com.weihengtech.utils.OperationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.weihengtech.consts.RoleConstants.ROLE_ADMIN;
import static com.weihengtech.consts.RoleConstants.ROLE_AFTER_SALE;
import static com.weihengtech.consts.RoleConstants.ROLE_AFTER_SALE_ADMIN;
import static com.weihengtech.consts.RoleConstants.ROLE_AGENT;
import static com.weihengtech.consts.RoleConstants.ROLE_DEALER;
import static com.weihengtech.consts.RoleConstants.ROLE_DEV;
import static com.weihengtech.consts.RoleConstants.ROLE_DEV_ADMIN;
import static com.weihengtech.consts.RoleConstants.ROLE_DEV_FIRMWARE;
import static com.weihengtech.consts.RoleConstants.ROLE_DEV_VERSION;
import static com.weihengtech.consts.RoleConstants.ROLE_RETAILER;
import static com.weihengtech.consts.RoleConstants.ROLE_SALE;
import static com.weihengtech.consts.RoleConstants.ROLE_SALE_ADMIN;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OperatingRecordServiceImpl extends ServiceImpl<OperatingRecordMapper, OperatingRecordDo>
		implements
		OperatingRecordService {

	@Resource
	private AuthCenterClient authCenterClient;

	@Override
	public PageInfoDTO<OperatingRecordPageDto> pageOperatingRecord(
			OperatingRecordPageVo operatingRecordPageVo
	) {
		String role = UserInfoUtil.currentUserRoleCategory();
		String userId = UserInfoUtil.currentUserId();
		PageInfoDTO<OperatingRecordPageDto> pageInfoDto = new PageInfoDTO<>();
		pageInfoDto.setTotalPages(0);
		pageInfoDto.setTotalCount(0L);
		pageInfoDto.setData(ListUtil.empty());

		LambdaQueryWrapper<OperatingRecordDo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
		lambdaQueryWrapper.orderByDesc(OperatingRecordDo::getCreateTime);


		switch (role) {
			case ROLE_DEV_ADMIN:
				List<UserResDTO> devUser = authCenterClient.userByRole(null, ROLE_DEV);
				List<UserResDTO> firmwareUser = authCenterClient.userByRole(null, ROLE_DEV_FIRMWARE);
				List<UserResDTO> versionUser = authCenterClient.userByRole(null, ROLE_DEV_VERSION);
				List<UserResDTO> devAdminUser = authCenterClient.userByRole(null, ROLE_DEV_ADMIN);
				List<Long> devIds = devUser.parallelStream().map(UserResDTO::getId).map(Long :: parseLong).collect(Collectors.toList());
				List<Long> firmwareIds = firmwareUser.parallelStream().map(UserResDTO::getId).map(Long :: parseLong).collect(Collectors.toList());
				List<Long> versionIds = versionUser.parallelStream().map(UserResDTO::getId).map(Long :: parseLong).collect(Collectors.toList());
				List<Long> devAdminIds = devAdminUser.parallelStream().map(UserResDTO::getId).map(Long :: parseLong).collect(Collectors.toList());

				List<Long> ids = new ArrayList<>();
				if (CollUtil.isNotEmpty(devIds)) ids.addAll(devIds);
				if (CollUtil.isNotEmpty(firmwareIds)) ids.addAll(firmwareIds);
				if (CollUtil.isNotEmpty(versionIds)) ids.addAll(versionIds);
				if (CollUtil.isNotEmpty(devAdminIds)) ids.addAll(devAdminIds);
				if (CollUtil.isNotEmpty(ids)) {
					lambdaQueryWrapper.in(OperatingRecordDo::getUserId, ids);
				} else {
					return pageInfoDto;
				}
				break;
			case ROLE_SALE_ADMIN:
				List<Long> saleAdminQueryIds = new ArrayList<>();
				List<UserResDTO> saleUserRoleBos = authCenterClient.userByRole(null, ROLE_SALE);
				List<UserResDTO> agentUserRoleBos = authCenterClient.userByRole(null, ROLE_AGENT);
				List<UserResDTO> dealerUserRoleBos = authCenterClient.userByRole(null, ROLE_DEALER);
				List<UserResDTO> retailerUserRoleBos = authCenterClient.userByRole(null, ROLE_RETAILER);
				List<Long> sIds = saleUserRoleBos.parallelStream().map(UserResDTO::getId).map(Long :: parseLong).collect(Collectors.toList());
				List<Long> aIds = agentUserRoleBos.parallelStream().map(UserResDTO::getId).map(Long :: parseLong).collect(Collectors.toList());
				List<Long> dIds = dealerUserRoleBos.parallelStream().map(UserResDTO::getId).map(Long :: parseLong).collect(Collectors.toList());
				List<Long> rIds = retailerUserRoleBos.parallelStream().map(UserResDTO::getId).map(Long :: parseLong).collect(Collectors.toList());
				if (CollUtil.isNotEmpty(sIds)) saleAdminQueryIds.addAll(sIds);
				if (CollUtil.isNotEmpty(aIds)) saleAdminQueryIds.addAll(aIds);
				if (CollUtil.isNotEmpty(dIds)) saleAdminQueryIds.addAll(dIds);
				if (CollUtil.isNotEmpty(rIds)) saleAdminQueryIds.addAll(rIds);
				saleAdminQueryIds.add(Long.parseLong(userId));
				lambdaQueryWrapper.in(OperatingRecordDo::getUserId, saleAdminQueryIds);
				break;
			case ROLE_SALE:
				List<SubUserResDTO> subUserList = authCenterClient.userByTopUser();
				List<Long> bindAgentIdList = subUserList.stream()
						.filter(i -> userId.equals(i.getParentUserId()))
						.map(SubUserResDTO::getUserId)
						.map(Long::parseLong)
						.collect(Collectors.toList());
				bindAgentIdList.add(Long.parseLong(userId));
				lambdaQueryWrapper.in(OperatingRecordDo::getUserId, bindAgentIdList);
				break;
//			case ROLE_AFTER_SALE_ADMIN:
//				List<UserResDTO> afterSaleRoleBos = authCenterClient.userByRole(null, ROLE_AFTER_SALE);
//				List<Long> afterSaleIdList = afterSaleRoleBos.parallelStream()
//						.map(UserResDTO::getId)
//						.map(Long :: parseLong)
//						.collect(Collectors.toList());
//				afterSaleIdList.add(Long.parseLong(userId));
//				lambdaQueryWrapper.in(OperatingRecordDo::getUserId, afterSaleIdList);
//				break;
			// agent和dealer逻辑一致
			case ROLE_AGENT:
			case ROLE_DEALER:
				List<Long> userIdList = new ArrayList<>();
				List<SubUserResDTO> agentSubList = authCenterClient.userByTopUser();
				if (CollUtil.isNotEmpty(agentSubList)) {
					List<Long> agentSubIdList = agentSubList.stream()
							.map(SubUserResDTO::getUserId)
							.map(Long::parseLong)
							.collect(Collectors.toList());
					userIdList.addAll(agentSubIdList);
				}
				userIdList.add(Long.parseLong(userId));
				lambdaQueryWrapper.in(OperatingRecordDo::getUserId, userIdList);
				break;
			// 售后管理、售后权限等同于管理员
			case ROLE_AFTER_SALE_ADMIN:
			case ROLE_AFTER_SALE:
			case ROLE_ADMIN:
				break;
			default:
				lambdaQueryWrapper.eq(OperatingRecordDo::getUserId, Long.parseLong(userId));
		}
		OperationUtil.of(operatingRecordPageVo.getUsername()).then(username -> {
			List<UserResDTO> users = authCenterClient.list(null, username);
			if (CollUtil.isNotEmpty(users)) {
				lambdaQueryWrapper.in(OperatingRecordDo::getUserId,
						users.stream()
								.map(UserResDTO::getId)
								.map(Long :: parseLong)
								.collect(Collectors.toList()));
			}
		});
		OperationUtil.of(operatingRecordPageVo.getDeviceName())
				.then(deviceName -> lambdaQueryWrapper.like(OperatingRecordDo::getContent, deviceName));
		OperationUtil.of(operatingRecordPageVo.getModuleId())
				.then(moduleId -> lambdaQueryWrapper.eq(OperatingRecordDo::getModule, moduleId));
		OperationUtil.of(operatingRecordPageVo.getStartTime())
				.then(start -> lambdaQueryWrapper.ge(OperatingRecordDo::getCreateTime, start));
		OperationUtil.of(operatingRecordPageVo.getEndTime())
				.then(end -> lambdaQueryWrapper.le(OperatingRecordDo::getCreateTime, end));

		PageHelper.startPage(operatingRecordPageVo.getPageNum(), operatingRecordPageVo.getPageSize());
		List<OperatingRecordDo> doList = this.list(lambdaQueryWrapper);

		PageInfo<OperatingRecordDo> pageInfo = new PageInfo<>(doList);
		pageInfoDto.setTotalPages(pageInfo.getPages());
		pageInfoDto.setTotalCount(pageInfo.getTotal());
		String idList = doList.stream()
				.map(OperatingRecordDo::getUserId)
				.map(String :: valueOf)
				.collect(Collectors.joining(Constants.COMMA));
		List<UserResDTO> userList = StrUtil.isBlank(idList) ? null : authCenterClient.list(idList, null);
		Map<String, UserResDTO> map = new HashMap<>();
		if (CollUtil.isNotEmpty(userList)) {
			map = userList.stream()
					.collect(Collectors.toMap(UserResDTO::getId, Function.identity()));
		}
		Map<String, UserResDTO> finalMap = map;
		pageInfoDto.setData(doList.stream().map(operatingRecordDo -> {
			OperatingRecordPageDto operatingRecordPageDto = new OperatingRecordPageDto();
			CglibUtil.copy(operatingRecordDo, operatingRecordPageDto);
			UserResDTO ecosUserDO = finalMap.get(String.valueOf(operatingRecordDo.getUserId()));
			if (ecosUserDO == null) {
				operatingRecordPageDto.setUsername("账号已删除");
				operatingRecordPageDto.setEmail("账号已删除");
			} else {
				operatingRecordPageDto.setUsername(ecosUserDO.getNickName());
				operatingRecordPageDto.setEmail(ecosUserDO.getEmail());
			}
			operatingRecordPageDto.setModule(EnumConstants.RecordModule.getModuleName(operatingRecordDo.getModule()));
			return operatingRecordPageDto;
		}).collect(Collectors.toList()));

		return pageInfoDto;
	}
}
