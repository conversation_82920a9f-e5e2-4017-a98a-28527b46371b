package com.weihengtech.service.other;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.other.OperatingRecordDo;
import com.weihengtech.pojo.dtos.other.OperatingRecordPageDto;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.other.OperatingRecordPageVo;

/**
 * <AUTHOR>
 */
public interface OperatingRecordService extends IService<OperatingRecordDo> {

	/**
	 * 分页查询操作记录
	 *
	 * @param operatingRecordPageVo 操作记录入参
	 * @return 分页数据
	 */
	PageInfoDTO<OperatingRecordPageDto> pageOperatingRecord(
			OperatingRecordPageVo operatingRecordPageVo
	);
}
