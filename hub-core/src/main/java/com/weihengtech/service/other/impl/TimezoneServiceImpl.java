package com.weihengtech.service.other.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.dao.other.TimezoneMapper;
import com.weihengtech.pojo.dos.other.TimezoneDo;
import com.weihengtech.pojo.dtos.other.TimezoneDto;
import com.weihengtech.service.other.TimezoneService;
import com.weihengtech.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TimezoneServiceImpl extends ServiceImpl<TimezoneMapper, TimezoneDo> implements TimezoneService {

	@Resource
	private RedisTemplate<String, TimezoneDto> redisTemplate;

	@Override
	public List<TimezoneDto> allTimezoneWithLocale() {
		String key = "HUB-Timezone";
		if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
			return Optional.ofNullable(redisTemplate.opsForSet().members(key))
					.orElse(new HashSet<>()).parallelStream().collect(Collectors.toList());
		}

		List<TimezoneDo> timezoneDoList = this.list();
		List<TimezoneDto> timezoneDtoList = timezoneDoList.stream().map(timezoneDo -> {
			TimezoneDto timezoneDto = new TimezoneDto();
			timezoneDto.setId(timezoneDo.getId());
			timezoneDto.setTimezone(TimeUtil.getLightingTimezone(timezoneDo.getTimezone()));
			timezoneDto.setName(timezoneDo.getName());
			timezoneDto.setCode(timezoneDo.getTimezone());
			return timezoneDto;
		}).collect(Collectors.toList());


		if (Boolean.FALSE.equals(redisTemplate.hasKey(key))) {
			redisTemplate.opsForSet().add(key, timezoneDtoList.toArray(new TimezoneDto[timezoneDoList.size()]));
			redisTemplate.expire(key, 24, TimeUnit.HOURS);
		}
		return timezoneDtoList;
	}
}
