package com.weihengtech.service.other.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.auth.dto.ResourceListReqDTO;
import com.weihengtech.auth.dto.ResourceResDTO;
import com.weihengtech.auth.dto.ResourceUpdDTO;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.enums.other.DatacenterEnum;
import com.weihengtech.consts.RedisRefConstants;
import com.weihengtech.dao.other.TuyaDatacenterMapper;
import com.weihengtech.pojo.dos.other.TuyaDatacenterDo;
import com.weihengtech.pojo.dtos.other.DatacenterListDto;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TuyaDatacenterServiceImpl extends ServiceImpl<TuyaDatacenterMapper, TuyaDatacenterDo> implements TuyaDatacenterService {

    @Value("${custom.datacenter.name}")
    private String datacenter;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private AuthCenterResClient authCenterResClient;
    @Resource
    private DeviceListService deviceListService;

    @Override
    public void syncDatacenter(List<String> wifiSnList) {
        String rk = RedisRefConstants.buildDatacenterKey(datacenter);
        RSet<String> rSet = redissonClient.getSet(rk);

        Set<String> parsedSnList = parseWifiSnList(wifiSnList);

        if (rSet.isExists()) {
            Set<String> existSet = rSet.readAll();
            parsedSnList.removeAll(existSet);
            if (CollUtil.isNotEmpty(parsedSnList)) {
                rSet.addAll(parsedSnList);
                rSet.expire(1, TimeUnit.HOURS);
            }
        } else {
            rSet.addAll(parsedSnList);
            rSet.expire(1, TimeUnit.HOURS);
        }


        if (CollUtil.isNotEmpty(parsedSnList)) {
            Integer datacenterId = DatacenterEnum.getDatacenterId(datacenter);
            List<String> ids = deviceListService.getBatchIdsByWifiSn(parsedSnList);
            if (CollUtil.isEmpty(ids)) {
                return;
            }
            ResourceUpdDTO updReq = ResourceUpdDTO.builder()
                    .resourceId(String.join(Constants.COMMA, ids))
                    .dataCenter(datacenterId)
                    .build();
            authCenterResClient.updResource(updReq);
        }
    }

    @Override
    public List<DatacenterListDto> listDatacenter(HttpServletRequest request) {
        return list()
                .stream()
                .map(tuyaDatacenterDo -> {
                    DatacenterListDto guideDatacenterListDto = new DatacenterListDto();
                    CglibUtil.copy(tuyaDatacenterDo, guideDatacenterListDto);
                    guideDatacenterListDto.setCountry(ReflectUtil.invoke(tuyaDatacenterDo, LocaleUtil.mapLocaleToDatabaseGetMethod(request)));
                    return guideDatacenterListDto;
                })
                .collect(Collectors.toList());
    }

    @Override
    public boolean isSamePhoneCode(Integer centerId1, Integer centerId2) {
        if (centerId1.equals(centerId2)) {
            return true;
        }
        List<Integer> list = Arrays.asList(centerId1, centerId2);
        List<TuyaDatacenterDo> itemList = list(Wrappers.<TuyaDatacenterDo>lambdaQuery()
                .in(TuyaDatacenterDo::getId, list));
        ActionFlagUtil.assertTrue(CollUtil.isNotEmpty(itemList) && itemList.size() == 2);
        return Objects.equals(itemList.get(0).getPhoneCode(), itemList.get(1).getPhoneCode());
    }

    private Set<String> parseWifiSnList(List<String> wifiSnList) {
        return wifiSnList.parallelStream().flatMap(sn -> Arrays.stream(sn.split(","))).collect(Collectors.toSet());
    }
}
