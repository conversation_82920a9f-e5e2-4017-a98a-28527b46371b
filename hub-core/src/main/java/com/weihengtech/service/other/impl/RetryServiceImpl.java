package com.weihengtech.service.other.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.common.exceptions.RetryException;
import com.weihengtech.enums.charger.ChargerStatusEnum;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.enums.socket.SocketStatusEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.firmware.FirmwareUpgradeRecordDO;
import com.weihengtech.pojo.dtos.device.DeviceSyncVersionDto;
import com.weihengtech.pojo.dtos.device.info.DeviceInformationDTO;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.Connector;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpStatusResponse;
import com.weihengtech.service.device.DeviceDetailService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.firmware.FirmwareUpgradeRecordService;
import com.weihengtech.service.firmware.MqService;
import com.weihengtech.service.iot.IotClientService;
import com.weihengtech.service.other.RetryService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.utils.ActionFlagUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RetryServiceImpl implements RetryService {

	@Resource
	private DeviceDetailService deviceDetailService;
	@Resource
	private DeviceListService deviceListService;
	@Resource
	private StrategyService strategyService;
	@Resource
	private FirmwareUpgradeRecordService firmwareUpgradeRecordService;
	@Resource
	private MqService mqService;
	@Resource
	private IotClientService ocppIotClient;

	@Override
	@Retryable(maxAttempts = 2, value = {RetryException.class}, backoff = @Backoff(delay = 2500, multiplier = 1))
	public DeviceSyncVersionDto syncDeviceVersion(
			DeviceListDO deviceListDO,
			Boolean retryable
	) {
		boolean canRetry;
		DeviceSyncVersionDto deviceSyncVersionDto = null;
		try {
			DeviceInformationDTO deviceInformationDTO = deviceDetailService
					.queryDeviceInformation(deviceListDO.getDeviceName(), Boolean.FALSE);
			Pair<DeviceSyncVersionDto, Boolean> deviceCanRetryPair = deviceListService
					.syncDeviceVersionByReadResult(deviceInformationDTO, deviceListDO);
			deviceSyncVersionDto = deviceCanRetryPair.getKey();
			canRetry = deviceCanRetryPair.getValue();
		} catch (Exception e) {
			canRetry = true;
		}
		if (canRetry && retryable) {
			throw new RetryException(ExceptionEnum.DEVICE_QUERY_DATA_ERROR);
		}
		return deviceSyncVersionDto;
	}

	@Override
	@Retryable(maxAttempts = 10, value = {RetryException.class}, backoff = @Backoff(delay = 5000, multiplier = 1))
	public void syncSocketState(String deviceSn) {
		DeviceListDO deviceInfo = deviceListService.lambdaQuery().eq(DeviceListDO::getDeviceName, deviceSn).one();
		if (null == deviceInfo) {
			log.error("socket device not exists: {}", deviceSn);
			throw new RetryException(9998, String.format("socket device not exists: %s", deviceSn));
		}
		if (deviceInfo.getState() > 0) {
			log.info("socket device already online: {}", deviceSn);
			return;
		}
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceInfo);
		DeviceStatusEnum deviceStatusEnum = specificServService.checkDeviceStatus(deviceInfo.getWifiSn());
		if (DeviceStatusEnum.OFFLINE.equals(deviceStatusEnum)) {
			log.error("socket sync state offline: {}", deviceSn);
			throw new RetryException(9998, String.format("socket sync state offline: %s", deviceSn));
		}
		JSONObject res = specificServService.getDeviceAssignProperty(deviceInfo.getDeviceName(), "switch_1");
		Integer switch11 = Optional.ofNullable(res)
				.map(i -> i.get("switch_1"))
				.map(String::valueOf)
				.map(Boolean::parseBoolean)
				.map(SocketStatusEnum::getIdByCode)
				.orElseThrow(() -> {
					log.error("socket read last state failed: {}", deviceSn);
					return new RetryException(9998, String.format("socket read last state failed: %s", deviceSn));
				});
		deviceInfo.setState(switch11);
		deviceListService.updateById(deviceInfo);
		log.info("socket {} sync state success: {}", deviceSn, switch11);
	}

	@Override
	@Retryable(maxAttempts = 10, value = {RetryException.class}, backoff = @Backoff(delay = 5000, multiplier = 1))
	public void syncChargerState(String deviceSn) {
		DeviceListDO deviceInfo = deviceListService.lambdaQuery().eq(DeviceListDO::getDeviceName, deviceSn).one();
		if (null == deviceInfo) {
			log.error("charger device not exists: {}", deviceSn);
			throw new RetryException(9998, String.format("charger device not exists: %s", deviceSn));
		}
		if (deviceInfo.getState() > 0) {
			log.info("charger already online: {}", deviceSn);
			return;
		}
		CpStatusResponse chargerStatus = ocppIotClient.getChargerStatus(deviceSn);
		List<Connector> connectors = chargerStatus.getConnectors();
		if (CollUtil.isEmpty(connectors) || connectors.get(0) == null || connectors.get(0).getStatus() == null) {
			log.error("charger state read failed: {}", deviceSn);
			throw new RetryException(9998, String.format("charger state read failed: %s", deviceSn));
		}
		Integer status = ChargerStatusEnum.getCodeByStatus(connectors.get(0).getStatus());
		deviceInfo.setState(status);
		deviceListService.updateById(deviceInfo);
		log.info("charger {} sync state success: {}", deviceSn, status);
	}

	@Override
	@Retryable(maxAttempts = 10, value = {RetryException.class}, backoff = @Backoff(delay = 5000, multiplier = 1))
	public void syncWhDeviceState(String deviceSn) {
		DeviceListDO deviceInfo = deviceListService.lambdaQuery().eq(DeviceListDO::getDeviceName, deviceSn).one();
		if (null == deviceInfo) {
			log.error("whIot device not exists: {}", deviceSn);
			throw new RetryException(9998, String.format("whIot device not exists: %s", deviceSn));
		}
		if (deviceInfo.getState() > 0) {
			log.info("whIot device already online: {}", deviceSn);
			return;
		}
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceInfo);
		DeviceStatusEnum deviceStatusEnum = specificServService.checkDeviceStatus(deviceInfo.getWifiSn());
		if (DeviceStatusEnum.OFFLINE.equals(deviceStatusEnum)) {
			log.error("whIot device sync state offline: {}", deviceSn);
			throw new RetryException(9998, String.format("whIot device sync state offline: %s", deviceSn));
		}
		JSONObject res = specificServService.getDeviceAssignProperty(deviceInfo.getDeviceName(), "sys_run_mode");
		Integer sysRunMode = Optional.ofNullable(res)
				.map(i -> i.get("sys_run_mode"))
				.map(String::valueOf)
				.map(i -> NumberUtil.round(i, 0))
				.map(BigDecimal::intValue)
				.orElseThrow(() -> {
					log.error("whIot device read last state failed: {}", deviceSn);
					return new RetryException(9998, String.format("whIot device read last state failed: %s", deviceSn));
				});
		deviceInfo.setState(sysRunMode);
		deviceListService.updateById(deviceInfo);
		log.info("whIot device {} sync state success: {}", deviceSn, sysRunMode);
	}

	@Override
	@Retryable(value = {RetryException.class}, backoff = @Backoff(delay = 5000, multiplier = 1))
	public void speedUpDevice(String wifiSn, int dataSource) {
		SpecificServService specificServService = strategyService.chooseSpecificServ(dataSource);
		try {
			specificServService.sendSpeedupCommand(wifiSn);
		} catch (Exception e) {
			throw new RetryException(9998, String.format("whIot device speedup failed: %s", wifiSn));
		}
	}


}
