package com.weihengtech.service.other;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.other.DictItemDO;
import com.weihengtech.pojo.dtos.other.DictItemDTO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface DictItemService extends IService<DictItemDO> {

    List<DictItemDTO> queryDictItemsByDictCode(String dictCode, Boolean isTranslate, String lang);

    void updateDictItem(Long dictId, List<DictItemDO> dictItemList);

    void removeDictItem(Long dictId);

}
