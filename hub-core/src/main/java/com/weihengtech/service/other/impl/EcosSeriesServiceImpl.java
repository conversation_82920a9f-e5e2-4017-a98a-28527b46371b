package com.weihengtech.service.other.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.dao.other.EcosSeriesMapper;
import com.weihengtech.pojo.dos.other.EcosSeriesDO;
import com.weihengtech.pojo.dtos.other.EcosSeriesDTO;
import com.weihengtech.pojo.vos.ecosseries.SeriesInsertVO;
import com.weihengtech.pojo.vos.ecosseries.SeriesUpdateVO;
import com.weihengtech.service.other.EcosSeriesService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.OperationUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class EcosSeriesServiceImpl extends ServiceImpl<EcosSeriesMapper, EcosSeriesDO> implements EcosSeriesService {

	@Resource
	private EcosSeriesMapper ecosSeriesMapper;

	@Override
	public List<EcosSeriesDTO> querySeries() {
		List<EcosSeriesDTO> dtoList = new ArrayList<>(32);
		List<EcosSeriesDO> ecosSeriesDOList = ecosSeriesMapper.selectList(new QueryWrapper<>());
		ecosSeriesDOList.forEach(ecosSeriesDO -> dtoList
				.add(EcosSeriesDTO.builder().id(ecosSeriesDO.getId()).cnName(ecosSeriesDO.getCnName())
						.enName(ecosSeriesDO.getEnName()).seriesRoute(ecosSeriesDO.getSeriesRoute()).build()));
		return dtoList;
	}

	@Override
	@DSTransactional
	public void insertSeries(SeriesInsertVO seriesInsertVO) {
		EcosSeriesDO ecosSeriesDO = new EcosSeriesDO();
		ecosSeriesDO.setCnName(seriesInsertVO.getCnName());
		ecosSeriesDO.setEnName(seriesInsertVO.getEnName());
		ecosSeriesDO.setSeriesRoute(seriesInsertVO.getSeriesRoute());
		ecosSeriesDO.setCreateTime(LocalDateTime.now());
		ecosSeriesDO.setCreateUser(Long.parseLong(UserInfoUtil.currentUserId()));
		ActionFlagUtil.singleAction(ecosSeriesMapper.insert(ecosSeriesDO));
	}

	@Override
	@DSTransactional
	public void updateSeries(SeriesUpdateVO seriesUpdateVO) {
		Integer id = seriesUpdateVO.getId();
		EcosSeriesDO ecosSeriesDO = ecosSeriesMapper.selectById(id);
		BeanUtil.assertNotNull(ecosSeriesDO);

		OperationUtil.of(seriesUpdateVO.getCnName()).then(ecosSeriesDO::setCnName);
		OperationUtil.of(seriesUpdateVO.getEnName()).then(ecosSeriesDO::setEnName);
		OperationUtil.of(seriesUpdateVO.getSeriesRoute()).then(ecosSeriesDO::setSeriesRoute);

		ActionFlagUtil.singleAction(ecosSeriesMapper.updateById(ecosSeriesDO));
	}
}
