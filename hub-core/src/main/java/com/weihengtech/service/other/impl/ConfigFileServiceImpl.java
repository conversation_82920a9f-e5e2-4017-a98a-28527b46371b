package com.weihengtech.service.other.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.dao.other.OssMapper;
import com.weihengtech.service.other.ConfigFileService;
import com.weihengtech.utils.InitUtil;
import com.weihengtech.utils.OperatingRecordUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/3 15:00
 */
@Service
public class ConfigFileServiceImpl implements ConfigFileService {

    @Value("${custom.ali.oss.config.bucket:dcdn-config}")
    private String bucket;

    @Resource
    private OssMapper ossMapper;

    @Override
    public void uploadConfig(String content) {
        // 校验json文件
        if (!JSONUtil.isTypeJSON(content)) {
            throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
        }
        // 构建filename
        String active = InitUtil.APPLICATION_CONTEXT.getEnvironment().getProperty("spring.profiles.active", "dev");
        String fileName = "prod".equals(active) ? "prod/config.json" : "test/config.json";
        // 操作记录
        OperatingRecordUtil.log(Long.parseLong(UserInfoUtil.currentUserId()), RecordContentConstants.SAVE_CONFIG_FILE,
                MapUtil.<String, String>builder().put("fileName", fileName).build(),
                EnumConstants.RecordModule.CONFIG_FILE.getCode()
        );
        String jsonPrettyStr = JSONUtil.toJsonPrettyStr(JSONUtil.parseObj(content));
        // 上传文件
        ossMapper.putFile(bucket, fileName, jsonPrettyStr.getBytes());
    }
}
