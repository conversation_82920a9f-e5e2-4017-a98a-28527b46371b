package com.weihengtech.service.other.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.dao.other.EcosGlobalVersionMapper;
import com.weihengtech.pojo.dos.other.EcosGlobalVersionDo;
import com.weihengtech.pojo.dtos.other.EcosHubGlobalVersionDto;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionAddVo;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionUpdateVo;
import com.weihengtech.service.other.EcosGlobalVersionService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.OperatingRecordUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EcosGlobalVersionServiceImpl extends ServiceImpl<EcosGlobalVersionMapper, EcosGlobalVersionDo>
		implements
		EcosGlobalVersionService {

	@Override
	public PageInfoDTO<EcosHubGlobalVersionDto> pageHubVersion(Integer pageNum, Integer pageSize) {
		PageHelper.startPage(pageNum, pageSize);
		List<EcosGlobalVersionDo> doList = this
				.list(Wrappers.<EcosGlobalVersionDo>lambdaQuery().orderByDesc(EcosGlobalVersionDo::getCreateTime));
		PageInfo<EcosGlobalVersionDo> pageInfo = new PageInfo<>(doList);

		PageInfoDTO<EcosHubGlobalVersionDto> pageInfoDto = new PageInfoDTO<>();
		pageInfoDto.setTotalPages(pageInfo.getPages());
		pageInfoDto.setTotalCount(pageInfo.getTotal());
		pageInfoDto.setData(doList.stream().map(ecosGlobalVersionDo -> {
			EcosHubGlobalVersionDto ecosHubGlobalVersionDto = new EcosHubGlobalVersionDto();
			CglibUtil.copy(ecosGlobalVersionDo, ecosHubGlobalVersionDto);
			return ecosHubGlobalVersionDto;
		}).collect(Collectors.toList()));
		return pageInfoDto;
	}

	@Override
	@DSTransactional
	public void addHubVersion(EcosGlobalVersionAddVo ecosGlobalVersionAddVo) {
		EcosGlobalVersionDo ecosGlobalVersionDo = new EcosGlobalVersionDo();
		ecosGlobalVersionDo.setCreateTime(System.currentTimeMillis());
		CglibUtil.copy(ecosGlobalVersionAddVo, ecosGlobalVersionDo);
		ActionFlagUtil.assertTrue(this.save(ecosGlobalVersionDo));
		// tag
		OperatingRecordUtil.log(
				RecordContentConstants.INSERT_HUB_VERSION,
				MapUtil.<String, String>builder().put("androidVersionName", ecosGlobalVersionAddVo.getAndroidVersion())
						.put("iosVersionName", ecosGlobalVersionAddVo.getIosVersion()).build(),
				EnumConstants.RecordModule.VERSION_MAINTAIN.getCode()
		);
	}

	@Override
	@DSTransactional
	public void updateHubVersion(EcosGlobalVersionUpdateVo ecosGlobalVersionUpdateVo) {
		EcosGlobalVersionDo ecosGlobalVersionDo = this.getById(ecosGlobalVersionUpdateVo.getId());
		ecosGlobalVersionDo.setCreateTime(System.currentTimeMillis());
		BeanUtil.assertNotNull(ecosGlobalVersionDo);
		CglibUtil.copy(ecosGlobalVersionUpdateVo, ecosGlobalVersionDo);
		ActionFlagUtil.assertTrue(this.updateById(ecosGlobalVersionDo));
		// tag
		OperatingRecordUtil.log(
				RecordContentConstants.UPDATE_HUB_VERSION,
				MapUtil.<String, String>builder().put("versionId", String.valueOf(ecosGlobalVersionUpdateVo.getId()))
						.put("androidVersionName", ecosGlobalVersionUpdateVo.getAndroidVersion())
						.put("iosVersionName", ecosGlobalVersionUpdateVo.getIosVersion()).build(),
				EnumConstants.RecordModule.VERSION_MAINTAIN.getCode()
		);
	}
}
