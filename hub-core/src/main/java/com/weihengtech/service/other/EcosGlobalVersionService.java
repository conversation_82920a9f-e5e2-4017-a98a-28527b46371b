package com.weihengtech.service.other;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.other.EcosGlobalVersionDo;
import com.weihengtech.pojo.dtos.other.EcosHubGlobalVersionDto;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionAddVo;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionUpdateVo;

/**
 * <AUTHOR>
 */
public interface EcosGlobalVersionService extends IService<EcosGlobalVersionDo> {

	/**
	 * 分页查询Hub的版本信息
	 *
	 * @param pageNum  页码
	 * @param pageSize 每页大小
	 * @return 分页hub版本信息
	 */
	PageInfoDTO<EcosHubGlobalVersionDto> pageHubVersion(Integer pageNum, Integer pageSize);

	/**
	 * 新增hub的版本信息
	 *
	 * @param ecosGlobalVersionAddVo 新增版本入参
	 */
	void addHubVersion(EcosGlobalVersionAddVo ecosGlobalVersionAddVo);

	/**
	 * 更新hub的版本信息
	 *
	 * @param ecosGlobalVersionUpdateVo 更新版本入参
	 */
	void updateHubVersion(EcosGlobalVersionUpdateVo ecosGlobalVersionUpdateVo);
}
