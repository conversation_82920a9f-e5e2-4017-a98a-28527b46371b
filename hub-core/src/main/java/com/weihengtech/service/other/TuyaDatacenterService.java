package com.weihengtech.service.other;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.other.TuyaDatacenterDo;
import com.weihengtech.pojo.dtos.other.DatacenterListDto;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TuyaDatacenterService extends IService<TuyaDatacenterDo> {

    /**
     * 同步设备的数据中心
     * @param wifiSnList wifi棒的sn
     */
    void syncDatacenter(List<String> wifiSnList);

    /**
     * 获取数据中心国家列表
     *
     * @return 数据中心国家列表
     */
    List<DatacenterListDto> listDatacenter(HttpServletRequest request);

    /**
     * 判断是否相同phone code
     *
     * @param centerId1 id
     * @param centerId2 id
     * @return res
     */
    boolean isSamePhoneCode(Integer centerId1, Integer centerId2);
}
