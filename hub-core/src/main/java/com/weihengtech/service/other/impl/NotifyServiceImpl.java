package com.weihengtech.service.other.impl;

import cn.hutool.core.collection.CollUtil;
import com.weihengtech.api.NotificationCenterApi;
import com.weihengtech.api.pojo.base.NotificationResponse;
import com.weihengtech.api.pojo.vos.NotificationEnterpriseMarkdownVo;
import com.weihengtech.pojo.vos.other.NotifyHybridPrometheusVo;
import com.weihengtech.service.other.NotifyService;
import com.weihengtech.utils.EnterpriseWeChatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class NotifyServiceImpl implements NotifyService {
	@Value("${custom.notify.to-user}")
	private String toUser;

	@Resource
	private NotificationCenterApi notificationCenterApi;

	@Override
	public void hybridNotify(NotifyHybridPrometheusVo notifyHybridPrometheusVo) {

		List<NotifyHybridPrometheusVo.AlertsDTO> alerts = notifyHybridPrometheusVo.getAlerts();
		if (CollUtil.isNotEmpty(alerts)) {
			List<HashMap<String, String>> params = alerts.stream().map(alert -> {
				HashMap<String, String> map = new HashMap<>(6);
				map.put("status", alert.getStatus());
				map.put("deviceId", alert.getLabels().getDeviceId());
				map.put("group", alert.getLabels().getGroup());
				map.put("level", alert.getLabels().getLevel());
				map.put("alertName", alert.getLabels().getAlertname());
				map.put("startAt", alert.getStartsAt());
				map.put("endAt", alert.getEndsAt());
				map.put("monitor", alert.getLabels().getMonitor());
				map.put("generatorURL", alert.getGeneratorURL());
				return map;
			}).collect(Collectors.toList());

			params.parallelStream().forEach(param -> {
				NotificationEnterpriseMarkdownVo notificationEnterpriseMarkdownVo = new NotificationEnterpriseMarkdownVo();
				notificationEnterpriseMarkdownVo.setToUser(toUser);
				notificationEnterpriseMarkdownVo.setContent(EnterpriseWeChatUtil.hybridContent(param));
				notificationEnterpriseMarkdownVo.setEnableDuplicateCheck(1);
				notificationEnterpriseMarkdownVo.setDuplicateCheckInterval(60);
				try {
					NotificationResponse<String> response = notificationCenterApi.v1AsyncEnterpriseMarkdown(notificationEnterpriseMarkdownVo);
					if (null == response || !response.getSuccess() || response.getCode() != HttpStatus.OK.value()) {
						log.warn("NotifyServiceImpl#hybridNotify {}", "request error");
					}
				} catch (Exception e) {
					log.warn("NotifyServiceImpl#hybridNotify {}", e.getMessage());
				}
			});
		}
	}
}
