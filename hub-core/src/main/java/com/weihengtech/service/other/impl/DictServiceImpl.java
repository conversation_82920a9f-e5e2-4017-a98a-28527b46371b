package com.weihengtech.service.other.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.dao.other.DictMapper;
import com.weihengtech.pojo.dos.other.DictDO;
import com.weihengtech.service.other.DictService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class DictServiceImpl extends ServiceImpl<DictMapper, DictDO> implements DictService {

    @Override
    public DictDO getDictByCode(String code) {
        return getOne(Wrappers.<DictDO>lambdaQuery().eq(DictDO::getCode, code));
    }

    @Override
    public DictDO saveDict(DictDO dictDO) {
        DictDO dict = getDictByCode(dictDO.getCode());
        if (dict != null) {
            return dict;
        }
        save(dictDO);
        return dictDO;
    }
}
