package com.weihengtech.service.other;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.other.EcosSeriesDO;
import com.weihengtech.pojo.dtos.other.EcosSeriesDTO;
import com.weihengtech.pojo.vos.ecosseries.SeriesInsertVO;
import com.weihengtech.pojo.vos.ecosseries.SeriesUpdateVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface EcosSeriesService extends IService<EcosSeriesDO> {

	/**
	 * 查询所有系列列表
	 *
	 * @return 系列列表
	 */
	List<EcosSeriesDTO> querySeries();

	/**
	 * 新增系列
	 *
	 * @param seriesInsertVO 新增系列的名称参数
	 */
	void insertSeries(SeriesInsertVO seriesInsertVO);

	/**
	 * 更新现有的系列
	 *
	 * @param seriesUpdateVO 系列改名的参数
	 */
	void updateSeries(SeriesUpdateVO seriesUpdateVO);
}
