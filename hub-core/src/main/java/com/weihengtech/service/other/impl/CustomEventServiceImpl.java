package com.weihengtech.service.other.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.api.pojo.vos.WriteDeviceVO;
import com.weihengtech.consts.RedisRefConstants;
import com.weihengtech.enums.other.MailModelEnum;
import com.weihengtech.pojo.dos.device.DeviceFocusDo;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.service.device.DeviceFocusService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.other.CustomEventService;
import com.weihengtech.service.thirdpart.AliCloudSmsService;
import com.weihengtech.service.thirdpart.TencentEmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CustomEventServiceImpl implements CustomEventService {

	@Resource
	private DeviceFocusService deviceFocusService;
	@Resource
	private StringRedisTemplate stringRedisTemplate;
	@Resource
	private TencentEmailService tencentEmailService;
	@Resource
	private AliCloudSmsService aliCloudSmsService;
	@Resource
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;
	@Resource
	private DeviceListService deviceListService;

	@Override
	public void focusedDeviceOffline(Object req) {
		List<String> params = (List<String>) req;
		List<DeviceFocusDo> deviceFocusDoList = deviceFocusService
				.list(Wrappers.<DeviceFocusDo>lambdaQuery().in(DeviceFocusDo::getDeviceFlag, params));
		for (DeviceFocusDo deviceFocusDo : deviceFocusDoList) {
			if (Boolean.FALSE.equals(stringRedisTemplate
					.hasKey(RedisRefConstants.buildFocusedDeviceOfflineKey(deviceFocusDo.getId())))) {
				threadPoolTaskExecutor.execute(() -> {
					boolean emailResult = false;
					boolean phoneResult = false;
					Integer notifyType = deviceFocusDo.getNotifyType();
					String device = StrUtil.isBlank(deviceFocusDo.getDeviceRemark())
							? deviceFocusDo.getDeviceFlag()
							: deviceFocusDo.getDeviceRemark();
					String notifyEmail = deviceFocusDo.getNotifyEmail();
					String notifyPhone = deviceFocusDo.getNotifyPhone();
					switch (notifyType) {
						case 0:
							emailResult = tencentEmailService.sendEmail(notifyEmail,
									MailModelEnum.HUB_FOCUS_DEVICE_OFFLINE, device
							);
							break;
						case 1:
							phoneResult = aliCloudSmsService.sendFocusDeviceOfflineSms(
									ListUtil.toList(notifyPhone),
									device
							);
							break;
						case 2:
							emailResult = tencentEmailService.sendEmail(notifyEmail,
									MailModelEnum.HUB_FOCUS_DEVICE_OFFLINE, device
							);
							phoneResult = aliCloudSmsService.sendFocusDeviceOfflineSms(
									ListUtil.toList(notifyPhone),
									device
							);
							break;
						default:
					}
					if (emailResult || phoneResult) {
						stringRedisTemplate.opsForValue().set(
								RedisRefConstants.buildFocusedDeviceOfflineKey(deviceFocusDo.getId()),
								deviceFocusDo.getDeviceFlag(), 3, TimeUnit.HOURS
						);
					}
				});
			}
		}
	}

	@Override
	public void sendWriteCommand(Object params) {
		WriteDeviceVO param = (WriteDeviceVO) params;
		// 不是设置vpp模式的不处理
		if (param.getStartAddress() != 41036 && param.getStartAddress() != 60001) {
			return;
		}
		log.info("handle vpp param: {}", JSONUtil.toJsonStr(param));
		// 如果是vpp侧请求过来，并且设置vpp模式和倒计时
		if (param.getStartAddress() == 60001 && param.getValues().get(0) == 1) {
			// 获取下发指定vpp模式持续时间
			Integer countdownSec = param.getValues().get(6);
			stringRedisTemplate.opsForValue().set(RedisRefConstants.buildVppModeKey(param.getDeviceId()), String.valueOf(DateUtil.currentSeconds() + countdownSec));
		}
		String deviceSn = param.getDeviceId();
		DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(deviceSn);
		Boolean curMode = deviceInfo.getVppMode();
		// 41036和60001设置是否开启vpp模式
		boolean val = 0 != param.getValues().get(0);
		if (!curMode.equals(val)) {
			deviceListService.updateVppMode(val, deviceInfo.getDeviceSn());
			log.info("device: [{}] vpp mode changed, old: [{}] new: [{}]", deviceSn, curMode, val);
		}
	}
}
