package com.weihengtech.service.other;

import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.firmware.FirmwareUpgradeRecordDO;
import com.weihengtech.pojo.dtos.device.DeviceSyncVersionDto;

/**
 * <AUTHOR>
 */
public interface RetryService {

	/**
	 * 同步设备版本信息
	 *
	 * @param deviceListDO 设备实例对象
	 * @param retryable           是否重试
	 * @return 同步到的版本信息
	 */
	DeviceSyncVersionDto syncDeviceVersion(DeviceListDO deviceListDO, Boolean retryable);

	void syncSocketState(String deviceSn);

	void syncChargerState(String deviceSn);

	void syncWhDeviceState(String deviceSn);

	void speedUpDevice(String wifiSn, int dataSource);

}
