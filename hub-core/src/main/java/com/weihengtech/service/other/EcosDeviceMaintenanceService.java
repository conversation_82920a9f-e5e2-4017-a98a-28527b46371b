package com.weihengtech.service.other;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.other.EcosDeviceMaintenanceDo;
import com.weihengtech.pojo.dtos.other.EcosDeviceMaintenanceInfoDto;
import com.weihengtech.pojo.vos.other.EcosDeviceMaintenanceInfoUpdateVo;

/**
 * <AUTHOR>
 */
public interface EcosDeviceMaintenanceService extends IService<EcosDeviceMaintenanceDo> {

	/**
	 * 更新设备维护信息
	 */
	void updateInfo(EcosDeviceMaintenanceInfoUpdateVo ecosDeviceMaintenanceInfoUpdateVo);

	/**
	 * 获取设备维护信息
	 */
	EcosDeviceMaintenanceInfoDto getInfo(Long deviceId);
}
