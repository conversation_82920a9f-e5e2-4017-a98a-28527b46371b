package com.weihengtech.service.firmware;

import com.weihengtech.pojo.dtos.firmware.HardwareCategoryTypeMapDTO;
import com.weihengtech.pojo.dtos.firmware.HardwareVersionAllDTO;
import com.weihengtech.pojo.dtos.firmware.HardwareVersionPageDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.dtos.firmware.SoftwareVersionAllDTO;
import com.weihengtech.pojo.dtos.firmware.SoftwareVersionPageDTO;
import com.weihengtech.pojo.vos.firmware.HardwareVersionInsertVO;
import com.weihengtech.pojo.vos.firmware.HardwareVersionPageVO;
import com.weihengtech.pojo.vos.firmware.HardwareVersionUpdateVO;
import com.weihengtech.pojo.vos.firmware.RemarksVo;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionInsertVO;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionPageVO;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionUpdateVO;
import com.weihengtech.pojo.vos.firmware.VersionBaseVO;
import com.weihengtech.pojo.vos.firmware.VersionBatchDeleteVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HardwareSoftwareVersionService {

	/**
	 * 新增软件版本
	 *
	 * @param softwareVersionInsertVO 软件版本参数
	 */
	void insertSoftwareVersion(SoftwareVersionInsertVO softwareVersionInsertVO);

	/**
	 * 新增硬件版本
	 *
	 * @param hardwareVersionInsertVO 硬件版本参数
	 */
	void insertHardwareVersion(HardwareVersionInsertVO hardwareVersionInsertVO);

	/**
	 * 维护软件版本信息
	 *
	 * @param softwareVersionUpdateVO 要更新的软件版本信息
	 */
	void updateSoftwareVersion(SoftwareVersionUpdateVO softwareVersionUpdateVO);

	/**
	 * 维护硬件版本信息
	 *
	 * @param hardwareVersionUpdateVO 要更新的硬件版本信息
	 */
	void updateHardwareVersion(HardwareVersionUpdateVO hardwareVersionUpdateVO);

	/**
	 * 查询所有的硬件版本信息
	 *
	 * @param versionBaseVo 软件版本信息
	 * @return 硬件版本详情列表
	 */
	HardwareVersionAllDTO queryAllHardwareVersion(VersionBaseVO versionBaseVo);

	/**
	 * 查询添加硬件页面所需要的软硬件信息
	 *
	 * @param versionBaseVo 硬件版本信息
	 * @return 相关软硬件信息
	 */
	SoftwareVersionAllDTO queryAllSoftwareVersion(VersionBaseVO versionBaseVo);

	/**
	 * 批量删除软件版本
	 *
	 * @param versionBatchDeleteVO 批量删除入参
	 */
	void batchDeleteSoftwareVersion(VersionBatchDeleteVO versionBatchDeleteVO);

	/**
	 * 批量删除硬件版本
	 *
	 * @param versionBatchDeleteVO 批量删除入参
	 */
	void batchDeleteHardwareVersion(VersionBatchDeleteVO versionBatchDeleteVO);

	/**
	 * 硬件版本分页
	 *
	 * @param hardwareVersionPageVO 硬件版本分页入参
	 * @return 分页数据
	 */
	PageInfoDTO<HardwareVersionPageDTO> pageHardwareVersion(HardwareVersionPageVO hardwareVersionPageVO);

	/**
	 * 软件版本分页
	 *
	 * @param softwareVersionPageVO 软件版本分页入参
	 * @return 分页数据
	 */
	PageInfoDTO<SoftwareVersionPageDTO> pageSoftwareVersion(SoftwareVersionPageVO softwareVersionPageVO);

	/**
	 * 硬件分类厂家映射表
	 *
	 * @return 映射列表
	 */
	List<HardwareCategoryTypeMapDTO> mapHardwareCategoryType();

	/**
	 * 添加软件版本备注
	 *
	 * @param remarksVo 软件版本备注入参
	 */
	void softwareRemarks(RemarksVo remarksVo);

	/**
	 * 添加硬件版本备注
	 *
	 * @param remarksVo 硬件版本备注入参
	 */
	void hardwareRemarks(RemarksVo remarksVo);
}
