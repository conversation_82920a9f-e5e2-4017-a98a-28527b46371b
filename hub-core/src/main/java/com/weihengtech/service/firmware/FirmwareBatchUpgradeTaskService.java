package com.weihengtech.service.firmware;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.firmware.FirmwareBatchUpgradeTaskDO;
import com.weihengtech.pojo.dtos.device.DeviceListPageDTO;
import com.weihengtech.pojo.dtos.firmware.FirmwareBatchUpgradeTaskDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.firmware.FirmwareBatchUpgradeDevicePageVo;
import com.weihengtech.pojo.vos.firmware.FirmwareBatchUpgradeTaskPageVo;
import com.weihengtech.pojo.vos.firmware.FirmwareBatchUpgradeTaskVo;

/**
 * @author: jiahao.jin
 * @create: 2025-07-14 16:19
 * @description:
 */
public interface FirmwareBatchUpgradeTaskService extends IService<FirmwareBatchUpgradeTaskDO> {

    // 添加一个批量升级任务
    void addBatchUpgradeTask(FirmwareBatchUpgradeTaskVo firmwareBatchUpgradeTaskVo);

    // 分页查询批量升级任务
    PageInfoDTO<FirmwareBatchUpgradeTaskDTO> pageBatchUpgradeTask(FirmwareBatchUpgradeTaskPageVo firmwareBatchUpgradeTaskPageVo);

    // 根据筛选条件查询要批量升级的设备
    PageInfoDTO<DeviceListPageDTO> pageBatchUpgradeDevice(FirmwareBatchUpgradeDevicePageVo firmwareBatchUpgradeDevicePageVo);

    // 终止批量升级任务
    void stopBatchUpgradeTask(String taskId);
}
