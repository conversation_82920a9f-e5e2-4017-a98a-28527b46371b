package com.weihengtech.service.firmware.impl;

import cn.hutool.core.lang.Pair;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.dao.firmware.FirmwareUploadMapper;
import com.weihengtech.pojo.dos.firmware.FirmwareUploadDO;
import com.weihengtech.service.firmware.FirmwareUploadService;
import com.weihengtech.service.firmware.MiddleFirmwareSoftwareService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.SnowFlakeUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FirmwareUploadServiceImpl extends ServiceImpl<FirmwareUploadMapper, FirmwareUploadDO>
		implements
		FirmwareUploadService {

	@Resource
	private SnowFlakeUtil snowFlakeUtil;

	@Resource
	private FirmwareUploadMapper firmwareUploadMapper;

	@Resource
	private MiddleFirmwareSoftwareService middleFirmwareSoftwareService;

	@Override
	@DSTransactional
	public Long insertWithLinkSoftwareVersion(
			Pair<String, Long> downloadUrlAndSize,
			Map<Long, String> softwareIdForTypeMap, String model
	) {
		Long operatorId = Long.parseLong(UserInfoUtil.currentUserId());
		long firmwareId = snowFlakeUtil.generateId();
		FirmwareUploadDO firmwareUploadDO = new FirmwareUploadDO();
		firmwareUploadDO.setId(firmwareId);
		firmwareUploadDO.setDownloadUrl(downloadUrlAndSize.getKey());
		firmwareUploadDO.setSize(downloadUrlAndSize.getValue());
		firmwareUploadDO.setOperatorId(operatorId);
		LocalDateTime now = LocalDateTime.now();
		firmwareUploadDO.setCreateTime(now);
		firmwareUploadDO.setUpdateTime(now);
		firmwareUploadDO.setModel(model);

		middleFirmwareSoftwareService.linkSoftwareVersion(firmwareId, softwareIdForTypeMap, operatorId,
				firmwareUploadDO
		);

		ActionFlagUtil.singleAction(firmwareUploadMapper.insert(firmwareUploadDO));
		return firmwareId;
	}

	@Override
	public FirmwareUploadDO selectBySerialId(String firmwareId) {
		FirmwareUploadDO firmwareUploadDO = firmwareUploadMapper.selectById(firmwareId);
		BeanUtil.assertNotNull(firmwareUploadDO);
		return firmwareUploadDO;
	}

	@Override
	@DSTransactional
	public void updateWithLinkSoftwareVersion(
			FirmwareUploadDO firmwareUploadDO, Map<Long, String> softwareIdForTypeMap
	) {
		Long operatorId = Long.parseLong(UserInfoUtil.currentUserId());
		firmwareUploadDO.setUpdateTime(LocalDateTime.now());
		firmwareUploadDO.setOperatorId(operatorId);

		middleFirmwareSoftwareService.reLinkSoftwareVersion(firmwareUploadDO.getId(), softwareIdForTypeMap, operatorId,
				firmwareUploadDO
		);

		ActionFlagUtil.singleAction(firmwareUploadMapper.updateById(firmwareUploadDO));
	}
}
