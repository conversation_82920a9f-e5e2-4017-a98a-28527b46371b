package com.weihengtech.service.firmware.impl;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.enums.firmware.UploadTypeEnum;
import com.weihengtech.dao.firmware.MiddleFirmwareSoftwareMapper;
import com.weihengtech.dao.firmware.SoftwareVersionMapper;
import com.weihengtech.pojo.dos.firmware.FirmwareUploadDO;
import com.weihengtech.pojo.dos.firmware.MiddleFirmwareSoftwareDO;
import com.weihengtech.pojo.dos.firmware.SoftwareVersionDO;
import com.weihengtech.service.firmware.HardwareSoftwareLinkService;
import com.weihengtech.service.firmware.MiddleFirmwareSoftwareService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.SnowFlakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MiddleFirmwareSoftwareImpl extends ServiceImpl<MiddleFirmwareSoftwareMapper, MiddleFirmwareSoftwareDO>
		implements
		MiddleFirmwareSoftwareService {

	@Resource
	private MiddleFirmwareSoftwareMapper middleFirmwareSoftwareMapper;

	@Resource
	private HardwareSoftwareLinkService hardwareSoftwareLinkService;

	@Resource
	private SoftwareVersionMapper softwareVersionMapper;

	@Resource
	private SnowFlakeUtil snowFlakeUtil;

	@Override
	public void linkSoftwareVersion(
			Long firmwareId, Map<Long, String> softwareIdForTypeMap, Long operatorId,
			FirmwareUploadDO firmwareUploadDO
	) {
		LocalDateTime now = LocalDateTime.now();

		StringBuilder stringBuilder = new StringBuilder();
		ArrayList<Long> arrayList = new ArrayList<>(softwareIdForTypeMap.keySet());

		for (int i = 0; i < arrayList.size(); i++) {
			if (i != 0) {
				stringBuilder.append("_");
			}
			Long softwareId = arrayList.get(i);
			Pair<String, String> pair = querySoftwareVersionAndHardwareType(softwareId);
			firmwareUploadDO.setType(pair.getValue());
			validType(softwareIdForTypeMap.get(softwareId), pair.getValue());
			insertMiddleFirmwareSoftware(firmwareId, softwareId, operatorId, now);
			stringBuilder.append(pair.getValue()).append("_").append(pair.getKey());
		}

		firmwareUploadDO.setFirmwareName(stringBuilder.toString());

		if (softwareIdForTypeMap.keySet().size() > 1) {
			firmwareUploadDO.setType("混合");
		}
	}

	@Override
	public void reLinkSoftwareVersion(
			Long firmwareId, Map<Long, String> softwareIdForTypeMap, Long operatorId,
			FirmwareUploadDO firmwareUploadDO
	) {
		LocalDateTime now = LocalDateTime.now();
		LambdaQueryWrapper<MiddleFirmwareSoftwareDO> middleFirmwareSoftwareLambdaQueryWrapper = new LambdaQueryWrapper<>();
		middleFirmwareSoftwareLambdaQueryWrapper.eq(MiddleFirmwareSoftwareDO::getFirmwareId, firmwareId);
		List<MiddleFirmwareSoftwareDO> firmwareSoftwareList = middleFirmwareSoftwareMapper
				.selectList(middleFirmwareSoftwareLambdaQueryWrapper);

		List<Long> newSoftwareIdList = new ArrayList<>(softwareIdForTypeMap.keySet());
		firmwareSoftwareList.stream()
				.filter(firmwareSoftware -> !newSoftwareIdList.contains(firmwareSoftware.getSoftwareId()))
				.forEach(firmwareSoftware -> middleFirmwareSoftwareMapper.deleteById(firmwareSoftware.getId()));

		newSoftwareIdList
				.stream().filter(softwareId -> !firmwareSoftwareList.stream()
						.map(MiddleFirmwareSoftwareDO::getSoftwareId).collect(Collectors.toList()).contains(softwareId))
				.forEach(softwareId -> {
					String dataTypeName = hardwareSoftwareLinkService.queryHardwareTypeNameBySoftwareId(softwareId);
					firmwareUploadDO.setType(dataTypeName);
					validType(softwareIdForTypeMap.get(softwareId), dataTypeName);
					insertMiddleFirmwareSoftware(firmwareId, softwareId, operatorId, now);
				});

		if (firmwareSoftwareList.size() > 1) {
			firmwareUploadDO.setType("混合");
		}

		StringBuilder stringBuilder = new StringBuilder();
		for (int i = 0; i < newSoftwareIdList.size(); i++) {
			if (i != 0) {
				stringBuilder.append("_");
			}
			Long softwareId = newSoftwareIdList.get(i);
			Pair<String, String> pair = querySoftwareVersionAndHardwareType(softwareId);
			stringBuilder.append(pair.getValue()).append("_").append(pair.getKey());
		}

		firmwareUploadDO.setFirmwareName(stringBuilder.toString());
	}

	private Pair<String, String> querySoftwareVersionAndHardwareType(Long softwareId) {
		SoftwareVersionDO softwareVersionDO = softwareVersionMapper.selectById(softwareId);
		BeanUtil.assertNotNull(softwareVersionDO);
		String dataTypeName = hardwareSoftwareLinkService.queryHardwareTypeNameBySoftwareId(softwareId);
		return Pair.of(softwareVersionDO.getSoftwareVersion(), dataTypeName);
	}

	private void insertMiddleFirmwareSoftware(Long firmwareId, Long softwareId, Long operatorId, LocalDateTime now) {
		long middleLinkId = snowFlakeUtil.generateId();
		MiddleFirmwareSoftwareDO middleFirmwareSoftwareDO = new MiddleFirmwareSoftwareDO();
		middleFirmwareSoftwareDO.setId(middleLinkId);
		middleFirmwareSoftwareDO.setFirmwareId(firmwareId);
		middleFirmwareSoftwareDO.setSoftwareId(softwareId);
		middleFirmwareSoftwareDO.setOperatorId(operatorId);
		middleFirmwareSoftwareDO.setCreateTime(now);
		middleFirmwareSoftwareDO.setUpdateTime(now);
		ActionFlagUtil.singleAction(middleFirmwareSoftwareMapper.insert(middleFirmwareSoftwareDO));
	}

	private void validType(String typeName, String dataTypeName) {
		for (UploadTypeEnum typeEnum : UploadTypeEnum.values()) {
			if (typeEnum.getPrefix().equals(typeName) && !typeEnum.name().equals(dataTypeName)) {
				log.warn("软件版本与选取的版本不匹配");
				throw new CustomException(ExceptionEnum.SOFTWARE_VERSION_NOT_FIT_CHOOSE_TYPE_ERROR);
			}
		}
	}
}
