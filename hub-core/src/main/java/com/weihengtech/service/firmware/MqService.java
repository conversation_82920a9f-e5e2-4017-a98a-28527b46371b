package com.weihengtech.service.firmware;

import com.weihengtech.pojo.dos.firmware.FirmwareUpgradeRecordDO;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.tasks.DelayQueueMessage;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MqService {

	/**
	 * 固件升级失败
	 */
	void upgradeFailure(DelayQueueMessage delayQueueMessage);

	/**
	 * 检查版本号是否已经升级
	 */

	void upgradeCheck(DelayQueueMessage delayQueueMessage);

	/**
	 * 检查批量升级任务是否已经升级
	 */
	void upgradeBatchCheck(DelayQueueMessage delayQueueMessage);

	/**
	 * 发送升级指令
	 */
	void upgradePost(DelayQueueMessage delayQueueMessage);

	/**
	 * 发送升级指令
	 */
	void addUpgradeTask(
			SpecificServService specificServService, String deviceName, String downloadUrl, Long size,
			Long recordId
	);

	/**
	 * 升级成功
	 */
	void processSuccess(FirmwareUpgradeRecordDO firmwareUpgradeRecordDO, String currentVersion);

	/**
	 * 发送定时发送升级任务（多个设备升级）
	 * 任务ID、升级时间
	 */
	void addBatchUpgradeTask(Long taskId, Long upgradeTime);

	/**
	 * 终止批量升级任务
	 */
	void stopBatchUpgradeTask(String taskId);
}
