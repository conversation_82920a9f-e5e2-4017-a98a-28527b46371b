package com.weihengtech.service.firmware.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.CommonConstants;
import com.weihengtech.dao.firmware.FirmwareUpgradeRecordMapper;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.firmware.FirmwareUpgradeRecordDO;
import com.weihengtech.pojo.dos.firmware.FirmwareUploadDO;
import com.weihengtech.service.firmware.FirmwareUpgradeRecordService;
import com.weihengtech.utils.SnowFlakeUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Service
public class FirmwareUpgradeRecordServiceImpl extends ServiceImpl<FirmwareUpgradeRecordMapper, FirmwareUpgradeRecordDO>
		implements
		FirmwareUpgradeRecordService {

	@Resource
	private SnowFlakeUtil snowFlakeUtil;

	@Override
	public FirmwareUpgradeRecordDO saveFirmwareUpgradeRecord(DeviceListDO deviceInfo, FirmwareUploadDO firmwareItem,
															  String typeName) {
		long recordId = snowFlakeUtil.generateId();
		FirmwareUpgradeRecordDO item = FirmwareUpgradeRecordDO.builder()
				.id(recordId)
				.deviceId(deviceInfo.getId())
				.deviceName(deviceInfo.getDeviceName())
				.firmwareId(firmwareItem.getId())
				.uploadUrl(firmwareItem.getDownloadUrl())
				.size(firmwareItem.getSize())
				.seriesTable(DeviceListDO.class.getAnnotation(TableName.class).value())
				.addressMap(typeName)
				.status(CommonConstants.DEVICE_UPGRADE_ING)
				.createTime(LocalDateTime.now())
				.updateTime(LocalDateTime.now())
				.sourceVersion(StrUtil.EMPTY)
				.finishVersion(StrUtil.EMPTY)
				.build();
		if (!save(item)) {
			log.warn("保存固件升级记录失败");
			throw new CustomException(ExceptionEnum.DATA_ASSERT_EXCEPTION);
		}
		return item;
	}
}
