package com.weihengtech.service.firmware;

import com.weihengtech.pojo.dtos.firmware.UpgradeResDTO;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionUpgradeVO;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionUpgradeWithSnVo;

/**
 * 固件升级服务
 *
 * <AUTHOR>
 * @date 2025/2/26 15:32
 * @version 1.0
 */
public interface FirmwareUpgradeService {

    /**
     * 固件包升级
     *
     * @param softwareVersionUpgradeVO 升级所需参数
     */
    void upgradeSoftware(SoftwareVersionUpgradeVO softwareVersionUpgradeVO);

    /**
     * 使用设备sn升级设备
     *
     * @param softwareVersionUpgradeWithSnVo 升级入参
     */
    UpgradeResDTO upgradeSoftwareWithSn(SoftwareVersionUpgradeWithSnVo softwareVersionUpgradeWithSnVo);

    /**
     * 后台监听失效时手动升级当前设备
     *
     * @return 升级结果
     */
    String upgradeCurDevice(String type);
}
