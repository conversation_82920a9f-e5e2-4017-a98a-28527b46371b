package com.weihengtech.service.firmware.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.CommonConstants;
import com.weihengtech.dao.device.DeviceListMapper;
import com.weihengtech.dao.firmware.DeviceUpgradeDetailMapper;
import com.weihengtech.dao.firmware.FirmwareBatchUpgradeTaskMapper;
import com.weihengtech.dao.firmware.FirmwareUpgradeRecordMapper;
import com.weihengtech.dao.firmware.FirmwareUploadMapper;
import com.weihengtech.enums.device.DeviceDatasourceEnum;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.enums.firmware.*;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.firmware.*;
import com.weihengtech.pojo.dtos.other.DictItemDTO;
import com.weihengtech.service.firmware.*;
import com.weihengtech.service.other.DictItemService;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.tasks.DelayQueueMessage;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.InitUtil;
import com.weihengtech.utils.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MqServiceImpl implements MqService {

	/** 首次检查间隔时间min */
	private static final int FIRST_CHECK_MINUTE = 5;

	/** 最终失败任务推送时间min */
	private static final int FAILURE_MINUTE_LIMIT = 30;

	/** 最大检查轮次 */
	private static final int TURN_CHECK_TIMES = 25;

	@Resource
	private FirmwareUpgradeRecordMapper firmwareUpgradeRecordMapper;

	@Resource
	private DeviceListMapper deviceListMapper;

	@Resource
	private StrategyService strategyService;

	@Resource
	private FirmwareBatchUpgradeTaskMapper firmwareBatchUpgradeTaskMapper;
	@Resource
	private DeviceUpgradeDetailMapper deviceUpgradeDetailMapper;
	@Resource
	private FirmwareUploadMapper firmwareUploadMapper;
	@Resource
	private TuyaDatacenterService tuyaDatacenterService;
	@Resource
	private DictItemService dictItemService;
	@Resource
	private DeviceUpgradeDetailService deviceUpgradeDetailService;

	@Override
	@DSTransactional
	public void upgradeFailure(DelayQueueMessage delayQueueMessage) {
		log.info("调用upgradeFailure {}", JSONUtil.toJsonStr(delayQueueMessage));
		Long recordId = (Long) delayQueueMessage.getParams().get("recordId");
		FirmwareUpgradeRecordDO record = firmwareUpgradeRecordMapper.selectById(recordId);
		if (record.getStatus() > CommonConstants.DEVICE_UPGRADE_FINISH) {
			record.setStatus(CommonConstants.DEVICE_UPGRADE_FAILURE);
			record.setUpdateTime(LocalDateTime.now());
			ActionFlagUtil.singleAction(firmwareUpgradeRecordMapper.updateById(record));
			log.warn("{}升级记录更新失败状态:{}", record.getDeviceName(), record.getAddressMap());
		}
	}

	@Override
	public void upgradeCheck(DelayQueueMessage delayQueueMessage) {
		log.info("调用upgradeCheck {}", JSONUtil.toJsonStr(delayQueueMessage));
		Dict params = delayQueueMessage.getParams();
		Long recordId = (Long) params.get("recordId");
		int check = Integer.parseInt(params.getOrDefault("check", "25").toString());
		log.info("设备{}开始第{}轮的检查", recordId, check);
		FirmwareUpgradeRecordDO record = firmwareUpgradeRecordMapper.selectById(recordId);
		boolean result = filterSuccessVersion(record);
		if (result) {
			log.info("设备{}已经升级结束", record.getDeviceName());
			return;
		}
		if (check < TURN_CHECK_TIMES) {
			// 每次检查间隔1分钟，推送下次检查任务
			delayQueueMessage.setDelayTime(System.currentTimeMillis() + MqTagEnum.TimeDelay.MINUTE);
			delayQueueMessage.setParams(params.set("type", MqTagEnum.UPGRADE_CHECK.getMethodName())
					.set("check", ++ check)
					.set("recordId", recordId));
			log.info("推送upgrade_check任务 {}", JSONUtil.toJsonStr(delayQueueMessage));
			InitUtil.DELAY_QUEUE.offer(delayQueueMessage);
		} else {
			upgradeFailure(delayQueueMessage);
		}
	}

	@Override
	public void upgradePost(DelayQueueMessage delayQueueMessage) {
		log.info("调用upgradePost {}", JSONUtil.toJsonStr(delayQueueMessage));
		Long recordId = (Long) delayQueueMessage.getParams().get("recordId");
		FirmwareUpgradeRecordDO record = firmwareUpgradeRecordMapper.selectById(recordId);
		if (record == null) {
			return;
		}
		SpecificServService specificServService = strategyService.chooseSpecificServ(record.getDeviceId());
		specificServService.sendUpgradeCommand(record.getDeviceName(),
				record.getUploadUrl(), record.getSize());
		// 状态设置为等待（0失败|1成功|2等待）
		record.setStatus(2);
		ActionFlagUtil.singleAction(firmwareUpgradeRecordMapper.updateById(record));
		log.info("设备{}下发升级指令成功", record.getDeviceName());
	}

	/** 过滤掉已经升级成功的设备 */
	private boolean filterSuccessVersion(FirmwareUpgradeRecordDO record) {
		try {
			// 升级失败0和升级成功1不处理
			if (record.getStatus() < 2) {
				log.info("设备{}升级状态为成功或失败，无需重试", record.getDeviceName());
				return true;
			}
			SpecificServService specificServService = strategyService.chooseSpecificServ(record.getDeviceId());
			Map<String, String> currentVersionMap = specificServService.getFirmwareVersionByTypeList(record.getDeviceName(),
					record.getAddressMap());
			if ("WIFI".equals(record.getAddressMap())) {
				String currentVersion = currentVersionMap.get("WIFI");
				log.info("查询到设备{}当前版本为：{}", record.getDeviceName(), currentVersion);
				if (!record.getSourceVersion().equals(currentVersion)
						&& StrUtil.isNotBlank(currentVersion)
				) {
					processSuccess(record, currentVersion);
				} else {
					log.info("设备{}升级尚未成功，继续重试", record.getDeviceName());
					return false;
				}
			} else {
				String currentVersion = currentVersionMap.get(record.getAddressMap());
				log.info("查询到设备{}当前版本为：{}", record.getDeviceName(), currentVersion);
				if (StrUtil.isNotBlank(currentVersion) && !record.getSourceVersion().equals(currentVersion)) {
					if (record.getSourceVersion().contains("(") && currentVersion.contains("(")) {
						processSuccess(record, currentVersion);
					} else if (!record.getSourceVersion().contains("(") && !currentVersion.contains("(")) {
						processSuccess(record, currentVersion);
					} else {
						log.info("设备{}升级尚未成功，继续重试", record.getDeviceName());
						return false;
					}
				} else {
					log.info("设备{}升级尚未成功，继续重试", record.getDeviceName());
					return false;
				}
			}
		} catch (Exception e) {
			log.error(String.format("设备%s检查升级结果异常：%s", record.getDeviceName(), e.getMessage()), e);
			return false;
		}
		return true;
	}

	@Override
	public void processSuccess(FirmwareUpgradeRecordDO item, String currentVersion) {
		log.info("更新设备{}升级状态为成功", item.getDeviceName());
		item.setStatus(CommonConstants.DEVICE_UPGRADE_FINISH);
		item.setUpdateTime(LocalDateTime.now());
		item.setFinishVersion(currentVersion);
		ActionFlagUtil.singleAction(firmwareUpgradeRecordMapper.updateById(item));
		Long deviceId = item.getDeviceId();
		DeviceListDO deviceListDO = deviceListMapper.selectById(deviceId);
		BeanUtil.assertNotNull(deviceListDO);
		String curVersion = currentVersion.contains("(") ? currentVersion.split("\\(")[0] : currentVersion;
		String subVersion = currentVersion.contains("(") ? currentVersion.split("\\(")[1].split("\\)")[0] : null;
		Pair<String, String> deviceVersionMethod = UploadTypeEnum.getDeviceVersionMethod(item.getAddressMap());
		if (StrUtil.isNotBlank(deviceVersionMethod.getKey()) && StrUtil.isNotBlank(curVersion)) {
			ReflectUtil.invoke(deviceListDO, deviceVersionMethod.getKey(), curVersion);
		}
		if (StrUtil.isNotBlank(deviceVersionMethod.getValue()) && StrUtil.isNotBlank(subVersion)) {
			ReflectUtil.invoke(deviceListDO, deviceVersionMethod.getValue(), subVersion);
		}
		deviceListDO.setUpdateTime(LocalDateTime.now());
		ActionFlagUtil.singleAction(deviceListMapper.updateById(deviceListDO));
	}

	@Override
	public void addUpgradeTask(SpecificServService specificServService, String deviceName, String downloadUrl,
			Long size, Long recordId) {
		log.info("添加升级任务 {}", deviceName);
		long now = System.currentTimeMillis();
		// 推送下发指令任务
		DelayQueueMessage postMessage = new DelayQueueMessage(now + MqTagEnum.TimeDelay.SECOND);
		postMessage.setParams(Dict.create().set("recordId", recordId).set("type", MqTagEnum.UPGRADE_POST.getMethodName()));
		log.info("推送upgrade_post任务 {}", JSONUtil.toJsonStr(postMessage));
		InitUtil.DELAY_QUEUE.offer(postMessage);

		// 推送最终失败任务
		DelayQueueMessage failureMessage = new DelayQueueMessage(now + MqTagEnum.TimeDelay.MINUTE * FAILURE_MINUTE_LIMIT);
		failureMessage.setParams(Dict.create().set("recordId", recordId).set("type", MqTagEnum.UPGRADE_FAILURE.getMethodName()));
		log.info("推送upgrade_failure任务 {}", JSONUtil.toJsonStr(failureMessage));
		InitUtil.DELAY_QUEUE.offer(failureMessage);

		// 推送检查任务
		DelayQueueMessage checkMessage = new DelayQueueMessage(now + MqTagEnum.TimeDelay.MINUTE * FIRST_CHECK_MINUTE);
		checkMessage.setParams(Dict.create().set("recordId", recordId).set("type", MqTagEnum.UPGRADE_CHECK.getMethodName())
				.set("check", 1));
		log.info("推送upgrade_check任务 {}", JSONUtil.toJsonStr(checkMessage));
		InitUtil.DELAY_QUEUE.offer(checkMessage);
	}


	@Override
	public void addBatchUpgradeTask(Long taskId, Long upgradeTime) {
		log.info("添加批量升级任务 {}", taskId);

		// 推送批量升级任务
		DelayQueueMessage postMessage = new DelayQueueMessage(upgradeTime);
		postMessage.setParams(Dict.create().set("taskId", taskId).set("type", MqTagEnum.UPGRADE_POST_BATCH.getMethodName()));
		log.info("推送UPGRADE_POST_BATCH任务 {}", JSONUtil.toJsonStr(postMessage));
		InitUtil.DELAY_QUEUE.offer(postMessage);

		// 推送检查批量升级任务
		DelayQueueMessage checkMessage = new DelayQueueMessage(upgradeTime + MqTagEnum.TimeDelay.MINUTE * FIRST_CHECK_MINUTE);
		checkMessage.setParams(Dict.create().set("taskId", taskId).set("type", MqTagEnum.UPGRADE_BATCH_CHECK.getMethodName())
				.set("check", 0));
		log.info("推送UPGRADE_BATCH_CHECK任务 {}", JSONUtil.toJsonStr(checkMessage));
		InitUtil.DELAY_QUEUE.offer(checkMessage);
	}

	// 终止批量升级任务
	@Override
	public void stopBatchUpgradeTask(String taskId) {
		// 删除延迟队列中批量升级任务
		InitUtil.DELAY_QUEUE.removeIf(i -> i.getParams().get("taskId").equals(taskId));
		log.info("批量升级任务{}已终止", taskId);
	}


	@Override
	public void upgradeBatchCheck(DelayQueueMessage delayQueueMessage){
		log.info("调用upgradeBatchCheck {}", JSONUtil.toJsonStr(delayQueueMessage));
		Long taskId = (Long) delayQueueMessage.getParams().get("taskId");
		int check = (Integer) delayQueueMessage.getParams().get("check");

		FirmwareBatchUpgradeTaskDO firmwareBatchUpgradeTaskDO = firmwareBatchUpgradeTaskMapper.selectById(taskId);
		com.weihengtech.utils.BeanUtil.assertNotNull(firmwareBatchUpgradeTaskDO);

		// 获取固件信息
		FirmwareUploadDO firmwareUploadDO = firmwareUploadMapper.selectById(firmwareBatchUpgradeTaskDO.getFirmwareId());
		com.weihengtech.utils.BeanUtil.assertNotNull(firmwareUploadDO);

		List<DeviceUpgradeDetailDO> deviceUpgradeDetailDOS = deviceUpgradeDetailMapper.selectList(Wrappers.<DeviceUpgradeDetailDO>lambdaQuery()
				.eq(DeviceUpgradeDetailDO::getBatchTaskId, taskId)
				.eq(DeviceUpgradeDetailDO::getUpgradeStatus, UpgradeDeviceStatusEnum.UPGRADEING.getStatus())
		);

		if (CollUtil.isEmpty(deviceUpgradeDetailDOS)) {
			log.info("批量升级任务{}没有正在升级的设备", taskId);
			updateBatchUpgradeTaskStats(firmwareBatchUpgradeTaskDO);
			return;
		}

		log.info("批量升级任务{}第{}次检查，共{}个设备需要检查", taskId, check + 1, deviceUpgradeDetailDOS.size());

		// 检查每个设备的版本号是否已更新
		List<DeviceUpgradeDetailDO> successDevices = new ArrayList<>();
		List<DeviceUpgradeDetailDO> stillUpgradingDevices = new ArrayList<>();
		List<DeviceUpgradeDetailDO> failedDevices = new ArrayList<>();

		for (DeviceUpgradeDetailDO deviceDetail : deviceUpgradeDetailDOS) {
			try {
				// 获取设备信息
				DeviceListDO deviceListDO = deviceListMapper.selectById(deviceDetail.getDeviceId());
				if (deviceListDO == null) {
					continue;
				}

				// 检查版本号是否已更新（仅主版本号校验）
				CheckResult checkResult = checkBatchDeviceUpgradeStatus(deviceDetail, deviceListDO, firmwareUploadDO);

				if (checkResult.getStatus() == 1) { // 升级成功
					deviceDetail.setUpgradeStatus(UpgradeDeviceStatusEnum.SUCCESS.getStatus());
					deviceDetail.setTargetVersion(checkResult.getCurrentVersion());
					deviceDetail.setEndTime(LocalDateTime.now());
					successDevices.add(deviceDetail);
					log.info("设备{}批量升级成功", deviceListDO.getDeviceName());
				} else if (checkResult.getStatus() == 2) { // 升级失败
					deviceDetail.setUpgradeStatus(UpgradeDeviceStatusEnum.FAILURE.getStatus());
					deviceDetail.setEndTime(LocalDateTime.now());
					deviceDetail.setFailureReason(checkResult.getFailureReason());
					failedDevices.add(deviceDetail);
					log.warn("设备{}批量升级失败: {}", deviceListDO.getDeviceName(), checkResult.getFailureReason());
				} else { // 仍在升级中
					stillUpgradingDevices.add(deviceDetail);
					log.info("设备{}仍在升级中", deviceListDO.getDeviceName());
				}
			} catch (Exception e) {
				log.error(String.format("设备%s检查升级结果异常: %s", deviceDetail.getDeviceSn(), e.getMessage()), e);

				// 第三次检查时，异常也当作失败处理
				if (check >= 2) { // 修正：第3次检查 (check从0开始，所以check=2表示第3次)
					deviceDetail.setUpgradeStatus(UpgradeDeviceStatusEnum.FAILURE.getStatus());
					deviceDetail.setEndTime(LocalDateTime.now());
					deviceDetail.setFailureReason("检查升级状态异常: " + e.getMessage());
					failedDevices.add(deviceDetail);
				} else {
					stillUpgradingDevices.add(deviceDetail);
				}
			}
		}

		// 更新成功的设备状态
		if (CollUtil.isNotEmpty(successDevices)) {
			ActionFlagUtil.assertTrue(deviceUpgradeDetailService.updateBatchById(successDevices));
			log.info("批量升级任务{}第{}次检查，{}个设备升级成功", taskId, check + 1, successDevices.size());
		}

		// 更新失败的设备状态
		if (CollUtil.isNotEmpty(failedDevices)) {
			ActionFlagUtil.assertTrue(deviceUpgradeDetailService.updateBatchById(failedDevices));
			log.warn("批量升级任务{}第{}次检查，{}个设备升级失败", taskId, check + 1, failedDevices.size());
		}

		// 修正：第三次检查后（check >= 2），剩余的设备直接标记为失败
		if (check >= 2) {
			if (CollUtil.isNotEmpty(stillUpgradingDevices)) {
				stillUpgradingDevices.forEach(i -> {
					i.setUpgradeStatus(UpgradeDeviceStatusEnum.FAILURE.getStatus());
					i.setEndTime(LocalDateTime.now());
					i.setFailureReason("升级超时失败");
				});
				ActionFlagUtil.assertTrue(deviceUpgradeDetailService.updateBatchById(stillUpgradingDevices));
				log.warn("批量升级任务{}第三次检查后，{}个设备最终升级失败", taskId, stillUpgradingDevices.size());
			}
			log.info("批量升级任务{}检查完成", taskId);
		} else {
			// 如果还有设备在升级中且不是第三次检查，继续推送下一次检查任务
			if (CollUtil.isNotEmpty(stillUpgradingDevices)) {
				DelayQueueMessage checkMessage = new DelayQueueMessage(System.currentTimeMillis() + MqTagEnum.TimeDelay.TEN_MINUTE);
				checkMessage.setParams(Dict.create()
						.set("taskId", taskId)
						.set("type", MqTagEnum.UPGRADE_BATCH_CHECK.getMethodName())
						.set("check", check + 1)
				);
				log.info("推送upgrade_batch_check任务，第{}次检查", check + 2);
				InitUtil.DELAY_QUEUE.offer(checkMessage);
			}
		}

		// 查询升级任务是否完成
		updateBatchUpgradeTaskStats(firmwareBatchUpgradeTaskDO);
	}


	/**
	 * 检查结果类
	 */
	private static class CheckResult {
		private int status; // 0=仍在升级, 1=升级成功, 2=升级失败
		// 当前版本号
		private String currentVersion;
		private String failureReason;

		public CheckResult(int status) {
			this.status = status;
		}

		public CheckResult(int status, String currentVersion) {
			this.status = status;
			this.currentVersion = currentVersion;
		}

		public CheckResult(int status, String currentVersion, String failureReason) {
			this.status = status;
			this.failureReason = failureReason;
		}

		// getters...
		public int getStatus() { return status; }
		public String getFailureReason() { return failureReason; }
		public String getCurrentVersion() { return currentVersion; }
	}

	/**
	 * 检查批量升级设备状态（简化版本，只比较当前版本和升级前版本）
	 * @param deviceDetail 设备升级详情
	 * @param deviceListDO 设备信息
	 * @param firmwareUploadDO 固件信息
	 * @return 检查结果 (0=仍在升级, 1=升级成功, 2=升级失败)
	 */
	private CheckResult checkBatchDeviceUpgradeStatus(DeviceUpgradeDetailDO deviceDetail, DeviceListDO deviceListDO,
													  FirmwareUploadDO firmwareUploadDO) {
		try {
			SpecificServService specificServService = strategyService.chooseSpecificServ(deviceDetail.getDeviceId());

			// 查询当前固件版本号
			Map<String, String> currentVersionMap;
			try {
				currentVersionMap = specificServService.getFirmwareVersionByDeviceListDOAndTypeName(deviceListDO, firmwareUploadDO.getType());
				if (CollUtil.isEmpty(currentVersionMap)) {
					log.warn("设备{}未查询到当前版本", deviceListDO.getDeviceName());
					return new CheckResult(0); // 仍在升级
				}
			} catch (Exception e) {
				log.error(String.format("设备%s查询当前版本号异常: %s", deviceListDO.getDeviceName(), e.getMessage()), e);
				return new CheckResult(0); // 仍在升级
			}

			String currentVersion = currentVersionMap.get(firmwareUploadDO.getType());
			String sourceVersion = deviceDetail.getCurrentVersion();

			if (StrUtil.isBlank(currentVersion) || StrUtil.isBlank(sourceVersion)) {
				log.warn("设备{}版本信息不完整，当前版本：{}，升级前版本：{}",
						deviceListDO.getDeviceName(), currentVersion, sourceVersion);
				return new CheckResult(0); // 仍在升级
			}

			// 提取主版本号进行比较
			String currentMainVersion = extractMainVersion(currentVersion);
			String sourceMainVersion = extractMainVersion(sourceVersion);

			log.info("设备{}版本检查：当前主版本={}, 升级前主版本={}",
					deviceListDO.getDeviceName(), currentMainVersion, sourceMainVersion);

			// 主版本号不同表示升级成功
			if (!sourceMainVersion.equals(currentMainVersion)) {
				log.info("设备{}升级成功，主版本号从{}变更为{}",
						deviceListDO.getDeviceName(), sourceMainVersion, currentMainVersion);
				return new CheckResult(1, currentVersion); // 升级成功
			}

			// 版本号没有变化，仍在升级中
			return new CheckResult(0); // 仍在升级

		} catch (Exception e) {
			log.error(String.format("设备%s检查升级结果异常: %s", deviceListDO.getDeviceName(), e.getMessage()), e);
			return new CheckResult(2, "检查升级状态异常: " + e.getMessage()); // 升级失败
		}
	}

	/**
	 * 提取主版本号（去除括号中的子版本号）
	 * @param version 完整版本号
	 * @return 主版本号
	 */
	private String extractMainVersion(String version) {
		if (StrUtil.isBlank(version)) {
			return "";
		}

		// 如果包含括号，提取括号前的部分作为主版本号
		if (version.contains("(")) {
			return version.split("\\(")[0].trim();
		}

		return version.trim();
	}

	public void upgradePostBatch(DelayQueueMessage delayQueueMessage) {
		log.info("批量升级下发指令");

		// 查询批量升级任务
		Long taskId = (Long) delayQueueMessage.getParams().get("taskId");
		FirmwareBatchUpgradeTaskDO firmwareBatchUpgradeTaskDO = firmwareBatchUpgradeTaskMapper.selectById(taskId);
		com.weihengtech.utils.BeanUtil.assertNotNull(firmwareBatchUpgradeTaskDO);

		// 查询固件包
		FirmwareUploadDO firmwareUploadDO = firmwareUploadMapper.selectById(firmwareBatchUpgradeTaskDO.getFirmwareId());
		com.weihengtech.utils.BeanUtil.assertNotNull(firmwareUploadDO);

		// 查询待升级设备
		List<DeviceUpgradeDetailDO> deviceUpgradeDetailDOS = deviceUpgradeDetailMapper.selectList(Wrappers.<DeviceUpgradeDetailDO>lambdaQuery()
				.eq(DeviceUpgradeDetailDO::getBatchTaskId, taskId)
				.eq(DeviceUpgradeDetailDO::getUpgradeStatus, UpgradeDeviceStatusEnum.WAITING.getStatus())
		);
		List<DeviceListDO> deviceListDOList = deviceListMapper.selectList(Wrappers.<DeviceListDO>lambdaQuery()
				.in(DeviceListDO::getId, deviceUpgradeDetailDOS.stream().map(DeviceUpgradeDetailDO::getDeviceId).collect(Collectors.toList()))
		);

		// 异步升级
		deviceListDOList.forEach(deviceListDO -> {
			com.weihengtech.utils.BeanUtil.assertNotNull(deviceListDO);
			LocalDateTime now = LocalDateTime.now();
			Optional<DeviceUpgradeDetailDO> deviceUpgradeDetailDO = deviceUpgradeDetailDOS.stream().filter(
					i -> i.getDeviceId().equals(deviceListDO.getId())
			).findFirst();

			deviceUpgradeDetailDO.ifPresent(i -> {
				i.setUpgradeStatus(UpgradeDeviceStatusEnum.UPGRADEING.getStatus());
				i.setStartTime(now);
				i.setUpdateTime(now);
			});

			if (checkGwyVersionTooOld(deviceListDO)) {
				deviceUpgradeDetailDO.ifPresent(i -> {
					i.setUpgradeStatus(UpgradeDeviceStatusEnum.FAILURE.getStatus());
					i.setEndTime(now);
					i.setFailureReason(LocaleUtil.getMessage(ExceptionEnum.CHOLLA_VERSION_TOO_OLD.getMsg()));
				});
				log.warn("设备{}cholla棒子版本过低，无法升级", deviceListDO.getDeviceName());
				return;
			}

			SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
			DeviceStatusEnum deviceStatusEnum = specificServService.checkDeviceStatus(deviceListDO.getWifiSn());
			if (!DeviceStatusEnum.ONLINE.equals(deviceStatusEnum)) {
				deviceUpgradeDetailDO.ifPresent(i -> {
					i.setUpgradeStatus(UpgradeDeviceStatusEnum.FAILURE.getStatus());
					i.setEndTime(now);
					i.setFailureReason(LocaleUtil.getMessage(ExceptionEnum.DEVICE_OFFLINE_ERROR.getMsg()));
				});
				log.warn("设备{}离线，无法升级", deviceListDO.getDeviceName());
				return;
			}


			Map<String, String> currentVersionMap;
			// 查询当前固件版本号
			try {
				currentVersionMap = specificServService.getFirmwareVersionByDeviceListDOAndTypeName(deviceListDO, firmwareUploadDO.getType());
				if (CollUtil.isNotEmpty(currentVersionMap)) {
					log.info("设备{}升级前查询到当前版本号为：{}", deviceListDO.getDeviceSn(), currentVersionMap);
					deviceUpgradeDetailDO.ifPresent(i -> {
						i.setCurrentVersion(currentVersionMap.get(firmwareUploadDO.getType()));
					});
				} else {
					deviceUpgradeDetailDO.ifPresent(i -> {
						i.setUpgradeStatus(UpgradeDeviceStatusEnum.FAILURE.getStatus());
						i.setEndTime(now);
						i.setFailureReason(String.format("设备%s未查询到当前版本，无法升级", deviceListDO.getDeviceSn()));
					});
					log.warn("设备{}未查询到当前版本，无法升级", deviceListDO.getDeviceName());
					return;
				}
			} catch (Exception e) {
				deviceUpgradeDetailDO.ifPresent(i -> {
					i.setUpgradeStatus(UpgradeDeviceStatusEnum.FAILURE.getStatus());
					i.setEndTime(now);
					i.setFailureReason(String.format("设备%s升级前查询当前版本号异常: %s", deviceListDO.getDeviceSn(), e.getMessage()));
				});
				log.error(String.format("设备%s升级前查询当前版本号异常: %s", deviceListDO.getDeviceSn(), e.getMessage()), e);
				return;
			}

			try {
				specificServService.sendUpgradeCommand(deviceListDO.getDeviceName(), firmwareUploadDO.getDownloadUrl(), firmwareUploadDO.getSize());
				log.info("设备{}下发升级指令成功", deviceListDO.getDeviceName());
			} catch (Exception e) {
				deviceUpgradeDetailDO.ifPresent(i -> {
					i.setUpgradeStatus(UpgradeDeviceStatusEnum.FAILURE.getStatus());
					i.setEndTime(now);
					i.setFailureReason("下发升级指令失败（设备通信存在问题）");
				});
				log.error(String.format("设备%s下发升级指令异常: %s", deviceListDO.getDeviceSn(), e.getMessage()), e);
			}

		});
		ActionFlagUtil.assertTrue(deviceUpgradeDetailService.updateBatchById(deviceUpgradeDetailDOS));
	}

	private boolean checkGwyVersionTooOld(DeviceListDO deviceInfo) {
		// 校验cholla棒子版本是否过低
		if (DeviceDatasourceEnum.WH.getDatasource() == deviceInfo.getDataSource()) {
			String dictItemCode = GwyTypeEnum.getCodeByPre(deviceInfo.getWifiSn().substring(0, 5));
			List<DictItemDTO> itemList = dictItemService.queryDictItemsByDictCode(CommonConstants.DICT_CODE_CHECK_VERSION,
					false, null);
			if (CollUtil.isNotEmpty(itemList)) {
				String wifiVersion = deviceInfo.getWifiSoftwareVersion();
				return itemList.stream()
						.filter(i -> i.getCode().equals(dictItemCode))
						.map(DictItemDTO::getName)
						.findFirst()
						.map(i -> {
							int iVal = Integer.parseInt(i.substring(i.length() - 2));
							int curVal = Integer.parseInt(wifiVersion.substring(wifiVersion.length() - 2));
							return curVal < iVal;
						})
						.orElse(false);
			}
		}
		return false;
	}

	/**
	 * 更新批量升级任务的统计信息
	 * @param updateTask 任务类
	 */
	private void updateBatchUpgradeTaskStats(FirmwareBatchUpgradeTaskDO updateTask) {
		Long taskId = updateTask.getId();
		try {
			// 查询任务下所有设备的升级状态统计
			List<DeviceUpgradeDetailDO> allDevices = deviceUpgradeDetailMapper.selectList(
					Wrappers.<DeviceUpgradeDetailDO>lambdaQuery()
							.eq(DeviceUpgradeDetailDO::getBatchTaskId, taskId)
			);

			// 统计各状态设备数量
			int totalCount = allDevices.size();
			int successCount = 0;
			int failureCount = 0;
			int upgradingCount = 0;
			int waitingCount = 0;

			for (DeviceUpgradeDetailDO device : allDevices) {
				String upgradeStatus = device.getUpgradeStatus();
				if (UpgradeDeviceStatusEnum.SUCCESS.getStatus().equals(upgradeStatus)) {
					successCount++;
				} else if (UpgradeDeviceStatusEnum.FAILURE.getStatus().equals(upgradeStatus)) {
					failureCount++;
				} else if (UpgradeDeviceStatusEnum.UPGRADEING.getStatus().equals(upgradeStatus)) {
					upgradingCount++;
				} else if (UpgradeDeviceStatusEnum.WAITING.getStatus().equals(upgradeStatus)) {
					waitingCount++;
				}
			}

			// 计算进度百分比（已完成的设备数量 / 总设备数量 * 100）
			int completedCount = successCount + failureCount;
			int progress = (int) Math.round((double) completedCount / totalCount * 100);

			// 判断任务状态
			String taskStatus;
			if (completedCount == totalCount) {
				// 所有设备都已完成（成功或失败）
				taskStatus = UpgradeTaskStatusEnum.FINISHED.getStatus();
			} else if (upgradingCount > 0) {
				// 有设备在升级中
				taskStatus = UpgradeTaskStatusEnum.RUNNING.getStatus();
			} else if (waitingCount > 0) {
				// 还有设备等待升级
				taskStatus = UpgradeTaskStatusEnum.WAITING.getStatus();
			} else {
				// 异常状态，默认为执行中
				taskStatus = UpgradeTaskStatusEnum.RUNNING.getStatus();
			}

			// 更新批量升级任务
			updateTask.setUpgradingCount(upgradingCount);
			updateTask.setSuccessCount(successCount);
			updateTask.setFailureCount(failureCount);
			updateTask.setProgress(progress);
			updateTask.setStatus(taskStatus);
			updateTask.setUpdateTime(LocalDateTime.now());

			ActionFlagUtil.singleAction(firmwareBatchUpgradeTaskMapper.updateById(updateTask));

			log.info("批量升级任务{}统计信息更新完成: 总数={}, 成功={}, 失败={}, 升级中={}, 等待={}, 进度={}%, 状态={}",
					taskId, totalCount, successCount, failureCount, upgradingCount, waitingCount, progress, taskStatus);

		} catch (Exception e) {
			log.error(String.format("更新批量升级任务%s统计信息异常: %s", taskId, e.getMessage()), e);
		}
	}
}
