package com.weihengtech.service.firmware;

import com.weihengtech.pojo.vos.firmware.GwyConfigVersionVO;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotGwyFirmwareResponse;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotGwyHistoryResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface GwyOtaService {

	String upgradeGwy(String wifiSn);

	String queryGwyStatus(String deviceSn, String taskId);

	List<IotGwyHistoryResponse> queryGwyHistory(String deviceSn);

	List<IotGwyFirmwareResponse> queryGwyFirmwareList(String productName);

	GwyConfigVersionVO queryGwyConfigVersion();

	void saveGwyConfigVersion(GwyConfigVersionVO param);
}