package com.weihengtech.service.firmware;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.firmware.FirmwareUploadDO;
import com.weihengtech.pojo.dos.firmware.MiddleFirmwareSoftwareDO;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface MiddleFirmwareSoftwareService extends IService<MiddleFirmwareSoftwareDO> {

	/**
	 * 关联软件版本
	 *
	 * @param firmwareId           固件包id
	 * @param softwareIdForTypeMap 要关联的软件版本
	 * @param operatorId           操作者id
	 * @param firmwareUploadDO     固件升级实例
	 */
	void linkSoftwareVersion(
			Long firmwareId, Map<Long, String> softwareIdForTypeMap, Long operatorId,
			FirmwareUploadDO firmwareUploadDO
	);

	/**
	 * 重新关联软件版本
	 *
	 * @param firmwareId           固件包id
	 * @param softwareIdForTypeMap 要关联的软件版本
	 * @param operatorId           操作者id
	 * @param firmwareUploadDO     固件升级实例
	 */
	void reLinkSoftwareVersion(
			Long firmwareId, Map<Long, String> softwareIdForTypeMap, Long operatorId,
			FirmwareUploadDO firmwareUploadDO
	);
}
