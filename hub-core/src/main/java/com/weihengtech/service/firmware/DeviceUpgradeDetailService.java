package com.weihengtech.service.firmware;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.firmware.DeviceUpgradeDetailDO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.firmware.DeviceUpgradeDetailPageVo;

import java.util.List;

/**
 * @author: jiahao.jin
 * @create: 2025-07-14 16:43
 * @description:
 */
public interface DeviceUpgradeDetailService extends IService<DeviceUpgradeDetailDO> {

    // 根据任务ID查询设备升级详情列表
    PageInfoDTO<DeviceUpgradeDetailDO> pageDeviceUpgradeDetail(DeviceUpgradeDetailPageVo deviceUpgradeDetailPageVo);
}
