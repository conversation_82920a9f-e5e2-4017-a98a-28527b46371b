package com.weihengtech.service.firmware.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.auth.dto.ResourceListReqDTO;
import com.weihengtech.auth.dto.ResourceResDTO;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.common.exceptions.RoleException;
import com.weihengtech.consts.CommonConstants;
import com.weihengtech.consts.RoleConstants;
import com.weihengtech.enums.device.DeviceDatasourceEnum;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.enums.firmware.FirmwareModelEnum;
import com.weihengtech.enums.firmware.GwyTypeEnum;
import com.weihengtech.enums.firmware.MqTagEnum;
import com.weihengtech.enums.firmware.UploadTypeEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.firmware.FirmwareUpgradeRecordDO;
import com.weihengtech.pojo.dos.firmware.FirmwareUploadDO;
import com.weihengtech.pojo.dos.firmware.HardwareTypeDO;
import com.weihengtech.pojo.dos.firmware.MiddleFirmwareSoftwareDO;
import com.weihengtech.pojo.dtos.firmware.UpgradeResDTO;
import com.weihengtech.pojo.dtos.other.DictItemDTO;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionUpgradeVO;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionUpgradeWithSnVo;
import com.weihengtech.service.device.DeviceBoundService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.firmware.FirmwareUpgradeRecordService;
import com.weihengtech.service.firmware.FirmwareUpgradeService;
import com.weihengtech.service.firmware.FirmwareUploadService;
import com.weihengtech.service.firmware.HardwareTypeService;
import com.weihengtech.service.firmware.MiddleFirmwareSoftwareService;
import com.weihengtech.service.firmware.MqService;
import com.weihengtech.service.firmware.SoftwareVersionService;
import com.weihengtech.service.other.DictItemService;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.tasks.DelayQueueMessage;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.InitUtil;
import com.weihengtech.utils.ModbusRequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.weihengtech.consts.RoleConstants.ROLE_AGENT;
import static com.weihengtech.consts.RoleConstants.ROLE_DEALER;
import static com.weihengtech.consts.RoleConstants.ROLE_RETAILER;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/26 15:35
 */
@Service
@Slf4j
public class FirmwareUpgradeServiceImpl implements FirmwareUpgradeService {

    @Value("${custom.datacenter.name}")
    private String datacenter;

    @Resource
    private DeviceListService deviceListService;
    @Resource
    private DeviceBoundService deviceBoundService;
    @Resource
    private TuyaDatacenterService tuyaDatacenterService;
    @Resource
    private AuthCenterResClient authCenterResClient;
    @Resource
    private FirmwareUploadService firmwareUploadService;
    @Resource
    private FirmwareUpgradeRecordService firmwareUpgradeRecordService;
    @Resource
    private MiddleFirmwareSoftwareService middleFirmwareSoftwareService;
    @Resource
    private SoftwareVersionService softwareVersionService;
    @Resource
    private HardwareTypeService hardwareTypeService;
    @Resource
    private StrategyService strategyService;
    @Resource
    private ModbusRequestUtil modbusRequestUtil;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private MqService mqService;
    @Resource
    private DictItemService dictItemService;

    @Override
    public void upgradeSoftware(SoftwareVersionUpgradeVO softwareVersionUpgradeVO) {
        List<String> deviceIdList = softwareVersionUpgradeVO.getDeviceIdList();
        List<String> firmwareIdList = softwareVersionUpgradeVO.getFirmwareIdList();
        String role = UserInfoUtil.currentUserRoleCategory();
        if (ROLE_AGENT.equals(role) || ROLE_DEALER.equals(role) || ROLE_RETAILER.equals(role)) {
            if (deviceIdList.size() != 1) {
                throw new RoleException(ExceptionEnum.ROLE_NOT_ALLOWED);
            } else {
                DeviceListDO deviceListDO = deviceListService.getById(deviceIdList.get(0));
                com.weihengtech.utils.BeanUtil.assertNotNull(deviceListDO);
                deviceBoundService.onlyProcessBoundDevice(deviceListDO.getDeviceName());
            }
        }
        if (deviceIdList.size() != firmwareIdList.size()) {
            log.warn("固件包和设备数量不匹配");
            throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
        }
        for (int i = 0; i < deviceIdList.size(); i++) {
            String deviceId = deviceIdList.get(i);
            String firmwareId = firmwareIdList.get(i);
            DeviceListDO deviceListDO = deviceListService.getById(deviceId);
            com.weihengtech.utils.BeanUtil.assertNotNull(deviceListDO);
            if (checkGwyVersionTooOld(deviceListDO)) {
                throw new CustomException(ExceptionEnum.CHOLLA_VERSION_TOO_OLD);
            }
            upgradeForDevice(deviceListDO, firmwareId);
        }
    }

    @Override
    public UpgradeResDTO upgradeSoftwareWithSn(SoftwareVersionUpgradeWithSnVo param) {
        List<String> deviceNameList = param.getDeviceNameList();
        List<String> firmwareIdList = param.getFirmwareIdList();
        if (deviceNameList.size() != firmwareIdList.size()) {
            log.warn("固件包与设备数量不匹配");
            throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
        }
        List<String> successList = new ArrayList<>();
        List<String> invalidList = new ArrayList<>();
        List<String> nonCompliantList = new ArrayList<>();
        List<String> needUpgradeGwyList = new ArrayList<>();
        List<String> latestVersionList = new ArrayList<>();
        for (int i = 0; i < deviceNameList.size(); i++) {
            String deviceName = deviceNameList.get(i);
            String firmwareId = firmwareIdList.get(i);
            DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(deviceName);
            List<ResourceResDTO> resourceList = authCenterResClient.resourceList(ResourceListReqDTO.builder()
                    .ids(String.valueOf(deviceInfo.getId()))
                    .dataCenter(datacenter)
                    .build());
            if (CollUtil.isNotEmpty(resourceList)) {
                ResourceResDTO resource = resourceList.get(0);
                Integer category = resource.getCategory();
                if (!FirmwareModelEnum.isMatchSeries(param.getModel(), category)) {
                    invalidList.add(resource.getCode());
                    continue;
                }
            }
            // 如果是外部用户，优先校验当前储能设备索要升级的固件是否已经是最新版本
            if (2 != UserInfoUtil.currentUser().getSource()) {
                if (isLatestVersion(deviceInfo, firmwareId)) {
                    latestVersionList.add(deviceName);
                    continue;
                }
            }
            if (checkGwyVersionTooOld(deviceInfo)) {
                needUpgradeGwyList.add(deviceName);
                continue;
            }
            try {
                upgradeForDevice(deviceInfo, firmwareId);
                successList.add(deviceName);
            } catch (CustomException e) {
                log.warn(String.format("设备%s升级请求异常:", deviceName), e);
                nonCompliantList.add(deviceName);
            } catch (Exception e) {
                log.warn(String.format("设备%s升级请求异常:", deviceName), e);
                invalidList.add(deviceName);
            }
        }
        return UpgradeResDTO.builder()
                .successList(successList)
                .invalidList(invalidList)
                .nonCompliantList(nonCompliantList)
                .needUpgradeGwyList(needUpgradeGwyList)
                .latestVersionList(latestVersionList)
                .build();
    }

    @Override
    public String upgradeCurDevice(String type) {
        if (InitUtil.DELAY_QUEUE.isEmpty()) {
            return "DELAY_QUEUE is empty";
        }
        if ("peek".equals(type)) {
            return String.format("DELAY_QUEUE size is %d, content is %s", InitUtil.DELAY_QUEUE.size(),
                    InitUtil.DELAY_QUEUE);
        }
        DelayQueueMessage peek = InitUtil.DELAY_QUEUE.peek();
        if (peek.getDelay(TimeUnit.SECONDS) < 0) {
            InitUtil.initDelayQueueTaskThread();
        }
        return "done";
    }

    private boolean checkGwyVersionTooOld(DeviceListDO deviceInfo) {
        // 校验cholla棒子版本是否过低
        if (DeviceDatasourceEnum.WH.getDatasource() == deviceInfo.getDataSource()) {
            String dictItemCode = GwyTypeEnum.getCodeByPre(deviceInfo.getWifiSn().substring(0, 5));
            List<DictItemDTO> itemList = dictItemService.queryDictItemsByDictCode(CommonConstants.DICT_CODE_CHECK_VERSION,
                    false, null);
            if (CollUtil.isNotEmpty(itemList)) {
                String wifiVersion = deviceInfo.getWifiSoftwareVersion();
                return itemList.stream()
                        .filter(i -> i.getCode().equals(dictItemCode))
                        .map(DictItemDTO::getName)
                        .findFirst()
                        .map(i -> {
                            int iVal = Integer.parseInt(i.substring(i.length() - 2));
                            int curVal = Integer.parseInt(wifiVersion.substring(wifiVersion.length() - 2));
                            return curVal < iVal;
                        })
                        .orElse(false);
            }
        }
        return false;
    }

    // isLatestVersion 校验当前储能设备索要升级的固件是否已经是最新版本
    private boolean isLatestVersion(DeviceListDO deviceInfo, String firmwareId) {
        FirmwareUploadDO firmwareItem = firmwareUploadService.getById(firmwareId);
        com.weihengtech.utils.BeanUtil.assertNotNull(firmwareItem);

        List<DictItemDTO> inverterVersionList = dictItemService.queryDictItemsByDictCode(CommonConstants.DICT_CODE_INVERTER_FIRMWARE,
                false, null);
        Map<String, List<String>> inverterMap = inverterVersionList.stream()
                .collect(Collectors.toMap(DictItemDTO::getCode, i -> JSONUtil.toList(i.getName(), String.class)));

        // 要升级的固件的类型
        String firmwareItemType = firmwareItem.getType();

        if (firmwareItemType.equals(UploadTypeEnum.EMS.name())) {
            String emsSoftwareVersion = deviceInfo.getEmsSoftwareVersion();
            return inverterMap.get(CommonConstants.ITEM_CODE_EMS).contains(emsSoftwareVersion);
        }
        if (firmwareItemType.equals(UploadTypeEnum.DSP1.name())) {
            String dsp1SoftwareVersion = deviceInfo.getDsp1SoftwareVersion();
            return inverterMap.get(CommonConstants.ITEM_CODE_DSP1).contains(dsp1SoftwareVersion);
        }
        if (firmwareItemType.equals(UploadTypeEnum.DSP2.name())) {
            String dsp2SoftwareVersion = deviceInfo.getDsp2SoftwareVersion();
            return inverterMap.get(CommonConstants.ITEM_CODE_DSP2).contains(dsp2SoftwareVersion);
        }
        if (firmwareItemType.equals(UploadTypeEnum.BMS.name())) {
            String bmsSoftwareVersion = deviceInfo.getBmsSoftwareVersion();
            return inverterMap.get(CommonConstants.ITEM_CODE_BMS).contains(bmsSoftwareVersion);
        }
        return false;
    }

    /** 获取固件详情、类型信息，异步升级 */
    private void upgradeForDevice(DeviceListDO deviceInfo, String firmwareId) {
        FirmwareUploadDO firmwareItem = firmwareUploadService.getById(firmwareId);
        com.weihengtech.utils.BeanUtil.assertNotNull(firmwareItem);
        SpecificServService specificServService = strategyService.chooseSpecificServ(deviceInfo);
        DeviceStatusEnum deviceStatusEnum = specificServService.checkDeviceStatus(deviceInfo.getWifiSn());
        if (!DeviceStatusEnum.ONLINE.equals(deviceStatusEnum)) {
            log.info("设备{}离线，无法升级", deviceInfo.getDeviceName());
            throw new CustomException(ExceptionEnum.DEVICE_OFFLINE_ERROR);
        }
        MiddleFirmwareSoftwareDO middle = middleFirmwareSoftwareService
                .getOne(Wrappers.<MiddleFirmwareSoftwareDO>lambdaQuery().eq(MiddleFirmwareSoftwareDO::getFirmwareId, firmwareId));
        String typeName = Optional.ofNullable(middle)
                .map(i -> softwareVersionService.getById(i.getSoftwareId()))
                .map(i -> hardwareTypeService.getById(i.getTypeId()))
                .map(HardwareTypeDO::getCategoryName)
                .orElseThrow(() -> new CustomException(ExceptionEnum.FIRMWARE_NOT_BIND_SOFTWARE));
        String roleCategory = UserInfoUtil.currentUserRoleCategory();
        if (!RoleConstants.ROLE_ADMIN.equals(roleCategory) && !RoleConstants.ROLE_DEV_ADMIN.equals(roleCategory)) {
            List<Integer> soc = modbusRequestUtil.postTransparentRead(deviceInfo.getDeviceName(), 32033, 1, 1);
            List<Integer> acVacR = modbusRequestUtil.postTransparentRead(deviceInfo.getDeviceName(), 31014, 1, 1);
            if (soc.get(0) < 10 && acVacR.get(0) < 1000) {
                log.info("设备{}电池SOC{}小于10%并且交流侧R相电压{}小于100V，无法升级", deviceInfo.getDeviceName(), soc.get(0), acVacR.get(0));
                throw new CustomException(ExceptionEnum.DEVICE_UPGRADE_SOC_LIMIT);
            }
        }
        threadPoolTaskExecutor.execute(() -> asyncUpgrade(deviceInfo, firmwareItem, typeName));
    }

    /** 异步升级：保存升级记录、版本预检查 */
    private void asyncUpgrade(DeviceListDO deviceInfo, FirmwareUploadDO firmwareItem, String typeName) {
        FirmwareUpgradeRecordDO record = firmwareUpgradeRecordService.saveFirmwareUpgradeRecord(deviceInfo, firmwareItem, typeName);
        try {
            this.preCheckDeviceVersion(record);
        } catch (Exception e) {
            log.error(String.format("设备%s异步升级异常: %s", deviceInfo.getDeviceSn(), e.getMessage()), e);
            DelayQueueMessage delayQueueMessage = new DelayQueueMessage(System.currentTimeMillis());
            delayQueueMessage.setParams(Dict.create().set("recordId", record.getId())
                    .set("type", MqTagEnum.UPGRADE_FAILURE.getMethodName()));
            mqService.upgradeFailure(delayQueueMessage);
        }
    }

    /** 升级前校验设备版本号 */
    private void preCheckDeviceVersion(FirmwareUpgradeRecordDO record) {
        String deviceName = record.getDeviceName();
        Long deviceId = record.getDeviceId();
        String typeName = record.getAddressMap();
        SpecificServService specificServService = strategyService.chooseSpecificServ(deviceId);
        Map<String, String> currentVersionMap;
        try {
            currentVersionMap = specificServService.getFirmwareVersionByTypeList(deviceName, typeName);
            log.info("设备{}升级前查询到当前版本号为：{}", deviceName, currentVersionMap);
        } catch (Exception e) {
            log.error(String.format("设备%s升级前查询当前版本号异常: %s", deviceName, e.getMessage()), e);
            throw new CustomException(ExceptionEnum.DEVICE_UPGRADE_ERROR);
        }
        if (CollUtil.isNotEmpty(currentVersionMap)) {
            String currentVersion = currentVersionMap.get(record.getAddressMap());
            String sourceVersion = record.getSourceVersion();
            if (StrUtil.isNotBlank(sourceVersion) && !sourceVersion.equals(currentVersion)) {
                log.info("设备{}升级前查询到原版本为{}, 当前版本为{}", deviceName, sourceVersion, currentVersion);
                if (sourceVersion.contains("(") && currentVersion.contains("(")) {
                    mqService.processSuccess(record, currentVersion);
                } else if (!sourceVersion.contains("(") && !currentVersion.contains("(")){
                    mqService.processSuccess(record, currentVersion);
                }
            } else {
                record.setSourceVersion(currentVersion);
            }
        } else {
            log.info("设备{}升级前未查询到当前版本, 仍发送固件升级指令...", deviceName);
            record.setSourceVersion(" [Fail to check current version] ");
        }
        record.setUpdateTime(LocalDateTime.now());
        ActionFlagUtil.assertTrue(firmwareUpgradeRecordService.updateById(record));
        mqService.addUpgradeTask(specificServService, record.getDeviceName(), record.getUploadUrl(),
                record.getSize(), record.getId());
    }
}
