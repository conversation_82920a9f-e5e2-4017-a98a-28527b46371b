package com.weihengtech.service.firmware;

import com.weihengtech.pojo.dtos.firmware.FirmwareAllDTO;
import com.weihengtech.pojo.dtos.firmware.FirmwarePageDTO;
import com.weihengtech.pojo.dtos.firmware.FirmwareUpgradeRecordPageDTO;
import com.weihengtech.pojo.dtos.firmware.FirmwareUploadAllDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.firmware.FirmwareDelVO;
import com.weihengtech.pojo.vos.firmware.FirmwarePageVO;
import com.weihengtech.pojo.vos.firmware.FirmwareTopVO;
import com.weihengtech.pojo.vos.firmware.FirmwareUpgradeRecordPageVO;
import com.weihengtech.pojo.vos.firmware.RemarksVo;
import com.weihengtech.pojo.vos.firmware.VersionBaseVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FirmwareSoftwareService {

	/**
	 * 查询固件包维护页面需要的软件版本信息
	 *
	 * @param versionBaseVo 可选版本号
	 * @return 软件版本封装类
	 */
	List<FirmwareUploadAllDTO> queryAllSoftwareVersion(VersionBaseVO versionBaseVo);

	/**
	 * 固件包分页
	 *
	 * @param firmwarePageVO 固件包分页入参
	 * @return 固件包分页信息回参
	 */
	PageInfoDTO<FirmwarePageDTO> pageFirmware(FirmwarePageVO firmwarePageVO);

	/**
	 * 删除指定固件包
	 *
	 * @param firmwareDelVO 固件包标识
	 */
	void delFirmware(FirmwareDelVO firmwareDelVO);

	/**
	 * 固件升级记录分页
	 *
	 * @param firmwareUpgradeRecordPageVO 固件升级记录入参
	 * @return 分页数据
	 */
	PageInfoDTO<FirmwareUpgradeRecordPageDTO> pageUpgradeRecord(
			FirmwareUpgradeRecordPageVO firmwareUpgradeRecordPageVO
	);

	/**
	 * 固件包信息按类型分
	 *
	 * @return 固件包信息分类列表
	 */
	List<FirmwareAllDTO> allFirmware(String model);

	/**
	 * 备注固件包
	 *
	 * @param remarksVo 固件包备注入参
	 */
	void remarks(RemarksVo remarksVo);

	/**
	 * 固件包置顶
	 *
	 * @param topVO 置顶入参
	 */
    void top(FirmwareTopVO topVO);
}
