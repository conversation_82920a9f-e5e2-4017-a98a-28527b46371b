package com.weihengtech.service.firmware.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.CommonConstants;
import com.weihengtech.dao.firmware.HardwareCategoryMapper;
import com.weihengtech.dao.firmware.HardwareTypeMapper;
import com.weihengtech.dao.firmware.HardwareVersionMapper;
import com.weihengtech.dao.firmware.MiddleFirmwareSoftwareMapper;
import com.weihengtech.dao.firmware.MiddleSoftwareHardwareMapper;
import com.weihengtech.dao.firmware.SoftwareVersionMapper;
import com.weihengtech.pojo.bos.firmware.VersionBO;
import com.weihengtech.pojo.bos.firmware.VersionBindBO;
import com.weihengtech.pojo.dos.firmware.HardwareCategoryDO;
import com.weihengtech.pojo.dos.firmware.HardwareTypeDO;
import com.weihengtech.pojo.dos.firmware.HardwareVersionDO;
import com.weihengtech.pojo.dos.firmware.MiddleFirmwareSoftwareDO;
import com.weihengtech.pojo.dos.firmware.MiddleSoftwareHardwareDO;
import com.weihengtech.pojo.dos.firmware.SoftwareVersionDO;
import com.weihengtech.pojo.dtos.firmware.HardwareCategoryTypeMapDTO;
import com.weihengtech.pojo.dtos.firmware.HardwareVersionAllDTO;
import com.weihengtech.pojo.dtos.firmware.HardwareVersionPageDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.dtos.firmware.SoftwareVersionAllDTO;
import com.weihengtech.pojo.dtos.firmware.SoftwareVersionPageDTO;
import com.weihengtech.pojo.vos.firmware.HardwareVersionInsertVO;
import com.weihengtech.pojo.vos.firmware.HardwareVersionPageVO;
import com.weihengtech.pojo.vos.firmware.HardwareVersionUpdateVO;
import com.weihengtech.pojo.vos.firmware.RemarksVo;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionInsertVO;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionPageVO;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionUpdateVO;
import com.weihengtech.pojo.vos.firmware.VersionBaseVO;
import com.weihengtech.pojo.vos.firmware.VersionBatchDeleteVO;
import com.weihengtech.service.firmware.HardwareSoftwareVersionService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.OperationUtil;
import com.weihengtech.utils.SnowFlakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HardwareSoftwareVersionServiceImpl implements HardwareSoftwareVersionService {

	@Resource
	private HardwareTypeMapper hardwareTypeMapper;

	@Resource
	private SoftwareVersionMapper softwareVersionMapper;

	@Resource
	private HardwareVersionMapper hardwareVersionMapper;

	@Resource
	private MiddleSoftwareHardwareMapper middleSoftwareHardwareMapper;

	@Resource
	private MiddleFirmwareSoftwareMapper middleFirmwareSoftwareMapper;

	@Resource
	private HardwareCategoryMapper hardwareCategoryMapper;

	@Resource
	private SnowFlakeUtil snowFlakeUtil;

	@Override
	@DSTransactional
	public void insertSoftwareVersion(SoftwareVersionInsertVO softwareVersionInsertVO) {

		String softwareVersion = softwareVersionInsertVO.getSoftwareVersion();
		List<SoftwareVersionDO> softwareVersionDOS = softwareVersionMapper.selectList(
				Wrappers.<SoftwareVersionDO>lambdaQuery().eq(SoftwareVersionDO::getSoftwareVersion, softwareVersion)
		);
		if (CollUtil.isNotEmpty(softwareVersionDOS)) {
			log.warn("软件版本已存在");
			throw new CustomException(ExceptionEnum.DATA_ALREADY_EXISTED);
		}
		long softwareVersionId = snowFlakeUtil.generateId();
		LocalDateTime now = LocalDateTime.now();
		Long operatorId = Long.parseLong(UserInfoUtil.currentUserId());
		String hardwareTypeId = softwareVersionInsertVO.getHardwareTypeId();
		HardwareTypeDO hardwareTypeDO = hardwareTypeMapper.selectById(hardwareTypeId);
		BeanUtil.assertNotNull(hardwareTypeDO);
		Long doId = hardwareTypeDO.getId();

		SoftwareVersionDO softwareVersionDO = new SoftwareVersionDO();
		softwareVersionDO.setId(softwareVersionId);
		softwareVersionDO.setSoftwareVersion(softwareVersion);
		softwareVersionDO.setOperatorId(operatorId);
		softwareVersionDO.setCreateTime(now);
		softwareVersionDO.setUpdateTime(now);
		softwareVersionDO.setTypeId(doId);
		softwareVersionDO.setModel(softwareVersionInsertVO.getModel());
		ActionFlagUtil.singleAction(softwareVersionMapper.insert(softwareVersionDO));

		List<String> hardwareVersionList = softwareVersionInsertVO.getHardwareVersionList();
		if (CollUtil.isNotEmpty(hardwareVersionList)) {
			hardwareVersionList.forEach(hardwareVersionId -> {
				HardwareVersionDO hardwareVersionDO = hardwareVersionMapper.selectById(hardwareVersionId);
				BeanUtil.assertNotNull(hardwareVersionDO);
				if (!doId.equals(hardwareVersionDO.getTypeId())) {
					log.warn("硬件类型不匹配");
					throw new CustomException(ExceptionEnum.HARDWARE_TYPE_NOT_SINGLE_ERROR);
				}
				MiddleSoftwareHardwareDO middleSoftwareHardwareDO = new MiddleSoftwareHardwareDO();
				middleSoftwareHardwareDO.setId(snowFlakeUtil.generateId());
				middleSoftwareHardwareDO.setSoftwareId(softwareVersionId);
				middleSoftwareHardwareDO.setHardwareId(Long.parseLong(hardwareVersionId));
				middleSoftwareHardwareDO.setOperatorId(operatorId);
				middleSoftwareHardwareDO.setCreateTime(now);
				middleSoftwareHardwareDO.setUpdateTime(now);

				ActionFlagUtil.singleAction(middleSoftwareHardwareMapper.insert(middleSoftwareHardwareDO));
			});
		}
	}

	@Override
	@DSTransactional
	public void insertHardwareVersion(HardwareVersionInsertVO hardwareVersionInsertVO) {

		String hardwareVersion = hardwareVersionInsertVO.getHardwareVersion();
		List<HardwareVersionDO> existHardwareVersion = hardwareVersionMapper.selectList(
				Wrappers.<HardwareVersionDO>lambdaQuery().eq(HardwareVersionDO::getHardwareVersion, hardwareVersion)
		);
		if (CollUtil.isNotEmpty(existHardwareVersion)) {
			log.warn("硬件版本已存在");
			throw new CustomException(ExceptionEnum.DATA_ALREADY_EXISTED);
		}

		String typeId = hardwareVersionInsertVO.getTypeId();
		HardwareTypeDO hardwareTypeDO = hardwareTypeMapper.selectById(typeId);
		BeanUtil.assertNotNull(hardwareTypeDO);

		Long operatorId = Long.parseLong(UserInfoUtil.currentUserId());
		LocalDateTime now = LocalDateTime.now();
		long hardwareVersionId = snowFlakeUtil.generateId();

		HardwareVersionDO hardwareVersionDO = new HardwareVersionDO();
		hardwareVersionDO.setId(hardwareVersionId);
		hardwareVersionDO.setTypeId(Long.parseLong(typeId));
		hardwareVersionDO.setHardwareVersion(hardwareVersion);
		hardwareVersionDO.setOperatorId(operatorId);
		hardwareVersionDO.setCreateTime(now);
		hardwareVersionDO.setUpdateTime(now);
		hardwareVersionDO.setModel(hardwareVersionInsertVO.getModel());

		ActionFlagUtil.singleAction(hardwareVersionMapper.insert(hardwareVersionDO));

		List<String> softwareVersionList = hardwareVersionInsertVO.getSoftwareVersionList();

		if (CollUtil.isNotEmpty(softwareVersionList)) {
			softwareVersionList.forEach(softwareVersionId -> {
				SoftwareVersionDO softwareVersionDO = softwareVersionMapper.selectById(softwareVersionId);
				BeanUtil.assertNotNull(softwareVersionDO);
				if (!softwareVersionDO.getTypeId().equals(Long.parseLong(typeId))) {
					log.warn("软件绑定硬件类型不匹配");
					throw new CustomException(ExceptionEnum.HARDWARE_TYPE_NOT_SINGLE_ERROR);
				}
				MiddleSoftwareHardwareDO middleSoftwareHardwareDO = new MiddleSoftwareHardwareDO();
				middleSoftwareHardwareDO.setId(snowFlakeUtil.generateId());
				middleSoftwareHardwareDO.setSoftwareId(Long.parseLong(softwareVersionId));
				middleSoftwareHardwareDO.setHardwareId(hardwareVersionId);
				middleSoftwareHardwareDO.setOperatorId(operatorId);
				middleSoftwareHardwareDO.setCreateTime(now);
				middleSoftwareHardwareDO.setUpdateTime(now);

				ActionFlagUtil.singleAction(middleSoftwareHardwareMapper.insert(middleSoftwareHardwareDO));
			});
		}
	}

	@Override
	@DSTransactional
	public void updateSoftwareVersion(SoftwareVersionUpdateVO softwareVersionUpdateVO) {
		String softwareVersionId = softwareVersionUpdateVO.getSoftwareVersionId();
		SoftwareVersionDO softwareVersionDO = softwareVersionMapper.selectById(softwareVersionId);
		BeanUtil.assertNotNull(softwareVersionDO);
		String userIdStr = UserInfoUtil.currentUserId();
		long userId = Long.parseLong(userIdStr);
		softwareVersionDO.setSoftwareVersion(softwareVersionUpdateVO.getSoftwareVersion());
		softwareVersionDO.setUpdateTime(LocalDateTime.now());
		softwareVersionDO.setOperatorId(userId);
		if (softwareVersionUpdateVO.getModel() != null) {
			softwareVersionDO.setModel(softwareVersionUpdateVO.getModel());
		}
		ActionFlagUtil.singleAction(softwareVersionMapper.updateById(softwareVersionDO));
		Long softwareTypeId = softwareVersionDO.getTypeId();
		List<String> hardwareVersionList = softwareVersionUpdateVO.getHardwareVersionList();

		hardwareVersionList.forEach(id -> {
			HardwareVersionDO hardwareVersionDO = hardwareVersionMapper.selectById(id);
			BeanUtil.assertNotNull(hardwareVersionDO);
			if (!softwareTypeId.equals(hardwareVersionDO.getTypeId())) {
				throw new CustomException(ExceptionEnum.HARDWARE_TYPE_NOT_SINGLE_ERROR);
			}
		});

		Map<Long, MiddleSoftwareHardwareDO> middleMap = middleSoftwareHardwareMapper
				.selectList(
						Wrappers.<MiddleSoftwareHardwareDO>lambdaQuery()
								.eq(MiddleSoftwareHardwareDO::getSoftwareId, Long.parseLong(softwareVersionId))
				)
				.stream()
				.collect(
						Collectors.toMap(
								MiddleSoftwareHardwareDO::getHardwareId,
								middleSoftwareHardwareDO -> middleSoftwareHardwareDO
						)
				);

		hardwareVersionList.stream().map(Long::parseLong).filter(id -> !middleMap.containsKey(id)).forEach(id -> {
			MiddleSoftwareHardwareDO isExistLink = queryIsExistMiddleSoftwareHardwareDo(Long.parseLong(softwareVersionId), id);
			if (isExistLink == null) {
				insertMiddleSoftwareHardwareDo(Long.parseLong(softwareVersionId), id, userId);
			}
		});
		deleteMiddleSoftwareHardwareDo(middleMap, hardwareVersionList);
	}

	private MiddleSoftwareHardwareDO queryIsExistMiddleSoftwareHardwareDo(Long softwareId, Long hardwareId) {
		LambdaQueryWrapper<MiddleSoftwareHardwareDO> mQw = new LambdaQueryWrapper<>();
		mQw.eq(MiddleSoftwareHardwareDO::getSoftwareId, softwareId).eq(MiddleSoftwareHardwareDO::getHardwareId, hardwareId);
		return middleSoftwareHardwareMapper.selectOne(mQw);
	}

	private void insertMiddleSoftwareHardwareDo(Long softwareId, Long hardwareId, Long operatorId) {
		MiddleSoftwareHardwareDO middleSoftwareHardwareDO = new MiddleSoftwareHardwareDO();
		middleSoftwareHardwareDO.setId(snowFlakeUtil.generateId());
		middleSoftwareHardwareDO.setSoftwareId(softwareId);
		middleSoftwareHardwareDO.setHardwareId(hardwareId);
		middleSoftwareHardwareDO.setOperatorId(operatorId);
		middleSoftwareHardwareDO.setCreateTime(LocalDateTime.now());
		middleSoftwareHardwareDO.setUpdateTime(LocalDateTime.now());
		ActionFlagUtil.singleAction(middleSoftwareHardwareMapper.insert(middleSoftwareHardwareDO));
	}

	@Override
	@DSTransactional
	public void updateHardwareVersion(HardwareVersionUpdateVO hardwareVersionUpdateVO) {
		String hardwareVersionId = hardwareVersionUpdateVO.getHardwareVersionId();
		HardwareVersionDO hardwareVersionDO = hardwareVersionMapper.selectById(hardwareVersionId);
		BeanUtil.assertNotNull(hardwareVersionDO);
		List<HardwareVersionDO> existHardwareVersion = hardwareVersionMapper.selectList(
				Wrappers.<HardwareVersionDO>lambdaQuery().eq(HardwareVersionDO::getHardwareVersion, hardwareVersionUpdateVO.getHardwareVersion())
		);
		if (CollUtil.isNotEmpty(existHardwareVersion)) {
			log.warn("硬件版本已存在");
			throw new CustomException(ExceptionEnum.DATA_ALREADY_EXISTED);
		}
		String userIdStr = UserInfoUtil.currentUserId();
		long userId = Long.parseLong(userIdStr);
		hardwareVersionDO.setHardwareVersion(hardwareVersionUpdateVO.getHardwareVersion());
		String typeId = hardwareVersionUpdateVO.getTypeId();
		HardwareTypeDO hardwareTypeDO = hardwareTypeMapper.selectById(typeId);
		BeanUtil.assertNotNull(hardwareTypeDO);
		hardwareVersionDO.setTypeId(Long.parseLong(typeId));
		hardwareVersionDO.setUpdateTime(LocalDateTime.now());
		hardwareVersionDO.setOperatorId(userId);
		if (hardwareVersionUpdateVO.getModel() != null) {
			hardwareVersionDO.setModel(hardwareVersionUpdateVO.getModel());
		}
		ActionFlagUtil.singleAction(hardwareVersionMapper.updateById(hardwareVersionDO));

		List<String> softwareVersionList = hardwareVersionUpdateVO.getSoftwareVersionList();

		softwareVersionList.forEach(id -> {
			SoftwareVersionDO softwareVersionDO = softwareVersionMapper.selectById(id);
			if (!softwareVersionDO.getTypeId().equals(hardwareVersionDO.getTypeId())) {
				log.warn("软件绑定硬件类型不匹配");
				throw new CustomException(ExceptionEnum.HARDWARE_TYPE_NOT_SINGLE_ERROR);
			}
		});

		Map<Long, MiddleSoftwareHardwareDO> middleMap = middleSoftwareHardwareMapper.selectList(
						Wrappers.<MiddleSoftwareHardwareDO>lambdaQuery()
								.eq(MiddleSoftwareHardwareDO::getHardwareId, Long.parseLong(hardwareVersionId))
				)
				.stream().collect(Collectors
						.toMap(
								MiddleSoftwareHardwareDO::getSoftwareId,
								middleSoftwareHardwareDO -> middleSoftwareHardwareDO
						));

		softwareVersionList.stream().map(Long::parseLong).filter(id -> !middleMap.containsKey(id)).forEach(id -> {
			MiddleSoftwareHardwareDO isExistDo = queryIsExistMiddleSoftwareHardwareDo(id, Long.parseLong(hardwareVersionId));
			if (isExistDo == null) {
				insertMiddleSoftwareHardwareDo(id, Long.parseLong(hardwareVersionId), userId);
			}
		});
		deleteMiddleSoftwareHardwareDo(middleMap, softwareVersionList);
	}

	private void deleteMiddleSoftwareHardwareDo(Map<Long, MiddleSoftwareHardwareDO> middleMap, List<String> versionList) {
		middleMap.keySet().stream().map(String::valueOf).filter(id -> !versionList.contains(id)).forEach(id -> {
			MiddleSoftwareHardwareDO middleSoftwareHardwareDO = middleMap.get(Long.parseLong(id));
			ActionFlagUtil.singleAction(middleSoftwareHardwareMapper.deleteById(middleSoftwareHardwareDO.getId()));
		});
	}

	@Override
	public HardwareVersionAllDTO queryAllHardwareVersion(VersionBaseVO versionBaseVo) {
		List<HardwareCategoryDO> hardwareCategoryList = hardwareCategoryMapper.selectList(Wrappers.emptyWrapper());
		String softwareVersionId = versionBaseVo.getId();
		List<VersionBO> result = new ArrayList<>();

		for (HardwareCategoryDO hardwareCategoryDO : hardwareCategoryList) {

			VersionBO versionBO = new VersionBO();
			versionBO.setHardwareCategoryId(String.valueOf(hardwareCategoryDO.getId()));
			versionBO.setHardwareCategoryName(hardwareCategoryDO.getCategoryName());

			List<HardwareTypeDO> hardwareTypeDOList = queryHardwareTypeList(hardwareCategoryDO.getId());

			if (CollUtil.isNotEmpty(hardwareTypeDOList)) {
				ArrayList<VersionBO.TypeBO> typeList = Lists.newArrayList();
				for (HardwareTypeDO hardwareTypeDO : hardwareTypeDOList) {
					Long typeId = hardwareTypeDO.getId();
					VersionBO.TypeBO typeBO = new VersionBO.TypeBO();
					typeBO.setHardwareTypeId(String.valueOf(typeId));
					typeBO.setHardwareTypeName(hardwareTypeDO.getTypeName());

					ArrayList<VersionBindBO> hardwareVersionList = Lists.newArrayList();
					ArrayList<VersionBindBO> softwareVersionList = Lists.newArrayList();

					LambdaQueryWrapper<HardwareVersionDO> wrapper = Wrappers.<HardwareVersionDO>lambdaQuery()
							.eq(HardwareVersionDO::getTypeId, typeId);
					if (StrUtil.isNotBlank(versionBaseVo.getModel())) {
						wrapper.eq(HardwareVersionDO::getModel, versionBaseVo.getModel());
					}
					List<HardwareVersionDO> hardwareVersionDoList = hardwareVersionMapper.selectList(wrapper);

					if (CollUtil.isNotEmpty(hardwareVersionDoList)) {
						for (HardwareVersionDO hardwareVersionDO : hardwareVersionDoList) {
							VersionBindBO versionBindBO = new VersionBindBO();
							versionBindBO.setId(String.valueOf(hardwareVersionDO.getId()));
							versionBindBO.setVersion(hardwareVersionDO.getHardwareVersion());
							computedBindFlag(
									Long.parseLong(StrUtil.isBlank(softwareVersionId) ? "-1" : softwareVersionId),
									hardwareVersionDO.getId(), versionBindBO
							);
							hardwareVersionList.add(versionBindBO);
						}
					}

					typeBO.setHardwareVersionList(hardwareVersionList);
					typeBO.setSoftwareVersionList(softwareVersionList);
					typeList.add(typeBO);
				}
				versionBO.setTypeList(typeList);
				result.add(versionBO);
			}
		}
		HardwareVersionAllDTO hardwareVersionAllDTO = new HardwareVersionAllDTO();
		Optional<SoftwareVersionDO> optionalSoftwareVersion = Optional.ofNullable(softwareVersionMapper.selectById(softwareVersionId));
		hardwareVersionAllDTO.setSoftwareVersionName(optionalSoftwareVersion.map(SoftwareVersionDO::getSoftwareVersion).orElse(""));
		hardwareVersionAllDTO.setHardwareVersionList(result);

		return hardwareVersionAllDTO;
	}

	private void computedBindFlag(Long softwareVersionId, Long hardwareVersionId, VersionBindBO versionBindBO) {
		List<MiddleSoftwareHardwareDO> middleList = middleSoftwareHardwareMapper.selectList(
				Wrappers.<MiddleSoftwareHardwareDO>lambdaQuery()
						.eq(MiddleSoftwareHardwareDO::getSoftwareId, softwareVersionId)
						.eq(MiddleSoftwareHardwareDO::getHardwareId, hardwareVersionId)
		);
		if (CollUtil.isNotEmpty(middleList)) {
			versionBindBO.setBindFlag(CommonConstants.VERSION_BIND_ON);
		} else {
			versionBindBO.setBindFlag(CommonConstants.VERSION_BIND_OFF);
		}
	}

	@Override
	public SoftwareVersionAllDTO queryAllSoftwareVersion(VersionBaseVO versionBaseVo) {

		List<HardwareCategoryDO> hardwareCategoryList = hardwareCategoryMapper.selectList(Wrappers.emptyWrapper());
		String hardwareVersionId = versionBaseVo.getId();
		List<VersionBO> result = new ArrayList<>();

		for (HardwareCategoryDO hardwareCategoryDO : hardwareCategoryList) {
			VersionBO versionBO = new VersionBO();
			versionBO.setHardwareCategoryId(String.valueOf(hardwareCategoryDO.getId()));
			versionBO.setHardwareCategoryName(hardwareCategoryDO.getCategoryName());

			List<HardwareTypeDO> hardwareTypeDOList = queryHardwareTypeList(hardwareCategoryDO.getId());

			if (CollUtil.isNotEmpty(hardwareTypeDOList)) {
				ArrayList<VersionBO.TypeBO> typeList = Lists.newArrayList();

				for (HardwareTypeDO hardwareTypeDO : hardwareTypeDOList) {
					ArrayList<VersionBindBO> hardwareVersionList = Lists.newArrayList();
					ArrayList<VersionBindBO> softwareVersionList = Lists.newArrayList();

					Long typeId = hardwareTypeDO.getId();
					VersionBO.TypeBO typeBO = new VersionBO.TypeBO();
					typeBO.setHardwareTypeId(String.valueOf(typeId));
					typeBO.setHardwareTypeName(hardwareTypeDO.getTypeName());

					LambdaQueryWrapper<SoftwareVersionDO> wrapper = Wrappers.<SoftwareVersionDO>lambdaQuery()
							.eq(SoftwareVersionDO::getTypeId, typeId);
					if (StrUtil.isNotBlank(versionBaseVo.getModel())) {
						wrapper.eq(SoftwareVersionDO::getModel, versionBaseVo.getModel());
					}
					Optional.ofNullable(softwareVersionMapper.selectList(wrapper))
							.ifPresent(softwareVersions -> {
								for (SoftwareVersionDO softwareVersionDo : softwareVersions) {
									VersionBindBO versionBindBO = new VersionBindBO();
									versionBindBO.setId(String.valueOf(softwareVersionDo.getId()));
									versionBindBO.setVersion(softwareVersionDo.getSoftwareVersion());
									computedBindFlag(
											softwareVersionDo.getId(),
											Long.parseLong(
													StrUtil.isBlank(hardwareVersionId) ? "-1" : hardwareVersionId),
											versionBindBO
									);
									softwareVersionList.add(versionBindBO);
								}
							});

					typeBO.setHardwareVersionList(hardwareVersionList);
					typeBO.setSoftwareVersionList(softwareVersionList);
					typeList.add(typeBO);
				}
				versionBO.setTypeList(typeList);
				result.add(versionBO);
			}
		}
		SoftwareVersionAllDTO softwareVersionAllDTO = new SoftwareVersionAllDTO();
		softwareVersionAllDTO
				.setHardwareVersionName(
						Optional.ofNullable(hardwareVersionMapper.selectById(hardwareVersionId))
								.map(HardwareVersionDO::getHardwareVersion).orElse("")
				);
		softwareVersionAllDTO.setHardwareList(result);
		return softwareVersionAllDTO;
	}

	private List<HardwareTypeDO> queryHardwareTypeList(Integer categoryId) {
		return hardwareTypeMapper.selectList(Wrappers.<HardwareTypeDO>lambdaQuery().eq(HardwareTypeDO::getCategoryId, categoryId));
	}

	@Override
	@DSTransactional
	public void batchDeleteSoftwareVersion(VersionBatchDeleteVO versionBatchDeleteVO) {
		List<String> versionIdList = versionBatchDeleteVO.getVersionIdList();
		versionIdList.stream().map(Long::parseLong).forEach(id -> {
			SoftwareVersionDO softwareVersionDO = softwareVersionMapper.selectById(id);
			BeanUtil.assertNotNull(softwareVersionDO);

			Integer middleFirmwareCount = middleFirmwareSoftwareMapper.selectCount(
					Wrappers.<MiddleFirmwareSoftwareDO>lambdaQuery().eq(MiddleFirmwareSoftwareDO::getSoftwareId, id)
			);
			if (middleFirmwareCount > 0) {
				log.warn("已经绑定的版本无法删除");
				throw new CustomException(ExceptionEnum.LINKED_VERSION_DELETE_ERROR);
			} else {
				List<MiddleSoftwareHardwareDO> middleList = middleSoftwareHardwareMapper.selectList(
						Wrappers.<MiddleSoftwareHardwareDO>lambdaQuery().eq(MiddleSoftwareHardwareDO::getSoftwareId, id)
				);

				for (MiddleSoftwareHardwareDO middleSoftwareHardwareDO : middleList) {
					middleSoftwareHardwareMapper.deleteById(middleSoftwareHardwareDO.getId());
				}
				ActionFlagUtil.singleAction(softwareVersionMapper.deleteById(softwareVersionDO.getId()));
			}
		});
	}

	@Override
	@DSTransactional
	public void batchDeleteHardwareVersion(VersionBatchDeleteVO versionBatchDeleteVO) {
		List<String> versionIdList = versionBatchDeleteVO.getVersionIdList();
		versionIdList.stream().map(Long::parseLong).forEach(id -> {
			HardwareVersionDO hardwareVersionDO = hardwareVersionMapper.selectById(id);
			BeanUtil.assertNotNull(hardwareVersionDO);

			Integer count = middleSoftwareHardwareMapper.selectCount(
					Wrappers.<MiddleSoftwareHardwareDO>lambdaQuery().eq(MiddleSoftwareHardwareDO::getHardwareId, id)
			);

			if (count > 0) {
				throw new CustomException(ExceptionEnum.LINKED_VERSION_DELETE_ERROR);
			} else {
				ActionFlagUtil.singleAction(hardwareVersionMapper.deleteById(hardwareVersionDO.getId()));
			}
		});
	}

	@Override
	public PageInfoDTO<HardwareVersionPageDTO> pageHardwareVersion(HardwareVersionPageVO hardwareVersionPageVO) {
		String hardwareVersion = hardwareVersionPageVO.getHardwareVersion();
		String hardwareTypeId = hardwareVersionPageVO.getHardwareTypeId();
		String hardwareModel = hardwareVersionPageVO.getModel();

		LambdaQueryWrapper<HardwareVersionDO> hardwareVersionQueryWrapper = new LambdaQueryWrapper<>();
		OperationUtil.of(hardwareVersion)
				.then(version -> hardwareVersionQueryWrapper.eq(HardwareVersionDO::getHardwareVersion, version));
		OperationUtil.of(hardwareTypeId)
				.then(typeId -> hardwareVersionQueryWrapper.eq(HardwareVersionDO::getTypeId, Long.parseLong(typeId)));
		OperationUtil.of(hardwareModel)
				.then(model -> hardwareVersionQueryWrapper.eq(HardwareVersionDO::getModel, model));
		hardwareVersionQueryWrapper.orderByDesc(HardwareVersionDO::getUpdateTime);

		PageHelper.startPage(hardwareVersionPageVO.getPageNum(), hardwareVersionPageVO.getPageSize());
		List<HardwareVersionDO> versionDOList = hardwareVersionMapper.selectList(hardwareVersionQueryWrapper);

		PageInfo<HardwareVersionDO> versionPageInfo = new PageInfo<>(versionDOList);

		List<HardwareVersionPageDTO> hardwarePageList = versionDOList.stream().map(hardwareVersionDO -> {
			List<MiddleSoftwareHardwareDO> middleSoftwareHardwareList = middleSoftwareHardwareMapper
					.selectList(
							Wrappers.<MiddleSoftwareHardwareDO>lambdaQuery()
									.eq(MiddleSoftwareHardwareDO::getHardwareId, hardwareVersionDO.getId())
					);
			Long typeId = hardwareVersionDO.getTypeId();
			HardwareTypeDO hardwareTypeDO = hardwareTypeMapper.selectById(typeId);
			BeanUtil.assertNotNull(hardwareTypeDO);

			HardwareVersionPageDTO hardwareVersionPageDTO = new HardwareVersionPageDTO();
			hardwareVersionPageDTO.setHardwareCategory(hardwareTypeDO.getCategoryName());
			hardwareVersionPageDTO.setHardwareType(hardwareTypeDO.getTypeName());
			hardwareVersionPageDTO.setHardwareId(String.valueOf(hardwareVersionDO.getId()));
			hardwareVersionPageDTO.setHardwareVersion(hardwareVersionDO.getHardwareVersion());
			hardwareVersionPageDTO.setRemarks(hardwareVersionDO.getRemarks());
			hardwareVersionPageDTO.setModel(hardwareVersionDO.getModel());
			hardwareVersionPageDTO.setSoftwareVersionList(
					middleSoftwareHardwareList.stream().map(MiddleSoftwareHardwareDO::getSoftwareId).map(softwareId -> {
						SoftwareVersionDO softwareVersionDO = softwareVersionMapper.selectById(softwareId);
						BeanUtil.assertNotNull(softwareVersionDO);
						return softwareVersionDO.getSoftwareVersion();
					}).collect(Collectors.toList()));
			return hardwareVersionPageDTO;
		}).collect(Collectors.toList());

		PageInfoDTO<HardwareVersionPageDTO> pageInfoDTO = new PageInfoDTO<>();
		pageInfoDTO.setTotalPages(versionPageInfo.getPages());
		pageInfoDTO.setTotalCount(versionPageInfo.getTotal());
		pageInfoDTO.setData(hardwarePageList);

		return pageInfoDTO;
	}

	@Override
	public PageInfoDTO<SoftwareVersionPageDTO> pageSoftwareVersion(SoftwareVersionPageVO softwareVersionPageVO) {
		String softwareVersion = softwareVersionPageVO.getSoftwareVersion();
		String hardwareTypeId = softwareVersionPageVO.getHardwareTypeId();
		String softwareModel = softwareVersionPageVO.getModel();

		LambdaQueryWrapper<SoftwareVersionDO> softwareVersionQueryWrapper = new LambdaQueryWrapper<>();
		OperationUtil.of(softwareVersion)
				.then(version -> softwareVersionQueryWrapper.eq(SoftwareVersionDO::getSoftwareVersion, version));
		OperationUtil.of(hardwareTypeId)
				.then(typeId -> softwareVersionQueryWrapper.eq(SoftwareVersionDO::getTypeId, typeId));
		OperationUtil.of(softwareModel)
				.then(model -> softwareVersionQueryWrapper.eq(SoftwareVersionDO::getModel, model));
		softwareVersionQueryWrapper.orderByDesc(SoftwareVersionDO::getUpdateTime);

		PageHelper.startPage(softwareVersionPageVO.getPageNum(), softwareVersionPageVO.getPageSize());
		List<SoftwareVersionDO> softwareVersionList = softwareVersionMapper.selectList(softwareVersionQueryWrapper);
		PageInfo<SoftwareVersionDO> pageInfo = new PageInfo<>(softwareVersionList);

		PageInfoDTO<SoftwareVersionPageDTO> pageInfoDTO = new PageInfoDTO<>();
		pageInfoDTO.setTotalPages(pageInfo.getPages());
		pageInfoDTO.setTotalCount(pageInfo.getTotal());
		pageInfoDTO.setData(softwareVersionList.stream().map(softwareVersionDO -> {
			Long softwareVersionId = softwareVersionDO.getId();
			List<MiddleSoftwareHardwareDO> middleSoftwareHardwareDOList = middleSoftwareHardwareMapper
					.selectList(
							Wrappers.<MiddleSoftwareHardwareDO>lambdaQuery().eq(MiddleSoftwareHardwareDO::getSoftwareId, softwareVersionId)
					);

			List<HardwareVersionDO> hardwareVersionList = middleSoftwareHardwareDOList.stream()
					.map(middleSoftwareHardwareDO -> {
						HardwareVersionDO hardwareVersionDO = hardwareVersionMapper
								.selectById(
										middleSoftwareHardwareDO.getHardwareId());
						BeanUtil.assertNotNull(hardwareVersionDO);
						return hardwareVersionDO;
					}).collect(Collectors.toList());

			if (hardwareVersionList.stream().map(HardwareVersionDO::getTypeId).distinct().count() > 1) {
				log.warn("硬件类型不唯一");
				throw new CustomException(ExceptionEnum.DATA_ASSERT_EXCEPTION);
			}

			SoftwareVersionPageDTO softwareVersionPageDTO = new SoftwareVersionPageDTO();
			softwareVersionPageDTO.setHardwareCategory("");
			softwareVersionPageDTO.setHardwareType("");
			softwareVersionPageDTO.setSoftwareId(String.valueOf(softwareVersionId));
			if (hardwareVersionList.size() > 0) {
				HardwareTypeDO hardwareTypeDO = hardwareTypeMapper.selectById(hardwareVersionList.get(0).getTypeId());
				BeanUtil.assertNotNull(hardwareTypeDO);
				softwareVersionPageDTO.setHardwareCategory(hardwareTypeDO.getCategoryName());
				softwareVersionPageDTO.setHardwareType(hardwareTypeDO.getTypeName());
			}

			softwareVersionPageDTO.setSoftwareVersion(softwareVersionDO.getSoftwareVersion());
			softwareVersionPageDTO.setHardwareVersionList(hardwareVersionList.stream()
					.map(HardwareVersionDO::getHardwareVersion)
					.collect(Collectors.toList()));
			softwareVersionPageDTO.setRemarks(softwareVersionDO.getRemarks());
			softwareVersionPageDTO.setModel(softwareVersionDO.getModel());
			return softwareVersionPageDTO;
		}).collect(Collectors.toList()));

		return pageInfoDTO;
	}

	@Override
	public List<HardwareCategoryTypeMapDTO> mapHardwareCategoryType() {
		return hardwareCategoryMapper.selectList(Wrappers.lambdaQuery()).stream().map(hardwareCategoryDO -> {
			HardwareCategoryTypeMapDTO hardwareCategoryTypeMapDTO = new HardwareCategoryTypeMapDTO();
			hardwareCategoryTypeMapDTO.setHardwareCategoryName(hardwareCategoryDO.getCategoryName());

			List<HardwareTypeDO> hardwareTypeList = hardwareTypeMapper.selectList(
					Wrappers.<HardwareTypeDO>lambdaQuery().eq(HardwareTypeDO::getCategoryId, hardwareCategoryDO.getId())
			);
			hardwareCategoryTypeMapDTO.setHardwareTypeList(hardwareTypeList.stream().map(hardwareTypeDO -> {
				HardwareCategoryTypeMapDTO.HardwareType hardwareType = new HardwareCategoryTypeMapDTO.HardwareType();
				hardwareType.setHardwareTypeId(String.valueOf(hardwareTypeDO.getId()));
				hardwareType.setHardwareTypeName(hardwareTypeDO.getTypeName());
				return hardwareType;
			}).collect(Collectors.toList()));
			return hardwareCategoryTypeMapDTO;
		}).collect(Collectors.toList());
	}

	@Override
	public void softwareRemarks(RemarksVo remarksVo) {
		SoftwareVersionDO softwareVersionDO = softwareVersionMapper.selectById(remarksVo.getId());
		BeanUtil.assertNotNull(softwareVersionDO);
		softwareVersionDO.setRemarks(Optional.ofNullable(remarksVo.getRemarks()).orElse(""));
		softwareVersionMapper.updateById(softwareVersionDO);
	}

	@Override
	public void hardwareRemarks(RemarksVo remarksVo) {
		HardwareVersionDO hardwareVersionDO = hardwareVersionMapper.selectById(remarksVo.getId());
		BeanUtil.assertNotNull(hardwareVersionDO);
		hardwareVersionDO.setRemarks(Optional.ofNullable(remarksVo.getRemarks()).orElse(""));
		hardwareVersionMapper.updateById(hardwareVersionDO);
	}
}
