package com.weihengtech.service.firmware.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.consts.CommonConstants;
import com.weihengtech.dao.firmware.HardwareCategoryMapper;
import com.weihengtech.dao.firmware.HardwareTypeMapper;
import com.weihengtech.pojo.dos.firmware.HardwareCategoryDO;
import com.weihengtech.pojo.dos.firmware.HardwareTypeDO;
import com.weihengtech.pojo.dtos.firmware.HardwareTypeDTO;
import com.weihengtech.pojo.dtos.firmware.HardwareTypeLinkCategoryDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.firmware.HardwareTypeInsertVO;
import com.weihengtech.pojo.vos.firmware.HardwareTypeQueryVO;
import com.weihengtech.pojo.vos.firmware.HardwareTypeUpdateVO;
import com.weihengtech.service.firmware.HardwareTypeService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.SnowFlakeUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class HardwareTypeServiceImpl extends ServiceImpl<HardwareTypeMapper, HardwareTypeDO>
		implements
		HardwareTypeService {

	@Resource
	private HardwareTypeMapper hardwareTypeMapper;

	@Resource
	private HardwareCategoryMapper hardwareCategoryMapper;

	@Resource
	private SnowFlakeUtil snowFlakeUtil;

	@Override
	@DSTransactional
	public void insertHardwareType(HardwareTypeInsertVO hardwareTypeInsertVO) {
		HardwareTypeDO hardwareTypeDO = new HardwareTypeDO();
		hardwareTypeDO.setId(snowFlakeUtil.generateId());
		hardwareTypeDO.setTypeName(hardwareTypeInsertVO.getTypeName());
		HardwareCategoryDO hardwareCategoryDO = hardwareCategoryMapper.selectById(hardwareTypeInsertVO.getCategoryId());
		BeanUtil.assertNotNull(hardwareCategoryDO);
		hardwareTypeDO.setCategoryName(hardwareCategoryDO.getCategoryName());
		hardwareTypeDO.setCategoryId(hardwareCategoryDO.getId());
		hardwareTypeDO.setOperatorId(Long.parseLong(UserInfoUtil.currentUserId()));
		hardwareTypeDO.setCreateTime(LocalDateTime.now());
		hardwareTypeDO.setUpdateTime(LocalDateTime.now());
		ActionFlagUtil.singleAction(hardwareTypeMapper.insert(hardwareTypeDO));
	}

	@Override
	@DSTransactional
	public void updateHardwareType(HardwareTypeUpdateVO hardwareTypeUpdateVO) {
		String id = hardwareTypeUpdateVO.getId();
		HardwareTypeDO hardwareTypeDO = hardwareTypeMapper.selectById(id);
		BeanUtil.assertNotNull(hardwareTypeDO);
		if (CommonConstants.TAG_DELETE.equals(hardwareTypeUpdateVO.getDeleteFlag())) {
			hardwareTypeMapper.deleteById(hardwareTypeDO.getId());
		} else {
			Optional.ofNullable(hardwareTypeUpdateVO.getTypeName()).ifPresent(hardwareTypeDO::setTypeName);
			Optional.ofNullable(hardwareTypeUpdateVO.getCategoryId()).ifPresent(categoryId -> {
				HardwareCategoryDO hardwareCategoryDO = hardwareCategoryMapper.selectById(categoryId);
				BeanUtil.assertNotNull(hardwareCategoryDO);
				hardwareTypeDO.setCategoryId(hardwareCategoryDO.getId());
				hardwareTypeDO.setCategoryName(hardwareCategoryDO.getCategoryName());
			});
			hardwareTypeDO.setUpdateTime(LocalDateTime.now());
			hardwareTypeDO.setOperatorId(Long.parseLong(UserInfoUtil.currentUserId()));
			ActionFlagUtil.singleAction(hardwareTypeMapper.updateById(hardwareTypeDO));
		}
	}

	@Override
	public PageInfoDTO<HardwareTypeDTO> pageHardwareType(HardwareTypeQueryVO hardwareTypeQueryVO) {

		PageHelper.startPage(hardwareTypeQueryVO.getPageNum(), hardwareTypeQueryVO.getPageSize());
		List<HardwareTypeDO> hardwareTypeDOList = hardwareTypeMapper.selectList(new QueryWrapper<>());

		PageInfo<HardwareTypeDO> pageInfo = new PageInfo<>(hardwareTypeDOList);
		PageInfoDTO<HardwareTypeDTO> pageInfoDTO = new PageInfoDTO<>();
		pageInfoDTO.setTotalPages(pageInfo.getPages());
		pageInfoDTO.setTotalCount(pageInfo.getTotal());

		ArrayList<HardwareTypeDTO> pageData = Lists.newArrayList();
		for (HardwareTypeDO hardwareTypeDO : hardwareTypeDOList) {
			pageData.add(HardwareTypeDTO.builder().typeName(hardwareTypeDO.getTypeName())
					.id(String.valueOf(hardwareTypeDO.getId()))
					.categoryId(hardwareTypeDO.getCategoryId()).categoryName(hardwareTypeDO.getCategoryName()).build());
		}

		pageInfoDTO.setData(pageData);
		return pageInfoDTO;
	}

	@Override
	public List<HardwareTypeLinkCategoryDTO> allWithCategory() {
		List<HardwareCategoryDO> hardwareCategoryDOList = hardwareCategoryMapper.selectList(Wrappers.emptyWrapper());
		LinkedList<HardwareTypeLinkCategoryDTO> dtoList = new LinkedList<>();
		for (HardwareCategoryDO hardwareCategoryDO : hardwareCategoryDOList) {
			Integer categoryId = hardwareCategoryDO.getId();
			HardwareTypeLinkCategoryDTO hardwareTypeLinkCategoryDTO = new HardwareTypeLinkCategoryDTO();
			hardwareTypeLinkCategoryDTO.setCategoryId(String.valueOf(categoryId));
			hardwareTypeLinkCategoryDTO.setCategoryName(hardwareCategoryDO.getCategoryName());

			List<HardwareTypeDO> typeList = hardwareTypeMapper
					.selectList(Wrappers.<HardwareTypeDO>lambdaQuery().eq(HardwareTypeDO::getCategoryId, categoryId));
			hardwareTypeLinkCategoryDTO.setTypeList(typeList.stream().map(type -> {
				HardwareTypeLinkCategoryDTO.TypeDTO typeDTO = new HardwareTypeLinkCategoryDTO.TypeDTO();
				typeDTO.setTypeId(String.valueOf(type.getId()));
				typeDTO.setTypeName(type.getTypeName());
				return typeDTO;
			}).collect(Collectors.toList()));
			dtoList.add(hardwareTypeLinkCategoryDTO);
		}
		return dtoList;
	}
}
