package com.weihengtech.service.firmware.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.weihengtech.auth.dto.ResourceConfigDTO;
import com.weihengtech.auth.dto.ResourceResDTO;
import com.weihengtech.auth.dto.ResourceSearchReqDTO;
import com.weihengtech.auth.model.dto.UserResDTO;
import com.weihengtech.auth.rpc.AuthCenterClient;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.RoleConstants;
import com.weihengtech.dao.firmware.FirmwareBatchUpgradeTaskMapper;
import com.weihengtech.dao.other.SqlMapper;
import com.weihengtech.enums.firmware.UpgradeDeviceStatusEnum;
import com.weihengtech.enums.firmware.UpgradeModeEnum;
import com.weihengtech.enums.firmware.UpgradeTaskStatusEnum;
import com.weihengtech.enums.list.ListCategoryEnum;
import com.weihengtech.enums.list.ListTypeEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.firmware.DeviceUpgradeDetailDO;
import com.weihengtech.pojo.dos.firmware.FirmwareBatchUpgradeTaskDO;
import com.weihengtech.pojo.dos.firmware.FirmwareUploadDO;
import com.weihengtech.pojo.dos.list.ListItemDO;
import com.weihengtech.pojo.dtos.device.DeviceListPageDTO;
import com.weihengtech.pojo.dtos.firmware.FirmwareBatchUpgradeTaskDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.firmware.FirmwareAllWhiteListVo;
import com.weihengtech.pojo.vos.firmware.FirmwareBatchUpgradeDevicePageVo;
import com.weihengtech.pojo.vos.firmware.FirmwareBatchUpgradeTaskPageVo;
import com.weihengtech.pojo.vos.firmware.FirmwareBatchUpgradeTaskVo;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.firmware.DeviceUpgradeDetailService;
import com.weihengtech.service.firmware.FirmwareBatchUpgradeTaskService;
import com.weihengtech.service.firmware.FirmwareUploadService;
import com.weihengtech.service.firmware.MqService;
import com.weihengtech.service.list.ListItemService;
import com.weihengtech.service.systeminfo.SystemInfoService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.SnowFlakeUtil;
import com.weihengtech.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.weihengtech.consts.RoleConstants.*;
import static com.weihengtech.consts.RoleConstants.ROLE_DEALER;
import static com.weihengtech.consts.RoleConstants.ROLE_RETAILER;

/**
 * @author: jiahao.jin
 * @create: 2025-07-14 16:20
 * @description:
 */
@Slf4j
@Service
public class FirmwareBatchUpgradeTaskServiceImpl extends ServiceImpl<FirmwareBatchUpgradeTaskMapper, FirmwareBatchUpgradeTaskDO>
		implements
        FirmwareBatchUpgradeTaskService {

	/**
	 * 反向过滤（不包含）的搜索条件
	 */
	private static final String EXCLUDE_PARAM = "0";

	@Value("${custom.datacenter.name}")
	private String datacenter;

	@Resource
	private AuthCenterResClient authCenterResClient;

	@Resource
	private FirmwareUploadService firmwareUploadService;

	@Resource
	private DeviceListService deviceListService;

	@Resource
	private FirmwareBatchUpgradeTaskMapper firmwareBatchUpgradeTaskMapper;

	@Resource
	private DeviceUpgradeDetailService deviceUpgradeDetailService;

	@Resource
	private SnowFlakeUtil snowFlakeUtil;

	@Resource
	private MqService mqService;

	@Resource
	private SystemInfoService systemInfoService;

	@Resource
	private SqlMapper sqlMapper;

	@Resource
	private AuthCenterClient authCenterClient;

	@Resource
	private ListItemService listItemService;

	@Override
	@DSTransactional
	public void addBatchUpgradeTask(FirmwareBatchUpgradeTaskVo firmwareBatchUpgradeTaskVo) {

		FirmwareBatchUpgradeTaskDO firmwareBatchUpgradeTaskDO = new FirmwareBatchUpgradeTaskDO();
		long id = snowFlakeUtil.generateId();
		firmwareBatchUpgradeTaskDO.setId(id);

		List<ResourceConfigDTO> resourceConfigDTOS = authCenterResClient.categoryTypeTree();

		boolean found = resourceConfigDTOS.stream()
				.filter(dto -> dto.getCode().equals(firmwareBatchUpgradeTaskVo.getResourceSeriesCode()))
				.flatMap(dto -> dto.getTypes().stream())
				.anyMatch(typeDto -> typeDto.getCode().equals(firmwareBatchUpgradeTaskVo.getResourceType()));

		if (!found) {
			log.warn("设备系列或者类型不存在");
			throw new CustomException(ExceptionEnum.DATA_ASSERT_EXCEPTION);
		}
		firmwareBatchUpgradeTaskDO.setResourceSeriesCode(firmwareBatchUpgradeTaskVo.getResourceSeriesCode());
		firmwareBatchUpgradeTaskDO.setResourceType(firmwareBatchUpgradeTaskVo.getResourceType());

		FirmwareUploadDO firmwareUploadDO = firmwareUploadService.selectBySerialId(firmwareBatchUpgradeTaskVo.getFirmwareId());

		if (firmwareUploadDO == null) {
			log.warn("所选固件包不存在");
			throw new CustomException(ExceptionEnum.DATA_ASSERT_EXCEPTION);
		}
		firmwareBatchUpgradeTaskDO.setFirmwareId(firmwareUploadDO.getId());
		firmwareBatchUpgradeTaskDO.setFirmwareName(firmwareUploadDO.getFirmwareName());
		firmwareBatchUpgradeTaskDO.setFirmwareType(firmwareUploadDO.getType());

		List<String> deviceIdList = firmwareBatchUpgradeTaskVo.getDeviceIdList();
		List<DeviceListDO> deviceListDOList = deviceListService.list(Wrappers.<DeviceListDO>lambdaQuery()
				.in(DeviceListDO::getId, deviceIdList));
		if (deviceListDOList.size() != deviceIdList.size()) {
			log.warn("所选设备不存在");
			throw new CustomException(ExceptionEnum.DATA_ASSERT_EXCEPTION);
		}
		firmwareBatchUpgradeTaskDO.setTargetDeviceCount(deviceListDOList.size());
		firmwareBatchUpgradeTaskDO.setUpgradingCount(0);
		firmwareBatchUpgradeTaskDO.setSuccessCount(0);
		firmwareBatchUpgradeTaskDO.setFailureCount(0);

		String upgradeMode = firmwareBatchUpgradeTaskVo.getUpgradeMode();

		String immediateMode = UpgradeModeEnum.IMEDIATE_EXECUTION.getModeName();
		String scheduledMode = UpgradeModeEnum.SCHEDULED_EXECUTION.getModeName();

		if (!upgradeMode.equals(immediateMode) && !upgradeMode.equals(scheduledMode)) {
			log.warn("升级方式不存在");
			throw new CustomException(ExceptionEnum.DATA_ASSERT_EXCEPTION);
		}

		firmwareBatchUpgradeTaskDO.setUpgradeMode(upgradeMode);
		firmwareBatchUpgradeTaskDO.setUpgradeStartTime(System.currentTimeMillis());

		if (upgradeMode.equals(scheduledMode)) {
			Long upgradeStartTime = firmwareBatchUpgradeTaskVo.getUpgradeStartTime();
			if (upgradeStartTime == null) {
				log.warn("定时升级时间不能为空");
				throw new CustomException(ExceptionEnum.DATA_ASSERT_EXCEPTION);
			}
			if (upgradeStartTime < System.currentTimeMillis()) {
				log.warn("定时升级时间不能小于当前时间");
				throw new CustomException(ExceptionEnum.DATA_ASSERT_EXCEPTION);
			}
			firmwareBatchUpgradeTaskDO.setUpgradeStartTime(upgradeStartTime);
		}

		firmwareBatchUpgradeTaskDO.setTaskName(firmwareBatchUpgradeTaskVo.getTaskName());
		firmwareBatchUpgradeTaskDO.setRemarks(firmwareBatchUpgradeTaskVo.getRemarks());
		firmwareBatchUpgradeTaskDO.setCreatorEmail(UserInfoUtil.currentUserEmail());
		LocalDateTime now = LocalDateTime.now();
		firmwareBatchUpgradeTaskDO.setCreateTime(now);
		firmwareBatchUpgradeTaskDO.setUpdateTime(now);
		firmwareBatchUpgradeTaskDO.setStatus(firmwareBatchUpgradeTaskVo.getUpgradeMode().equals(UpgradeModeEnum.IMEDIATE_EXECUTION.getModeName()) ? UpgradeTaskStatusEnum.RUNNING.getStatus() : UpgradeDeviceStatusEnum.WAITING.getStatus());
		firmwareBatchUpgradeTaskDO.setProgress(0);

		ActionFlagUtil.singleAction(firmwareBatchUpgradeTaskMapper.insert(firmwareBatchUpgradeTaskDO));

		// 批量创建设备升级详情
		List<DeviceUpgradeDetailDO> upgradeDetailDOArrayList = new ArrayList<>();
		deviceListDOList.forEach(deviceListDO -> {
			DeviceUpgradeDetailDO deviceUpgradeDetailDO = new DeviceUpgradeDetailDO();
			long deviceUpgradeDetailId = snowFlakeUtil.generateId();
			deviceUpgradeDetailDO.setId(deviceUpgradeDetailId);
			deviceUpgradeDetailDO.setBatchTaskId(id);
			deviceUpgradeDetailDO.setDeviceId(deviceListDO.getId());
			deviceUpgradeDetailDO.setDeviceSn(deviceListDO.getDeviceSn());
			deviceUpgradeDetailDO.setFirmwareType(firmwareUploadDO.getType());
			deviceUpgradeDetailDO.setUpgradeStatus(UpgradeDeviceStatusEnum.WAITING.getStatus());
			deviceUpgradeDetailDO.setCreateTime(now);
			deviceUpgradeDetailDO.setUpdateTime(now);
			upgradeDetailDOArrayList.add(deviceUpgradeDetailDO);
		});

		ActionFlagUtil.assertTrue(deviceUpgradeDetailService.saveBatch(upgradeDetailDOArrayList));

		if (firmwareBatchUpgradeTaskVo.getUpgradeMode().equals(UpgradeModeEnum.IMEDIATE_EXECUTION.getModeName())) {
			// 立即升级
			// 创建一个5秒后执行的定时任务
			log.info("立即升级");
			mqService.addBatchUpgradeTask(id, System.currentTimeMillis() + 10000);
		} else if (firmwareBatchUpgradeTaskVo.getUpgradeMode().equals(UpgradeModeEnum.SCHEDULED_EXECUTION.getModeName())) {
			// 定时升级
			// 创建一个指定时间的定时任务
			log.info("定时升级");
			mqService.addBatchUpgradeTask(id, firmwareBatchUpgradeTaskVo.getUpgradeStartTime());
		}
	}

	@Override
	public PageInfoDTO<FirmwareBatchUpgradeTaskDTO> pageBatchUpgradeTask(FirmwareBatchUpgradeTaskPageVo firmwareBatchUpgradeTaskPageVo) {

		// 开启分页
		PageHelper.startPage(firmwareBatchUpgradeTaskPageVo.getPageNum(), firmwareBatchUpgradeTaskPageVo.getPageSize());

		// 构建查询条件
		LambdaQueryWrapper<FirmwareBatchUpgradeTaskDO> queryWrapper = Wrappers.<FirmwareBatchUpgradeTaskDO>lambdaQuery()
				// 任务名称 - 模糊查询
				.like(StringUtils.isNotBlank(firmwareBatchUpgradeTaskPageVo.getTaskName()),
						FirmwareBatchUpgradeTaskDO::getTaskName, firmwareBatchUpgradeTaskPageVo.getTaskName())
				// 设备系列编号 - 精确匹配
				.eq(StringUtils.isNotBlank(firmwareBatchUpgradeTaskPageVo.getResourceSeriesCode()),
						FirmwareBatchUpgradeTaskDO::getResourceSeriesCode, firmwareBatchUpgradeTaskPageVo.getResourceSeriesCode())
				// 设备类型 - 精确匹配
				.in(CollUtil.isNotEmpty(firmwareBatchUpgradeTaskPageVo.getResourceType()),
						FirmwareBatchUpgradeTaskDO::getResourceType, firmwareBatchUpgradeTaskPageVo.getResourceType())
				// 固件类型 - 精确匹配
				.eq(StringUtils.isNotBlank(firmwareBatchUpgradeTaskPageVo.getFirmwareType()),
						FirmwareBatchUpgradeTaskDO::getFirmwareType, firmwareBatchUpgradeTaskPageVo.getFirmwareType())
				// 固件包名称 - 模糊查询
				.like(StringUtils.isNotBlank(firmwareBatchUpgradeTaskPageVo.getFirmwareName()),
						FirmwareBatchUpgradeTaskDO::getFirmwareName, firmwareBatchUpgradeTaskPageVo.getFirmwareName())
				// 升级方式 - 精确匹配
				.eq(StringUtils.isNotBlank(firmwareBatchUpgradeTaskPageVo.getUpgradeMode()),
						FirmwareBatchUpgradeTaskDO::getUpgradeMode, firmwareBatchUpgradeTaskPageVo.getUpgradeMode())
				// 任务状态 - 精确匹配
				.eq(StringUtils.isNotBlank(firmwareBatchUpgradeTaskPageVo.getStatus()),
						FirmwareBatchUpgradeTaskDO::getStatus, firmwareBatchUpgradeTaskPageVo.getStatus())
				// 创建时间范围查询
				.ge(firmwareBatchUpgradeTaskPageVo.getCreateTimeStart() != null,
						FirmwareBatchUpgradeTaskDO::getCreateTime,
						firmwareBatchUpgradeTaskPageVo.getCreateTimeStart() != null ?
								new Date(firmwareBatchUpgradeTaskPageVo.getCreateTimeStart()) : null)
				.le(firmwareBatchUpgradeTaskPageVo.getCreateTimeEnd() != null,
						FirmwareBatchUpgradeTaskDO::getCreateTime,
						firmwareBatchUpgradeTaskPageVo.getCreateTimeEnd() != null ?
								new Date(firmwareBatchUpgradeTaskPageVo.getCreateTimeEnd()) : null)
				// 升级开始时间范围查询
				.ge(firmwareBatchUpgradeTaskPageVo.getUpgradeStartTime() != null,
						FirmwareBatchUpgradeTaskDO::getUpgradeStartTime,
						firmwareBatchUpgradeTaskPageVo.getUpgradeStartTime())
				.le(firmwareBatchUpgradeTaskPageVo.getUpgradeEndTime() != null,
						FirmwareBatchUpgradeTaskDO::getUpgradeStartTime,
						firmwareBatchUpgradeTaskPageVo.getUpgradeEndTime())
				// 按创建时间倒序排列，保证分页结果稳定
				.orderByDesc(FirmwareBatchUpgradeTaskDO::getCreateTime);

		// 执行查询
		List<FirmwareBatchUpgradeTaskDO> firmwareBatchUpgradeTaskDOList = firmwareBatchUpgradeTaskMapper.selectList(queryWrapper);

		// 构建分页信息
		PageInfo<FirmwareBatchUpgradeTaskDO> pageInfo = new PageInfo<>(firmwareBatchUpgradeTaskDOList);

		// 构建返回结果
		PageInfoDTO<FirmwareBatchUpgradeTaskDTO> pageInfoDTO = new PageInfoDTO<>();
		pageInfoDTO.setTotalPages(pageInfo.getPages());
		pageInfoDTO.setTotalCount(pageInfo.getTotal());

		// 对象转换 - 使用Stream映射
		List<FirmwareBatchUpgradeTaskDTO> dtoList = firmwareBatchUpgradeTaskDOList.stream()
				.map(this::convertToDTO)
				.collect(Collectors.toList());

		pageInfoDTO.setData(dtoList);
		return pageInfoDTO;
	}

	/**
	 * DO转DTO的私有方法，提取出来便于维护
	 */
	private FirmwareBatchUpgradeTaskDTO convertToDTO(FirmwareBatchUpgradeTaskDO firmwareBatchUpgradeTaskDO) {
		FirmwareBatchUpgradeTaskDTO dto = new FirmwareBatchUpgradeTaskDTO();

		// 可以使用 BeanUtils.copyProperties 进行属性拷贝，减少手动设置
		BeanUtils.copyProperties(firmwareBatchUpgradeTaskDO, dto);
		dto.setUpgradeTime(firmwareBatchUpgradeTaskDO.getUpgradeStartTime());
		dto.setId(String.valueOf(firmwareBatchUpgradeTaskDO.getId()));
		dto.setFirmwareId(String.valueOf(firmwareBatchUpgradeTaskDO.getFirmwareId()));

		return dto;
	}


	@Override
	public PageInfoDTO<DeviceListPageDTO> pageBatchUpgradeDevice(FirmwareBatchUpgradeDevicePageVo param) {
		// 通过资源平台查询当前账号绑定设备
		List<ResourceResDTO> resourceList = getResourceList(param);
		if (CollUtil.isEmpty(resourceList)) {
			return new PageInfoDTO<>();
		}


		List<ListItemDO> list = listItemService.list();

		// 查询黑名单设备SN
		List<String> deviceSnBlackList = list.stream().filter(i -> i.getCategoryId().equals(ListCategoryEnum.DEVICE_SN.getId())
				&& i.getListType().equals(ListTypeEnum.BLACK_LIST.getCode()))
				.map(ListItemDO::getContent)
				.collect(Collectors.toList());

		// 查询白名单固件版本
		FirmwareAllWhiteListVo firmwareAllWhiteListVo = FirmwareAllWhiteListVo.builder()
				.emsSoftwareWhiteList(list.stream().filter(i -> i.getCategoryId().equals(ListCategoryEnum.EMS_VERSION.getId())
								&& i.getListType().equals(ListTypeEnum.WHITE_LIST.getCode()))
						.map(ListItemDO::getContent)
						.collect(Collectors.toList()))
				.emsSubWhiteList(list.stream().filter(i -> i.getCategoryId().equals(ListCategoryEnum.EMS_SUBVERSION.getId())
								&& i.getListType().equals(ListTypeEnum.WHITE_LIST.getCode()))
						.map(ListItemDO::getContent)
						.collect(Collectors.toList()))
				.dsp1SoftwareWhiteList(list.stream().filter(i -> i.getCategoryId().equals(ListCategoryEnum.DSP1_VERSION.getId())
								&& i.getListType().equals(ListTypeEnum.WHITE_LIST.getCode()))
						.map(ListItemDO::getContent)
						.collect(Collectors.toList()))
				.dsp1SubWhiteList(list.stream().filter(i -> i.getCategoryId().equals(ListCategoryEnum.DSP1_SUBVERSION.getId())
								&& i.getListType().equals(ListTypeEnum.WHITE_LIST.getCode()))
						.map(ListItemDO::getContent)
						.collect(Collectors.toList()))
				.dsp2SoftwareWhiteList(list.stream().filter(i -> i.getCategoryId().equals(ListCategoryEnum.DSP2_VERSION.getId())
								&& i.getListType().equals(ListTypeEnum.WHITE_LIST.getCode()))
						.map(ListItemDO::getContent)
						.collect(Collectors.toList()))
				.dsp2SubWhiteList(list.stream().filter(i -> i.getCategoryId().equals(ListCategoryEnum.DSP2_SUBVERSION.getId())
								&& i.getListType().equals(ListTypeEnum.WHITE_LIST.getCode()))
						.map(ListItemDO::getContent)
						.collect(Collectors.toList()))
				.arcDspSoftwareWhiteList(list.stream().filter(i -> i.getCategoryId().equals(ListCategoryEnum.ARCDSP_VERSION.getId())
								&& i.getListType().equals(ListTypeEnum.WHITE_LIST.getCode()))
						.map(ListItemDO::getContent)
						.collect(Collectors.toList()))
				.arcDspSubWhiteList(list.stream().filter(i -> i.getCategoryId().equals(ListCategoryEnum.ARCDSP_SUBVERSION.getId())
								&& i.getListType().equals(ListTypeEnum.WHITE_LIST.getCode()))
						.map(ListItemDO::getContent)
						.collect(Collectors.toList()))
				.bmsSoftwareWhiteList(list.stream().filter(i -> i.getCategoryId().equals(ListCategoryEnum.BMS_VERSION.getId())
								&& i.getListType().equals(ListTypeEnum.WHITE_LIST.getCode()))
						.map(ListItemDO::getContent)
						.collect(Collectors.toList()))
				.build();

		PageInfoDTO<DeviceListPageDTO> pageInfoDTO = new PageInfoDTO<>();
		// 构建设备基础数据
		Map<String, ResourceResDTO> resourceMap = resourceList.stream()
				// 过滤黑名单的设备SN
				.filter(i -> deviceSnBlackList.stream().noneMatch(j -> j.equals(i.getCode())))
				.collect(Collectors.toMap(ResourceResDTO::getId, Function.identity()));
		// 分页查询
		param.setDeviceIdList(resourceMap.keySet());
		param.setFirmwareAllWhiteListVo(firmwareAllWhiteListVo);
		PageHelper.startPage(param.getPageNum(), param.getPageSize());
		List<DeviceListPageDTO> resList = sqlMapper.queryFirmwareBatchUpgradeDeviceList(param);
		// 构造分页信息
		PageInfo<DeviceListPageDTO> pageInfo = new PageInfo<>(resList);
		pageInfoDTO.setTotalPages(pageInfo.getPages());
		pageInfoDTO.setTotalCount(pageInfo.getTotal());
		// 从平台读取账号数据
		List<ResourceResDTO> needTransList = resList.stream()
				.map(i -> resourceMap.get(i.getId()))
				.collect(Collectors.toList());
		List<UserResDTO> userList = getAgentUserList(needTransList);
		Map<String, String> userMap = userList.stream()
				.filter(i -> StrUtil.isNotBlank(i.getNickName()))
				.collect(Collectors.toMap(UserResDTO::getId, UserResDTO::getNickName));
		// 从平台读取系列数据
		Map<Integer, String> seriesMap = getSeriesCodeMap();
		resList.forEach(dto -> {
			ResourceResDTO resource = resourceMap.get(dto.getId());
			Map<String, String> userInRoleCategories = resource.getUserInRoleCategories();
			dto.setId(resource.getId());
			dto.setDeviceSn(resource.getCode());
			dto.setDeviceName(resource.getName());
			dto.setAlias(resource.getRemark());
			dto.setRemark(resource.getRemark());
			dto.setCountry(resource.getDeliverAreaName());
			dto.setResourceType(resource.getTypeName());
			dto.setResourceSeries(resource.getCategoryName());
			dto.setResourceSeriesCode(seriesMap.get(resource.getCategory()));
			Optional.ofNullable(userInRoleCategories.get(ROLE_SALE)).ifPresent(
					k -> dto.setSaleName(userMap.getOrDefault(k, "--"))
			);
			Optional.ofNullable(userInRoleCategories.get(ROLE_AGENT)).ifPresent(
					k -> dto.setAgentName(userMap.getOrDefault(k, "--"))
			);
			Optional.ofNullable(userInRoleCategories.get(ROLE_DEALER)).ifPresent(
					k -> dto.setDealerName(userMap.getOrDefault(k, "--"))
			);
			Optional.ofNullable(userInRoleCategories.get(ROLE_RETAILER)).ifPresent(
					k -> dto.setRetailerName(userMap.getOrDefault(k, "--"))
			);
			dto.setSetupTime((null == dto.getSetupTime() || 0 == dto.getSetupTime()) ? -1 : dto.getSetupTime());
			dto.setUpdateTime(TimeUtil.localDateTimeToSerialString(dto.getUpdTime()));
			if (resource.getExpireAt() != null && resource.getExpireAt() != 0) {
				long expiredTime = DateUtil.between(new Date(), new Date(resource.getExpireAt()), DateUnit.SECOND);
				dto.setCountDownTime(expiredTime);
				dto.setIsNeedCountdown(Boolean.TRUE);
			}
			if (2 != UserInfoUtil.currentUser().getSource()) {
				dto.buildDefaultVersion();
			}
		});
		pageInfoDTO.setData(resList);
		return pageInfoDTO;
	}

	/** 查询账号平台资源数据 */
	private List<ResourceResDTO> getResourceList(FirmwareBatchUpgradeDevicePageVo param) {
		String curRoleId = UserInfoUtil.currentUserRoleCategory();
		Map<String, String> userInRoleCategories = new HashMap<>();
		Map<String, String> excludeUserInRoleCategories = new HashMap<>();
		List<ResourceResDTO> list;
		try {
			com.weihengtech.auth.model.bo.PageInfo<ResourceResDTO> pageInfo = authCenterResClient.resourceSearch(
					ResourceSearchReqDTO.builder()
							.userInRoleCategories(userInRoleCategories)
							.excludeUserInRoleCategories(excludeUserInRoleCategories)
							.deliverArea(param.getCountryId())
							.dataCenterCode(datacenter)
							.type(param.getResourceType())
							.build());
			list = pageInfo.getData() == null ? Collections.emptyList() : pageInfo.getData();
		} catch (Exception e) {
			log.error(String.format("getResourceList failed: %s", e.getMessage()), e);
			list = Collections.emptyList();
		}
		// 如果是安装商，需要对设备列表特殊过滤
		if (RoleConstants.ROLE_INSTALLER.equals(curRoleId)) {
			List<String> validDeviceList = systemInfoService.getValidDeviceList(UserInfoUtil.currentUserId(),
					list.stream()
							.map(ResourceResDTO::getId)
							.collect(Collectors.toList()));
			list = list.stream()
					.filter(i -> validDeviceList.contains(i.getId()))
					.collect(Collectors.toList());
		}
		return list;
	}

	/** 获取系列映射关系 */
	private Map<Integer, String> getSeriesCodeMap() {
		List<ResourceConfigDTO> seriesList = authCenterResClient.categoryList();
		return seriesList.stream()
				.collect(Collectors.toMap(ResourceConfigDTO::getId, ResourceConfigDTO::getCode));
	}

	/** 获取资源绑定账号信息 */
	private List<UserResDTO> getAgentUserList(List<ResourceResDTO> resourceList) {
		Set<String> userIdList = resourceList.stream()
				.map(ResourceResDTO::getUserInRoleCategories)
				.filter(CollUtil :: isNotEmpty)
				.map(i -> {
					Set<String> userSet = new HashSet<>();
					Optional.ofNullable(i.get(ROLE_SALE)).ifPresent(userSet::add);
					Optional.ofNullable(i.get(ROLE_AGENT)).ifPresent(userSet::add);
					Optional.ofNullable(i.get(ROLE_DEALER)).ifPresent(userSet::add);
					Optional.ofNullable(i.get(ROLE_RETAILER)).ifPresent(userSet::add);
					return userSet;
				})
				.flatMap(Collection::parallelStream)
				.collect(Collectors.toSet());
		if (CollUtil.isEmpty(userIdList)) {
			return Collections.emptyList();
		}
		return authCenterClient.list(String.join(Constants.COMMA, userIdList), null);
	}


	@Override
	public void stopBatchUpgradeTask(String taskId) {
		log.info("终止批量升级任务 {}", taskId);
		FirmwareBatchUpgradeTaskDO firmwareBatchUpgradeTaskDO = firmwareBatchUpgradeTaskMapper.selectById(taskId);
		com.weihengtech.utils.BeanUtil.assertNotNull(firmwareBatchUpgradeTaskDO);
		// 必须是定时执行任务, 必须是没有执行的任务
		if (!firmwareBatchUpgradeTaskDO.getUpgradeMode().equals(UpgradeModeEnum.SCHEDULED_EXECUTION.getModeName())
				|| !firmwareBatchUpgradeTaskDO.getStatus().equals(UpgradeTaskStatusEnum.WAITING.getStatus())) {
			log.warn("批量升级任务{}不是定时执行任务或已执行，无法终止", taskId);
			throw new CustomException(ExceptionEnum.TASK_TERMINATE_FAILED);
		}
		firmwareBatchUpgradeTaskDO.setStatus(UpgradeTaskStatusEnum.TERMINATED.getStatus());
		firmwareBatchUpgradeTaskDO.setUpdateTime(LocalDateTime.now());
		firmwareBatchUpgradeTaskDO.setFailureCount(firmwareBatchUpgradeTaskDO.getTargetDeviceCount());
		firmwareBatchUpgradeTaskDO.setProgress(100);
		ActionFlagUtil.singleAction(firmwareBatchUpgradeTaskMapper.updateById(firmwareBatchUpgradeTaskDO));

		List<DeviceUpgradeDetailDO> deviceUpgradeDetailDOList = deviceUpgradeDetailService.list(
				new QueryWrapper<DeviceUpgradeDetailDO>().eq("batch_task_id", taskId)
		);

		deviceUpgradeDetailDOList.forEach(deviceUpgradeDetailDO -> {
			deviceUpgradeDetailDO.setUpgradeStatus(UpgradeDeviceStatusEnum.FAILURE.getStatus());
			deviceUpgradeDetailDO.setEndTime(LocalDateTime.now());
			deviceUpgradeDetailDO.setFailureReason("批量升级任务已终止");
		});
		ActionFlagUtil.assertTrue(deviceUpgradeDetailService.updateBatchById(deviceUpgradeDetailDOList));

		mqService.stopBatchUpgradeTask(taskId);
	}
}
