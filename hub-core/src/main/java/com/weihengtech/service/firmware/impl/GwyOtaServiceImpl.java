package com.weihengtech.service.firmware.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.api.pojo.vos.UpgradeDeviceVO;
import com.weihengtech.consts.CommonConstants;
import com.weihengtech.enums.firmware.GwyTypeEnum;
import com.weihengtech.pojo.dos.other.DictDO;
import com.weihengtech.pojo.dos.other.DictItemDO;
import com.weihengtech.pojo.dtos.other.DictItemDTO;
import com.weihengtech.pojo.vos.firmware.GwyConfigVersionVO;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotGwyFirmwareResponse;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotGwyHistoryResponse;
import com.weihengtech.service.firmware.GwyOtaService;
import com.weihengtech.service.iot.IotClientService;
import com.weihengtech.service.other.DictItemService;
import com.weihengtech.service.other.DictService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/24 15:08
 */
@Service
public class GwyOtaServiceImpl implements GwyOtaService {

    @Resource
    private IotClientService whIotClient;
    @Resource
    private DictService dictService;
    @Resource
    private DictItemService dictItemService;

    @Override
    public String upgradeGwy(String wifiSn) {
        String dictItemCode = GwyTypeEnum.getCodeByPre(wifiSn.substring(0, 5));
        List<DictItemDTO> itemList = dictItemService.queryDictItemsByDictCode(CommonConstants.DICT_CODE_TOP_VERSION,
                false, null);
        Assert.notEmpty(itemList, "gwy top version is empty");
        String latestVersion = itemList.stream()
                .filter(i -> i.getCode().equals(dictItemCode))
                .map(DictItemDTO::getName)
                .findFirst()
                .orElseThrow(() -> new RuntimeException("gwy top version is not exist"));
        List<IotGwyFirmwareResponse> firmwareList = queryGwyFirmwareList(dictItemCode);
        Assert.notEmpty(firmwareList, String.format("gwy %s version is empty", dictItemCode));
        String jsonStr = JSONUtil.toJsonStr(firmwareList);
        List<JSONObject> list = JSONUtil.toList(jsonStr, JSONObject.class);
        String url = list.stream()
                .filter(i -> i.get("version").equals(latestVersion))
                .map(i -> i.getStr("fileUrl"))
                .findFirst()
                .orElseThrow(() -> new RuntimeException(String.format("gwy %s version is empty", dictItemCode)));
        return whIotClient.upgradeGwy(UpgradeDeviceVO.builder()
                        .deviceId(wifiSn)
                        .url(url)
                        .isGateway(true)
                .build());
    }

    @Override
    public String queryGwyStatus(String deviceSn, String taskId) {
        return whIotClient.queryGwyStatus(deviceSn, taskId);
    }

    @Override
    public List<IotGwyHistoryResponse> queryGwyHistory(String deviceSn) {
        return whIotClient.queryGwyHistory(deviceSn);
    }

    @Override
    public List<IotGwyFirmwareResponse> queryGwyFirmwareList(String productName) {
        return whIotClient.queryGwyFirmwareList(productName);
    }

    @Override
    public GwyConfigVersionVO queryGwyConfigVersion() {
        List<DictItemDTO> topVersionList = dictItemService.queryDictItemsByDictCode(CommonConstants.DICT_CODE_TOP_VERSION,
                false, null);
        Map<String, String> topMap = topVersionList.stream()
                .collect(Collectors.toMap(DictItemDTO::getCode, DictItemDTO::getName));
        List<DictItemDTO> checkVersionList = dictItemService.queryDictItemsByDictCode(CommonConstants.DICT_CODE_CHECK_VERSION,
                false, null);
        Map<String, String> checkMap = checkVersionList.stream()
                .collect(Collectors.toMap(DictItemDTO::getCode, DictItemDTO::getName));
        List<DictItemDTO> inverterVersionList = dictItemService.queryDictItemsByDictCode(CommonConstants.DICT_CODE_INVERTER_FIRMWARE,
                false, null);
        Map<String, List<String>> inverterMap = inverterVersionList.stream()
                .collect(Collectors.toMap(DictItemDTO::getCode, i -> JSONUtil.toList(i.getName(), String.class)));
        return GwyConfigVersionVO.builder()
                .topWifiVersion(topMap.get(CommonConstants.ITEM_CODE_WIFI))
                .topEthVersion(topMap.get(CommonConstants.ITEM_CODE_ETH))
                .top4gVersion(topMap.get(CommonConstants.ITEM_CODE_4G))
                .isCheck(CollUtil.isNotEmpty(checkVersionList))
                .checkWifiVersion(checkMap.get(CommonConstants.ITEM_CODE_WIFI))
                .checkEthVersion(checkMap.get(CommonConstants.ITEM_CODE_ETH))
                .check4gVersion(checkMap.get(CommonConstants.ITEM_CODE_4G))
                .lastEmsVersion(inverterMap.get(CommonConstants.ITEM_CODE_EMS))
                .lastDsp1Version(inverterMap.get(CommonConstants.ITEM_CODE_DSP1))
                .lastDsp2Version(inverterMap.get(CommonConstants.ITEM_CODE_DSP2))
                .lastBmsVersion(inverterMap.get(CommonConstants.ITEM_CODE_BMS))
                .build();
    }

    @Override
    public void saveGwyConfigVersion(GwyConfigVersionVO param) {
        // 置顶固件包更新
        DictDO topDict = dictService.saveDict(DictDO.builder()
                .code(CommonConstants.DICT_CODE_TOP_VERSION)
                .name("cholla置顶固件包")
                .build());
        List<DictItemDO> topList = new ArrayList<>(3);
        if (StrUtil.isNotBlank(param.getTopWifiVersion())) {
            topList.add(DictItemDO.builder()
                    .dictId(topDict.getId())
                    .code(CommonConstants.ITEM_CODE_WIFI)
                    .zhCN(param.getTopWifiVersion())
                    .build());
        }
        if (StrUtil.isNotBlank(param.getTopEthVersion())) {
            topList.add(DictItemDO.builder()
                    .dictId(topDict.getId())
                    .code(CommonConstants.ITEM_CODE_ETH)
                    .zhCN(param.getTopEthVersion())
                    .build());
        }
        if (StrUtil.isNotBlank(param.getTop4gVersion())) {
            topList.add(DictItemDO.builder()
                    .dictId(topDict.getId())
                    .code(CommonConstants.ITEM_CODE_4G)
                    .zhCN(param.getTop4gVersion())
                    .build());
        }
        dictItemService.updateDictItem(topDict.getId(), topList);

        // 逆变器固件配置更新
        DictDO inverterDict = dictService.saveDict(DictDO.builder()
                .code(CommonConstants.DICT_CODE_INVERTER_FIRMWARE)
                .name("逆变器固件配置")
                .build());
        List<DictItemDO> inverterList = new ArrayList<>(4);
        if (CollUtil.isNotEmpty(param.getLastEmsVersion())) {
            inverterList.add(DictItemDO.builder()
                    .dictId(inverterDict.getId())
                    .code(CommonConstants.ITEM_CODE_EMS)
                    .zhCN(JSONUtil.toJsonStr(param.getLastEmsVersion()))
                    .build());
        }
        if (CollUtil.isNotEmpty(param.getLastDsp1Version())) {
            inverterList.add(DictItemDO.builder()
                    .dictId(inverterDict.getId())
                    .code(CommonConstants.ITEM_CODE_DSP1)
                    .zhCN(JSONUtil.toJsonStr(param.getLastDsp1Version()))
                    .build());
        }
        if (CollUtil.isNotEmpty(param.getLastDsp2Version())) {
            inverterList.add(DictItemDO.builder()
                    .dictId(inverterDict.getId())
                    .code(CommonConstants.ITEM_CODE_DSP2)
                    .zhCN(JSONUtil.toJsonStr(param.getLastDsp2Version()))
                    .build());
        }
        if (CollUtil.isNotEmpty(param.getLastBmsVersion())) {
            inverterList.add(DictItemDO.builder()
                    .dictId(inverterDict.getId())
                    .code(CommonConstants.ITEM_CODE_BMS)
                    .zhCN(JSONUtil.toJsonStr(param.getLastBmsVersion()))
                    .build());
        }
        dictItemService.updateDictItem(inverterDict.getId(), inverterList);

        // 校验版本号更新
        DictDO checkDict = dictService.saveDict(DictDO.builder()
                .code(CommonConstants.DICT_CODE_CHECK_VERSION)
                .name("cholla校验版本号")
                .build());
        if (!param.getIsCheck()) {
            dictItemService.removeDictItem(checkDict.getId());
            return;
        }
        List<DictItemDO> checkList = new ArrayList<>(3);
        if (StrUtil.isNotBlank(param.getCheckWifiVersion())) {
            checkList.add(DictItemDO.builder()
                    .dictId(checkDict.getId())
                    .code(CommonConstants.ITEM_CODE_WIFI)
                    .zhCN(param.getCheckWifiVersion())
                    .build());
        }
        if (StrUtil.isNotBlank(param.getCheckEthVersion())) {
            checkList.add(DictItemDO.builder()
                    .dictId(checkDict.getId())
                    .code(CommonConstants.ITEM_CODE_ETH)
                    .zhCN(param.getCheckEthVersion())
                    .build());
        }
        if (StrUtil.isNotBlank(param.getCheck4gVersion())) {
            checkList.add(DictItemDO.builder()
                    .dictId(checkDict.getId())
                    .code(CommonConstants.ITEM_CODE_4G)
                    .zhCN(param.getCheck4gVersion())
                    .build());
        }
        dictItemService.updateDictItem(checkDict.getId(), checkList);
    }
}