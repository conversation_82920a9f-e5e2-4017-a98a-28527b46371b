package com.weihengtech.service.firmware.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.CommonConstants;
import com.weihengtech.enums.firmware.FirmwareTopEnum;
import com.weihengtech.dao.firmware.FirmwareUpgradeRecordMapper;
import com.weihengtech.dao.firmware.FirmwareUploadMapper;
import com.weihengtech.dao.firmware.HardwareCategoryMapper;
import com.weihengtech.dao.firmware.HardwareTypeMapper;
import com.weihengtech.dao.firmware.HardwareVersionMapper;
import com.weihengtech.dao.firmware.MiddleFirmwareSoftwareMapper;
import com.weihengtech.dao.firmware.MiddleSoftwareHardwareMapper;
import com.weihengtech.dao.firmware.SoftwareVersionMapper;
import com.weihengtech.pojo.dos.firmware.FirmwareUpgradeRecordDO;
import com.weihengtech.pojo.dos.firmware.FirmwareUploadDO;
import com.weihengtech.pojo.dos.firmware.HardwareCategoryDO;
import com.weihengtech.pojo.dos.firmware.HardwareTypeDO;
import com.weihengtech.pojo.dos.firmware.HardwareVersionDO;
import com.weihengtech.pojo.dos.firmware.MiddleFirmwareSoftwareDO;
import com.weihengtech.pojo.dos.firmware.MiddleSoftwareHardwareDO;
import com.weihengtech.pojo.dos.firmware.SoftwareVersionDO;
import com.weihengtech.pojo.dtos.firmware.FirmwareAllDTO;
import com.weihengtech.pojo.dtos.firmware.FirmwarePageDTO;
import com.weihengtech.pojo.dtos.firmware.FirmwareUpgradeRecordPageDTO;
import com.weihengtech.pojo.dtos.firmware.FirmwareUploadAllDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.firmware.FirmwareDelVO;
import com.weihengtech.pojo.vos.firmware.FirmwarePageVO;
import com.weihengtech.pojo.vos.firmware.FirmwareTopVO;
import com.weihengtech.pojo.vos.firmware.FirmwareUpgradeRecordPageVO;
import com.weihengtech.pojo.vos.firmware.RemarksVo;
import com.weihengtech.pojo.vos.firmware.VersionBaseVO;
import com.weihengtech.service.firmware.FirmwareSoftwareService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.OperationUtil;
import lombok.val;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.weihengtech.consts.RoleConstants.ROLE_AGENT;
import static com.weihengtech.consts.RoleConstants.ROLE_DEALER;
import static com.weihengtech.consts.RoleConstants.ROLE_INSTALLER;
import static com.weihengtech.consts.RoleConstants.ROLE_RETAILER;

/**
 * <AUTHOR>
 */
@Service
public class FirmwareSoftwareServiceImpl implements FirmwareSoftwareService {

	@Resource
	private HardwareCategoryMapper hardwareCategoryMapper;

	@Resource
	private HardwareTypeMapper hardwareTypeMapper;

	@Resource
	private HardwareVersionMapper hardwareVersionMapper;

	@Resource
	private MiddleSoftwareHardwareMapper middleSoftwareHardwareMapper;

	@Resource
	private SoftwareVersionMapper softwareVersionMapper;

	@Resource
	private MiddleFirmwareSoftwareMapper middleFirmwareSoftwareMapper;

	@Resource
	private FirmwareUploadMapper firmwareUploadMapper;

	@Resource
	private FirmwareUpgradeRecordMapper firmwareUpgradeRecordMapper;

	@Override
	public List<FirmwareUploadAllDTO> queryAllSoftwareVersion(VersionBaseVO versionBaseVo) {
		return querySoftwareVersion(versionBaseVo);
	}

	@Override
	public PageInfoDTO<FirmwarePageDTO> pageFirmware(FirmwarePageVO firmwarePageVO) {

		PageInfoDTO<FirmwarePageDTO> pageInfoDTO = new PageInfoDTO<>();
		pageInfoDTO.setTotalPages(0);
		pageInfoDTO.setTotalCount(0L);
		pageInfoDTO.setData(ListUtil.empty());

		String hardwareTypeId = firmwarePageVO.getHardwareTypeId();
		List<Long> firmwareIdList = new ArrayList<>();
		String typeName = "";
		if (StrUtil.isNotBlank(hardwareTypeId)) {
			List<Long> existIdList = isParamWithHardwareTypeId(hardwareTypeId);
			if (CollUtil.isEmpty(existIdList)) {
				return pageInfoDTO;
			}
			firmwareIdList = existIdList;
			HardwareTypeDO hardwareTypeDO = hardwareTypeMapper.selectById(hardwareTypeId);
			BeanUtil.assertNotNull(hardwareTypeDO);
			typeName = hardwareTypeDO.getTypeName();
		}

		final String hardwareTypeName = typeName;

		PageHelper.startPage(firmwarePageVO.getPageNum(), firmwarePageVO.getPageSize());
		LambdaQueryWrapper<FirmwareUploadDO> firmwareQueryWrapper = new LambdaQueryWrapper<>();
		firmwareQueryWrapper.orderByDesc(FirmwareUploadDO::getIsTop)
						.orderByDesc(FirmwareUploadDO::getUpdateTime);

		OperationUtil.of(firmwareIdList).then(list -> firmwareQueryWrapper.in(FirmwareUploadDO::getId, list));
		OperationUtil.of(firmwarePageVO.getModel()).then(model -> firmwareQueryWrapper.eq(FirmwareUploadDO::getModel, model));
		OperationUtil.of(firmwarePageVO.getFirmwareName())
				.then(firmwareName -> firmwareQueryWrapper.like(FirmwareUploadDO::getFirmwareName, firmwareName));

		List<FirmwareUploadDO> firmwareUploadList = firmwareUploadMapper.selectList(firmwareQueryWrapper);
		PageInfo<FirmwareUploadDO> pageInfo = new PageInfo<>(firmwareUploadList);

		pageInfoDTO.setTotalPages(pageInfo.getPages());
		pageInfoDTO.setTotalCount(pageInfo.getTotal());
		pageInfoDTO.setData(firmwareUploadList.stream().map(firmwareUploadDO -> {
			Long firmwareId = firmwareUploadDO.getId();
			FirmwarePageDTO firmwarePageDTO = new FirmwarePageDTO();
			firmwarePageDTO.setRemarks(firmwareUploadDO.getRemarks());
			firmwarePageDTO.setFirmwareId(String.valueOf(firmwareId));
			firmwarePageDTO.setFirmwareName(firmwareUploadDO.getFirmwareName());
			firmwarePageDTO.setOperatorId(String.valueOf(firmwareUploadDO.getOperatorId()));
			firmwarePageDTO.setUploadTime(
					LocalDateTimeUtil.format(firmwareUploadDO.getUpdateTime(), DatePattern.NORM_DATETIME_PATTERN));
			firmwarePageDTO.setModel(firmwareUploadDO.getModel());
			firmwarePageDTO.setIsTop(firmwareUploadDO.getIsTop());

			if (StrUtil.isNotBlank(hardwareTypeName)) {
				firmwarePageDTO.setHardwareTypeList(ListUtil.toList(hardwareTypeName));
			} else {
				val softIdList = middleFirmwareSoftwareMapper.selectList(
						Wrappers.<MiddleFirmwareSoftwareDO>lambdaQuery()
								.eq(MiddleFirmwareSoftwareDO::getFirmwareId, firmwareId)
				).parallelStream().map(MiddleFirmwareSoftwareDO::getSoftwareId).collect(Collectors.toList());

				if (CollUtil.isNotEmpty(softIdList)) {
					val hardwareIdList = middleSoftwareHardwareMapper.selectList(
							Wrappers.<MiddleSoftwareHardwareDO>lambdaQuery()
									.in(MiddleSoftwareHardwareDO::getSoftwareId, softIdList)
					).parallelStream().map(MiddleSoftwareHardwareDO::getHardwareId).collect(Collectors.toList());

					if (CollUtil.isNotEmpty(hardwareIdList)) {
						val typeIdList = hardwareVersionMapper.selectBatchIds(hardwareIdList)
								.parallelStream().map(HardwareVersionDO::getTypeId)
								.collect(Collectors.toList());

						if (CollUtil.isNotEmpty(typeIdList)) {
							val typeNameList = hardwareTypeMapper.selectBatchIds(typeIdList)
									.parallelStream()
									.map(HardwareTypeDO::getTypeName)
									.collect(Collectors.toList());

							if (CollUtil.isNotEmpty(typeNameList)) {
								firmwarePageDTO.setHardwareTypeList(typeNameList);
							}
						}
					}
				} else {
					firmwarePageDTO.setHardwareTypeList(ListUtil.empty());
				}
			}
			return firmwarePageDTO;
		}).collect(Collectors.toList()));

		return pageInfoDTO;
	}

	@Override
	@DSTransactional
	public void delFirmware(FirmwareDelVO firmwareDelVO) {
		List<String> firmwareIdList = firmwareDelVO.getFirmwareIdList();
		firmwareIdList.forEach(firmwareId -> {
			FirmwareUploadDO firmwareUploadDO = firmwareUploadMapper.selectById(firmwareId);
			BeanUtil.assertNotNull(firmwareUploadDO);
			ActionFlagUtil.singleAction(firmwareUploadMapper.deleteById(firmwareUploadDO));
			middleFirmwareSoftwareMapper.delete(Wrappers.<MiddleFirmwareSoftwareDO>lambdaQuery().eq(MiddleFirmwareSoftwareDO::getFirmwareId, firmwareId));
		});
	}

	@Override
	public PageInfoDTO<FirmwareUpgradeRecordPageDTO> pageUpgradeRecord(
			FirmwareUpgradeRecordPageVO firmwareUpgradeRecordPageVO
	) {

		PageHelper.startPage(firmwareUpgradeRecordPageVO.getPageNum(), firmwareUpgradeRecordPageVO.getPageSize());
		List<FirmwareUpgradeRecordDO> firmwareUpgradeRecordList = firmwareUpgradeRecordMapper
				.selectList(Wrappers.<FirmwareUpgradeRecordDO>lambdaQuery()
						.eq(FirmwareUpgradeRecordDO::getDeviceId, Long.parseLong(firmwareUpgradeRecordPageVO.getDeviceId()))
						.orderByDesc(FirmwareUpgradeRecordDO::getUpdateTime)
				);

		PageInfo<FirmwareUpgradeRecordDO> pageInfo = new PageInfo<>(firmwareUpgradeRecordList);

		PageInfoDTO<FirmwareUpgradeRecordPageDTO> pageInfoDTO = new PageInfoDTO<>();
		pageInfoDTO.setTotalPages(pageInfo.getPages());
		pageInfoDTO.setTotalCount(pageInfo.getTotal());
		pageInfoDTO.setData(firmwareUpgradeRecordList.stream().map(record -> {
			FirmwareUpgradeRecordPageDTO firmwareUpgradeRecordPageDTO = new FirmwareUpgradeRecordPageDTO();
			firmwareUpgradeRecordPageDTO.setDeviceType(record.getAddressMap());
			firmwareUpgradeRecordPageDTO
					.setTime(LocalDateTimeUtil.format(record.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
			firmwareUpgradeRecordPageDTO.setStatus(record.getStatus());
			firmwareUpgradeRecordPageDTO.setStartVersion(record.getSourceVersion());
			firmwareUpgradeRecordPageDTO.setEndVersion(record.getFinishVersion());
			return firmwareUpgradeRecordPageDTO;
		}).collect(Collectors.toList()));

		return pageInfoDTO;
	}

	@Override
	public List<FirmwareAllDTO> allFirmware(String model) {
		String role = UserInfoUtil.currentUserRoleCategory();
		LambdaQueryWrapper<FirmwareUploadDO> wrapper = Wrappers.<FirmwareUploadDO>lambdaQuery()
				.orderByDesc(FirmwareUploadDO::getIsTop)
				.orderByDesc(FirmwareUploadDO::getUpdateTime);
		if (StrUtil.isNotBlank(model)) {
			wrapper.eq(FirmwareUploadDO::getModel, model);
		}
		Map<String, List<FirmwareUploadDO>> groupMap = firmwareUploadMapper.selectList(wrapper)
				.stream()
				.collect(Collectors.groupingBy(FirmwareUploadDO::getType));

		List<String> keyList = groupMap.keySet().stream()
				.filter(key -> groupMap.get(key).size() > 0)
				.collect(Collectors.toList());
		if (ROLE_AGENT.equals(role) || ROLE_DEALER.equals(role) || ROLE_RETAILER.equals(role) ||
				ROLE_INSTALLER.equals(role)) {
			keyList = keyList.stream()
					.filter(key -> FirmwareTopEnum.top.getCode() == groupMap.get(key).get(0).getIsTop())
					.collect(Collectors.toList());
		}
		return keyList.stream().map(key -> {
			FirmwareAllDTO firmwareAllDTO = new FirmwareAllDTO();
			firmwareAllDTO.setTypeName(key);
			List<FirmwareAllDTO.FirmwareInfo> firmwareInfoList = groupMap.get(key).stream().map(firmwareUploadDO -> {
				FirmwareAllDTO.FirmwareInfo firmwareInfo = new FirmwareAllDTO.FirmwareInfo();
				firmwareInfo.setFirmwareId(String.valueOf(firmwareUploadDO.getId()));
				firmwareInfo.setFirmwareName(firmwareUploadDO.getFirmwareName());
				firmwareInfo.setRemarks(firmwareUploadDO.getRemarks());
				firmwareInfo.setIsTop(firmwareUploadDO.getIsTop());
				return firmwareInfo;
			}).collect(Collectors.toList());
			List<FirmwareAllDTO.FirmwareInfo> firmwareInfos;
			if (ROLE_AGENT.equals(role) || ROLE_DEALER.equals(role) || ROLE_RETAILER.equals(role) ||
					ROLE_INSTALLER.equals(role)) {
				firmwareInfos = ListUtil.toList(firmwareInfoList.get(0));
			} else {
				firmwareInfos = firmwareInfoList;
			}
			firmwareAllDTO.setChildren(firmwareInfos);
			return firmwareAllDTO;
		}).collect(Collectors.toList());
	}

	@Override
	public void remarks(RemarksVo remarksVo) {
		String id = remarksVo.getId();
		FirmwareUploadDO firmwareUploadDO = firmwareUploadMapper.selectById(id);
		BeanUtil.assertNotNull(firmwareUploadDO);
		String remarks = remarksVo.getRemarks();
		firmwareUploadDO.setRemarks(Optional.ofNullable(remarks).orElse(""));
		firmwareUploadMapper.updateById(firmwareUploadDO);
	}

    @Override
    public void top(FirmwareTopVO topVO) {
		// 取消置顶
		if (FirmwareTopEnum.cancel.getCode() == topVO.getIsTop()) {
			updateTop(topVO.getId(), topVO.getIsTop());
			return;
		}
		FirmwareUploadDO firmwareItem = firmwareUploadMapper.selectById(topVO.getId());
		if (FirmwareTopEnum.top.getCode() == firmwareItem.getIsTop()) {
			return;
		}
		if (StrUtil.isBlank(firmwareItem.getModel())) {
			throw new CustomException(ExceptionEnum.BLANK_MODEL);
		}
		LambdaQueryWrapper<FirmwareUploadDO> wrapper = Wrappers.<FirmwareUploadDO>lambdaQuery()
				.eq(FirmwareUploadDO::getType, firmwareItem.getType())
				.eq(FirmwareUploadDO::getIsTop, FirmwareTopEnum.top.getCode());
		List<FirmwareUploadDO> topList = firmwareUploadMapper.selectList(wrapper);
		if (CollUtil.isEmpty(topList)) {
			updateTop(topVO.getId(), topVO.getIsTop());
			return;
		}
		boolean alreadyTop = topList.stream()
				.anyMatch(i -> firmwareItem.getModel().equals(i.getModel()));
		if (alreadyTop) {
			throw new CustomException(ExceptionEnum.TOP_FAILED_EXCEPTION);
		}
		updateTop(topVO.getId(), topVO.getIsTop());
	}

	/**
	 * 更新置顶逻辑
	 *
	 * @param id 固件包Id
	 * @param isTop 置顶|取消
	 */
	private void updateTop(Long id, int isTop) {
		Long userId = Long.parseLong(UserInfoUtil.currentUserId());
		FirmwareUploadDO updItem = new FirmwareUploadDO();
		updItem.setId(id);
		updItem.setIsTop(isTop);
		updItem.setUpdateTime(LocalDateTime.now());
		updItem.setOperatorId(userId);
		firmwareUploadMapper.updateById(updItem);
	}

    private List<Long> isParamWithHardwareTypeId(String hardwareTypeId) {
		List<SoftwareVersionDO> softwareVersionList = softwareVersionMapper.selectList(
				Wrappers.<SoftwareVersionDO>lambdaQuery()
						.eq(SoftwareVersionDO::getTypeId, hardwareTypeId)
		);

		if (CollUtil.isEmpty(softwareVersionList)) {
			return ListUtil.empty();
		}

		List<Long> softwareVersionIdList = softwareVersionList.stream()
				.map(SoftwareVersionDO::getId)
				.collect(Collectors.toList());

		LambdaQueryWrapper<MiddleFirmwareSoftwareDO> middleFirmwareSoftware = new LambdaQueryWrapper<>();

		middleFirmwareSoftware
				.in(MiddleFirmwareSoftwareDO::getSoftwareId, softwareVersionIdList)
				.orderByDesc(MiddleFirmwareSoftwareDO::getUpdateTime);

		List<MiddleFirmwareSoftwareDO> middleFirmwareSoftwareList = middleFirmwareSoftwareMapper.selectList(middleFirmwareSoftware);

		if (CollUtil.isEmpty(middleFirmwareSoftwareList)) {
			return ListUtil.empty();
		}

		return middleFirmwareSoftwareList
				.stream()
				.map(MiddleFirmwareSoftwareDO::getFirmwareId)
				.collect(Collectors.toList());
	}

	private List<FirmwareUploadAllDTO> querySoftwareVersion(VersionBaseVO versionBaseVo) {
		List<FirmwareUploadAllDTO> firmwareUploadAllDTOList = new ArrayList<>();
		hardwareCategoryMapper.selectList(Wrappers.lambdaQuery())
				.forEach(category -> packageSoftwareVersionDTO(category, firmwareUploadAllDTOList, versionBaseVo));
		return firmwareUploadAllDTOList;
	}

	private void packageSoftwareVersionDTO(
			HardwareCategoryDO category,
			List<FirmwareUploadAllDTO> firmwareUploadAllDTOList, VersionBaseVO versionBaseVo
	) {

		FirmwareUploadAllDTO firmwareUploadAllDTO = new FirmwareUploadAllDTO();
		firmwareUploadAllDTO.setHardwareCategoryName(category.getCategoryName());

		List<FirmwareUploadAllDTO.HardwareType> hardwareTypeList = new ArrayList<>();
		packageHardwareTypeList(category.getId(), hardwareTypeList, versionBaseVo);

		firmwareUploadAllDTO.setHardwareTypeList(hardwareTypeList);
		firmwareUploadAllDTOList.add(firmwareUploadAllDTO);
	}

	private void packageHardwareTypeList(
			Integer categoryId, List<FirmwareUploadAllDTO.HardwareType> hardwareTypeList,
			VersionBaseVO versionBaseVo
	) {
		hardwareTypeMapper.selectList(Wrappers.<HardwareTypeDO>lambdaQuery().eq(HardwareTypeDO::getCategoryId, categoryId))
				.forEach(hardwareType -> {
					FirmwareUploadAllDTO.HardwareType type = new FirmwareUploadAllDTO.HardwareType();
					type.setHardwareTypeName(hardwareType.getTypeName());

					Long hardwareTypeId = hardwareType.getId();
					List<FirmwareUploadAllDTO.SoftwareVersion> softwareVersionList = new ArrayList<>();
					packageSoftwareVersionList(hardwareTypeId, softwareVersionList, versionBaseVo);
					type.setSoftwareVersionList(softwareVersionList);

					hardwareTypeList.add(type);
				});
	}

	private void packageSoftwareVersionList(
			Long hardwareTypeId,
			List<FirmwareUploadAllDTO.SoftwareVersion> softwareVersionList, VersionBaseVO versionBaseVo
	) {
		LambdaQueryWrapper<HardwareVersionDO> wrapper = Wrappers.<HardwareVersionDO>lambdaQuery()
				.eq(HardwareVersionDO::getTypeId, hardwareTypeId);
		if (versionBaseVo.getModel() != null) {
			wrapper.eq(HardwareVersionDO::getModel, versionBaseVo.getModel());
		}
        hardwareVersionMapper
				.selectList(wrapper)
				.forEach(hardwareVersionDO -> querySoftwareVersionByHardwareVersionId(hardwareVersionDO.getId(),
						softwareVersionList, versionBaseVo.getId()
				));
	}

	private void querySoftwareVersionByHardwareVersionId(
			Long hardwareVersionId,
			List<FirmwareUploadAllDTO.SoftwareVersion> softwareVersionList, String firmwareId
	) {
		middleSoftwareHardwareMapper
				.selectList(Wrappers.<MiddleSoftwareHardwareDO>lambdaQuery()
						.eq(MiddleSoftwareHardwareDO::getHardwareId, hardwareVersionId)
						.orderByDesc(MiddleSoftwareHardwareDO::getCreateTime)
				)
				.stream().map(MiddleSoftwareHardwareDO::getSoftwareId).forEach(softwareId -> {
					SoftwareVersionDO softwareVersionDO = softwareVersionMapper.selectById(softwareId);
					BeanUtil.assertNotNull(softwareVersionDO);
					AtomicInteger atomicBind = new AtomicInteger(CommonConstants.VERSION_BIND_OFF);
					OperationUtil.of(firmwareId).then(firmware -> {
						LambdaQueryWrapper<MiddleFirmwareSoftwareDO> firmwareQueryWrapper = new LambdaQueryWrapper<>();
						firmwareQueryWrapper.eq(MiddleFirmwareSoftwareDO::getSoftwareId, softwareId)
								.eq(MiddleFirmwareSoftwareDO::getFirmwareId, Long.parseLong(firmwareId));
						List<MiddleFirmwareSoftwareDO> middleFirmwareSoftwareDOList = middleFirmwareSoftwareMapper
								.selectList(firmwareQueryWrapper);
						if (CollUtil.isNotEmpty(middleFirmwareSoftwareDOList)) {
							atomicBind.set(CommonConstants.VERSION_BIND_ON);
						}
					});

					softwareVersionList.add(new FirmwareUploadAllDTO.SoftwareVersion(String.valueOf(softwareVersionDO.getId()),
							softwareVersionDO.getSoftwareVersion(), atomicBind.get()
					));
				});
	}
}
