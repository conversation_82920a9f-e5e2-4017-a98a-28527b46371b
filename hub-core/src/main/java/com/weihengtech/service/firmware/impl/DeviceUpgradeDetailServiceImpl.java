package com.weihengtech.service.firmware.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.weihengtech.dao.firmware.DeviceUpgradeDetailMapper;
import com.weihengtech.pojo.dos.firmware.DeviceUpgradeDetailDO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.firmware.DeviceUpgradeDetailPageVo;
import com.weihengtech.service.firmware.DeviceUpgradeDetailService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: jiahao.jin
 * @create: 2025-07-14 16:43
 * @description:
 */
@Service
public class DeviceUpgradeDetailServiceImpl extends ServiceImpl<DeviceUpgradeDetailMapper, DeviceUpgradeDetailDO>
        implements DeviceUpgradeDetailService {

    @Resource
    private DeviceUpgradeDetailMapper deviceUpgradeDetailMapper;


    @Override
    public PageInfoDTO<DeviceUpgradeDetailDO> pageDeviceUpgradeDetail(DeviceUpgradeDetailPageVo param) {

        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<DeviceUpgradeDetailDO> deviceUpgradeDetailDOList = deviceUpgradeDetailMapper.selectList(Wrappers.<DeviceUpgradeDetailDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(param.getBatchTaskId()), DeviceUpgradeDetailDO::getBatchTaskId, param.getBatchTaskId())
                .eq(StringUtils.isNotBlank(param.getUpgradeStatus()), DeviceUpgradeDetailDO::getUpgradeStatus, param.getUpgradeStatus())
                .like(StringUtils.isNotBlank(param.getDeviceSn()), DeviceUpgradeDetailDO::getDeviceSn, param.getDeviceSn())
                .orderByDesc(DeviceUpgradeDetailDO::getCreateTime)
        );

        PageInfo<DeviceUpgradeDetailDO> pageInfo = new PageInfo<>(deviceUpgradeDetailDOList);

        PageInfoDTO<DeviceUpgradeDetailDO> pageInfoDTO = new PageInfoDTO<>();
        pageInfoDTO.setTotalPages(pageInfo.getPages());
        pageInfoDTO.setTotalCount(pageInfo.getTotal());
        pageInfoDTO.setData(deviceUpgradeDetailDOList);
        return pageInfoDTO;
    }
}
