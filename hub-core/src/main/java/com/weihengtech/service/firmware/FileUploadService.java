package com.weihengtech.service.firmware;

import cn.hutool.core.lang.Pair;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface FileUploadService {

	/**
	 * 上传固件包
	 *
	 * @param multipartFile  文件流
	 * @param idForPrefixMap 软件版本与对应的校验前缀
	 * @param firmwareId     固件版本id
	 * @return 下载连接
	 */
	Pair<String, Long> uploadSingleBinFile(
			MultipartFile multipartFile, Map<Long, String> idForPrefixMap,
			String firmwareId, String model
	);
}
