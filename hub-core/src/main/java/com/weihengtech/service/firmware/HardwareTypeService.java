package com.weihengtech.service.firmware;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.firmware.HardwareTypeDO;
import com.weihengtech.pojo.dtos.firmware.HardwareTypeDTO;
import com.weihengtech.pojo.dtos.firmware.HardwareTypeLinkCategoryDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.firmware.HardwareTypeInsertVO;
import com.weihengtech.pojo.vos.firmware.HardwareTypeQueryVO;
import com.weihengtech.pojo.vos.firmware.HardwareTypeUpdateVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HardwareTypeService extends IService<HardwareTypeDO> {

	/**
	 * 新增硬件类型
	 *
	 * @param hardwareTypeInsertVO 硬件类型名称
	 */
	void insertHardwareType(HardwareTypeInsertVO hardwareTypeInsertVO);

	/**
	 * 更新硬件类型
	 *
	 * @param hardwareTypeUpdateVO 要更新的硬件类型名称
	 */
	void updateHardwareType(HardwareTypeUpdateVO hardwareTypeUpdateVO);

	/**
	 * 分页查询硬件类型
	 *
	 * @param hardwareTypeQueryVO 查询参数
	 * @return com.weihengtech.pojo.dtos.other.PageInfoDTO
	 */
	PageInfoDTO<HardwareTypeDTO> pageHardwareType(HardwareTypeQueryVO hardwareTypeQueryVO);

	/**
	 * 查询所有的分类与类型
	 *
	 * @return com.weihengtech.pojo.dtos.firmware.HardwareTypeLinkCategoryDTO
	 */
	List<HardwareTypeLinkCategoryDTO> allWithCategory();
}
