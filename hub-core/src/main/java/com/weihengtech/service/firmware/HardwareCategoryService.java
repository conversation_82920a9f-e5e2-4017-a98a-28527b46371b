package com.weihengtech.service.firmware;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.firmware.HardwareCategoryDO;
import com.weihengtech.pojo.dtos.firmware.HardwareCategoryDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HardwareCategoryService extends IService<HardwareCategoryDO> {

	/**
	 * 查询所有的硬件总分类信息
	 *
	 * @return 硬件总分类列表
	 */
	List<HardwareCategoryDTO> queryAllHardwareCategory();
}
