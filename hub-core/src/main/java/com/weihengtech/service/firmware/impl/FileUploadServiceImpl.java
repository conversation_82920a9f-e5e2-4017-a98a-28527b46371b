package com.weihengtech.service.firmware.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.dao.other.OssMapper;
import com.weihengtech.pojo.dos.firmware.FirmwareUploadDO;
import com.weihengtech.service.firmware.FileUploadService;
import com.weihengtech.service.firmware.FirmwareUploadService;
import com.weihengtech.utils.OperatingRecordUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FileUploadServiceImpl implements FileUploadService {

	@Value("${upload.bin.wifi.name}")
	private String wifiName;

	@Value(("${upload.bin.max.size}"))
	private Integer maxSize;

	@Resource
	private FirmwareUploadService firmwareUploadService;

	@Resource
	private OssMapper ossMapper;

	@Override
	public Pair<String, Long> uploadSingleBinFile(
			MultipartFile multipartFile, Map<Long, String> idForPrefixMap,
			String firmwareId, String model
	) {
		final String zero = "0";
		if (StrUtil.isBlank(firmwareId) || zero.equals(firmwareId)) {
			return newBinUpload(idForPrefixMap, multipartFile, model);
		} else {
			return updateBinUpload(idForPrefixMap, multipartFile, model, firmwareId);
		}
	}

	private Pair<String, Long> updateBinUpload(
			Map<Long, String> idForPrefixMap, MultipartFile multipartFile,
			String model, String firmwareId
	) {
		FirmwareUploadDO firmwareUploadDO = firmwareUploadService.selectBySerialId(firmwareId);
		String downloadUrl = firmwareUploadDO.getDownloadUrl();
		Long size = firmwareUploadDO.getSize();
		if (multipartFile != null) {
			Pair<String, Long> downloadUrlAndSize = routeUpload(idForPrefixMap, multipartFile);
			downloadUrl = downloadUrlAndSize.getKey();
			size = downloadUrlAndSize.getValue();
			firmwareUploadDO.setDownloadUrl(downloadUrl);
			firmwareUploadDO.setSize(size);
		}
		firmwareUploadDO.setModel(model);
		firmwareUploadService.updateWithLinkSoftwareVersion(firmwareUploadDO, idForPrefixMap);
		// tag
		OperatingRecordUtil.log(Long.parseLong(UserInfoUtil.currentUserId()), RecordContentConstants.UPDATE_FIRMWARE,
				MapUtil.<String, String>builder().put("firmwareId", String.valueOf(firmwareId))
						.put("downloadUrl", downloadUrl).put("size", String.valueOf(size)).build(),
				EnumConstants.RecordModule.FIRMWARE_MAINTAIN.getCode()
		);
		return Pair.of(downloadUrl, size);
	}

	private Pair<String, Long> newBinUpload(
			Map<Long, String> idForPrefixMap, MultipartFile multipartFile,
			String model
	) {
		Pair<String, Long> downloadUrlAndSize = routeUpload(idForPrefixMap, multipartFile);
		Long firmwareId = firmwareUploadService.insertWithLinkSoftwareVersion(downloadUrlAndSize, idForPrefixMap,
				model
		);
		// tag
		OperatingRecordUtil.log(Long.parseLong(UserInfoUtil.currentUserId()), RecordContentConstants.UPLOAD_FIRMWARE,
				MapUtil.<String, String>builder().put("firmwareId", String.valueOf(firmwareId))
						.put("downloadUrl", downloadUrlAndSize.getKey())
						.put("size", String.valueOf(downloadUrlAndSize.getValue())).build(),
				EnumConstants.RecordModule.FIRMWARE_MAINTAIN.getCode()
		);
		return downloadUrlAndSize;
	}

	private Pair<String, Long> routeUpload(Map<Long, String> idForPrefixMap, MultipartFile multipartFile) {
		if (idForPrefixMap.size() == 1 && idForPrefixMap.values().stream().filter(StrUtil::isBlank).count() == 1) {
			return upload(multipartFile, wifiName, Lists.newArrayList());
		} else {
			return upload(multipartFile, RandomUtil.randomString(4), new ArrayList<>(idForPrefixMap.values()));
		}
	}

	private Pair<String, Long> upload(MultipartFile multipartFile, String fileName, List<String> prefixList) {
		limitSize(multipartFile.getSize());

		String binSuffix = validSuffix(multipartFile.getOriginalFilename());
		String uniquePath = IdUtil.fastSimpleUUID().substring(0, 4);
		String name = fileName + "." + binSuffix;
		String fullPath = StrUtil.format("{}/{}/{}", binSuffix, uniquePath, name);
		try (
				InputStream inputStream = multipartFile.getInputStream()
		) {
			byte[] bytes = IoUtil.readBytes(inputStream);
			validBin(prefixList, bytes);
			ossMapper.putObject(fullPath, bytes);
		} catch (IOException e) {
			throw new CustomException(ExceptionEnum.FILE_UPLOAD_ERROR);
		}
		return Pair.of(fullPath, multipartFile.getSize());
	}

	private void validBin(List<String> prefixList, byte[] bytes) {
		int length = bytes.length;
		List<Integer> integerList = buildIndexList(length);
		for (String prefix : prefixList) {
			boolean res = Arrays.stream(prefix.split(",")).anyMatch(i -> {
				if ("BMS".equals(i)) {
					return true;
				}
				byte[] prefixBytes = i.getBytes(StandardCharsets.UTF_8);
				return integerList.stream().anyMatch(j -> {
					if (j + prefixBytes.length < length) {
						byte[] splitArray = Arrays.copyOfRange(bytes, j, j + prefixBytes.length);
						String splitStr = new String(splitArray);
						if (splitStr.indexOf(i) == 0) {
							return true;
						}
					}
					return false;
				});
			});
			if (!res) {
				throw new CustomException(ExceptionEnum.FIRMWARE_VERSION_VALID_ERROR);
			}
		}
	}

	private void limitSize(long size) {
		long byteSize = 1024;
		if (byteSize * byteSize * maxSize < size) {
			throw new CustomException(ExceptionEnum.FILE_SIZE_LIMIT_ERROR);
		}
	}

	private String validSuffix(String originalName) {
		String suffix = FileNameUtil.getSuffix(originalName);
		final String binSuffix = "bin";
		if (!binSuffix.equals(suffix)) {
			throw new CustomException(ExceptionEnum.SUFFIX_NOT_SUPPORT_EXCEPTION);
		}
		return suffix;
	}

	private List<Integer> buildIndexList(int len) {
		List<Integer> indexList = new ArrayList<>();
		int count = len / 224;
		for (int i = 0; i < count; i++) {
			indexList.add(i * 224);
		}
		return indexList;
	}
}
