package com.weihengtech.service.firmware;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.firmware.FirmwareUpgradeRecordDO;
import com.weihengtech.pojo.dos.firmware.FirmwareUploadDO;

/**
 * <AUTHOR>
 */
public interface FirmwareUpgradeRecordService extends IService<FirmwareUpgradeRecordDO> {

    /**
     * 保存固件升级记录
     *
     * @param deviceInfo 设备信息
     * @param firmwareItem 固件信息
     * @param typeName 固件类型
     * @return FirmwareUpgradeRecordDO
     */
    FirmwareUpgradeRecordDO saveFirmwareUpgradeRecord(DeviceListDO deviceInfo, FirmwareUploadDO firmwareItem,
                                                      String typeName);
}
