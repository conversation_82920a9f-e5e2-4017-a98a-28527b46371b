package com.weihengtech.service.firmware;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.firmware.FirmwareUploadDO;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface FirmwareUploadService extends IService<FirmwareUploadDO> {

	/**
	 * 新增固件包关联软件版本
	 *
	 * @param downloadUrlAndSize    固件包下载地址与大小
	 * @param softwareVersionIdList 软件版本列表
	 * @return
	 */
	Long insertWithLinkSoftwareVersion(
			Pair<String, Long> downloadUrlAndSize, Map<Long, String> softwareVersionIdList,
			String model
	);

	/**
	 * 根据id查询固件包信息
	 *
	 * @param firmwareId 固件包id
	 * @return 固件包实体类
	 */
	FirmwareUploadDO selectBySerialId(String firmwareId);

	/**
	 * 更新固件包关联软件版本
	 *
	 * @param firmwareUploadDO 要更新的固件包实体类
	 * @param idForPrefixMap   软件版本映射
	 */
	void updateWithLinkSoftwareVersion(
			FirmwareUploadDO firmwareUploadDO, Map<Long, String> idForPrefixMap
	);
}
