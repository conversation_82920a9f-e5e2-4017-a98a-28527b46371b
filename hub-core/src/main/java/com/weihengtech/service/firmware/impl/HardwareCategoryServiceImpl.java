package com.weihengtech.service.firmware.impl;

import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.dao.firmware.HardwareCategoryMapper;
import com.weihengtech.pojo.dos.firmware.HardwareCategoryDO;
import com.weihengtech.pojo.dtos.firmware.HardwareCategoryDTO;
import com.weihengtech.service.firmware.HardwareCategoryService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class HardwareCategoryServiceImpl extends ServiceImpl<HardwareCategoryMapper, HardwareCategoryDO>
		implements
		HardwareCategoryService {

	@Override
	public List<HardwareCategoryDTO> queryAllHardwareCategory() {
		List<HardwareCategoryDO> list = this.list();
		ArrayList<HardwareCategoryDTO> result = new ArrayList<>();
		for (HardwareCategoryDO hardwareCategoryDO : list) {
			HardwareCategoryDTO hardwareCategoryDTO = new HardwareCategoryDTO();
			CglibUtil.copy(hardwareCategoryDO, hardwareCategoryDTO);
			result.add(hardwareCategoryDTO);
		}
		return result;
	}
}
