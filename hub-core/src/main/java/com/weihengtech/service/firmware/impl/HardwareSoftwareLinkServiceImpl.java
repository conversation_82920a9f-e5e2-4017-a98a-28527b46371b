package com.weihengtech.service.firmware.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.dao.firmware.HardwareTypeMapper;
import com.weihengtech.dao.firmware.HardwareVersionMapper;
import com.weihengtech.dao.firmware.MiddleSoftwareHardwareMapper;
import com.weihengtech.pojo.dos.firmware.HardwareTypeDO;
import com.weihengtech.pojo.dos.firmware.HardwareVersionDO;
import com.weihengtech.pojo.dos.firmware.MiddleSoftwareHardwareDO;
import com.weihengtech.service.firmware.HardwareSoftwareLinkService;
import com.weihengtech.utils.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HardwareSoftwareLinkServiceImpl implements HardwareSoftwareLinkService {

	@Resource
	private HardwareVersionMapper hardwareVersionMapper;

	@Resource
	private MiddleSoftwareHardwareMapper middleSoftwareHardwareMapper;

	@Resource
	private HardwareTypeMapper hardwareTypeMapper;

	@Override
	public String queryHardwareTypeNameBySoftwareId(Long softwareId) {

		List<MiddleSoftwareHardwareDO> middleSoftwareHardwareDOList = middleSoftwareHardwareMapper.selectList(
				Wrappers.<MiddleSoftwareHardwareDO>lambdaQuery().eq(MiddleSoftwareHardwareDO::getSoftwareId, softwareId)
		);
		if (CollUtil.isEmpty(middleSoftwareHardwareDOList)) {
			log.info("软件版本与硬件类型未绑定");
			throw new CustomException(ExceptionEnum.SOFTWARE_VERSION_NOT_BIND_HARDWARE_TYPE_ERROR);
		} else {
			Long hardwareId = middleSoftwareHardwareDOList.get(0).getHardwareId();
			HardwareVersionDO hardwareVersionDO = hardwareVersionMapper.selectById(hardwareId);
			BeanUtil.assertNotNull(hardwareVersionDO);
			Long typeId = hardwareVersionDO.getTypeId();
			HardwareTypeDO hardwareTypeDO = hardwareTypeMapper.selectById(typeId);
			BeanUtil.assertNotNull(hardwareTypeDO);
			return hardwareTypeDO.getCategoryName();
		}
	}
}
