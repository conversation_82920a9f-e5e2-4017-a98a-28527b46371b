package com.weihengtech.service.ext;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 设备扩展信息通用服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/8 14:32
 */
public interface ExtCommonService<T> {

    /**
     * 根据设备Id查询单个扩展信息
     *
     * @param deviceId 设备Id
     * @return 扩展信息
     */
    T getExtInfoByDeviceId(Long deviceId);

    /**
     * 根据设备Id批量查询设备扩展信息
     *
     * @param deviceIdList 设备Id集合
     * @return 扩展信息集合
     */
    List<T> getExtInfoByDeviceIds(Collection<Long> deviceIdList);

    /**
     * 根据设备Id批量查询设备扩展信息
     *
     * @param deviceIdList 设备Id集合
     * @return 设备Id与扩展信息的映射
     */
    Map<Long, T> getExtMapByDeviceIds(Collection<Long> deviceIdList);

    /**
     * 根据设备Id更新扩展信息
     *
     * @param t 扩展信息
     */
    void updExtByDeviceId(T t);

}
