package com.weihengtech.service.systeminfo.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.auth.dto.ResourceListReqDTO;
import com.weihengtech.auth.dto.ResourceResDTO;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.dao.systeminfo.SystemDeviceRelMapper;
import com.weihengtech.pojo.dos.systeminfo.SystemDeviceRelDO;
import com.weihengtech.service.systeminfo.SystemDeviceRelService;
import com.weihengtech.utils.SnowFlakeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 系统关联设备 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Service
public class SystemDeviceRelServiceImpl extends ServiceImpl<SystemDeviceRelMapper, SystemDeviceRelDO> implements SystemDeviceRelService {

    @Autowired
    private SnowFlakeUtil snowFlakeUtil;
    @Resource
    private SystemDeviceRelMapper systemDeviceRelMapper;
    @Resource
    private AuthCenterResClient authCenterResClient;

    @Override
    public boolean addDevice(Long systemId, Long deviceId) {
        // 已存在的设备关联无需重复添加
        SystemDeviceRelDO exists = getOne(Wrappers.<SystemDeviceRelDO>lambdaQuery()
                .eq(SystemDeviceRelDO::getDeviceId, deviceId)
                .eq(SystemDeviceRelDO::getSystemId, systemId));
        if (exists != null) {
            SystemDeviceRelDO upd = SystemDeviceRelDO.builder()
                    .id(exists.getId())
                    .isCheck(0)
                    .build();
            return updateById(upd);
        }
        SystemDeviceRelDO rel = SystemDeviceRelDO.builder()
                .id(snowFlakeUtil.generateId())
                .systemId(systemId)
                .deviceId(deviceId)
                .isCheck(0)
                .createTime(new Date())
                .build();
        return save(rel);
    }

    @Override
    public boolean removeRel(Long installerId, Long deviceId) {
        List<Long> idList = systemDeviceRelMapper.getRelByUserAndDevice(installerId, deviceId);
        if (CollUtil.isNotEmpty(idList)) {
            return removeByIds(idList);
        }
        return true;
    }

    @Override
    public List<SystemDeviceRelDO> listBySystemId(Long systemId) {
        List<SystemDeviceRelDO> deviceRelList = systemDeviceRelMapper.listBySystemId(systemId);
        String ids = deviceRelList.stream()
                .map(i -> String.valueOf(i.getDeviceId()))
                .collect(Collectors.joining(Constants.COMMA));
        List<ResourceResDTO> resourceList = authCenterResClient.resourceList(ResourceListReqDTO.builder()
                .ids(ids).build());
        Map<String, Integer> map = resourceList.stream()
                .collect(Collectors.toMap(ResourceResDTO::getId, ResourceResDTO::getCategory));
        deviceRelList.forEach(i -> i.setResourceSeries(map.get(String.valueOf(i.getDeviceId()))));
        return deviceRelList;
    }
}
