package com.weihengtech.service.systeminfo;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.systeminfo.SystemDeviceRelDO;

import java.util.List;

/**
 * <p>
 * 系统关联设备 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
public interface SystemDeviceRelService extends IService<SystemDeviceRelDO> {

    /**
     * 添加设备
     *
     * @param systemId 系统id
     * @param deviceId 设备id
     * @return 保存结果
     */
    boolean addDevice(Long systemId, Long deviceId);

    /**
     * 解除指定安装商对应系统与设备的关系
     *
     * @param installerId 安装商id
     * @param deviceId 设备id
     * @return 处理结果
     */
    boolean removeRel(Long installerId, Long deviceId);

    /**
     * 获取指定系统下的所有设备
     *
     * @param systemId 系统id
     * @return 设备列表
     */
    List<SystemDeviceRelDO> listBySystemId(Long systemId);

}
