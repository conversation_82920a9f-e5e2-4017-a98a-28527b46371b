package com.weihengtech.service.systeminfo;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.systeminfo.SystemInfoDO;
import com.weihengtech.pojo.vos.systeminfo.SettingTransferVO;
import com.weihengtech.pojo.vos.systeminfo.SystemInfoSaveResVO;
import com.weihengtech.pojo.vos.systeminfo.SystemInfoSaveVO;
import com.weihengtech.pojo.vos.systeminfo.SystemInfoUpdVO;

import java.util.List;

/**
 * <p>
 * 系统信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
public interface SystemInfoService extends IService<SystemInfoDO> {

    /**
     * 保存系统信息
     *
     * @param saveVO
     * @return
     */
    SystemInfoSaveResVO saveInfo(SystemInfoSaveVO saveVO);

    /**
     * 查询系统详情
     *
     * @param id
     * @return
     */
    SystemInfoDO getSysInfo(Long id);

    /**
     * 更新系统详情
     *
     * @param updVO
     */
    void updById(SystemInfoUpdVO updVO);

    /**
     * 根据安装商账号查询系统列表
     *
     * @param installerId
     * @return
     */
    List<SystemInfoDO> listByAccount(Long installerId);

    /**
     * 回退系统步骤
     *
     * @param installerId 安装商id
     * @param deviceId 设备id
     * @return
     */
    boolean backStepSystem(Long installerId, Long deviceId);

    /**
     * 生成二维码密文
     *
     * @param dto 二维码入参
     * @return 密文
     */
    String qrCodeEncryption(SettingTransferVO dto);

    /**
     * 获取合法的设备列表
     *
     * @param installerId 安装商Id
     * @param deviceIdList 设备Id
     * @return 合法的设备列表
     */
    List<String> getValidDeviceList(String installerId, List<String> deviceIdList);

    /**
     * 删除未完成转移的系统
     *
     * @param id 系统id
     */
    void removeUnFinishedSystem(Long id);
}
