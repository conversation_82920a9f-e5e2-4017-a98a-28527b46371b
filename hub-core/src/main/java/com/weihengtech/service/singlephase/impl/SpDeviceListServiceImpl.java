package com.weihengtech.service.singlephase.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.consts.TsdbMetricsConstants;
import com.weihengtech.dao.device.DeviceListMapper;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.device.DeviceStatusAndSocDTO;
import com.weihengtech.pojo.vos.device.DeviceSyncStateVo;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.service.singlephase.SpDeviceListService;
import com.weihengtech.service.thirdpart.TimeSeriesDatabaseService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.OperatingRecordUtil;
import com.weihengtech.utils.OperationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 单相机列表服务实现
 *
 * <AUTHOR>
 * @date 2024/1/22 16:58
 * @version 1.0
 */
@Slf4j
@Service
public class SpDeviceListServiceImpl extends ServiceImpl<DeviceListMapper, DeviceListDO>
		implements
		SpDeviceListService {

	@Resource
	private TuyaDatacenterService tuyaDatacenterService;
	@Resource
	private StrategyService strategyService;

	@Override
	@DSTransactional
	public Integer syncDeviceState(DeviceSyncStateVo deviceSyncStateVo) {
		DeviceListDO deviceListDO = this.getById(deviceSyncStateVo.getDeviceId());
		com.weihengtech.utils.BeanUtil.assertNotNull(deviceListDO);

		DeviceStatusEnum deviceStatusEnum;
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		// 没有首次安装时间，就是未知
		if (deviceListDO.getFirstInstall() == 0) {
			deviceStatusEnum = DeviceStatusEnum.UNKNOWN;
		} else {
			String wifiSn = deviceListDO.getWifiSn();
			deviceStatusEnum = specificServService.checkDeviceStatus(wifiSn);
		}

		AtomicInteger state = new AtomicInteger(deviceStatusEnum.getDbCode());
		deviceListDO.setState(state.get());
		deviceListDO.setUpdateTime(LocalDateTime.now());
		if (deviceStatusEnum.equals(DeviceStatusEnum.ONLINE)) {
			OperationUtil
					.of(specificServService.getDeviceAssignProperty(
							deviceListDO.getDeviceName(),
							"sys_run_mode"
					))
					.ifPresentOrElse(
							dict -> {
								try {
									int stateVal = NumberUtil.round(String.valueOf(dict.get("sys_run_mode")), 0).intValue();
									log.info("device: {}, updateState is: {}", deviceSyncStateVo.getDeviceId(), stateVal);
									deviceListDO.setState(stateVal);
								} catch (Exception e) {
									deviceListDO.setState(DeviceStatusEnum.ONLINE.getCode());
								}
							},
							() -> deviceListDO.setState(DeviceStatusEnum.OFFLINE.getDbCode())
					);
		}
		ActionFlagUtil.assertTrue(this.updateById(deviceListDO));
		return deviceListDO.getState();
	}

	@Override
	@DSTransactional
	public DeviceStatusAndSocDTO syncDeviceStateAndSoc(DeviceSyncStateVo deviceSyncStateVo) {
		DeviceStatusAndSocDTO deviceStatusAndSocDTO = new DeviceStatusAndSocDTO();
		DeviceListDO deviceListDO = this.getById(deviceSyncStateVo.getDeviceId());
		com.weihengtech.utils.BeanUtil.assertNotNull(deviceListDO);

		deviceStatusAndSocDTO.setDeviceName(deviceListDO.getDeviceName());
		DeviceStatusEnum deviceStatusEnum;
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		// 没有首次安装时间，就是未知
		if (deviceListDO.getFirstInstall() == 0) {
			deviceStatusEnum = DeviceStatusEnum.UNKNOWN;
		} else {
			String wifiSn = deviceListDO.getWifiSn();
			deviceStatusEnum = specificServService.checkDeviceStatus(wifiSn);
		}

		AtomicInteger state = new AtomicInteger(deviceStatusEnum.getDbCode());
		deviceListDO.setState(state.get());
		deviceListDO.setUpdateTime(LocalDateTime.now());
		deviceStatusAndSocDTO.setBatSoc(null);
		if (deviceStatusEnum.equals(DeviceStatusEnum.ONLINE)) {
			OperationUtil
					.of(specificServService.getDeviceAssignProperty(
							deviceListDO.getDeviceName(),
							"sys_run_mode"
					))
					.ifPresentOrElse(
							dict -> {
								try {
									int stateVal = NumberUtil.round(String.valueOf(dict.get("sys_run_mode")), 0).intValue();
									log.info("device: {}, updateState is: {}", deviceSyncStateVo.getDeviceId(), stateVal);
									deviceListDO.setState(stateVal);
								} catch (Exception e) {
									deviceListDO.setState(DeviceStatusEnum.ONLINE.getCode());
								}
							},
							() -> deviceListDO.setState(DeviceStatusEnum.OFFLINE.getDbCode())
					);
			querySocForOnlineDevice(deviceListDO);
			deviceStatusAndSocDTO.setBatSoc(deviceListDO.getBatSoc());
		}
		deviceStatusAndSocDTO.setState(deviceListDO.getState());
		ActionFlagUtil.assertTrue(updateById(deviceListDO));
		return  deviceStatusAndSocDTO;
	}

	private void querySocForOnlineDevice(DeviceListDO deviceListDO) {
		try {
			TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
					.chooseTimeSeriesDatabaseService(deviceListDO);

			// 获取最近点位数据
			Dict lastPointDict = timeSeriesDatabaseService.lastDataPoint(
					deviceListDO.getDeviceSn(),
					new LinkedList<>(ListUtil.of(TsdbMetricsConstants.BAT_SOC)),
					DateUtil.currentSeconds()
			);

			// 检查数据完整性
			if (lastPointDict.values().stream().anyMatch(Objects::isNull)) {
				log.warn("设备 {} SOC数据存在Null: {}", deviceListDO.getDeviceName(), JSONUtil.toJsonStr(lastPointDict));
				deviceListDO.setBatSoc(null);
			}

			String batSoc = lastPointDict.getStr(TsdbMetricsConstants.BAT_SOC);
			if (StrUtil.isNotBlank(batSoc)) {
				deviceListDO.setBatSoc(NumberUtil.div(new BigDecimal(batSoc), new BigDecimal("100"), 2, RoundingMode.HALF_UP));
			} else {
				deviceListDO.setBatSoc(null);
			}
		} catch (Exception e) {
			log.warn("查询设备 {} SOC失败: {}", deviceListDO.getDeviceName(), e.getMessage());
		}
	}
}
