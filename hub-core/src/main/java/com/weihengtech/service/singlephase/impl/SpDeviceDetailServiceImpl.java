package com.weihengtech.service.singlephase.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.common.exceptions.EcosException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.CommonConstants;
import com.weihengtech.consts.TsdbMetricsConstants;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.pojo.bos.device.DeviceConfigBO;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.device.info.BatteryDataDTO;
import com.weihengtech.pojo.dtos.device.info.DeviceInformationDTO;
import com.weihengtech.pojo.dtos.device.info.EspParameterDTO;
import com.weihengtech.pojo.dtos.device.info.InverterInformationDTO;
import com.weihengtech.pojo.dtos.device.info.MeterInformationDTO;
import com.weihengtech.pojo.dtos.device.info.ParallelInfoDTO;
import com.weihengtech.pojo.dtos.device.info.PowerFlowDTO;
import com.weihengtech.pojo.dtos.device.info.WeiHengBatteryInfoDTO;
import com.weihengtech.pojo.dtos.device.info.WeiHengBatteryInternalDTO;
import com.weihengtech.pojo.dtos.device.info.WeiHengBatterySingleCellDTO;
import com.weihengtech.pojo.dtos.relation.DeviceRelPowerDTO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.device.DeviceRelationService;
import com.weihengtech.service.parser.ParseErrorService;
import com.weihengtech.service.singlephase.SpDeviceDetailService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.service.thirdpart.TimeSeriesDatabaseService;
import com.weihengtech.utils.AsyncResultUtil;
import com.weihengtech.utils.InitUtil;
import com.weihengtech.utils.ModbusRequestUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SpDeviceDetailServiceImpl implements SpDeviceDetailService {

	@Value("${custom.datacenter.name}")
	private String datacenter;

	@Resource
	private DeviceConfigBO deviceConfig;
	@Resource
	private ModbusRequestUtil modbusRequestUtil;
	@Resource
	private DeviceListService deviceListService;
	@Resource
	private StrategyService strategyService;
	@Resource
	private AuthCenterResClient authCenterResClient;
	@Resource
	private DeviceRelationService deviceRelationService;
	@Resource
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;

	@Override
	public PowerFlowDTO queryPowerFlow(String deviceName) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		PowerFlowDTO powerFlowDTO = new PowerFlowDTO();
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		DeviceStatusEnum curState = specificServService.checkDeviceStatus(deviceListDO.getWifiSn());
		if (DeviceStatusEnum.OFFLINE.equals(curState)) {
			log.info("设备id： " + deviceName + " 离线");
			return powerFlowDTO;
		}
		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
				.chooseTimeSeriesDatabaseService(deviceListDO);
		// 获取最近点位数据
		Dict lastPointDict = timeSeriesDatabaseService.lastDataPoint(deviceName, new LinkedList<>(CommonConstants.SP_REALTIME_POWER),
				DateUtil.currentSeconds());
		if (((int) lastPointDict.values().stream().filter(Objects::isNull).count()) > 0) {
			log.warn("首页数据存在Null: {}", JSONUtil.toJsonStr(lastPointDict));
			throw new EcosException(ExceptionEnum.DATA_ASSERT_EXCEPTION);
		}
		String solarPower = StrUtil.toString(NumberUtil.add(lastPointDict.getStr(TsdbMetricsConstants.METER_P_PV), lastPointDict.getStr(TsdbMetricsConstants.METER_PV_P)));
		reflectSetRunData("setSolarPower", solarPower, powerFlowDTO);
		reflectSetRunData("setGridPower", lastPointDict.getStr(TsdbMetricsConstants.AC_P), powerFlowDTO);
		reflectSetRunData("setBatteryPower", lastPointDict.getStr(TsdbMetricsConstants.BAT_P), powerFlowDTO);
		reflectSetRunData("setMeterPower", lastPointDict.getStr(TsdbMetricsConstants.METER_P), powerFlowDTO);
		reflectSetRunData("setEpsPower", lastPointDict.getStr(TsdbMetricsConstants.EPS_P_1), powerFlowDTO);
		setDeviceRunDataBatterySoc(lastPointDict.getStr(TsdbMetricsConstants.BAT_SOC), powerFlowDTO);
		val sysPowerConfig = lastPointDict.getOrDefault(TsdbMetricsConstants.SYS_POWER_CONFIG, "-1").toString();
		powerFlowDTO.setSysPowerConfig(new BigDecimal(sysPowerConfig).intValue());
		powerFlowDTO.setHomePower(NumberUtil.round(
				NumberUtil.sub(NumberUtil.sub(NumberUtil.add(
						powerFlowDTO.getSolarPower(),
						powerFlowDTO.getMeterPower()
				), powerFlowDTO.getBatteryPower()),powerFlowDTO.getEpsPower()),
				0, RoundingMode.HALF_UP
		));
		// 查询关联设备功率
		List<DeviceRelPowerDTO> relationInfo = deviceRelationService.getRelationInfo(deviceListDO.getId());
		List<DeviceRelPowerDTO> relList = relDevicePower(timeSeriesDatabaseService, relationInfo);
		if (CollUtil.isNotEmpty(relList)) {
			relList.sort(Comparator.comparingInt(DeviceRelPowerDTO::hashCode));
		}
		powerFlowDTO.setRelDevicePower(relList);
		return powerFlowDTO;

	}

	/**
	 * 并发查询关联设备功率信息
	 *
	 * @param list 关联设备
	 * @return 关联设备功率信息
	 */
	private List<DeviceRelPowerDTO> relDevicePower(TimeSeriesDatabaseService timeSeriesDatabaseService,
												   List<DeviceRelPowerDTO> list) {
		if (CollUtil.isEmpty(list)) {
			return Collections.emptyList();
		}
		return AsyncResultUtil.multiThreadDone(list, i -> this.getRelDevicePower(timeSeriesDatabaseService, i),
				threadPoolTaskExecutor);
	}

	/**
	 * 查询指定设备功率信息
	 *
	 * @param timeSeriesDatabaseService tsdb服务
	 * @param item 功率数据
	 * @return 设备功率信息
	 */
	private DeviceRelPowerDTO getRelDevicePower(TimeSeriesDatabaseService timeSeriesDatabaseService,
												DeviceRelPowerDTO item) {
		SpecificServService specificServService = strategyService.chooseSpecificServ(Long.parseLong(item.getDeviceId()));
		DeviceStatusEnum deviceStatusEnum = specificServService.checkDeviceStatus(item.getWifiSn());
		if (DeviceStatusEnum.UNKNOWN.equals(deviceStatusEnum) || DeviceStatusEnum.OFFLINE.equals(deviceStatusEnum)) {
			return item;
		}
		// 获取最近点位数据
		Dict lastPointDict = timeSeriesDatabaseService.lastDataPoint(item.getDeviceSn(),
				new LinkedList<>(ListUtil.of(TsdbMetricsConstants.BAT_P, TsdbMetricsConstants.BAT_SOC)), DateUtil.currentSeconds());
		item.setBatteryPower(lastPointDict.getBigDecimal(TsdbMetricsConstants.BAT_P));
		item.setBatterySoc(NumberUtil.div(lastPointDict.getBigDecimal(TsdbMetricsConstants.BAT_SOC),
				new BigDecimal(100), 2, RoundingMode.HALF_UP));
		return item;
	}


	@Override
	public DeviceInformationDTO queryDeviceInformation(String deviceName, Boolean isSyncVersion) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		DeviceInformationDTO deviceInformationDTO = ReflectUtil.newInstance(DeviceInformationDTO.class);
		// 设备不在线
		if (!isDeviceOnline(deviceListDO)) {
			return deviceInformationDTO;
		}
		Map<String, Map<String, Map<String, String>>> config = deviceConfig.getConfig();
		Map<String, Map<String, String>> deviceInformation = config.get("DeviceInformation");
		modbusRequestUtil.processDeviceConfig(deviceInformation, ListUtil.toList("config", "DeviceInformation"),
				deviceName, deviceInformationDTO, 1
		);
		Map<String, Map<String, String>> armInformation = config.get("ARMInformation");
		List<String> emsFilterList = ListUtil.toList("21926-003R", "22308-001R", "22325-003R", "21A22-002R",
				"22606-003R"
		);
		if (emsFilterList.contains(deviceListDO.getEmsSubVersion())) {
			modbusRequestUtil.processDeviceConfig(armInformation, ListUtil.toList("config", "ARMOLDInformation"),
					deviceName, deviceInformationDTO, 1
			);
		} else {
			modbusRequestUtil.processDeviceConfig(armInformation, ListUtil.toList("config", "ARMInformation"),
					deviceName, deviceInformationDTO, 1
			);
		}

		Map<String, Map<String, String>> safetyParameter = config.get("SafetyParameter");
		modbusRequestUtil.processDeviceConfig(safetyParameter, ListUtil.toList("config", "SafetyParameter"),
				deviceName, deviceInformationDTO, 1
		);

		if (isSyncVersion) {
			threadPoolTaskExecutor.execute(() -> deviceListService.syncDeviceVersionByReadResult(deviceInformationDTO, deviceListDO));
		}
		return deviceInformationDTO;
	}

	@Override
	public InverterInformationDTO queryInverterInformation(String deviceName) {

		InverterInformationDTO inverterInformationDTO = ReflectUtil.newInstance(InverterInformationDTO.class);
		// 设备不在线
		if (!isDeviceOnline(deviceName)) {
			return inverterInformationDTO;
		}
		Map<String, Map<String, Map<String, String>>> config = deviceConfig.getConfig();
		// 读取runData
		InverterInformationDTO.RunData runData = ReflectUtil.newInstance(InverterInformationDTO.RunData.class);
		Map<String, Map<String, String>> runningStatus = config.get("RunningStatus");
		modbusRequestUtil.processDeviceConfig(runningStatus, ListUtil.toList("config", "RunningStatus"), deviceName,
				runData, 1
		);
		ParseErrorService parseErrorService = InitUtil.getBean("singleParseErrorImpl", ParseErrorService.class);
		runData.buildParseList(parseErrorService, deviceName);
		inverterInformationDTO.setRunData(runData);
		// 读取dcData
		InverterInformationDTO.DcData dcData = ReflectUtil.newInstance(InverterInformationDTO.DcData.class);
		Map<String, Map<String, String>> dcParameters = config.get("DC_Parameters");
		modbusRequestUtil.processDeviceConfig(dcParameters, ListUtil.toList("config", "DC_Parameters"), deviceName,
				dcData, 1
		);
		dcData.hideVal();
		inverterInformationDTO.setDcData(dcData);
		// 读取acData
		InverterInformationDTO.AcData acData = ReflectUtil.newInstance(InverterInformationDTO.AcData.class);
		Map<String, Map<String, String>> acParameters = config.get("Single_AC_Parameters");
		modbusRequestUtil.processDeviceConfig(acParameters, ListUtil.toList("config", "Single_AC_Parameters"), deviceName,
				acData, 1
		);
		inverterInformationDTO.setAcData(acData);

		return inverterInformationDTO;
	}

	@Override
	public MeterInformationDTO queryMeterInformation(String deviceFlag) {
		MeterInformationDTO meterInformationDTO = ReflectUtil.newInstance(MeterInformationDTO.class);
		// 设备不在线
		if (!isDeviceOnline(deviceFlag)) {
			return meterInformationDTO;
		}
		modbusRequestUtil.processDeviceConfig(deviceConfig.getConfig().get("Single_Meter_Parameter"),
				ListUtil.toList("config", "Single_Meter_Parameter"), deviceFlag, meterInformationDTO, 1
		);
		return meterInformationDTO;
	}

	@Override
	public EspParameterDTO queryEspParameter(String deviceFlag) {
		EspParameterDTO espParameterDTO = ReflectUtil.newInstance(EspParameterDTO.class);
		// 设备不在线
		if (!isDeviceOnline(deviceFlag)) {
			return espParameterDTO;
		}
		modbusRequestUtil.processDeviceConfig(deviceConfig.getConfig().get("Single_EPS_Parameter"),
				ListUtil.toList("config", "Single_EPS_Parameter"), deviceFlag, espParameterDTO, 1
		);
		return espParameterDTO;
	}

	@Override
	public BatteryDataDTO queryBatteryData(String deviceFlag) {
		BatteryDataDTO batteryDataDTO = ReflectUtil.newInstance(BatteryDataDTO.class);
		// 设备不在线
		if (!isDeviceOnline(deviceFlag)) {
			return batteryDataDTO;
		}
		modbusRequestUtil.processDeviceConfig(deviceConfig.getConfig().get("Battery_Parameter"),
				ListUtil.toList("config", "Battery_Parameter"), deviceFlag, batteryDataDTO, 1
		);
		ParseErrorService parseErrorService = strategyService.chooseParseErrServ(deviceFlag);
		batteryDataDTO.buildParseList(parseErrorService, deviceFlag);
		return batteryDataDTO;
	}

	@Override
	public WeiHengBatteryInfoDTO queryWeiHengBatteryInfo(String deviceName) {
		WeiHengBatteryInfoDTO weiHengBatteryInfoDTO = ReflectUtil.newInstance(WeiHengBatteryInfoDTO.class);
		// 设备不在线
		if (!isDeviceOnline(deviceName)) {
			return weiHengBatteryInfoDTO;
		}
		modbusRequestUtil.processDeviceConfig(deviceConfig.getConfig().get("WeiHengBatteryInfo"),
				ListUtil.toList("config", "WeiHengBatteryInfo"), deviceName, weiHengBatteryInfoDTO, 201
		);
		return weiHengBatteryInfoDTO;
	}

	@Override
	public WeiHengBatteryInternalDTO queryWeiHengBatteryInternal(String deviceName) {
		WeiHengBatteryInternalDTO weiHengBatteryInternalDTO = ReflectUtil.newInstance(WeiHengBatteryInternalDTO.class);
		// 设备不在线
		if (!isDeviceOnline(deviceName)) {
			return weiHengBatteryInternalDTO;
		}
		modbusRequestUtil.processDeviceConfig(deviceConfig.getConfig().get("WeiHengBatteryInternalData"),
				ListUtil.toList("config", "WeiHengBatteryInternalData"), deviceName,
				weiHengBatteryInternalDTO,
				201
		);
		return weiHengBatteryInternalDTO;
	}

	@Override
	public WeiHengBatterySingleCellDTO queryWeiHengBatterySingleCell(String deviceName) {
		WeiHengBatterySingleCellDTO weiHengBatterySingleCellDTO = new WeiHengBatterySingleCellDTO();
		weiHengBatterySingleCellDTO.setVoltages(Lists.newArrayList());
		weiHengBatterySingleCellDTO.setTemperatures(Lists.newArrayList());
		weiHengBatterySingleCellDTO.setResistance(Lists.newArrayList());
		// 设备不在线
		if (!isDeviceOnline(deviceName)) {
			return weiHengBatterySingleCellDTO;
		}
		List<Integer> integerList = modbusRequestUtil.postTransparentRead(deviceName, 200, 2, 201);
		if (CollUtil.isEmpty(integerList) || integerList.size() != 2) {
			return weiHengBatterySingleCellDTO;
		}
		Integer batteryInSerial = integerList.get(0);
		Integer temperatureChannels = integerList.get(1);
		List<Integer> voltagesParam = buildSingleCellRequestLength(batteryInSerial, 1000);
		List<Integer> temperatureParam = buildSingleCellRequestLength(temperatureChannels, 2000);
		List<Integer> resistanceParam = buildSingleCellRequestLength(batteryInSerial, 3000);

		weiHengBatterySingleCellDTO.setVoltages(requestByPart(voltagesParam, 1000F, deviceName, 3));
		weiHengBatterySingleCellDTO.setTemperatures(requestByPart(temperatureParam, 10F, deviceName, 2));
		weiHengBatterySingleCellDTO.setResistance(requestByPart(resistanceParam, 1000F, deviceName, 2));

		return weiHengBatterySingleCellDTO;
	}

	@Override
	public Boolean isWeiHengBattery(String deviceName) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		String bmsManufacturer = deviceListDO.getBmsVendor();
		if (StrUtil.isBlank(bmsManufacturer) || "WeiHeng".equals(bmsManufacturer) || "WEIHENG".equals(
				bmsManufacturer.toUpperCase(Locale.ROOT))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	@Override
	public ParallelInfoDTO queryParallelInfo(String deviceName) {
		ParallelInfoDTO res = new ParallelInfoDTO();
		if (!isDeviceOnline(deviceName)) {
			return res;
		}
		List<Integer> integerList = modbusRequestUtil.postTransparentRead(deviceName, 48120, 2, 1);
		if (CollUtil.isNotEmpty(integerList) && integerList.size() == 2) {
			res.setParallelWorkMode(String.valueOf(integerList.get(0)));
			res.setParallelNum(String.valueOf(integerList.get(1)));
		}
		return res;
	}

	private static List<Integer> buildSingleCellRequestLength(Integer num, Integer startAddress) {
		int coefficient = 24;
		int i = num / coefficient;
		int y = num % coefficient;
		List<Integer> result = ListUtil.toLinkedList(startAddress);
		if (i == 0 && y > 0) {
			result.add(y);
		} else {
			for (int j = 1; j <= i; j++) {
				result.add(coefficient);
				result.add(startAddress + j * coefficient);
				if (j == i) {
					if (y > 0) {
						result.add(num % coefficient);
					} else {
						result.remove(result.size() - 1);
					}
				}
			}
		}
		return result;
	}

	private List<String> requestByPart(List<Integer> params, Float mul, String deviceName, Integer scale) {
		List<List<Integer>> splitList = ListUtil.split(params, 2);
		ArrayList<String> result = new ArrayList<>();
		for (List<Integer> addressList : splitList) {
			Integer start = addressList.get(0);
			Integer len = addressList.get(1);
			try {
				List<Integer> integerList = modbusRequestUtil.postTransparentRead(deviceName, start, len, 201);
				result.addAll(integerList.stream()
						.map(i -> NumberUtil.round(NumberUtil.div(i, mul), scale, RoundingMode.HALF_UP).toPlainString())
						.collect(Collectors.toList()));
			} catch (Exception e) {
				return ListUtil.empty();
			}
		}
		return result;
	}

	private boolean isDeviceOnline(DeviceListDO deviceListDO) {
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		DeviceStatusEnum deviceStatusEnum = specificServService.checkDeviceStatus(deviceListDO.getWifiSn());
		return DeviceStatusEnum.ONLINE.equals(deviceStatusEnum);
	}

	private boolean isDeviceOnline(String deviceName) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		return isDeviceOnline(deviceListDO);
	}

	private void setDeviceRunDataBatterySoc(String val, PowerFlowDTO powerFlowDTO) {
		if (StrUtil.isNotBlank(val)) {
			BigDecimal soc = NumberUtil.div(new BigDecimal(val), new BigDecimal("100"), 2, RoundingMode.HALF_UP);
			powerFlowDTO.setBatterySoc(soc);
		} else {
			powerFlowDTO.setBatterySoc(new BigDecimal("0"));
		}
	}

	private void reflectSetRunData(String method, String val, PowerFlowDTO powerFlowDTO) {
		log.info("{} {}", method, val);
		if (StrUtil.isNotBlank(val)) {
			BigDecimal data = NumberUtil.round(new BigDecimal(val), 0, RoundingMode.HALF_UP);
			ReflectUtil.invoke(powerFlowDTO, method,
					(Math.abs(data.doubleValue()) < 10L) ? BigDecimal.ZERO : data
			);
		} else {
			ReflectUtil.invoke(powerFlowDTO, method, BigDecimal.ZERO);
		}
	}
}
