package com.weihengtech.service.singlephase;

import com.weihengtech.pojo.dtos.device.info.BatteryDataDTO;
import com.weihengtech.pojo.dtos.device.info.DeviceInformationDTO;
import com.weihengtech.pojo.dtos.device.info.EspParameterDTO;
import com.weihengtech.pojo.dtos.device.info.InverterInformationDTO;
import com.weihengtech.pojo.dtos.device.info.MeterInformationDTO;
import com.weihengtech.pojo.dtos.device.info.ParallelInfoDTO;
import com.weihengtech.pojo.dtos.device.info.PowerFlowDTO;
import com.weihengtech.pojo.dtos.device.info.WeiHengBatteryInfoDTO;
import com.weihengtech.pojo.dtos.device.info.WeiHengBatteryInternalDTO;
import com.weihengtech.pojo.dtos.device.info.WeiHengBatterySingleCellDTO;

/**
 * <AUTHOR>
 */
public interface SpDeviceDetailService {

	/**
	 * 获取 power flow 页面数据
	 *
	 * @param deviceName    设备标识
	 * @return com.weihengtech.pojo.dtos.device.info.DeviceInformationDTO
	 */
	PowerFlowDTO queryPowerFlow(String deviceName);

	/**
	 * 获取 device information 页面数据
	 *
	 * @param deviceName    设备标识
	 * @param isSyncVersion 是否同步信息
	 * @return com.weihengtech.pojo.dtos.device.info.DeviceInformationDTO
	 */
	DeviceInformationDTO queryDeviceInformation(String deviceName, Boolean isSyncVersion);

	/**
	 * 获取 inverter information 页面数据
	 *
	 * @param deviceName 设备标识
	 * @return com.weihengtech.pojo.dtos.device.info.InverterInformationDTO
	 */
	InverterInformationDTO queryInverterInformation(String deviceName);

	/**
	 * 获取 Meter Information 页面数据
	 *
	 * @param deviceFlag 设备标识
	 * @return com.weihengtech.pojo.dtos.device.info.MeterInformationDTO
	 */
	MeterInformationDTO queryMeterInformation(String deviceFlag);

	/**
	 * 获取 Esp Parameter 页面数据
	 *
	 * @param deviceFlag 设备标识
	 * @return com.weihengtech.pojo.dtos.device.info.EspParameterDTO
	 */
	EspParameterDTO queryEspParameter(String deviceFlag);

	/**
	 * 获取 Battery Data 页面数据
	 *
	 * @param deviceFlag 设备标识
	 * @return com.weihengtech.pojo.dtos.device.info.BatteryDataDTO
	 */
	BatteryDataDTO queryBatteryData(String deviceFlag);

	/**
	 * 为恒电池信息
	 *
	 * @param deviceName 设备标识
	 * @return com.weihengtech.pojo.dtos.device.info.WeiHengBatteryInfoDTO
	 */
	WeiHengBatteryInfoDTO queryWeiHengBatteryInfo(String deviceName);

	/**
	 * 为恒电池额外信息
	 *
	 * @param deviceName 设备标识
	 * @return com.weihengtech.pojo.dtos.device.info.WeiHengBatteryInternalDTO
	 */
	WeiHengBatteryInternalDTO queryWeiHengBatteryInternal(String deviceName);

	/**
	 * 为恒电池SingleCell
	 *
	 * @param deviceName 设备标识
	 * @return com.weihengtech.pojo.dtos.device.info.WeiHengBatterySingleCellDTO
	 */
	WeiHengBatterySingleCellDTO queryWeiHengBatterySingleCell(String deviceName);

	/**
	 * 是否为为恒电池
	 *
	 * @param deviceName 设备标识
	 * @return bool
	 */
	Boolean isWeiHengBattery(String deviceName);

	/**
	 * 查询并机信息
	 *
	 * @param deviceName 设备标识
	 * @return 并机信息
	 */
	ParallelInfoDTO queryParallelInfo(String deviceName);
}
