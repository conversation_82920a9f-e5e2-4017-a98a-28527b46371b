package com.weihengtech.service.singlephase.impl;

import cn.hutool.aop.ProxyUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.weihengtech.api.EcosClientApi;
import com.weihengtech.api.EcosConfigApi;
import com.weihengtech.aspects.TimeCostLogAspect;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.common.exceptions.RoleException;
import com.weihengtech.enums.device.DeviceInfoEncodeTypeEnum;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.pojo.bos.config.OssGlobalConfigBO;
import com.weihengtech.pojo.bos.device.DeviceConfigBO;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.ecos.ClientCustomizeDo;
import com.weihengtech.pojo.dtos.device.DeviceDefaultConfigDto;
import com.weihengtech.pojo.vos.device.DeviceConfigConfigureVO;
import com.weihengtech.pojo.vos.device.DeviceSyncStateVo;
import com.weihengtech.service.device.DeviceBoundService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.singlephase.SpDeviceConfigService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.utils.AesUtil;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.InterfaceUtil;
import com.weihengtech.utils.ModbusParseUtil;
import com.weihengtech.utils.ModbusRequestUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.weihengtech.consts.RoleConstants.ROLE_ADMIN;
import static com.weihengtech.consts.RoleConstants.ROLE_AFTER_SALE;
import static com.weihengtech.consts.RoleConstants.ROLE_AFTER_SALE_ADMIN;
import static com.weihengtech.consts.RoleConstants.ROLE_AGENT;
import static com.weihengtech.consts.RoleConstants.ROLE_DEALER;
import static com.weihengtech.consts.RoleConstants.ROLE_DEV;
import static com.weihengtech.consts.RoleConstants.ROLE_DEV_ADMIN;
import static com.weihengtech.consts.RoleConstants.ROLE_DEV_FIRMWARE;
import static com.weihengtech.consts.RoleConstants.ROLE_DEV_VERSION;
import static com.weihengtech.consts.RoleConstants.ROLE_INSTALLER;
import static com.weihengtech.consts.RoleConstants.ROLE_RETAILER;

/**
 * <AUTHOR>
 */
@Service
public class SpDeviceConfigServiceImpl implements SpDeviceConfigService {

	private static final String MULTI_CHARGE_FLAG = "config:battery_setting:loading_shifting:MultistageChargeAndDischarge";
	private static final String ABANDON_PV_FLAG = "config:battery_setting:loading_shifting:abandonPV";

	@Value("${custom.device.config.path}")
	private String path;

	@Resource
	private ModbusRequestUtil modbusRequestUtil;
	@Resource
	private DeviceConfigBO deviceConfig;
	@Resource
	private DeviceListService deviceListService;
	@Resource
	private DeviceBoundService deviceBoundService;
	@Resource
	private StrategyService strategyService;
	@Resource
	private EcosClientApi ecosClientApi;
	@Resource
	private EcosConfigApi ecosConfigApi;

	@Override
	public Object query(String deviceFlag, String code) {
		// 解码
		String decode = AesUtil.decode(code);
		// 权限校验
		queryRoleFilter(decode, deviceFlag);
		// 构造结果集
		Object o = ReflectUtil.newInstance(path + "." + decode + "DTO");
		// 获取设备信息
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceFlag);
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		DeviceStatusEnum deviceStatusEnum = specificServService.checkDeviceStatus(deviceListDO.getWifiSn());
		// 设备在线进行透传
		if (DeviceStatusEnum.ONLINE.equals(deviceStatusEnum)) {
			int slaveId = "WeiHengBatteryInfoSetting".equals(decode) ? 201 : 1;
			ProxyUtil.proxy(modbusRequestUtil, TimeCostLogAspect.class).processDeviceConfig(
					deviceConfig.getSettings().get(decode), ListUtil.toList("settings", decode), deviceFlag, o,
					slaveId
			);
			// 数据后置处理器
			InterfaceUtil.processConfigParam(decode, o);
		}
		return o;
	}

	@Override
	public DeviceDefaultConfigDto queryDefault(String deviceFlag) {

		DeviceDefaultConfigDto deviceDefaultConfigDto = ReflectUtil.newInstance(DeviceDefaultConfigDto.class);
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceFlag);
		BeanUtil.assertNotNull(deviceListDO);
		DeviceSyncStateVo deviceSyncStateVo = new DeviceSyncStateVo();
		deviceSyncStateVo.setDeviceId(StrUtil.toString(deviceListDO.getId()));
		Integer state = deviceListService.syncDeviceState(deviceSyncStateVo);
		deviceDefaultConfigDto.setDeviceSn(deviceFlag);
		deviceDefaultConfigDto.setState(state);
		deviceDefaultConfigDto.setDsp1SoftwareVersion(deviceListDO.getDsp1SoftwareVersion());
		deviceDefaultConfigDto.setEmsSoftwareVersion(deviceListDO.getEmsSoftwareVersion());

		// 获取电表启用、电表类型、馈网相位
		queryRoleFilter("ProtectFunctionEnable", deviceFlag);
		Map<String, Map<String, Map<String, String>>> settings = deviceConfig.getSettings();
		Map<String, Map<String, String>> protectInformation = settings.get("ProtectFunctionEnable");
		if (state > 0) {
			ProxyUtil.proxy(modbusRequestUtil, TimeCostLogAspect.class).processDeviceConfig(protectInformation, ListUtil.toList( "settings","ProtectFunctionEnableMeter"),
					deviceFlag, deviceDefaultConfigDto, 1
			);
		}
		// meterType点位(1,2)，1代表高8位PV电表类型，2代表低8位电网电表类型
		String meterType = deviceDefaultConfigDto.getMeterType();
		if (StrUtil.isNotBlank(meterType)) {
			int meterTypeInt = Integer.parseInt(meterType);
			deviceDefaultConfigDto.setMeterType(String.format("%d,%d", meterTypeInt / 256, meterTypeInt % 256));
		}
		return deviceDefaultConfigDto;
	}

	private void queryRoleFilter(String decode, String deviceName) {
		String role = UserInfoUtil.currentUserRoleCategory();
		deviceBoundService.onlyProcessBoundDevice(deviceName);
		if ("ParameterSetting".equals(decode)) {
			if (!ROLE_AGENT.equals(role) && !ROLE_DEALER.equals(role) &&
					!ROLE_RETAILER.equals(role) && !ROLE_INSTALLER.equals(role)) {
				throw new RoleException(ExceptionEnum.ROLE_NOT_ALLOWED);
			}
		} else if ("WeiHengBatteryInfoSetting".equals(decode)) {
			List<String> allowList = ListUtil.toList(
					ROLE_ADMIN,
					ROLE_DEV_ADMIN,
					ROLE_DEV,
					ROLE_DEV_FIRMWARE,
					ROLE_DEV_VERSION,
					ROLE_AFTER_SALE_ADMIN,
					ROLE_AFTER_SALE,
					ROLE_AGENT,
					ROLE_DEALER,
					ROLE_RETAILER
			);
			if (!allowList.contains(role)) {
				throw new RoleException(ExceptionEnum.ROLE_NOT_ALLOWED);
			}
		} else if ("EmsArmSettingBatter".equals(decode) || "PvEnableSetting".equals(decode)) {
			List<String> allowList = ListUtil.toList(ROLE_ADMIN, ROLE_DEV_ADMIN, ROLE_AFTER_SALE_ADMIN);
			if (!allowList.contains(role)) {
				throw new RoleException(ExceptionEnum.ROLE_NOT_ALLOWED);
			}
		}
	}

	@Override
	public JSONObject queryInfo() {
		JSONObject json = new JSONObject();
		for (String key : deviceConfig.getSettings().keySet()) {
			json.set(key, AesUtil.encode(key));
		}
		return json;
	}

	@Override
	public void configureDeviceConfig(DeviceConfigConfigureVO deviceConfigConfigureVO, String deviceName) {
		String decode = AesUtil.decode(deviceConfigConfigureVO.getCode());
		configureRoleFilter(decode, deviceName, deviceConfigConfigureVO.getPropNameList());
		List<String> propNameList = deviceConfigConfigureVO.getPropNameList();
		List<String> propValueList = deviceConfigConfigureVO.getPropValueList();
		int slaveId = 1;
		if ("WeiHengBatteryInfoSetting".equals(decode)) {
			slaveId = 201;
		}
		if ("ProtectFunctionEnable".equals(decode) && propNameList.contains("meterType")) {
			int index = propNameList.indexOf("meterType");
			String val = propValueList.get(index);
			// meterType点位(1,2)，1代表高8位PV电表类型，2代表低8位电网电表类型
			if (val.contains(Constants.COMMA)) {
				String[] valArr = val.split(Constants.COMMA);
				String realVal = String.valueOf(256 * Integer.parseInt(valArr[0]) + Integer.parseInt(valArr[1]));
				propValueList.set(index, realVal);
			}
		}
		if ("ParameterSetting".equals(decode) && propNameList.contains("meterType")) {
			int index = propNameList.indexOf("meterType");
			String val = propValueList.get(index);
			// meterType点位(1,2)，1代表高8位PV电表类型，2代表低8位电网电表类型
			if (val.contains(Constants.COMMA)) {
				String[] valArr = val.split(Constants.COMMA);
				String realVal = String.valueOf(256 * Integer.parseInt(valArr[0]) + Integer.parseInt(valArr[1]));
				propValueList.set(index, realVal);
			}
		}
		if ("BatterySetting".equals(decode)) {
			singleFrameForBatterySetting(deviceName, propNameList, propValueList);
		} else {
			ProxyUtil.proxy(modbusRequestUtil, TimeCostLogAspect.class).configureDeviceConfig(
					deviceConfig.getSettings().get(decode), deviceName, propNameList, propValueList, slaveId
			);
		}
	}

	@DSTransactional
	private void singleFrameForBatterySetting(String deviceName, List<String> propNameList, List<String> propValueList) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		if (deviceListDO == null || StrUtil.isBlank(deviceListDO.getWifiSn())) {
			throw new CustomException(ExceptionEnum.DEVICE_NOT_EXIST);
		}
		ClientCustomizeDo clientCustomizeDo = ClientCustomizeDo.builder().deviceId(deviceListDO.getId()).build();
		List<Integer> oldParams = new ArrayList<>(26);
		for (int i = 0; i < 26; i++) {
			oldParams.add(0);
		}
		List<Integer> multiChargeParams = new ArrayList<>(100);
		for (int i = 0; i < 100; i++) {
			multiChargeParams.add(0);
		}
		List<Integer> abandonPvParams = new ArrayList<>(24);
		for (int i = 0; i < 24; i++) {
			abandonPvParams.add(0);
		}

		String wifiSn = deviceListDO.getWifiSn();
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);

		for (int i = 0; i < propNameList.size(); i++) {
			String name = propNameList.get(i);
			Integer val = Integer.parseInt(propValueList.get(i));
			clientCustomizeDo.buildVal(wifiSn, specificServService, name, val, oldParams, multiChargeParams, abandonPvParams);
		}

		Boolean res = specificServService.sendWriteCommand(wifiSn, 1, 41002, 26, oldParams);
		Boolean newRes = true;
		Boolean abandonPvRes = true;
		DataResponse<OssGlobalConfigBO> globalConfig = ecosConfigApi.getGlobalConfig();
		if (globalConfig != null) {
			JSONObject firmware = globalConfig.getData().getFirmware();
			if (firmware.containsKey("EMS_version")) {
				if (firmware.getJSONObject("EMS_version").containsKey(deviceListDO.getEmsSoftwareVersion())) {
					List<String> optionList = firmware.getJSONObject("EMS_version").getBeanList(deviceListDO.getEmsSoftwareVersion(), String.class);
					if (optionList.contains(MULTI_CHARGE_FLAG)) {
						newRes = specificServService.sendWriteCommand(wifiSn, 1, 41043, 100, multiChargeParams);
					}
					if (optionList.contains(ABANDON_PV_FLAG)) {
						abandonPvRes = specificServService.sendWriteCommand(wifiSn, 1, 41143, 24, abandonPvParams);
					}
				}
			}
		}
		if (Boolean.FALSE.equals(res) || Boolean.FALSE.equals(newRes) || Boolean.FALSE.equals(abandonPvRes)) {
			throw new CustomException(ExceptionEnum.DEVICE_CONFIG_CHANGE_ERROR);
		} else {
			ecosClientApi.updateCustomizeInfo(clientCustomizeDo);
		}
	}

	@Override
	public void restartBatSps(String deviceName) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		Boolean res = specificServService.sendWriteCommand(deviceListDO.getWifiSn(), 201, 158, 1,
				ListUtil.toList(1)
		);
		if (!res) {
			throw new CustomException(ExceptionEnum.DEVICE_CONFIG_CHANGE_ERROR);
		}
	}

	@Override
	public String checkSelfTestState(String deviceName) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		List<Integer> res;
		try {
			res = specificServService.sendReadCommand(deviceListDO.getWifiSn(), 1, 30148, 1);
			return ModbusParseUtil.parseForDeviceInfoTypeEnum(DeviceInfoEncodeTypeEnum.U16, res);
		} catch (Exception e) {
			return "";
		}
	}

	private void configureRoleFilter(String decode, String deviceName, List<String> propNameList) {
		String role = UserInfoUtil.currentUserRoleCategory();
		deviceBoundService.onlyProcessBoundDevice(deviceName);

		if ("ParameterSetting".equals(decode)) {
			if (!ROLE_DEALER.equals(role) && !ROLE_AGENT.equals(role) &&
					!ROLE_RETAILER.equals(role) && !ROLE_INSTALLER.equals(role)) {
				throw new RoleException(ExceptionEnum.ROLE_NOT_ALLOWED);
			}
		} else if ("WeiHengBatteryInfoSetting".equals(decode)) {
			List<String> allowList = ListUtil.toList(
					ROLE_ADMIN, ROLE_DEV_ADMIN, ROLE_DEV,
					ROLE_AFTER_SALE_ADMIN, ROLE_AFTER_SALE,
					ROLE_DEV_FIRMWARE, ROLE_DEV_VERSION
			);
			if (!allowList.contains(role)) {
				throw new RoleException(ExceptionEnum.ROLE_NOT_ALLOWED);
			}
		} else {
			boolean isAgent = ROLE_DEALER.equals(role) || ROLE_AGENT.equals(role) || ROLE_RETAILER.equals(role);
			if ("ProtectFunctionEnable".equals(decode)) {
				if (isAgent && (propNameList.contains("PeChk") || propNameList.contains("GridNChk"))) {
					throw new RoleException(ExceptionEnum.ROLE_NOT_ALLOWED);
				}
			} else if ("EmsArmSettingBatter".equals(decode) || "PvEnableSetting".equals(decode)) {
				List<String> allowList = ListUtil.toList(ROLE_ADMIN, ROLE_DEV_ADMIN, ROLE_AFTER_SALE_ADMIN);
				if (!allowList.contains(role)) {
					throw new RoleException(ExceptionEnum.ROLE_NOT_ALLOWED);
				}
			} else {
				List<String> agentCannotWrite = ListUtil.toList("BasicInformation", "DspCalibration", "EnergySetting",
						"EmsArmSetting"
				);
				if (isAgent && agentCannotWrite.contains(decode)) {
					throw new RoleException(ExceptionEnum.ROLE_NOT_ALLOWED);
				}
			}
		}
	}
}
