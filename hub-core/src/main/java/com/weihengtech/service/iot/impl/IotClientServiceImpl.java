package com.weihengtech.service.iot.impl;

import cn.hutool.json.JSONUtil;
import com.influxdb.query.FluxTable;
import com.weihengtech.api.pojo.dtos.DeviceBasicInfoDTO;
import com.weihengtech.api.pojo.vos.ReadBatchDeviceVO;
import com.weihengtech.api.pojo.vos.ReadDeviceVO;
import com.weihengtech.api.pojo.vos.UpgradeDeviceVO;
import com.weihengtech.api.pojo.vos.WriteBatchDeviceVO;
import com.weihengtech.api.pojo.vos.WriteDeviceVO;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.pojo.dtos.device.TuyaDevicePropertyDto;
import com.weihengtech.pojo.dtos.other.TuyaLogQueryDTO;
import com.weihengtech.pojo.dtos.tsdb.TsdbQueryDTO;
import com.weihengtech.pojo.vos.ecos.DeviceEventVO;
import com.weihengtech.sdk.iot.ecos.EcosIotClient;
import com.weihengtech.sdk.iot.ecos.model.EcosIotResponse;
import com.weihengtech.sdk.iot.ecos.model.constant.CloudCategoryEnum;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotDeviceEventResponse;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotGwyFirmwareResponse;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotGwyHistoryResponse;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpInfoResponse;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpStatusResponse;
import com.weihengtech.sdk.iot.ecos.model.request.*;
import com.weihengtech.sdk.iot.ecos.model.response.CloudBatchDevicePropertyResponse;
import com.weihengtech.sdk.iot.ecos.model.response.CloudDeviceDetailResponse;
import com.weihengtech.sdk.iot.ecos.model.tuya.response.TuyaDeviceLogsResponse;
import com.weihengtech.service.iot.IotClientService;
import com.weihengtech.utils.ActionFlagUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * iot服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/8 11:06
 */
@Slf4j
public class IotClientServiceImpl implements IotClientService {

    @Resource
    private EcosIotClient ecosIotClient;

    /** 平台 */
    private final String cloud;

    /**
     * 指定构造方法
     * @param cloud 平台
     */
    public IotClientServiceImpl(String cloud) {
        this.cloud = cloud;
    }

    @Override
    public void speedupDevice(String cloudId) {
        DeviceBasicInfoDTO deviceBasicInfo = getDeviceBasicInfo(cloudId);
        CloudCategoryEnum cloudCategoryEnum = getEnumByCategory(deviceBasicInfo.getCategory());
        EsSpeedupCommandRequest request = new EsSpeedupCommandRequest(cloudId, cloud, cloudCategoryEnum, deviceBasicInfo.getProductId());
        Boolean res = getIotCloudRes(request, ecosIotClient::sendEsSpeedupCommand, "speedupDevice");
        ActionFlagUtil.assertTrue(res);
    }

    @Override
    public List<TuyaDevicePropertyDto> getDeviceInfo(List<String> cloudIds) {
        CloudBatchDevicePropertyRequest request = new CloudBatchDevicePropertyRequest(cloudIds, cloud);
        List<CloudBatchDevicePropertyResponse> res = getIotCloudRes(request, ecosIotClient::queryCloudBatchDeviceProperty, "getDeviceInfo");
        return res.stream()
                .map(i -> TuyaDevicePropertyDto.builder()
                                .id(i.getId())
                                .status(i.getStatus().stream()
                                                .map(k -> TuyaDevicePropertyDto.StatusInfo.builder()
                                                        .code(k.getCode())
                                                        .value(k.getValue())
                                                        .build())
                                        .collect(Collectors.toList()))
                                .build()
                        )
                .collect(Collectors.toList());
    }

    @Override
    public Boolean queryIsOnlineStatus(String cloudId) {
        CloudDeviceOnlineStatusRequest request = new CloudDeviceOnlineStatusRequest(cloudId, cloud);
        return getIotCloudRes(request, ecosIotClient::queryCloudDeviceOnlineStatus, "queryIsOnlineStatus");
    }

    @Override
    public List<Integer> readDevice(ReadDeviceVO req) {
        DeviceBasicInfoDTO deviceBasicInfo = getDeviceBasicInfo(req.getDeviceId());
        CloudCategoryEnum cloudCategoryEnum = getEnumByCategory(deviceBasicInfo.getCategory());
        int funcCode = req.getStartAddress() >= 30000 && req.getStartAddress() <= 40000 ? 4 : 3;
        EsTransportReadCommandRequest request = new EsTransportReadCommandRequest(
                req.getDeviceId(), req.getSlaveId(), req.getStartAddress(), req.getLen(), funcCode,
                cloud, cloudCategoryEnum, deviceBasicInfo.getProductId());
        return getIotCloudRes(request, ecosIotClient :: sendEsTransportReadCommand, "readDevice");
    }

    @Override
    public Map<String, List<Integer>> readBatchDevice(ReadBatchDeviceVO req) {
        DeviceBasicInfoDTO deviceBasicInfo = getDeviceBasicInfo(req.getDeviceId());
        CloudCategoryEnum cloudCategoryEnum = getEnumByCategory(deviceBasicInfo.getCategory());
        List<EsBatchTransportReadCommand> valList = new ArrayList<>(req.getValList().size());
        for (int i = 0; i < req.getValList().size(); i++) {
            ReadDeviceVO item = req.getValList().get(i);
            valList.add(new EsBatchTransportReadCommand(item.getSlaveId(), item.getStartAddress(), item.getLen(),
                    item.getStartAddress() >= 30000 && item.getStartAddress() <= 40000 ? 4 : 3, i));
        }
        EsBatchTransportReadCommandRequest request = new EsBatchTransportReadCommandRequest(req.getDeviceId(),
                valList, cloud, cloudCategoryEnum, deviceBasicInfo.getProductId());
        return getIotCloudRes(request, ecosIotClient :: sendEsBatchTransportReadCommand, "readBatchDevice");
    }

    @Override
    public Boolean writeDevice(WriteDeviceVO req) {
        DeviceBasicInfoDTO deviceBasicInfo = getDeviceBasicInfo(req.getDeviceId());
        CloudCategoryEnum cloudCategoryEnum = getEnumByCategory(deviceBasicInfo.getCategory());
        EsTransportWriteCommandRequest request = new EsTransportWriteCommandRequest(
                req.getDeviceId(), req.getSlaveId(), req.getStartAddress(), req.getLen(),
                req.getValues(), cloud, cloudCategoryEnum, deviceBasicInfo.getProductId()
        );
        return getIotCloudRes(request, ecosIotClient :: sendEsTransportWriteCommand, "writeDevice");
    }

    @Override
    public Boolean writeBatchDevice(WriteBatchDeviceVO req) {
        DeviceBasicInfoDTO deviceBasicInfo = getDeviceBasicInfo(req.getDeviceId());
        CloudCategoryEnum cloudCategoryEnum = getEnumByCategory(deviceBasicInfo.getCategory());
        List<EsBatchTransportWriteCommand> valList = new ArrayList<>(req.getValList().size());
        for (int i = 0; i < req.getValList().size(); i++) {
            WriteDeviceVO item = req.getValList().get(i);
            valList.add(new EsBatchTransportWriteCommand(item.getSlaveId(), item.getStartAddress(), item.getLen(), i, item.getValues()));
        }
        EsBatchTransportWriteCommandRequest request = new EsBatchTransportWriteCommandRequest(
                req.getDeviceId(), valList, cloud, cloudCategoryEnum, deviceBasicInfo.getProductId()
        );
        return getIotCloudRes(request, ecosIotClient :: sendEsBatchTransportWriteCommand, "writeBatchDevice");
    }

    @Override
    public Boolean upgradeDevice(UpgradeDeviceVO req) {
        DeviceBasicInfoDTO deviceBasicInfo = getDeviceBasicInfo(req.getDeviceId());
        CloudCategoryEnum cloudCategoryEnum = getEnumByCategory(deviceBasicInfo.getCategory());
        EsUpgradeCommandRequest request = new EsUpgradeCommandRequest(req.getDeviceId(), req.getUrl(), req.getLen(),
                cloud, cloudCategoryEnum, deviceBasicInfo.getProductId());
        return getIotCloudRes(request, ecosIotClient :: sendEsUpgradeCommand, "upgradeDevice");
    }

    @Override
    public DeviceBasicInfoDTO getDeviceBasicInfo(String cloudId) {
        CloudSingleDeviceDetailRequest request = new CloudSingleDeviceDetailRequest(cloudId, cloud);
        CloudDeviceDetailResponse res = getIotCloudRes(request, ecosIotClient::queryCloudSingleDeviceDetail, "getDeviceBasicInfo");
        return DeviceBasicInfoDTO.builder()
                .id(res.getId())
                .online(res.getOnline())
                .category(res.getCategory())
                .productId(res.getProductId())
                .ip(res.getIp())
                .lat(res.getLat())
                .lon(res.getLon())
                .softwareVersion(res.getSoftwareVersion())
                .hardwareVersion(res.getHardwareVersion())
                .build();
    }

    @Override
    public List<String> getAllOnlineDevice() {
        return getIotCloudRes(new CloudAllOnlineDeviceRequest(cloud), ecosIotClient :: queryCloudAllOnlineDevice, "getAllOnlineDevice");
    }

    @Override
    public String getDeviceIp(String cloudId) {
        CloudDeviceIpRequest request = new CloudDeviceIpRequest(cloudId, cloud);
        return getIotCloudRes(request, ecosIotClient :: queryCloudDeviceIp, "getDeviceIp");
    }

    @Override
    public CpInfoResponse getChargerInfo(String cloudId) {
        CpInfoRequest request = new CpInfoRequest(cloudId, cloud, CloudCategoryEnum.CHARGING_POINT);
        return getIotCloudRes(request, ecosIotClient :: queryCpInfo, "getChargerInfo");
    }

    @Override
    public CpStatusResponse getChargerStatus(String cloudId) {
        CpStatusRequest request = new CpStatusRequest(cloudId, cloud, CloudCategoryEnum.CHARGING_POINT);
        return getIotCloudRes(request, ecosIotClient :: queryCpStatus, "getChargerStatus");
    }

    @Override
    public TuyaDeviceLogsResponse getDeviceLogs(TuyaLogQueryDTO req) {
        CloudTuyaDeviceLogsRequest request = new CloudTuyaDeviceLogsRequest(req.getCloudId(), cloud,
                req.getType(), req.getStartTime(), req.getEndTime(),
                req.getSize(), req.getCodes(), req.getQueryType(), req.getStartRowKey());
        return getIotCloudRes(request, ecosIotClient :: queryCloudTuyaDeviceLogs, "getDeviceLogs");
    }

    @Override
    public FluxTable queryIotLastPoint(TsdbQueryDTO req) {
        CloudIotLastPointRequest request = new CloudIotLastPointRequest(req.getCloudId(), cloud,
                req.getStartTime(), req.getEndTime(), req.getMetricList(),
                Optional.ofNullable(req.getBatteryIdx()).map(String :: valueOf).orElse("0"));
        return getIotCloudRes(request, ecosIotClient::queryIotLastPoint, "queryIotLastPoint");
    }

    @Override
    public FluxTable queryIotWithSample(TsdbQueryDTO req) {
        CloudIotWithSampleRequest request = new CloudIotWithSampleRequest(req.getCloudId(), cloud,
                req.getStartTime(), req.getEndTime(), req.getMetricList(), req.getTimes(),
                "first", Optional.ofNullable(req.getBatteryIdx()).map(String :: valueOf).orElse("0"));
        return getIotCloudRes(request, ecosIotClient :: queryIotWithSample, "queryIotWithSample");
    }

    @Override
    public FluxTable queryIotWithoutSample(TsdbQueryDTO req) {
        CloudIotWithoutSampleRequest request = new CloudIotWithoutSampleRequest(req.getCloudId(), cloud,
                req.getStartTime(), req.getEndTime(), req.getMetricList(),
                Optional.ofNullable(req.getBatteryIdx()).map(String :: valueOf).orElse("0"));
        return getIotCloudRes(request, ecosIotClient :: queryIotWithoutSample, "queryIotWithoutSample");
    }

    @Override
    public FluxTable queryIotDelta(TsdbQueryDTO param) {
        CloudIotDeltaRequest request = new CloudIotDeltaRequest(param.getCloudId(), cloud,
                param.getStartTime(), param.getEndTime(), param.getMetricList(), param.getTimes(), "0");
        return getIotCloudRes(request, ecosIotClient::queryIotDelta, "queryIotDelta");
    }

    @Override
    public void sendEsResetCommand(String cloudId) {
        DeviceBasicInfoDTO deviceBasicInfo = getDeviceBasicInfo(cloudId);
        CloudCategoryEnum cloudCategoryEnum = getEnumByCategory(deviceBasicInfo.getCategory());
        EsResetCommandRequest request = new EsResetCommandRequest(cloudId, cloud,
                cloudCategoryEnum, deviceBasicInfo.getProductId());
        getIotCloudRes(request, ecosIotClient :: sendEsResetCommand, "sendEsResetCommand");
    }

    @Override
    public IotDeviceEventResponse queryCloudIotDeviceEvent(String cloudId, DeviceEventVO pageInfo) {
        CloudIotDeviceEventRequest request = new CloudIotDeviceEventRequest(cloudId, cloud, pageInfo.getLevel(),
                pageInfo.getStartTime() * 1000, pageInfo.getEndTime() * 1000, pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getContent());
        return getIotCloudRes(request, ecosIotClient :: queryCloudIotDeviceEvent, "queryCloudIotDeviceEvent");
    }

    @Override
    public String upgradeGwy(UpgradeDeviceVO param) {
        DeviceBasicInfoDTO deviceBasicInfo = getDeviceBasicInfo(param.getDeviceId());
        CloudCategoryEnum cloudCategoryEnum = getEnumByCategory(deviceBasicInfo.getCategory());
        GwyUpgradeCommandRequest request = new GwyUpgradeCommandRequest(param.getDeviceId(), param.getUrl(),
                param.getIsGateway(), cloud, cloudCategoryEnum, deviceBasicInfo.getProductId());
        return getIotCloudRes(request, ecosIotClient :: sendGwyUpgradeCommand, "upgradeGwy");
    }

    @Override
    public String queryGwyStatus(String deviceSn, String taskId) {
        GwyQueryStatusCommandRequest request = new GwyQueryStatusCommandRequest(cloud, deviceSn, taskId);
        return getIotCloudRes(request, ecosIotClient :: gwyQueryStatus, "queryGwyStatus");
    }

    @Override
    public List<IotGwyHistoryResponse> queryGwyHistory(String deviceSn) {
        GwyQueryHistoryCommandRequest request = new GwyQueryHistoryCommandRequest(cloud, deviceSn);
        return getIotCloudRes(request, ecosIotClient :: gwyQueryHistory, "queryGwyHistory");
    }

    @Override
    public List<IotGwyFirmwareResponse> queryGwyFirmwareList(String productName) {
        GwyQueryFirmwareCommandRequest request = new GwyQueryFirmwareCommandRequest(cloud, productName);
        return getIotCloudRes(request, ecosIotClient :: gwyQueryFirmwareList, "queryGwyFirmwareList");
    }

    @Override
    public Boolean changeDeviceCategory(String deviceSn, String wifiSn, String category) {
        DeviceBasicInfoDTO deviceBasicInfo = getDeviceBasicInfo(wifiSn);
        CloudCategoryEnum cloudCategoryEnum = getEnumByCategory(deviceBasicInfo.getCategory());
        CloudIotUpdateProductCategoryRequest cloudIotUpdateProductCategoryRequest = new CloudIotUpdateProductCategoryRequest(deviceSn, cloud, category, cloudCategoryEnum, deviceBasicInfo.getProductId());
        return getIotCloudRes(cloudIotUpdateProductCategoryRequest, ecosIotClient :: sendUpdateProductType, "changeDeviceCategory");
    }

    /**
     * 执行iot接口并获取结果数据
     *
     * @param t 入参
     * @param function 执行方法
     * @param <P> 入参类型
     * @param <T> 返参类型
     * @return 结果
     */
    private <P, T> T getIotCloudRes(P t, Function<P, EcosIotResponse<T>> function, String funcName) {
        EcosIotResponse<T> response = function.apply(t);
        if (!response.getSuccess()) {
            throw new CustomException(ExceptionEnum.UNKNOWN_EXCEPTION);
        }
        String resStr = response.getResult() instanceof Boolean ? String.valueOf(response.getResult()) : JSONUtil.toJsonStr(response.getResult());
        log.info("getIotCloudRes func is {}, param is : {}, res is {}", funcName, JSONUtil.toJsonStr(t), resStr);
        return response.getResult();
    }
}
