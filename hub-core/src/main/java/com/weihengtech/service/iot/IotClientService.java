package com.weihengtech.service.iot;

import com.influxdb.query.FluxTable;
import com.weihengtech.api.pojo.dtos.DeviceBasicInfoDTO;
import com.weihengtech.api.pojo.vos.ReadBatchDeviceVO;
import com.weihengtech.api.pojo.vos.ReadDeviceVO;
import com.weihengtech.api.pojo.vos.UpgradeDeviceVO;
import com.weihengtech.api.pojo.vos.WriteBatchDeviceVO;
import com.weihengtech.api.pojo.vos.WriteDeviceVO;
import com.weihengtech.pojo.dtos.device.TuyaDevicePropertyDto;
import com.weihengtech.pojo.dtos.other.TuyaLogQueryDTO;
import com.weihengtech.pojo.dtos.tsdb.TsdbQueryDTO;
import com.weihengtech.pojo.vos.ecos.DeviceEventVO;
import com.weihengtech.sdk.iot.ecos.model.constant.CloudCategoryEnum;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotDeviceEventResponse;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotGwyFirmwareResponse;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotGwyHistoryResponse;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpInfoResponse;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpStatusResponse;
import com.weihengtech.sdk.iot.ecos.model.tuya.response.TuyaDeviceLogsResponse;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * iot服务
 *
 * <AUTHOR>
 * @date 2023/11/8 14:23
 * @version 1.0
 */
public interface IotClientService {

	void speedupDevice(String cloudId);

	List<TuyaDevicePropertyDto> getDeviceInfo(List<String> cloudIds);

	Boolean queryIsOnlineStatus(String cloudId);

	List<Integer> readDevice(ReadDeviceVO tuyaReadDeviceVo);

	Map<String, List<Integer>> readBatchDevice(ReadBatchDeviceVO req);

	Boolean writeDevice(WriteDeviceVO req);

	Boolean writeBatchDevice(WriteBatchDeviceVO req);

	Boolean upgradeDevice(UpgradeDeviceVO tuyaUpgradeDeviceVo);

	DeviceBasicInfoDTO getDeviceBasicInfo(String cloudId);

	List<String> getAllOnlineDevice();

	String getDeviceIp(String cloudId);

	CpInfoResponse getChargerInfo(String cloudId);

	CpStatusResponse getChargerStatus(String cloudId);

	TuyaDeviceLogsResponse getDeviceLogs(TuyaLogQueryDTO logQueryDTO);

	FluxTable queryIotLastPoint(TsdbQueryDTO param);

	FluxTable queryIotWithSample(TsdbQueryDTO param);

	FluxTable queryIotWithoutSample(TsdbQueryDTO param);

	FluxTable queryIotDelta(TsdbQueryDTO param);

	void sendEsResetCommand(String cloudId);

	IotDeviceEventResponse queryCloudIotDeviceEvent(String cloudId, DeviceEventVO pageInfo);

	String upgradeGwy(UpgradeDeviceVO param);

	String queryGwyStatus(String deviceSn, String taskId);

	List<IotGwyHistoryResponse> queryGwyHistory(String deviceSn);

	List<IotGwyFirmwareResponse> queryGwyFirmwareList(String productName);

	Boolean changeDeviceCategory(String deviceSn, String wifiSn, String category);


	/**
	 * 映射品类枚举
	 *
	 * @param category 品类
	 * @return 品类枚举
	 */
	default CloudCategoryEnum getEnumByCategory(String category) {
		return Arrays.stream(CloudCategoryEnum.values())
				.filter(i -> i.getValue().equals(category))
				.findFirst()
				.orElse(null);
	}

}
