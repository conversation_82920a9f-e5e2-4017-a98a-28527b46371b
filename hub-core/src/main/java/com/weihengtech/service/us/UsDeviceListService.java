package com.weihengtech.service.us;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.device.DeviceStatusAndSocDTO;
import com.weihengtech.pojo.vos.device.DeviceSyncStateVo;

/**
 * <AUTHOR>
 */
public interface UsDeviceListService extends IService<DeviceListDO> {

    /**
     * 同步设备状态
     *
     * @param deviceSyncStateVo 设备id
     * @return 设备状态
     */
    Integer syncDeviceState(DeviceSyncStateVo deviceSyncStateVo);

    /**
     * 同步设备状态和SOC
     *
     * @param deviceSyncStateVo 要同步的设备id
     * @return 同步后的状态和SOC
     */
    DeviceStatusAndSocDTO syncDeviceStateAndSoc(DeviceSyncStateVo deviceSyncStateVo);
}
