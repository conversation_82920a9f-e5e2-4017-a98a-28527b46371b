package com.weihengtech.service.ecos;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.ecos.ClientGlobalVersionDo;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.dtos.ecos.EcosClientGlobalVersionDto;
import com.weihengtech.pojo.dtos.ecos.GlobalVersionDto;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionAddVo;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionUpdateVo;

/**
 * <AUTHOR>
 */
public interface ClientGlobalVersionService extends IService<ClientGlobalVersionDo> {

	/**
	 * 分页查询客户端版本信息
	 *
	 * @param pageNum  页码
	 * @param pageSize 每页数量
	 * @return 分页信息列表
	 */
	PageInfoDTO<EcosClientGlobalVersionDto> pageClientVersion(Integer pageNum, Integer pageSize);

	/**
	 * 新增客户端版本信息
	 *
	 * @param ecosGlobalVersionAddVo 客户端版本信息入参
	 */
	void addClientVersion(EcosGlobalVersionAddVo ecosGlobalVersionAddVo);

	/**
	 * 更新客户端版本信息
	 *
	 * @param ecosGlobalVersionUpdateVo 客户端版本信息更新入参
	 */
	void updateClientVersion(EcosGlobalVersionUpdateVo ecosGlobalVersionUpdateVo);

	/**
	 * ecos 最新的版本信息
	 */
	GlobalVersionDto latestVersion(String language);
}
