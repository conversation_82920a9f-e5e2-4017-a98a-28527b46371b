package com.weihengtech.service.ecos.impl;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.weihengtech.dao.ecos.ClientGlobalVersionMapper;
import com.weihengtech.pojo.dos.ecos.ClientGlobalVersionDo;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.dtos.ecos.EcosClientGlobalVersionDto;
import com.weihengtech.pojo.dtos.ecos.GlobalVersionDto;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionAddVo;
import com.weihengtech.pojo.vos.ecos.EcosGlobalVersionUpdateVo;
import com.weihengtech.service.ecos.ClientGlobalVersionService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.LocaleUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ClientGlobalVersionServiceImpl extends ServiceImpl<ClientGlobalVersionMapper, ClientGlobalVersionDo>
		implements
		ClientGlobalVersionService {

	@Override
	public PageInfoDTO<EcosClientGlobalVersionDto> pageClientVersion(Integer pageNum, Integer pageSize) {
		PageHelper.startPage(pageNum, pageSize);
		List<ClientGlobalVersionDo> doList = this
				.list(Wrappers.<ClientGlobalVersionDo>lambdaQuery().orderByDesc(ClientGlobalVersionDo::getCreateTime));
		PageInfo<ClientGlobalVersionDo> pageInfo = new PageInfo<>(doList);

		PageInfoDTO<EcosClientGlobalVersionDto> pageInfoDto = new PageInfoDTO<>();
		pageInfoDto.setTotalPages(pageInfo.getPages());
		pageInfoDto.setTotalCount(pageInfo.getTotal());
		pageInfoDto.setData(doList.stream().map(clientGlobalVersionDo -> {
			EcosClientGlobalVersionDto ecosClientGlobalVersionPageDto = new EcosClientGlobalVersionDto();
			CglibUtil.copy(clientGlobalVersionDo, ecosClientGlobalVersionPageDto);
			return ecosClientGlobalVersionPageDto;
		}).collect(Collectors.toList()));
		return pageInfoDto;
	}

	@Override
	@DSTransactional
	public void addClientVersion(EcosGlobalVersionAddVo ecosGlobalVersionAddVo) {
		ClientGlobalVersionDo clientGlobalVersionDo = new ClientGlobalVersionDo();
		CglibUtil.copy(ecosGlobalVersionAddVo, clientGlobalVersionDo);
		clientGlobalVersionDo.setCreateTime(System.currentTimeMillis());
		clientGlobalVersionDo.setChinese(ecosGlobalVersionAddVo.getZhCn());
		clientGlobalVersionDo.setEnglish(ecosGlobalVersionAddVo.getEnUs());
		clientGlobalVersionDo.setIsImportant(ecosGlobalVersionAddVo.getFlag() == 1);
		ActionFlagUtil.assertTrue(this.save(clientGlobalVersionDo));
	}

	@Override
	@DSTransactional
	public void updateClientVersion(EcosGlobalVersionUpdateVo ecosGlobalVersionUpdateVo) {
		ClientGlobalVersionDo clientGlobalVersionDo = this.getById(ecosGlobalVersionUpdateVo.getId());
		clientGlobalVersionDo.setCreateTime(System.currentTimeMillis());
		BeanUtil.assertNotNull(clientGlobalVersionDo);
		CglibUtil.copy(ecosGlobalVersionUpdateVo, clientGlobalVersionDo);
		clientGlobalVersionDo.setChinese(ecosGlobalVersionUpdateVo.getZhCn());
		clientGlobalVersionDo.setEnglish(ecosGlobalVersionUpdateVo.getEnUs());
		clientGlobalVersionDo.setIsImportant(ecosGlobalVersionUpdateVo.getFlag() == 1);
		ActionFlagUtil.assertTrue(this.updateById(clientGlobalVersionDo));
	}

    @Override
    public GlobalVersionDto latestVersion(String language) {
		GlobalVersionDto globalVersionDto = new GlobalVersionDto();
		ClientGlobalVersionDo clientGlobalVersionDo = lambdaQuery().orderByDesc(ClientGlobalVersionDo::getId).last("LIMIT 1").one();
		ClientGlobalVersionDo forceVersionDo = lambdaQuery().orderByDesc(ClientGlobalVersionDo::getId).eq(ClientGlobalVersionDo::getFlag, 2).last("LIMIT 1").one();
		BeanUtil.assertNotNull(clientGlobalVersionDo);
		CglibUtil.copy(clientGlobalVersionDo, globalVersionDto);
		if (null != forceVersionDo) {
			globalVersionDto.setPreForceAndroidVersion(forceVersionDo.getAndroidVersion());
			globalVersionDto.setPreForceIosVersion(forceVersionDo.getIosVersion());
		} else {
			globalVersionDto.setPreForceAndroidVersion("");
			globalVersionDto.setPreForceIosVersion("");
		}
		try {
			String methodName = LocaleUtil.mapLocaleToDatabaseGetMethod(language);
			String content = ReflectUtil.invoke(clientGlobalVersionDo, "getZhCn".equals(methodName) ? methodName : "getEnUs").toString();
			globalVersionDto.setContent(content);
		} catch (Exception e) {
			globalVersionDto.setContent(clientGlobalVersionDo.getEnUs());
		}
		return globalVersionDto;
    }
}
