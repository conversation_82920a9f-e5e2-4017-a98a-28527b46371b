package com.weihengtech.service.ecos;

import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.dtos.ecos.DeviceEventPageDTO;
import com.weihengtech.pojo.dtos.ecos.DeviceEventStatisticsDTO;
import com.weihengtech.pojo.vos.ecos.DeviceEventVO;
import com.weihengtech.pojo.vos.tuya.TuyaDeviceEventQueryVo;
import com.weihengtech.pojo.vos.tuya.TuyaDeviceEventVo;

/**
 * <AUTHOR>
 */
public interface DeviceEventService {

	/**
	 * 设备事件分页
	 *
	 * @param deviceEventVO 设备事件分页查询参数
	 * @return 分页数据
	 */
	PageInfoDTO<DeviceEventPageDTO> pageDeviceEvent(DeviceEventVO deviceEventVO);

	/**
	 * 设备事件分页（支持iot平台事件列表）
	 *
	 * @param deviceEventVO 设备事件分页查询参数
	 * @return 分页数据
	 */
	PageInfoDTO<DeviceEventPageDTO> pageDeviceEventV2(DeviceEventVO deviceEventVO);

	/**
	 * 统计设备事件
	 *
	 * @return 统计信息
	 */
	DeviceEventStatisticsDTO statisticsDeviceEvent();

	/**
	 * 设备事件保存
	 */
	void save(TuyaDeviceEventVo tuyaDeviceEventVo);

	/**
	 * 是否存在事件
	 */
	Boolean exist(TuyaDeviceEventQueryVo tuyaDeviceEventQueryVo);
}
