package com.weihengtech.service.ecos.impl;

import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.dao.ecos.ClientDeviceConfigMapper;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.ecos.ClientDeviceConfigDo;
import com.weihengtech.pojo.vos.ecos.EcosDeviceConfigUpdateVo;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.ecos.ClientDeviceConfigService;
import com.weihengtech.utils.ActionFlagUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class ClientDeviceConfigServiceImpl extends ServiceImpl<ClientDeviceConfigMapper, ClientDeviceConfigDo> implements ClientDeviceConfigService {

	@Resource
	private DeviceListService deviceListService;

	@Override
	@Transactional
	public void updateOrInsertDeviceConfig(EcosDeviceConfigUpdateVo ecosDeviceConfigUpdateVo) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(ecosDeviceConfigUpdateVo.getDeviceName());
		ClientDeviceConfigDo clientDeviceConfigDo = getById(deviceListDO.getId());

		if (null == clientDeviceConfigDo) {
			clientDeviceConfigDo = new ClientDeviceConfigDo();
			clientDeviceConfigDo.setId(deviceListDO.getId());
			CglibUtil.copy(ecosDeviceConfigUpdateVo, clientDeviceConfigDo);
			ActionFlagUtil.assertTrue(save(clientDeviceConfigDo));
		} else {
			CglibUtil.copy(ecosDeviceConfigUpdateVo, clientDeviceConfigDo);
			ActionFlagUtil.assertTrue(updateById(clientDeviceConfigDo));
		}
	}
}
