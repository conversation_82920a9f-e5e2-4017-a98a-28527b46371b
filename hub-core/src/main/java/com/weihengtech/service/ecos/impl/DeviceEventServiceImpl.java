package com.weihengtech.service.ecos.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.influxdb.client.domain.Source;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.consts.RoleConstants;
import com.weihengtech.dao.ecos.DeviceEventMapper;
import com.weihengtech.enums.device.DeviceDatasourceEnum;
import com.weihengtech.enums.device.DeviceEventLevelEnum;
import com.weihengtech.pojo.bos.ecos.DeviceEventPageBO;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.ecos.DeviceEventPageDTO;
import com.weihengtech.pojo.dtos.ecos.DeviceEventStatisticsDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.ecos.DeviceEventVO;
import com.weihengtech.pojo.vos.tuya.TuyaDeviceEventQueryVo;
import com.weihengtech.pojo.vos.tuya.TuyaDeviceEventVo;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotDeviceEvent;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotDeviceEventResponse;
import com.weihengtech.service.device.DeviceBoundService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.ecos.DeviceEventService;
import com.weihengtech.service.iot.IotClientService;
import com.weihengtech.utils.LocaleUtil;
import com.weihengtech.utils.OperatingRecordUtil;
import com.weihengtech.utils.OperationUtil;
import org.apache.commons.codec.language.bm.Languages;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.servlet.LocaleResolver;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.weihengtech.consts.RoleConstants.ROLE_AGENT;
import static com.weihengtech.consts.RoleConstants.ROLE_DEALER;
import static com.weihengtech.consts.RoleConstants.ROLE_RETAILER;
import static com.weihengtech.consts.RoleConstants.ROLE_SALE;

/**
 * <AUTHOR>
 */
@Service
public class DeviceEventServiceImpl implements DeviceEventService {

	@Resource
	private DeviceEventMapper deviceEventMapper;
	@Resource
	private DeviceBoundService deviceBoundService;
	@Resource
	private DeviceListService deviceListService;
	@Resource
	private StringRedisTemplate stringRedisTemplate;
	@Resource
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;
	@Resource
	private IotClientService whIotClient;

	@Override
	public PageInfoDTO<DeviceEventPageDTO> pageDeviceEvent(DeviceEventVO deviceEventVO) {
		deviceEventVO.checkParams();
		String role = UserInfoUtil.currentUserRoleCategory();
		List<String> deviceNameList = new ArrayList<>();
		if (RoleConstants.NEED_FILTER_ROLE.contains(role)) {
			OperationUtil.of(deviceEventVO.getDeviceName()).ifPresentOrElse(deviceName -> {
				deviceBoundService.onlyProcessBoundDevice(deviceName);
				deviceNameList.add(deviceName);
			}, () -> {
				List<String> deviceNames = deviceBoundService.filterBoundDevice();
				if (CollUtil.isNotEmpty(deviceNames)) {
					deviceNameList.addAll(deviceNames);
				}
			});
		}
		PageInfoDTO<DeviceEventPageDTO> pageInfoDTO = new PageInfoDTO<>();
		pageInfoDTO.setTotalPages(0);
		pageInfoDTO.setTotalCount(0L);
		pageInfoDTO.setData(ListUtil.empty());

		List<DeviceEventPageBO> deviceEventPageList;

		if (CollUtil.isEmpty(deviceNameList) && RoleConstants.NEED_FILTER_ROLE.contains(role)) {
			return pageInfoDTO;
		} else if (CollUtil.isNotEmpty(deviceNameList) && RoleConstants.NEED_FILTER_ROLE.contains(role)) {
			PageHelper.startPage(deviceEventVO.getPageNum(), deviceEventVO.getPageSize());
			deviceEventPageList = deviceEventMapper.selectConditionAgentAndEngineer(deviceEventVO, deviceNameList);
		} else {
			PageHelper.startPage(deviceEventVO.getPageNum(), deviceEventVO.getPageSize());
			deviceEventPageList = deviceEventMapper.selectCondition(deviceEventVO);
		}

		PageInfo<DeviceEventPageBO> pageInfo = new PageInfo<>(deviceEventPageList);
		pageInfoDTO.setTotalPages(pageInfo.getPages());
		pageInfoDTO.setTotalCount(pageInfo.getTotal());
		pageInfoDTO.setData(deviceEventPageList.stream().map(event -> {
			DeviceEventPageDTO deviceEventDTO = new DeviceEventPageDTO();
			BeanUtil.copyProperties(event, deviceEventDTO);
			deviceEventDTO.setUploadTime(event.getUploadTime());
			deviceEventDTO.setSubsystem(event.getSubsystem());
			return deviceEventDTO;
		}).collect(Collectors.toList()));

		return pageInfoDTO;
	}

	@Override
	public PageInfoDTO<DeviceEventPageDTO> pageDeviceEventV2(DeviceEventVO deviceEventVO) {
		// 校验参数
		deviceEventVO.checkParams();
		// 权限校验
		Assert.hasLength(deviceEventVO.getDeviceName(),  "deviceName cannot be null");
		deviceBoundService.onlyProcessBoundDevice(deviceEventVO.getDeviceName());
		// 判断国际化
		deviceEventVO.setIsChinese("zh_CN".equals(LocaleUtil.getLanguage()));
		DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(deviceEventVO.getDeviceName());
		if (DeviceDatasourceEnum.WH.getDatasource() == deviceInfo.getDataSource()) {
			// 自研棒子请求iot接口
			return pageEventByIot(deviceInfo.getWifiSn(), deviceEventVO);
		} else {
			// 其他设备默认走本地数据库
			return pageEventByLocal(deviceEventVO);
		}
	}

	/** 本地事件分页 */
	private PageInfoDTO<DeviceEventPageDTO> pageEventByLocal(DeviceEventVO param) {
		PageInfoDTO<DeviceEventPageDTO> pageInfoDTO = new PageInfoDTO<>();
		PageHelper.startPage(param.getPageNum(), param.getPageSize());
		List<DeviceEventPageBO> deviceEventPageList = deviceEventMapper.selectCondition(param);
		PageInfo<DeviceEventPageBO> pageInfo = new PageInfo<>(deviceEventPageList);
		pageInfoDTO.setTotalPages(pageInfo.getPages());
		pageInfoDTO.setTotalCount(pageInfo.getTotal());
		pageInfoDTO.setData(deviceEventPageList.stream().map(event -> {
			DeviceEventPageDTO deviceEventDTO = new DeviceEventPageDTO();
			BeanUtil.copyProperties(event, deviceEventDTO);
			deviceEventDTO.setUploadTime(event.getUploadTime());
			deviceEventDTO.setSubsystem(event.getSubsystem());
			return deviceEventDTO;
		}).collect(Collectors.toList()));
		return pageInfoDTO;
	}

	/** iot事件分页 */
	private PageInfoDTO<DeviceEventPageDTO> pageEventByIot(String wifiSn, DeviceEventVO param) {
		if (param.getStartTime() == null) {
			// 默认三天
			param.setEndTime(DateUtil.currentSeconds());
			param.setStartTime(DateUtil.offsetDay(new Date(), -3).getTime() / 1000);
		}
		IotDeviceEventResponse response = whIotClient.queryCloudIotDeviceEvent(wifiSn, param);
		if (CollUtil.isEmpty(response.getEventOpenVoList())) {
			return new PageInfoDTO<>();
		}
		List<IotDeviceEvent> eventList = response.getEventOpenVoList();
		List<DeviceEventPageDTO> eventInfoList = eventList.stream()
				.map(i -> DeviceEventPageDTO.builder()
						.deviceName(param.getDeviceName())
						.subsystem(i.getSubsystem())
						.level(i.getLevel())
						.code(i.getCode() == null ? null : Integer.parseInt(i.getCode()))
						.chinese(i.getChinese())
						.english(i.getEnglish())
						.eventAction(i.getEventAction())
						.uploadTime(i.getUploadTime() / 1000)
						.build())
				.collect(Collectors.toList());
		int totalPages = response.getTotal() % response.getPageSize() == 0 ? response.getTotal() / response.getPageSize() :
				response.getTotal() / response.getPageSize() + 1;
		return new PageInfoDTO<>(totalPages, (long) response.getTotal(), eventInfoList);
	}

	@Override
	public DeviceEventStatisticsDTO statisticsDeviceEvent() {

		String role = UserInfoUtil.currentUserRoleCategory();
		List<String> deviceNameList = null;
		List<String> fileRoleList = ListUtil.toList(ROLE_AGENT, ROLE_DEALER, ROLE_RETAILER, ROLE_SALE);
		if (fileRoleList.contains(role)) {
			deviceNameList = deviceBoundService.filterBoundDevice();
		}

		DeviceEventStatisticsDTO deviceEventStatisticsDTO = new DeviceEventStatisticsDTO();
		deviceEventStatisticsDTO
				.setFault(deviceEventMapper.selectLevelCount(DeviceEventLevelEnum.FAULT.getEnglish(), deviceNameList));
		deviceEventStatisticsDTO
				.setAlarm(deviceEventMapper.selectLevelCount(DeviceEventLevelEnum.ALARM.getEnglish(), deviceNameList));
		deviceEventStatisticsDTO
				.setEvent(deviceEventMapper.selectLevelCount(DeviceEventLevelEnum.EVENT.getEnglish(), deviceNameList));
		return deviceEventStatisticsDTO;
	}

	@Override
	public void save(TuyaDeviceEventVo tuyaDeviceEventVo) {
		deviceEventMapper.save(tuyaDeviceEventVo);
	}

	@Override
	public Boolean exist(TuyaDeviceEventQueryVo tuyaDeviceEventQueryVo) {
		return null != deviceEventMapper.selectOne(tuyaDeviceEventQueryVo);
	}
}
