package com.weihengtech.service.ecos.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.weihengtech.dao.ecos.ClientAgreementVersionMapper;
import com.weihengtech.pojo.dos.ecos.ClientAgreementVersionDo;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.service.ecos.ClientAgreementVersionService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class ClientAgreementVersionServiceImpl extends ServiceImpl<ClientAgreementVersionMapper, ClientAgreementVersionDo> implements ClientAgreementVersionService {
	@Override
	public PageInfoDTO<ClientAgreementVersionDo> pageAgreementVersion(Integer pageNum, Integer pageSize) {
		PageHelper.startPage(pageNum, pageSize);
		List<ClientAgreementVersionDo> list = list(Wrappers.<ClientAgreementVersionDo>lambdaQuery().orderByDesc(ClientAgreementVersionDo::getId));
		PageInfo<ClientAgreementVersionDo> pageInfo = new PageInfo<>(list);

		PageInfoDTO<ClientAgreementVersionDo> pageInfoDTO = new PageInfoDTO<>();
		pageInfoDTO.setTotalPages(pageInfo.getPages());
		pageInfoDTO.setTotalCount(pageInfo.getTotal());
		pageInfoDTO.setData(list);

		return pageInfoDTO;
	}
}
