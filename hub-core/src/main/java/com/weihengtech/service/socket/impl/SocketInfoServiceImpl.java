package com.weihengtech.service.socket.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.auth.dto.ResourceAddDTO;
import com.weihengtech.auth.dto.ResourceConfigDTO;
import com.weihengtech.auth.dto.ResourceUpdDTO;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.enums.socket.SocketStatusEnum;
import com.weihengtech.enums.socket.SocketTypeEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.device.DeviceListPageDTO;
import com.weihengtech.pojo.vos.device.DeviceSyncStateVo;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.other.EcosCountryService;
import com.weihengtech.service.socket.SocketInfoService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.OperatingRecordUtil;
import com.weihengtech.utils.OperationUtil;
import com.weihengtech.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/31 16:07
 */
@Service
@Slf4j
public class SocketInfoServiceImpl implements SocketInfoService {

    @Resource
    private DeviceListService deviceListService;
    @Resource
    private AuthCenterResClient authCenterResClient;
    @Resource
    private EcosCountryService ecosCountryService;
    @Resource
    private TuyaDatacenterService tuyaDatacenterService;
    @Resource
    private StrategyService strategyService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveNewSocket(DeviceListDO deviceInfo) {
        String ip = deviceInfo.getIp();
        if (StrUtil.isNotBlank(ip)) {
            Map<String, String> ipMap = ecosCountryService.queryIpList(Collections.singletonList(ip));
            deviceInfo.setIp(ipMap.getOrDefault(ip, ip));
        }
        deviceListService.save(deviceInfo);
        // 同步平台资源信息
        authCenterResClient.createResourceBatch(
                Collections.singletonList(ResourceAddDTO.builder()
                        .id(String.valueOf(deviceInfo.getId()))
                        .code(deviceInfo.getDeviceSn())
                        .name(deviceInfo.getDeviceName())
                        // 固定为单插类型
                        .type(SocketTypeEnum.FERO.getId())
                        // 1-mes;2-hub;3-pangu
                        .source(2)
                        // 1-户储 2-大储
                        .sourceType(1)
                        .dataCenter(deviceInfo.getDatacenterId())
                        .build()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updSocket(DeviceListDO deviceInfo) {
        String ip = deviceInfo.getIp();
        if (StrUtil.isNotBlank(ip)) {
            Map<String, String> ipMap = ecosCountryService.queryIpList(Collections.singletonList(ip));
            deviceInfo.setIp(ipMap.getOrDefault(ip, ip));
        }
        deviceListService.updateById(deviceInfo);
        // 同步资源信息到平台
        if (deviceInfo.getDatacenterId() != null) {
            authCenterResClient.updResource(ResourceUpdDTO.builder()
                    .resourceId(String.valueOf(deviceInfo.getId()))
                    .dataCenter(deviceInfo.getDatacenterId())
                    .build());
        }
    }

    @Override
    public Integer syncDeviceState(DeviceSyncStateVo deviceSyncStateVo) {
        DeviceListDO deviceListDO = deviceListService.getById(deviceSyncStateVo.getDeviceId());
        com.weihengtech.utils.BeanUtil.assertNotNull(deviceListDO);

        DeviceStatusEnum deviceStatusEnum;
        SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
        // 没有首次安装时间，就是未知
        if (deviceListDO.getFirstInstall() == 0) {
            deviceStatusEnum = DeviceStatusEnum.UNKNOWN;
        } else {
            String wifiSn = deviceListDO.getWifiSn();
            deviceStatusEnum = specificServService.checkDeviceStatus(wifiSn);
        }

        AtomicInteger state = new AtomicInteger(deviceStatusEnum.getDbCode());
        deviceListDO.setState(state.get());
        deviceListDO.setUpdateTime(LocalDateTime.now());
        if (deviceStatusEnum.equals(DeviceStatusEnum.ONLINE)) {
            OperationUtil
                    .of(specificServService.getDeviceAssignProperty(
                            deviceListDO.getDeviceName(),
                            "switch_1"
                    ))
                    .ifPresentOrElse(
                            dict -> {
                                try {
                                    boolean switch1 = Boolean.parseBoolean(String.valueOf(dict.get("switch_1")));
                                    Integer dbCode = SocketStatusEnum.getIdByCode(switch1);
                                    log.info("device: {}, updateState is: {}", deviceSyncStateVo.getDeviceId(), dbCode);
                                    deviceListDO.setState(dbCode);
                                } catch (Exception e) {
                                    deviceListDO.setState(DeviceStatusEnum.ONLINE.getCode());
                                }
                            },
                            () -> deviceListDO.setState(DeviceStatusEnum.OFFLINE.getDbCode())
                    );
        }
        ActionFlagUtil.assertTrue(deviceListService.updateById(deviceListDO));
        return deviceListDO.getState();
    }

    @Override
    public DeviceListPageDTO queryByDeviceId(Long id) {
        DeviceListDO deviceById = deviceListService.getDeviceById(id);
        DeviceListPageDTO resItem = new DeviceListPageDTO();
        BeanUtil.copyProperties(deviceById, resItem);
        // 从平台读取系列数据
        Map<Integer, String> typeMap = getTypeMap();
        resItem.setResourceType(typeMap.get(deviceById.getResourceTypeId()));
        resItem.setResourceSeries(typeMap.get(deviceById.getResourceSeriesId()));
        resItem.setSetupTime(deviceById.getFirstInstall());
        resItem.setUpdateTime(TimeUtil.localDateTimeToSerialString(deviceById.getUpdateTime()));
        return resItem;
    }

    private Map<Integer, String> getTypeMap() {
        Map<Integer, String> resMap = new HashMap<>();
        List<ResourceConfigDTO> tree = authCenterResClient.categoryTypeTree();
        tree.forEach(i -> {
            resMap.put(i.getId(), i.getName());
            if (i.getTypes() != null) {
                i.getTypes().forEach(j -> {
                    resMap.put(j.getId(), j.getName());
                });
            }
        });
        return resMap;
    }
}
