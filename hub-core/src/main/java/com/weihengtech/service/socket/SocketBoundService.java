package com.weihengtech.service.socket;

import com.weihengtech.pojo.dtos.device.NetBindDeviceDTO;
import com.weihengtech.pojo.vos.socket.NetBindSocketVO;

/**
 * 单插绑定服务
 *
 * <AUTHOR>
 * @date 2024/1/25 14:03
 * @version 1.0
 */
public interface SocketBoundService {

	/**
	 * 配网流程账号绑定设备
	 *
	 * @param netBindSocketVO 绑定入参
     * @return 绑定结果
	 */
	NetBindDeviceDTO netDeviceBind(NetBindSocketVO netBindSocketVO);
}
