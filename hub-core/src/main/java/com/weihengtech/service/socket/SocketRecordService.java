package com.weihengtech.service.socket;

import com.weihengtech.pojo.vos.socket.SocketRecordQueryVO;
import com.weihengtech.sdk.iot.ecos.model.tuya.response.TuyaDeviceLog;

import java.util.List;

/**
 * <p>
 * 单插开关记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-30
 */
public interface SocketRecordService {
    /**
     * 查询充电桩充电记录
     *
     * @param req 请求参数
     * @return 充电记录
     */
    List<TuyaDeviceLog> queryChargeRecord(SocketRecordQueryVO req);
}
