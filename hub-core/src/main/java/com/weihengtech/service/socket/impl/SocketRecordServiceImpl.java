package com.weihengtech.service.socket.impl;

import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.other.TuyaLogQueryDTO;
import com.weihengtech.pojo.vos.socket.SocketRecordQueryVO;
import com.weihengtech.sdk.iot.ecos.model.tuya.response.TuyaDeviceLog;
import com.weihengtech.sdk.iot.ecos.model.tuya.response.TuyaDeviceLogsResponse;
import com.weihengtech.service.device.DeviceBoundService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.iot.IotClientService;
import com.weihengtech.service.socket.SocketRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 单插开关记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-30
 */
@Service
public class SocketRecordServiceImpl implements SocketRecordService {
    @Resource
    private DeviceBoundService deviceBoundService;
    @Resource
    private DeviceListService deviceListService;
    @Resource
    private IotClientService tuyaIotClient;

    @Override
    public List<TuyaDeviceLog> queryChargeRecord(SocketRecordQueryVO req) {
        req.checkParams();
        deviceBoundService.onlyProcessBoundDevice(req.getDeviceName());
        DeviceListDO deviceList = deviceListService.getDeviceInfoByDeviceName(req.getDeviceName());
        // 参考文档：https://iot.tuya.com/cloud/explorer?id=p16915491257323edxce&groupId=group-1633641687672688668&interfaceId=1633016861404692500
        TuyaDeviceLogsResponse deviceLogs = tuyaIotClient.getDeviceLogs(TuyaLogQueryDTO.builder()
                        .cloudId(deviceList.getWifiSn())
                        .type("7")
                        .startTime(req.getStartTime().getTime())
                        .endTime(req.getEndTime().getTime())
                        .size(Integer.MAX_VALUE)
                        .queryType(1)
                        .codes("switch_1")
                        .startRowKey("")
                        .build());
        return deviceLogs.getLogs();
    }
}
