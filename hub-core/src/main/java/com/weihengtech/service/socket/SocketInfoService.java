package com.weihengtech.service.socket;

import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.device.DeviceListPageDTO;
import com.weihengtech.pojo.vos.device.DeviceSyncStateVo;

/**
 * <p>
 * 充电桩信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
public interface SocketInfoService {

    /**
     * 保存单插信息
     *
     * @param deviceListDO 单插数据
     */
    void saveNewSocket(DeviceListDO deviceListDO);

    /**
     * 更新单插信息
     *
     * @param deviceInfo 单插数据
     */
    void updSocket(DeviceListDO deviceInfo);

    /**
     * 同步单插状态
     *
     * @param deviceSyncStateVo 设备id
     * @return 单插状态
     */
    Integer syncDeviceState(DeviceSyncStateVo deviceSyncStateVo);

    /**
     * 单插详情
     *
     * @param id id
     * @return 详情
     */
    DeviceListPageDTO queryByDeviceId(Long id);
}
