package com.weihengtech.service.socket.impl;

import cn.hutool.core.bean.BeanUtil;
import com.weihengtech.auth.dto.ResourceConfigDTO;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.device.DeviceListPageDTO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.socket.SocketDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 充电桩详情服务实现类
 *
 * <AUTHOR>
 * @date 2024/1/25 10:51
 * @version 1.0
 */
@Service
public class SocketDetailServiceImpl implements SocketDetailService {

    @Resource
    private DeviceListService deviceListService;
    @Resource
    private AuthCenterResClient authCenterResClient;

    @Override
    public DeviceListPageDTO queryDeviceInformation(String deviceName) {
        DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(deviceName);
        DeviceListDO deviceById = deviceListService.getDeviceById(deviceInfo.getId());
        DeviceListPageDTO resItem = new DeviceListPageDTO();
        BeanUtil.copyProperties(deviceById, resItem);
        // 从平台读取系列数据
        Map<Integer, String> typeMap = getTypeMap();
        resItem.setResourceType(typeMap.get(deviceById.getResourceTypeId()));
        resItem.setResourceSeries(typeMap.get(deviceById.getResourceSeriesId()));
        return resItem;
    }

    private Map<Integer, String> getTypeMap() {
        Map<Integer, String> resMap = new HashMap<>();
        List<ResourceConfigDTO> tree = authCenterResClient.categoryTypeTree();
        tree.forEach(i -> {
            resMap.put(i.getId(), i.getName());
            if (i.getTypes() != null) {
                i.getTypes().forEach(j -> {
                    resMap.put(j.getId(), j.getName());
                });
            }
        });
        return resMap;
    }
}
