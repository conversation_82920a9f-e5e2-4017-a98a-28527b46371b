package com.weihengtech.service.specific.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.weihengtech.api.pojo.vos.ReadBatchDeviceVO;
import com.weihengtech.api.pojo.vos.ReadDeviceVO;
import com.weihengtech.api.pojo.vos.UpgradeDeviceVO;
import com.weihengtech.api.pojo.vos.WriteDeviceVO;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.CommonConstants;
import com.weihengtech.consts.RedisRefConstants;
import com.weihengtech.dao.other.SqlMapper;
import com.weihengtech.enums.device.DeviceDatasourceEnum;
import com.weihengtech.enums.device.DeviceInfoEncodeTypeEnum;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.enums.firmware.UploadTypeEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.device.TuyaDevicePropertyDto;
import com.weihengtech.pojo.dtos.device.info.DeviceInformationDTO;
import com.weihengtech.pojo.vos.device.DeviceSpeedUpVO;
import com.weihengtech.service.device.DeviceDetailService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.device.DeviceSpeedUpService;
import com.weihengtech.service.iot.IotClientService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.service.thirdpart.RedisService;
import com.weihengtech.service.thirdpart.TimeSeriesDatabaseService;
import com.weihengtech.utils.*;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service(value = "specificServ0")
public class SpecificServElinkServiceImpl implements SpecificServService {

	@Value("${upload.base.url.http}")
	private String baseUrl;
	@Resource
	private RedisService redisService;
	@Resource
	private DeviceDetailService deviceDetailService;
	@Resource
	private AliMqttUtil aliMqttUtil;
	@Resource
	private DeviceListService deviceListService;
	@Resource
	private DeviceSpeedUpService deviceSpeedUpService;
	@Resource
	private SnowFlakeUtil snowFlakeUtil;
	@Resource
	private SqlMapper sqlMapper;
	@Resource
	private IotClientService eLinkIotClient;


	@Override
	public void speedupOnce(String deviceName) {
		redisService.cacheSpeedupDevice(deviceName, RedisRefConstants.SPEEDUP_ELINK_FLAG,
				RedisRefConstants.SPEEDUP_ELINK_TIMEOUT
		);
	}

	@Override
	@DSTransactional
	public void speedupKeep(String deviceName, DeviceSpeedUpVO param) {
		deviceSpeedUpService.speedupKeep(param, DeviceDatasourceEnum.ELINK.getDatasource());
	}

	@Override
	public void sendSpeedupCommand(String wifiSn) {
		try {
			eLinkIotClient.speedupDevice(wifiSn);
		} catch (Exception e) {
			log.error(e.getMessage());
		}
	}

	@Override
	public List<String> batchGetSpeedUpDeviceList() {
		List<String> elinkDeviceSnList = sqlMapper.queryDeviceSnByType(CommonConstants.DEVICE_ELINK);
		Map<String, String> elinkDeviceMap = redisService
				.batchGetDataSourceTypeCachedDeviceFlag(RedisRefConstants.SPEEDUP_ELINK_FLAG);

		List<String> result = new ArrayList<>();
		if (CollUtil.isNotEmpty(elinkDeviceSnList)) {
			result.addAll(elinkDeviceSnList);
		}
		if (CollUtil.isNotEmpty(elinkDeviceMap)) {
			result.addAll(elinkDeviceMap.keySet());
		}
		return result;
	}

	@Override
	public void sendUpgradeCommand(String deviceName, String downloadUrl, Long size) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		BeanUtil.assertNotNull(deviceListDO);
		if (!downloadUrl.startsWith("http")) {
			downloadUrl = UrlUtil.httpUrl(baseUrl + (downloadUrl.startsWith("/") ? downloadUrl : StrUtil.format("/{}", downloadUrl)));
		}
		log.info("Elinter upgrade url {}", downloadUrl);
		Boolean res = eLinkIotClient.upgradeDevice(UpgradeDeviceVO.builder()
				.deviceId(deviceName)
				.url(downloadUrl)
				.len(size)
				.build());
		if (!res) {
			log.error("elink sendUpgradeCommand failed: {}", deviceName);
		}
	}

	@Override
	public Map<String, String> getFirmwareVersionByTypeList(String deviceName, String typeName) {

		Map<String, String> result = new HashMap<>(16);
		val wifiTypeName = UploadTypeEnum.WIFI.name();

		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		BeanUtil.assertNotNull(deviceListDO);

		if (wifiTypeName.equals(typeName)) {
			JSONObject jsonObject = getDeviceInfo(deviceListDO.getWifiSn());
			if (ResultUtil.isRequestSuccess(jsonObject)) {
				try {
					JSONObject innerJson = JSONUtil.parseObj(jsonObject.get("result"));
					result.put(wifiTypeName, innerJson.getStr("software_version"));
					result.put("wifiSoftwareVersion", innerJson.getStr("software_version"));
					result.put("wifiHardwareVersion", innerJson.getStr("hardware_version"));
					result.put("wifiSerial", innerJson.getStr("serial"));
				} catch (Exception e) {
					log.error(e.getMessage());
				}
			}
		} else {
			DeviceInformationDTO deviceInformation = deviceDetailService.queryDeviceInformation(
					deviceName,
					Boolean.TRUE
			);
			if (typeName.equals(UploadTypeEnum.BMS.name())) {
				String bmsSoftwareVersion = deviceInformation.getBmsSoftwareVersion();
				if (StrUtil.isNotBlank(bmsSoftwareVersion)) {
					result.put(typeName, bmsSoftwareVersion);
				}
			} else if (typeName.equals(UploadTypeEnum.EMS.name())) {
				String emsSoftwareVersion = deviceInformation.getEmsSoftwareVersion();
				String emsSubVersion = deviceInformation.getEmsSubVersion();
				if (StrUtil.isNotBlank(emsSubVersion)) {
					result.put(typeName, String.format("%s(%s)", emsSoftwareVersion, emsSubVersion));
				} else {
					result.put(typeName, emsSoftwareVersion);
				}
			} else if (typeName.equals(UploadTypeEnum.DSP1.name())) {
				String dsp1Version = deviceInformation.getDsp1Version();
				String dsp1SubVersion = deviceInformation.getDsp1SubVersion();
				if (StrUtil.isNotBlank(dsp1SubVersion)) {
					result.put(typeName, String.format("%s(%s)", dsp1Version, dsp1SubVersion));
				} else {
					result.put(typeName, dsp1Version);
				}
			} else if (typeName.equals(UploadTypeEnum.DSP2.name())) {
				String dsp2Version = deviceInformation.getDsp2Version();
				String dsp2SubVersion = deviceInformation.getDsp2SubVersion();
				if (StrUtil.isNotBlank(dsp2SubVersion)) {
					result.put(typeName, String.format("%s(%s)", dsp2Version, dsp2SubVersion));
				} else {
					result.put(typeName, dsp2Version);
				}
			} else if (typeName.equals(UploadTypeEnum.ARCDSP.name())) {
				String arcDspVersion = deviceInformation.getArcDspSoftwareVersion();
				String arcDspSubVersion = deviceInformation.getArcDspSubVersion();
				if (StrUtil.isNotBlank(arcDspSubVersion)) {
					result.put(typeName, String.format("%s(%s)", arcDspVersion, arcDspSubVersion));
				} else {
					result.put(typeName, arcDspVersion);
				}
			}
		}
		return result;
	}

	@Override
	public Map<String, String> getFirmwareVersionByDeviceListDOAndTypeName(DeviceListDO deviceListDO, String typeName) {
		Map<String, String> result = new HashMap<>(12);
		if (UploadTypeEnum.WIFI.name().equals(typeName)) {
			JSONObject jsonObject = getDeviceInfo(deviceListDO.getWifiSn());
			if (ResultUtil.isRequestSuccess(jsonObject)) {
				try {
					JSONObject innerJson = JSONUtil.parseObj(jsonObject.get("result"));
					result.put(UploadTypeEnum.WIFI.name(), innerJson.getStr("software_version"));
					result.put("wifiSoftwareVersion", innerJson.getStr("software_version"));
					result.put("wifiHardwareVersion", innerJson.getStr("hardware_version"));
					result.put("wifiSerial", innerJson.getStr("serial"));
				} catch (Exception e) {
					log.error(e.getMessage());
				}
			}
		} else {
			if (typeName.equals(UploadTypeEnum.BMS.name())) {
				StrategyService ss = InitUtil.getBean(StrategyService.class);
				TimeSeriesDatabaseService tsdbService = ss.chooseTimeSeriesDatabaseService(deviceListDO);
				Map<String, Object> lastPoint = tsdbService.lastPoint(deviceListDO.getDeviceName(),
						ListUtil.toList("bms_software_version"),
						System.currentTimeMillis() / 1000 - 3600, System.currentTimeMillis() / 1000
				);
				if (StrUtil.isNotBlank(lastPoint.get("bms_software_version").toString())) {
					result.put(typeName, lastPoint.get("bms_software_version").toString());
				}
			} else if (typeName.equals(UploadTypeEnum.EMS.name())) {
				StrategyService ss = InitUtil.getBean(StrategyService.class);
				TimeSeriesDatabaseService tsdbService = ss.chooseTimeSeriesDatabaseService(deviceListDO);
				Map<String, Object> lastPoint = tsdbService.lastPoint(deviceListDO.getDeviceName(),
						ListUtil.toList("ems_software_version"),
						System.currentTimeMillis() / 1000 - 3600, System.currentTimeMillis() / 1000
				);
				if (StrUtil.isNotBlank(lastPoint.get("ems_software_version").toString())) {
					result.put(typeName, lastPoint.get("ems_software_version").toString());
				}
			} else if (typeName.equals(UploadTypeEnum.DSP1.name())) {
				StrategyService ss = InitUtil.getBean(StrategyService.class);
				TimeSeriesDatabaseService tsdbService = ss.chooseTimeSeriesDatabaseService(deviceListDO);
				Map<String, Object> lastPoint = tsdbService.lastPoint(deviceListDO.getDeviceName(),
						ListUtil.toList("dsp1_version"),
						System.currentTimeMillis() / 1000 - 3600, System.currentTimeMillis() / 1000
				);
				if (StrUtil.isNotBlank(lastPoint.get("dsp1_version").toString())) {
					result.put(typeName, lastPoint.get("dsp1_version").toString());
				}
			} else if (typeName.equals(UploadTypeEnum.DSP2.name())) {
				StrategyService ss = InitUtil.getBean(StrategyService.class);
				TimeSeriesDatabaseService tsdbService = ss.chooseTimeSeriesDatabaseService(deviceListDO);
				Map<String, Object> lastPoint = tsdbService.lastPoint(deviceListDO.getDeviceName(),
						ListUtil.toList("dsp2_version"),
						System.currentTimeMillis() / 1000 - 3600, System.currentTimeMillis() / 1000
				);
				if (StrUtil.isNotBlank(lastPoint.get("dsp2_version").toString())) {
					result.put(typeName, lastPoint.get("dsp2_version").toString());
				}
			} else if (typeName.equals(UploadTypeEnum.ARCDSP.name())) {
				StrategyService ss = InitUtil.getBean(StrategyService.class);
				TimeSeriesDatabaseService tsdbService = ss.chooseTimeSeriesDatabaseService(deviceListDO);
				Map<String, Object> lastPoint = tsdbService.lastPoint(deviceListDO.getDeviceName(),
						ListUtil.toList("dsp_arc_version"),
						System.currentTimeMillis() / 1000 - 3600, System.currentTimeMillis() / 1000
				);
				if (StrUtil.isNotBlank(lastPoint.get("dsp_arc_version").toString())) {
					result.put(typeName, lastPoint.get("dsp_arc_version").toString());
				}
			}
		}
		return result;
	}

	private JSONObject getDeviceInfo(String wifiSn) {
		List<TuyaDevicePropertyDto> propList;
		try {
			propList = eLinkIotClient.getDeviceInfo(Collections.singletonList(wifiSn));
			Assert.notEmpty(propList, "propList is empty");
		} catch (Exception e) {
			throw new CustomException(ExceptionEnum.TUYA_SERVICE_READ_ERROR);
		}
		TuyaDevicePropertyDto item = propList.get(0);
		List<TuyaDevicePropertyDto.StatusInfo> status = item.getStatus();
		JSONObject res = new JSONObject();
		status.forEach((i -> res.set(i.getCode(), i.getValue())));
		return res;
	}

	@Override
	public DeviceStatusEnum checkDeviceStatus(String wifiSn) {
		if (StrUtil.isEmpty(wifiSn)) {
			return DeviceStatusEnum.OFFLINE;
		}
		DeviceListDO deviceListDO = deviceListService.getDeviceBeanByWifiSn(wifiSn);
		BeanUtil.assertNotNull(deviceListDO);
		if (deviceListDO.getFirstInstall() == 0) {
			return DeviceStatusEnum.UNKNOWN;
		}
		return aliMqttUtil.queryDeviceStatus(wifiSn);
	}

	@Override
	public String getDeviceSnByWifiSn(String wifiSn) {
		List<Integer> res = sendReadCommand(wifiSn, 1, 30042, 10);
		return ModbusParseUtil.parseAscii(res);
	}

	@Override
	public String getDeviceIpByWifiSn(String wifiSn) {
		return eLinkIotClient.getDeviceIp(wifiSn);
	}

    @Override
    public Boolean isOnline(String wifiSn) {
        return eLinkIotClient.queryIsOnlineStatus(wifiSn);
    }

	@Override
	public void changeDeviceCategory(String deviceSn, String wifiSn, String category) {

	}


	@Override
	public List<Integer> sendReadCommand(String wifiSn, Integer slaveId, Integer startAddress, Integer len) {
		ReadDeviceVO param = ReadDeviceVO.builder()
				.deviceId(wifiSn)
				.slaveId(slaveId)
				.startAddress(startAddress)
				.len(len)
				.build();

		return eLinkIotClient.readDevice(param);
	}

	@Override
	public Map<String, List<Integer>> sendBatchReadCommand(ReadBatchDeviceVO param) {
		return Collections.emptyMap();
	}

	@Override
	public Boolean sendWriteCommand(
			String wifiSn, Integer slaveId, Integer startAddress, Integer len,
			List<Integer> values
	) {
		WriteDeviceVO param = WriteDeviceVO.builder()
				.deviceId(wifiSn)
				.slaveId(slaveId)
				.startAddress(startAddress)
				.len(len)
				.values(values)
				.build();
		return eLinkIotClient.writeDevice(param);
	}
}
