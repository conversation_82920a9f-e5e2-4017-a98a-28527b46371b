package com.weihengtech.service.specific.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.weihengtech.api.EcosConfigApi;
import com.weihengtech.api.pojo.dtos.DeviceBasicInfoDTO;
import com.weihengtech.api.pojo.dtos.EcosConfigDto;
import com.weihengtech.api.pojo.vos.ReadBatchDeviceVO;
import com.weihengtech.api.pojo.vos.ReadDeviceVO;
import com.weihengtech.api.pojo.vos.UpgradeDeviceVO;
import com.weihengtech.api.pojo.vos.WriteDeviceVO;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.config.DeviceAddressMapConfig;
import com.weihengtech.consts.RedisRefConstants;
import com.weihengtech.dao.other.SqlMapper;
import com.weihengtech.enums.device.DeviceDatasourceEnum;
import com.weihengtech.enums.device.DeviceInfoEncodeTypeEnum;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.enums.firmware.UploadTypeEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.vos.device.DeviceSpeedUpVO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.device.DeviceSpeedUpService;
import com.weihengtech.service.iot.IotClientService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.service.thirdpart.RedisService;
import com.weihengtech.service.thirdpart.TimeSeriesDatabaseService;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.InitUtil;
import com.weihengtech.utils.ModbusParseUtil;
import com.weihengtech.utils.ModbusRequestUtil;
import com.weihengtech.utils.UrlUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URL;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service(value = "specificServ3")
public class SpecificServWhServiceImpl implements SpecificServService {

	@Value("${custom.ali.oss.bucket.sign}")
	private String bucket;
	@Value("${upload.base.url}")
	private String baseUrl;

	@Resource
	private RedisService redisService;
	@Resource
	private DeviceSpeedUpService deviceSpeedUpService;
	@Resource
	private DeviceListService deviceListService;
	@Resource
	private SqlMapper sqlMapper;
	@Resource
	private IotClientService whIotClient;
	@Resource
	private EcosConfigApi ecosConfigApi;
	@Resource
	private OSS ossClient;
	@Resource
	private ModbusRequestUtil modbusRequestUtil;
	@Resource
	private DeviceAddressMapConfig deviceAddressMapConfig;

	@Override
	public void speedupOnce(String deviceFlag) {
		redisService.cacheSpeedupDevice(deviceFlag, RedisRefConstants.SPEEDUP_WH_FLAG,
				RedisRefConstants.SPEEDUP_WH_TIMEOUT
		);
	}

	@Override
	public void speedupKeep(String deviceFlag, DeviceSpeedUpVO param) {
		deviceSpeedUpService.speedupKeep(param, DeviceDatasourceEnum.WH.getDatasource());
	}

	@Override
	public void sendSpeedupCommand(String wifiSn) {
		try {
			whIotClient.speedupDevice(wifiSn);
		} catch (Exception e) {
			log.error(String.format("whIotClient speedupDevice failed: %s", wifiSn), e);
		}
	}

	@Override
	public List<String> batchGetSpeedUpDeviceList() {

		List<String> whDeviceSnList = sqlMapper.queryDeviceSnByType(DeviceDatasourceEnum.WH.getDatasource());
		Map<String, String> whDeviceMap = redisService
				.batchGetDataSourceTypeCachedDeviceFlag(RedisRefConstants.SPEEDUP_WH_FLAG);
		List<String> result = new ArrayList<>();
		if (CollUtil.isNotEmpty(whDeviceSnList)) {
			result.addAll(whDeviceSnList);
		}
		if (CollUtil.isNotEmpty(whDeviceMap)) {
			result.addAll(whDeviceMap.keySet());
		}
		return result;
	}

	@Override
	public void sendUpgradeCommand(String deviceName, String downloadUrl, Long size) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		BeanUtil.assertNotNull(deviceListDO);
		UpgradeDeviceVO upgradeDeviceVO = new UpgradeDeviceVO();
		upgradeDeviceVO.setDeviceId(deviceListDO.getWifiSn());
		if (!downloadUrl.startsWith("http")) {
			String active = InitUtil.APPLICATION_CONTEXT.getEnvironment().getProperty("spring.profiles.active", "dev");
			EcosConfigDto ecosConfigDto = ecosConfigApi.get();
			String signUrl = generateSingedUrl(downloadUrl);
			if ("prod".equals(active)) {
				if (ecosConfigDto.getUpgradeFirmwareHttps().getProd()) {
					downloadUrl = UrlUtil.httpsUrl(signUrl);
				} else {
					downloadUrl = UrlUtil.httpUrl(signUrl);
				}
			} else {
				if (ecosConfigDto.getUpgradeFirmwareHttps().getTest()) {
					downloadUrl = UrlUtil.httpsUrl(signUrl);
				} else {
					downloadUrl = UrlUtil.httpUrl(signUrl);
				}
			}
		}
		log.info("wh upgrade url {}", downloadUrl);
		upgradeDeviceVO.setUrl(downloadUrl);
		upgradeDeviceVO.setLen(size);
		Boolean res = whIotClient.upgradeDevice(upgradeDeviceVO);
		if (!res) {
			throw new CustomException(ExceptionEnum.DEVICE_UPGRADE_ERROR);
		}
    }

	@Override
	public Map<String, String> getFirmwareVersionByTypeList(String deviceName, String typeName) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		Map<String, String> result = new HashMap<>(12);
		if (UploadTypeEnum.WIFI.name().equals(typeName)) {
			DeviceBasicInfoDTO deviceBasicInfo = whIotClient.getDeviceBasicInfo(deviceListDO.getWifiSn());
			result.put(UploadTypeEnum.WIFI.name(), deviceBasicInfo.getSoftwareVersion());
			result.put("wifiSoftwareVersion", deviceBasicInfo.getSoftwareVersion());
			result.put("wifiHardwareVersion", deviceBasicInfo.getHardwareVersion());
			result.put("wifiSerial", deviceListDO.getWifiSn());
		} else {
			if (typeName.equals(UploadTypeEnum.BMS.name())) {
				List<Integer> addressList = deviceAddressMapConfig.getAddressList(typeName);
				String bmsSoftwareVersion = ModbusParseUtil.parseForDeviceInfoTypeEnum(
						DeviceInfoEncodeTypeEnum.ASCII,
						modbusRequestUtil.postTransparentRead(deviceListDO.getDeviceName(), addressList.get(0), addressList.get(1), 1)
				);
				if (StrUtil.isNotBlank(bmsSoftwareVersion)) {
					result.put(typeName, bmsSoftwareVersion);
				}
			} else if (typeName.equals(UploadTypeEnum.EMS.name())) {
				List<Integer> addressList = deviceAddressMapConfig.getAddressList(typeName);
				String emsSoftwareVersion = ModbusParseUtil.parseForDeviceInfoTypeEnum(
						DeviceInfoEncodeTypeEnum.ASCII,
						modbusRequestUtil.postTransparentRead(deviceListDO.getDeviceName(), addressList.get(0), addressList.get(1), 1)
				);
				if (StrUtil.isNotBlank(emsSoftwareVersion)) {
					result.put(typeName, emsSoftwareVersion);
				}
				List<Integer> subAddressList = deviceAddressMapConfig.getAddressList(typeName + "_SUB");
				String emsSubVersion = ModbusParseUtil.parseForDeviceInfoTypeEnum(
						DeviceInfoEncodeTypeEnum.ASCII,
						modbusRequestUtil.postTransparentRead(deviceListDO.getDeviceName(), subAddressList.get(0), subAddressList.get(1), 1)
				);
				if (StrUtil.isNotBlank(emsSubVersion)) {
					result.put(typeName, String.format("%s(%s)", emsSoftwareVersion, emsSubVersion));
				}
			} else if (typeName.equals(UploadTypeEnum.DSP1.name())) {
				List<Integer> addressList = deviceAddressMapConfig.getAddressList(typeName);
				String dsp1Version = ModbusParseUtil.parseForDeviceInfoTypeEnum(
						DeviceInfoEncodeTypeEnum.ASCII,
						modbusRequestUtil.postTransparentRead(deviceListDO.getDeviceName(), addressList.get(0), addressList.get(1), 1)
				);
				if (StrUtil.isNotBlank(dsp1Version)) {
					result.put(typeName, dsp1Version);
				}
				List<Integer> subAddress1List = deviceAddressMapConfig.getAddressList(typeName + "_SUB1");
				List<Integer> subAddress2List = deviceAddressMapConfig.getAddressList(typeName + "_SUB2");
				String dsp1SubVersion1 = ModbusParseUtil.parseForDeviceInfoTypeEnum(
						DeviceInfoEncodeTypeEnum.U16,
						modbusRequestUtil.postTransparentRead(deviceListDO.getDeviceName(), subAddress1List.get(0), subAddress1List.get(1), 1)
				);
				String dsp1SubVersion2 = ModbusParseUtil.parseForDeviceInfoTypeEnum(
						DeviceInfoEncodeTypeEnum.U16,
						modbusRequestUtil.postTransparentRead(deviceListDO.getDeviceName(), subAddress2List.get(0), subAddress2List.get(1), 1)
				);
				if (StrUtil.isNotBlank(dsp1SubVersion1) && StrUtil.isNotBlank(dsp1SubVersion2)) {
					result.put(typeName, String.format("%s(%s-%s)", dsp1Version, dsp1SubVersion1, dsp1SubVersion2));
				}
			} else if (typeName.equals(UploadTypeEnum.DSP2.name())) {
				List<Integer> addressList = deviceAddressMapConfig.getAddressList(typeName);
				String dsp2Version = ModbusParseUtil.parseForDeviceInfoTypeEnum(
						DeviceInfoEncodeTypeEnum.ASCII,
						modbusRequestUtil.postTransparentRead(deviceListDO.getDeviceName(), addressList.get(0), addressList.get(1), 1)
				);
				if (StrUtil.isNotBlank(dsp2Version)) {
					result.put(typeName, dsp2Version);
				}
				List<Integer> subAddress1List = deviceAddressMapConfig.getAddressList(typeName + "_SUB1");
				List<Integer> subAddress2List = deviceAddressMapConfig.getAddressList(typeName + "_SUB2");
				String dsp2SubVersion1 = ModbusParseUtil.parseForDeviceInfoTypeEnum(
						DeviceInfoEncodeTypeEnum.U16,
						modbusRequestUtil.postTransparentRead(deviceListDO.getDeviceName(), subAddress1List.get(0), subAddress1List.get(1), 1)
				);
				String dsp2SubVersion2 = ModbusParseUtil.parseForDeviceInfoTypeEnum(
						DeviceInfoEncodeTypeEnum.U16,
						modbusRequestUtil.postTransparentRead(deviceListDO.getDeviceName(), subAddress2List.get(0), subAddress2List.get(1), 1)
				);
				if (StrUtil.isNotBlank(dsp2SubVersion1) && StrUtil.isNotBlank(dsp2SubVersion2)) {
					result.put(typeName, String.format("%s(%s-%s)", dsp2Version, dsp2SubVersion1, dsp2SubVersion2));
				}
			} else if (typeName.equals(UploadTypeEnum.ARCDSP.name())) {
				List<Integer> addressList = deviceAddressMapConfig.getAddressList(typeName);
				String arcDspVersion = ModbusParseUtil.parseForDeviceInfoTypeEnum(
						DeviceInfoEncodeTypeEnum.ASCII,
						modbusRequestUtil.postTransparentRead(deviceListDO.getDeviceName(), addressList.get(0), addressList.get(1), 1)
				);
				if (StrUtil.isNotBlank(arcDspVersion)) {
					result.put(typeName, arcDspVersion);
				}
				List<Integer> subAddressList = deviceAddressMapConfig.getAddressList(typeName + "_SUB");
				String subArcDspVersion = ModbusParseUtil.parseForDeviceInfoTypeEnum(
						DeviceInfoEncodeTypeEnum.U16,
						modbusRequestUtil.postTransparentRead(deviceListDO.getDeviceName(), subAddressList.get(0), subAddressList.get(1), 1)
				);
				if (StrUtil.isNotBlank(subArcDspVersion)) {
					result.put(typeName, String.format("%s(%s)", arcDspVersion, subArcDspVersion));
				}
			}
		}
		return result;
	}

	@Override
	public Map<String, String> getFirmwareVersionByDeviceListDOAndTypeName(DeviceListDO deviceListDO, String typeName) {
		Map<String, String> result = new HashMap<>(12);
		if (UploadTypeEnum.WIFI.name().equals(typeName)) {
			DeviceBasicInfoDTO deviceBasicInfo = whIotClient.getDeviceBasicInfo(deviceListDO.getWifiSn());
			result.put(UploadTypeEnum.WIFI.name(), deviceBasicInfo.getSoftwareVersion());
			result.put("wifiSoftwareVersion", deviceBasicInfo.getSoftwareVersion());
			result.put("wifiHardwareVersion", deviceBasicInfo.getHardwareVersion());
			result.put("wifiSerial", deviceListDO.getWifiSn());
		} else {
			if (typeName.equals(UploadTypeEnum.BMS.name())) {
				StrategyService ss = InitUtil.getBean(StrategyService.class);
				TimeSeriesDatabaseService tsdbService = ss.chooseTimeSeriesDatabaseService(deviceListDO);
				Map<String, Object> lastPoint = tsdbService.lastPoint(deviceListDO.getDeviceName(),
						ListUtil.toList("bms_software_version"),
						System.currentTimeMillis() / 1000 - 3600, System.currentTimeMillis() / 1000
				);
				if (StrUtil.isNotBlank(lastPoint.get("bms_software_version").toString())) {
					result.put(typeName, lastPoint.get("bms_software_version").toString());
				}
			} else if (typeName.equals(UploadTypeEnum.EMS.name())) {
				StrategyService ss = InitUtil.getBean(StrategyService.class);
				TimeSeriesDatabaseService tsdbService = ss.chooseTimeSeriesDatabaseService(deviceListDO);
				Map<String, Object> lastPoint = tsdbService.lastPoint(deviceListDO.getDeviceName(),
						ListUtil.toList("ems_software_version"),
						System.currentTimeMillis() / 1000 - 3600, System.currentTimeMillis() / 1000
				);
				if (StrUtil.isNotBlank(lastPoint.get("ems_software_version").toString())) {
					result.put(typeName, lastPoint.get("ems_software_version").toString());
				}
			} else if (typeName.equals(UploadTypeEnum.DSP1.name())) {
				StrategyService ss = InitUtil.getBean(StrategyService.class);
				TimeSeriesDatabaseService tsdbService = ss.chooseTimeSeriesDatabaseService(deviceListDO);
				Map<String, Object> lastPoint = tsdbService.lastPoint(deviceListDO.getDeviceName(),
						ListUtil.toList("dsp1_version"),
						System.currentTimeMillis() / 1000 - 3600, System.currentTimeMillis() / 1000
				);
				if (StrUtil.isNotBlank(lastPoint.get("dsp1_version").toString())) {
					result.put(typeName, lastPoint.get("dsp1_version").toString());
				}
			} else if (typeName.equals(UploadTypeEnum.DSP2.name())) {
				StrategyService ss = InitUtil.getBean(StrategyService.class);
				TimeSeriesDatabaseService tsdbService = ss.chooseTimeSeriesDatabaseService(deviceListDO);
				Map<String, Object> lastPoint = tsdbService.lastPoint(deviceListDO.getDeviceName(),
						ListUtil.toList("dsp2_version"),
						System.currentTimeMillis() / 1000 - 3600, System.currentTimeMillis() / 1000
				);
				if (StrUtil.isNotBlank(lastPoint.get("dsp2_version").toString())) {
					result.put(typeName, lastPoint.get("dsp2_version").toString());
				}
			} else if (typeName.equals(UploadTypeEnum.ARCDSP.name())) {
				StrategyService ss = InitUtil.getBean(StrategyService.class);
				TimeSeriesDatabaseService tsdbService = ss.chooseTimeSeriesDatabaseService(deviceListDO);
				Map<String, Object> lastPoint = tsdbService.lastPoint(deviceListDO.getDeviceName(),
						ListUtil.toList("dsp_arc_version"),
						System.currentTimeMillis() / 1000 - 3600, System.currentTimeMillis() / 1000
				);
				if (StrUtil.isNotBlank(lastPoint.get("dsp_arc_version").toString())) {
					result.put(typeName, lastPoint.get("dsp_arc_version").toString());
				}
			}
		}
		return result;
	}

	@Override
	public DeviceStatusEnum checkDeviceStatus(String wifiSn) {
		if (StrUtil.isEmpty(wifiSn)) {
			return DeviceStatusEnum.OFFLINE;
		}
		DeviceListDO deviceListDO = deviceListService.getDeviceBeanByWifiSn(wifiSn);
		BeanUtil.assertNotNull(deviceListDO);
		if (deviceListDO.getFirstInstall() == 0) {
			return DeviceStatusEnum.UNKNOWN;
		}
		try {
			Boolean isOnlineStatus = whIotClient.queryIsOnlineStatus(wifiSn);
			return isOnlineStatus ? DeviceStatusEnum.ONLINE : DeviceStatusEnum.OFFLINE;
		} catch (Exception e) {
			log.error(String.format("whIotClient getDeviceBasicInfo failed, wifiSn is %s", wifiSn), e);
			return DeviceStatusEnum.OFFLINE;
		}
	}

	@Override
	public List<Integer> sendReadCommand(String wifiSn, Integer slaveId, Integer startAddress, Integer len) {
		ReadDeviceVO tuyaReadDeviceVo = new ReadDeviceVO();
		tuyaReadDeviceVo.setDeviceId(wifiSn);
		tuyaReadDeviceVo.setSlaveId(slaveId);
		tuyaReadDeviceVo.setStartAddress(startAddress);
		tuyaReadDeviceVo.setLen(len);
		return whIotClient.readDevice(tuyaReadDeviceVo);
	}

	@Override
	public Map<String, List<Integer>> sendBatchReadCommand(ReadBatchDeviceVO param) {
		return whIotClient.readBatchDevice(param);
	}

	@Override
	public Boolean sendWriteCommand(
			String wifiSn, Integer slaveId, Integer startAddress, Integer len,
			List<Integer> values
	) {
		WriteDeviceVO tuyaWriteDeviceVo = new WriteDeviceVO();
		tuyaWriteDeviceVo.setDeviceId(wifiSn);
		tuyaWriteDeviceVo.setSlaveId(slaveId);
		tuyaWriteDeviceVo.setStartAddress(startAddress);
		tuyaWriteDeviceVo.setLen(len);
		tuyaWriteDeviceVo.setValues(values);
		return whIotClient.writeDevice(tuyaWriteDeviceVo);
	}

	@Override
	public String getDeviceSnByWifiSn(String wifiSn) {
		try {
			List<Integer> listRes = this.sendReadCommand(wifiSn, 1, 30042, 10);
			return ModbusParseUtil.parseAscii(listRes);
		} catch (Exception e) {
			log.error(String.format("whIotClient getDeviceSnByWifiSn failed， wifiSn is %s", wifiSn), e);
			return StrUtil.EMPTY;
		}
	}

	@Override
	public String getDeviceIpByWifiSn(String wifiSn) {
		try {
			return whIotClient.getDeviceIp(wifiSn);
		} catch (Exception e) {
			log.error(String.format("whIotClient getDeviceIpByWifiSn failed， wifiSn is %s", wifiSn), e);
			return StrUtil.EMPTY;
		}
	}

	@Override
	public Boolean isOnline(String wifiSn) {
		return whIotClient.queryIsOnlineStatus(wifiSn);
	}

	@Override
	public void changeDeviceCategory(String deviceName, String wifiSn, String category) {
		whIotClient.changeDeviceCategory(deviceName, wifiSn, category);
	}

	private String generateSingedUrl(String objectName) {
		URL signedUrl;
		try {
			// 指定生成的签名URL过期时间，单位为毫秒。
			Date expiration = new Date(new Date().getTime() + 900000);
			// 生成签名URL。
			GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucket, objectName, HttpMethod.GET);
			// 设置过期时间。
			request.setExpiration(expiration);
			// 通过HTTP GET请求生成签名URL。
			signedUrl = ossClient.generatePresignedUrl(request);
			// 打印签名URL。
			log.info("signedUrl: " + signedUrl);
			return signedUrl.toString().replaceAll(signedUrl.getHost(), baseUrl);
		} catch (OSSException oe) {
			log.warn("Caught an OSSException, which means your request made it to OSS, "
					+ "but was rejected with an error response for some reason.");
			log.warn("Error Message:" + oe.getErrorMessage());
			log.warn("Error Code:" + oe.getErrorCode());
			log.warn("Request ID:" + oe.getRequestId());
			log.warn("Host ID:" + oe.getHostId());
		} catch (ClientException ce) {
			log.warn("Caught an ClientException, which means the client encountered "
					+ "a serious internal problem while trying to communicate with OSS, "
					+ "such as not being able to access the network.");
			log.warn("Error Message:" + ce.getMessage());
		} catch (Exception e) {
			log.warn(e.getMessage());
		}
		throw new CustomException(ExceptionEnum.UNKNOWN_EXCEPTION);
	}
}
