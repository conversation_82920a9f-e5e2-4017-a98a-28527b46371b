package com.weihengtech.service.specific;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONObject;
import com.weihengtech.api.pojo.vos.ReadBatchDeviceVO;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.vos.device.DeviceSpeedUpVO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.thirdpart.TimeSeriesDatabaseService;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.InitUtil;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SpecificServService {

	/**
	 * 加速采集一个周期
	 *
	 * @param deviceFlag 设备标识
	 */
	void speedupOnce(String deviceFlag);

	/**
	 * 加速采集无时限
	 *
	 * @param deviceFlag 设备标识
	 */
	void speedupKeep(String deviceFlag, DeviceSpeedUpVO param);

	/**
	 * 获取所有的加速采集的设备列表
	 *
	 * @return 设备标识列表
	 */
	List<String> batchGetSpeedUpDeviceList();

	/**
	 * 发送加速命令
	 *
	 * @param wifiSn wifiSn
	 */
	void sendSpeedupCommand(String wifiSn);

	/**
	 * 发送升级命令
	 *
	 * @param deviceName  设备标识
	 * @param downloadUrl 固件下载地址
	 * @param size        固件包长度
	 */
	void sendUpgradeCommand(String deviceName, String downloadUrl, Long size);

	/**
	 * 发送透传读命令
	 *
	 * @param deviceFlag   设备标识
	 * @param slaveId      从机号
	 * @param startAddress 开始地址
	 * @param len          长度
	 * @return 读取数据
	 */
	List<Integer> sendReadCommand(String deviceFlag, Integer slaveId, Integer startAddress, Integer len);

	/**
	 * 发送批量透传读命令
	 *
	 * @param param param
	 * @return 读取数据
	 */
	Map<String, List<Integer>> sendBatchReadCommand(ReadBatchDeviceVO param);

	/**
	 * 发送透传读命令
	 *
	 * @param wifiSn       wifi棒标识
	 * @param slaveId      从机号
	 * @param startAddress 开始地址
	 * @param len          长度
	 * @param values       要写入的长度
	 * @return 写入结果
	 */
	Boolean sendWriteCommand(
			String wifiSn, Integer slaveId, Integer startAddress, Integer len,
			List<Integer> values
	);

	/**
	 * 根据类型列表获取固件类型与版本的映射
	 *
	 * @param deviceName 设备标识
	 * @param typeName 固件类型
     * @return 版本映射
	 */
	Map<String, String> getFirmwareVersionByTypeList(String deviceName, String typeName);

	/**
	 * 根据类型列表获取固件类型与版本的映射
	 *
	 * @param deviceListDO 设备DO
	 * @param typeName 固件类型
	 * @return
	 */
	Map<String, String> getFirmwareVersionByDeviceListDOAndTypeName(DeviceListDO deviceListDO, String typeName);

	/**
	 * 查询设备当前状态
	 *
	 * @param wifiSn 设备标识
	 * @return 设备状态枚举类
	 */
	DeviceStatusEnum checkDeviceStatus(String wifiSn);

	/**
	 * 根据WiFi SN获取设备SN
	 *
	 * @param wifiSn wifi sn
	 * @return 设备SN
	 */
	String getDeviceSnByWifiSn(String wifiSn);

	/**
	 * 根据WiFi SN获取设备IP
	 *
	 * @param wifiSn WiFi SN
	 * @return IP
	 */
	String getDeviceIpByWifiSn(String wifiSn);

	/**
	 * 判断wifi棒子是否在线，不查询具体设备状态
	 *
	 * @param wifiSn wifiSn
	 * @return 是否在线
	 */
	Boolean isOnline(String wifiSn);

	/**
	 * 更改设备的类型
	 *
	 * @param deviceName 设备标识
	 * @param wifiSn     wifi sn
	 * @param category   类型
	 */
	void changeDeviceCategory(String deviceName, String wifiSn, String category);

	/**
	 * 获取设备指定属性
	 *
	 * @param deviceName   设备标识
	 * @param propertyName 属性名
	 * @return 属性Dict
	 */
	default JSONObject getDeviceAssignProperty(String deviceName, String propertyName) {
		try {
			long now = System.currentTimeMillis();
			DeviceListService deviceListService = InitUtil.getBean(DeviceListService.class);
			DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
			BeanUtil.assertNotNull(deviceListDO);
			StrategyService ss = InitUtil.getBean(StrategyService.class);
			TimeSeriesDatabaseService ts = ss.chooseTimeSeriesDatabaseService(deviceListDO);
			Map<String, Object> lastPointMap = ts.lastPoint(deviceName,
					ListUtil.toList(propertyName),
					now / 1000 - 3600, now / 1000
			);
			return lastPointMap.containsKey(propertyName) ?
					new JSONObject().set(propertyName, lastPointMap.get(propertyName)) : new JSONObject();
		} catch (Exception e) {
			return new JSONObject();
		}
	}
}
