package com.weihengtech.service.specific;

import com.weihengtech.auth.dto.ResourceResDTO;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.service.ext.ExtCommonService;
import com.weihengtech.service.parser.ParseErrorService;
import com.weihengtech.service.thirdpart.TimeSeriesDatabaseService;

/**
 * <AUTHOR>
 */
public interface StrategyService {

	/**
	 * 选择渠道服务
	 *
	 * @param deviceId 设备id
	 * @return 渠道服务实例
	 */
	SpecificServService chooseSpecificServ(Long deviceId);

	/**
	 * 选择渠道服务
	 *
	 * @param deviceListDO 设备实例
	 * @return 渠道服务实例
	 */
	SpecificServService chooseSpecificServ(DeviceListDO deviceListDO);

	/**
	 * 选择渠道服务
	 *
	 * @param dataSource 数据来源
	 * @return 渠道服务实例
	 */
	SpecificServService chooseSpecificServ(int dataSource);

	/**
	 * 选择tsdb服务
	 *
	 * @param deviceListDO 设备实例
	 * @return tsdb实例
	 */
	TimeSeriesDatabaseService chooseTimeSeriesDatabaseService(DeviceListDO deviceListDO);

	/**
	 * 选择扩展信息服务
	 *
	 * @param seriesCode 系列编码
	 * @return 扩展信息服务实例
	 */
	<T> ExtCommonService<T> chooseExtServ(Integer seriesCode);

	/**
	 * 选择异常解析服务
	 *
	 * @param deviceSn 设备sn
	 * @return 异常解析服务实例
	 */
	ParseErrorService chooseParseErrServ(String deviceSn);

	/**
	 * 选择异常解析服务
	 *
	 * @param resource 设备
	 * @return 异常解析服务实例
	 */
	ParseErrorService chooseParseErrServ(ResourceResDTO resource);
}
