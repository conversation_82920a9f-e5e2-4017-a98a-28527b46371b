package com.weihengtech.service.specific.impl;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.api.pojo.vos.ReadBatchDeviceVO;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.vos.device.DeviceSpeedUpVO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.iot.IotClientService;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.ModbusParseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service(value = "specificServ2")
public class SpecificServOcppServiceImpl implements SpecificServService {

	@Resource
	private DeviceListService deviceListService;
	@Resource
	private IotClientService ocppIotClient;

	@Override
	public void speedupOnce(String deviceFlag) {
		// do nothing
	}

	@Override
	public void speedupKeep(String deviceFlag, DeviceSpeedUpVO param) {
		// do nothing
	}

	@Override
	public void sendSpeedupCommand(String wifiSn) {
		// do nothing
	}

	@Override
	public List<String> batchGetSpeedUpDeviceList() {
		return Collections.emptyList();
	}

	@Override
	public void sendUpgradeCommand(String deviceName, String downloadUrl, Long size) {
		// do nothing
	}

	@Override
	public Map<String, String> getFirmwareVersionByTypeList(String deviceName, String typeName) {
		return Collections.emptyMap();
	}

	@Override
	public Map<String, String> getFirmwareVersionByDeviceListDOAndTypeName(DeviceListDO deviceListDO, String typeName) {
		return Collections.emptyMap();
	}

	@Override
	public DeviceStatusEnum checkDeviceStatus(String wifiSn) {
		if (StrUtil.isEmpty(wifiSn)) {
			return DeviceStatusEnum.OFFLINE;
		}
		DeviceListDO deviceListDO = deviceListService.getDeviceBeanByWifiSn(wifiSn);
		BeanUtil.assertNotNull(deviceListDO);
		if (deviceListDO.getFirstInstall() == 0) {
			return DeviceStatusEnum.UNKNOWN;
		}
		try {
//			TuyaDeviceBasicInfoDto deviceBasicInfo = ocppIotClient.getDeviceBasicInfo(wifiSn);
//			return deviceBasicInfo.getOnline() ? DeviceStatusEnum.ONLINE : DeviceStatusEnum.OFFLINE;
			return ocppIotClient.queryIsOnlineStatus(wifiSn) ? DeviceStatusEnum.ONLINE : DeviceStatusEnum.OFFLINE;
		} catch (Exception e) {
			log.error("ocppIotClient getDeviceBasicInfo failed", e);
			return DeviceStatusEnum.OFFLINE;
		}
	}

	@Override
	public List<Integer> sendReadCommand(String wifiSn, Integer slaveId, Integer startAddress, Integer len) {
		return Collections.emptyList();
	}

	@Override
	public Map<String, List<Integer>> sendBatchReadCommand(ReadBatchDeviceVO param) {
		return Collections.emptyMap();
	}

	@Override
	public Boolean sendWriteCommand(
			String wifiSn, Integer slaveId, Integer startAddress, Integer len,
			List<Integer> values
	) {
		return false;
	}

	@Override
	public String getDeviceSnByWifiSn(String wifiSn) {
		try {
			List<Integer> listRes = this.sendReadCommand(wifiSn, 1, 30042, 10);
			return ModbusParseUtil.parseAscii(listRes);
		} catch (Exception e) {
			return "";
		}
	}

	@Override
	public String getDeviceIpByWifiSn(String wifiSn) {
		return "";
	}

    @Override
    public Boolean isOnline(String wifiSn) {
        return ocppIotClient.queryIsOnlineStatus(wifiSn);
    }

	@Override
	public void changeDeviceCategory(String deviceSn, String wifiSn, String category) {

	}
}
