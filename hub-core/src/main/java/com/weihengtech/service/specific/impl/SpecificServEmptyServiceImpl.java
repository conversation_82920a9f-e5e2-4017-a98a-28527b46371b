package com.weihengtech.service.specific.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONObject;
import com.weihengtech.api.pojo.vos.ReadBatchDeviceVO;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.vos.device.DeviceSpeedUpVO;
import com.weihengtech.service.specific.SpecificServService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service(value = "specificServ9")
@Slf4j
public class SpecificServEmptyServiceImpl implements SpecificServService {

	@Override
	public void speedupOnce(String deviceFlag) {
	}

	@Override
	public void speedupKeep(String deviceFlag, DeviceSpeedUpVO param) {
	}

	@Override
	public List<String> batchGetSpeedUpDeviceList() {
		return ListUtil.empty();
	}

	@Override
	public void sendSpeedupCommand(String deviceFlag) {
		throw new CustomException(ExceptionEnum.DEVICE_NO_WIFISN);
	}

	@Override
	public void sendUpgradeCommand(String deviceFlag, String downloadUrl, Long size) {
	}

	@Override
	public List<Integer> sendReadCommand(String deviceFlag, Integer slaveId, Integer startAddress, Integer len) {
		return Collections.emptyList();
	}

	@Override
	public Map<String, List<Integer>> sendBatchReadCommand(ReadBatchDeviceVO param) {
		return Collections.emptyMap();
	}

	@Override
	public Boolean sendWriteCommand(
			String deviceFlag, Integer slaveId, Integer startAddress, Integer len,
			List<Integer> values
	) {
		return Boolean.FALSE;
	}

	@Override
	public Map<String, String> getFirmwareVersionByTypeList(String deviceFlag, String typeName) {
		return MapUtil.empty();
	}

	@Override
	public Map<String, String> getFirmwareVersionByDeviceListDOAndTypeName(DeviceListDO deviceListDO, String typeName) {
		return MapUtil.empty();
	}

	@Override
	public DeviceStatusEnum checkDeviceStatus(String deviceFlag) {
		return DeviceStatusEnum.OFFLINE;
	}

	@Override
	public String getDeviceSnByWifiSn(String wifiSn) {
		return "";
	}

	@Override
	public String getDeviceIpByWifiSn(String wifiSn) {
		return null;
	}

    @Override
    public Boolean isOnline(String wifiSn) {
        return null;
    }

	@Override
	public void changeDeviceCategory(String deviceSn, String wifiSn, String category) {

	}

	@Override
	public JSONObject getDeviceAssignProperty(String deviceFlag, String propertyName) {
		return new JSONObject();
	}
}
