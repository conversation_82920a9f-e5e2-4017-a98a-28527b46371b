package com.weihengtech.service.specific.impl;

import com.weihengtech.auth.dto.ResourceListReqDTO;
import com.weihengtech.auth.dto.ResourceResDTO;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.service.charger.impl.ChargerInfoServiceImpl;
import com.weihengtech.consts.CommonConstants;
import com.weihengtech.enums.device.SeriesEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.ext.ExtCommonService;
import com.weihengtech.service.parser.ParseErrorService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.service.thirdpart.TimeSeriesDatabaseService;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.InitUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class StrategyServiceImpl implements StrategyService {

	@Value("${custom.datacenter.name}")
	private String datacenter;

	@Resource
	private AuthCenterResClient authCenterResClient;

	private final Map<Integer, Class<? extends ExtCommonService>> extCommonServiceMap = new HashMap<>();

	@PostConstruct
	public void init() {
		// 充电桩扩展信息服务
		extCommonServiceMap.put(SeriesEnum.AC_Charger.getId(), ChargerInfoServiceImpl.class);
	}

	@Resource
	private DeviceListService deviceListService;

	@Override
	public SpecificServService chooseSpecificServ(Long deviceId) {
		return chooseSpecificServ(deviceListService.getById(deviceId));
	}

	@Override
	public SpecificServService chooseSpecificServ(DeviceListDO deviceListDO) {
		BeanUtil.assertNotNull(deviceListDO);
		return chooseSpecificServ(deviceListDO.getDataSource());
	}

	@Override
	public SpecificServService chooseSpecificServ(int dataSource) {
		return InitUtil.getBean("specificServ" + dataSource, SpecificServService.class);
	}

	@Override
	public TimeSeriesDatabaseService chooseTimeSeriesDatabaseService(DeviceListDO deviceListDO) {
		BeanUtil.assertNotNull(deviceListDO);
		return InitUtil.getBean("tsdbService" + deviceListDO.getTsdbSource(), TimeSeriesDatabaseService.class);
	}

	@Override
	public <T> ExtCommonService<T> chooseExtServ(Integer seriesCode) {
		if (!extCommonServiceMap.containsKey(seriesCode)) {
			return null;
		}
		return InitUtil.getBean(extCommonServiceMap.get(seriesCode));
	}

	@Override
	public ParseErrorService chooseParseErrServ(String deviceSn) {
		List<ResourceResDTO> resourceList = authCenterResClient.resourceList(ResourceListReqDTO.builder()
				.code(deviceSn)
				.dataCenter(datacenter)
				.build());
		Assert.notEmpty(resourceList, "device not exists");
		return chooseParseErrServ(resourceList.get(0));
	}

    @Override
    public ParseErrorService chooseParseErrServ(ResourceResDTO resource) {
		boolean isSingle = SeriesEnum.Single_phase.getId().equals(resource.getCategory());
		boolean isThree = SeriesEnum.Three_phase.getId().equals(resource.getCategory());
		boolean isUs = SeriesEnum.NA_Device.getId().equals(resource.getCategory());
		if (isUs) {
			return InitUtil.getBean("usParseErrorImpl", ParseErrorService.class);
		} else if (isSingle) {
			return InitUtil.getBean("singleParseErrorImpl", ParseErrorService.class);
		} else if (isThree) {
			return InitUtil.getBean("threeParseErrorImpl", ParseErrorService.class);
		} else {
			throw new IllegalArgumentException("no parseErrorService found");
		}
    }
}
