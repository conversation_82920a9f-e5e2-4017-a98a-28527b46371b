package com.weihengtech.service.order;

import com.weihengtech.api.pojo.dtos.WorkOrderDetailDto;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.order.PageWorkOrderVo;
import com.weihengtech.pojo.vos.order.ReplyWorkOrderVo;

/**
 * <AUTHOR>
 */
public interface IWorkOrderService {

	/**
	 * 分页查询用户工单
	 *
	 * @param pageWorkOrderVo 页码
	 * @return 分页数据
	 */
	PageInfoDTO<WorkOrderDetailDto> pageWorkOrder(PageWorkOrderVo pageWorkOrderVo);


	/**
	 * 回复工单
	 *
	 * @param replyWorkOrderVo 回复工单入参
	 */
	void replyWorkOrder(ReplyWorkOrderVo replyWorkOrderVo);

	/**
	 * 获取工单详情
	 *
	 * @param workOrderId 工单id
	 * @return 工单详情
	 */
	WorkOrderDetailDto detailWorkOrder(String workOrderId);
}
