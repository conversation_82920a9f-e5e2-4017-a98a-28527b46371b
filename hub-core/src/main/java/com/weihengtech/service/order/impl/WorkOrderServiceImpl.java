package com.weihengtech.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.api.WorkOrderApi;
import com.weihengtech.api.pojo.base.WorkOrderResp;
import com.weihengtech.api.pojo.dtos.WorkOrderDetailDto;
import com.weihengtech.api.pojo.dtos.WorkOrderPageDto;
import com.weihengtech.api.pojo.vos.WorkOrderListVo;
import com.weihengtech.api.pojo.vos.WorkOrderPageVo;
import com.weihengtech.api.pojo.vos.WorkOrderReplyVo;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.RoleConstants;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.order.PageWorkOrderVo;
import com.weihengtech.pojo.vos.order.ReplyWorkOrderVo;
import com.weihengtech.service.device.DeviceBoundService;
import com.weihengtech.service.order.IWorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkOrderServiceImpl implements IWorkOrderService {

	@Value("${custom.order.project}")
	private String projectToken;
	@Resource
	private WorkOrderApi workOrderApi;
	@Resource
	private DeviceBoundService deviceBoundService;

	@Override
	public PageInfoDTO<WorkOrderDetailDto> pageWorkOrder(PageWorkOrderVo pageWorkOrderVo) {
		String userRole = UserInfoUtil.currentUserRoleCategory();
		List<String> deviceSnList = new ArrayList<>();
		PageInfoDTO<WorkOrderDetailDto> pageInfo = new PageInfoDTO<>();

		if (RoleConstants.AGENT_ROLE.contains(userRole)) {
			deviceSnList = deviceBoundService.filterBoundDevice();
		}

		if (CollUtil.isNotEmpty(deviceSnList)) {
			WorkOrderPageVo workOrderPageVo = new WorkOrderPageVo().setPage(pageWorkOrderVo.getPageNum())
					.setSize(pageWorkOrderVo.getPageSize())
					.setAttachments(deviceSnList);
			Optional.ofNullable(pageWorkOrderVo.getDeviceSn()).ifPresent(workOrderPageVo::setAttachmentField);
			Optional.ofNullable(pageWorkOrderVo.getState()).ifPresent(workOrderPageVo::setState);

			WorkOrderResp<WorkOrderPageDto> resp = workOrderApi.pageWorkOrder(projectToken, workOrderPageVo);
			if (resp.getSuccess()) {
				WorkOrderPageDto workOrderPageDto = resp.getData();
				pageInfo.setTotalPages(workOrderPageDto.getPages());
				pageInfo.setTotalCount(workOrderPageDto.getTotal().longValue());
				pageInfo.setData(workOrderPageDto.getRecords());
			}
		}
		return pageInfo;
	}

	@Override
	public void replyWorkOrder(ReplyWorkOrderVo replyWorkOrderVo) {
		String name = UserInfoUtil.currentUserNickName();
		WorkOrderResp<Object> resp = workOrderApi.replyWorkOrder(projectToken,
				new WorkOrderReplyVo()
						.setOrderId(replyWorkOrderVo.getWorkOrderId())
						.setPicList(CollUtil.isEmpty(replyWorkOrderVo.getPicList()) ? ListUtil.empty() : replyWorkOrderVo.getPicList())
						.setName(name)
						.setContent(StrUtil.isBlank(replyWorkOrderVo.getContent()) ? "" : replyWorkOrderVo.getContent())
		);
		if (!resp.getSuccess()) throw new CustomException(ExceptionEnum.ACTION_NOT_SUCCESS_ERROR);
	}

	@Override
	public WorkOrderDetailDto detailWorkOrder(String workOrderId) {
		WorkOrderResp<List<WorkOrderDetailDto>> resp = workOrderApi.listWorkOrder(
				projectToken, new WorkOrderListVo().setOrderIdList(ListUtil.toList(workOrderId)));
		List<WorkOrderDetailDto> data = resp.getData();
		if (!resp.getSuccess() && CollUtil.isEmpty(data))
			throw new CustomException(ExceptionEnum.ACTION_NOT_SUCCESS_ERROR);
		return data.get(0);
	}
}
