package com.weihengtech.service.charger;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.charger.ChargerInfoDO;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.device.info.ChargerDeviceInfoDTO;
import com.weihengtech.pojo.vos.device.DeviceSyncStateVo;
import com.weihengtech.pojo.vos.charger.ChargerSaveVO;

/**
 * <p>
 * 充电桩信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
public interface ChargerInfoService extends IService<ChargerInfoDO> {
    /**
     * 根据设备列表id查询充电桩扩展信息
     *
     * @param deviceId 设备列表id
     * @return 充电桩扩展信息
     */
    ChargerDeviceInfoDTO queryByDeviceId(Long deviceId);

    /**
     * 保存充电桩信息
     *
     * @param chargerSaveVO 充电桩保存信息
     */
    void saveNewCharger(ChargerSaveVO chargerSaveVO);

    /**
     * 更新充电桩信息
     *
     * @param chargerSaveVO 充电桩保存信息
     */
    void updCharger(ChargerSaveVO chargerSaveVO);

    /**
     * 同步设备状态
     *
     * @param deviceSyncStateVo 设备id
     * @return 设备当前状态
     */
    Integer syncDeviceState(DeviceSyncStateVo deviceSyncStateVo);

    /**
     * 查询设备状态
     *
     * @param deviceInfo
     * @return
     */
    Integer queryDeviceState(DeviceListDO deviceInfo);
}
