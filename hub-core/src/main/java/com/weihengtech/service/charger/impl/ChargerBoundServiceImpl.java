package com.weihengtech.service.charger.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.weihengtech.auth.dto.BindInfoDTO;
import com.weihengtech.auth.dto.BindReqDTO;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.pojo.dos.device.DeviceListDefaultDO;
import com.weihengtech.prometheus.BindFailMetrics;
import com.weihengtech.service.charger.ChargerBoundService;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.RoleConstants;
import com.weihengtech.enums.charger.BindModeEnum;
import com.weihengtech.enums.charger.ChargerTypeModelEnum;
import com.weihengtech.enums.device.SeriesEnum;
import com.weihengtech.enums.system.StepEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.systeminfo.SystemDeviceRelDO;
import com.weihengtech.pojo.dos.systeminfo.SystemInfoDO;
import com.weihengtech.pojo.dtos.charger.NetBindChargerDTO;
import com.weihengtech.pojo.vos.charger.NetBindChargerVO;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpInfoResponse;
import com.weihengtech.service.device.DeviceListDefaultService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.iot.IotClientService;
import com.weihengtech.service.other.EcosCountryService;
import com.weihengtech.service.other.RetryService;
import com.weihengtech.service.systeminfo.SystemDeviceRelService;
import com.weihengtech.service.systeminfo.SystemInfoService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.TransactionUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.ServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 充电桩绑定服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/25 14:03
 */
@Service
@Slf4j
public class ChargerBoundServiceImpl implements ChargerBoundService {

    /**
     * 未知充电桩类型
     */
    protected static final Integer UNKNOWN_CHARGER = 15;

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private SystemInfoService systemInfoService;
    @Resource
    private SystemDeviceRelService systemDeviceRelService;
    @Resource
    private DeviceListService deviceListService;
    @Resource
    private AuthCenterResClient authCenterResClient;
    @Resource
    private EcosCountryService ecosCountryService;
    @Resource
    private IotClientService ocppIotClient;
    @Resource
    private RetryService retryService;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private BindFailMetrics bindFailMetrics;
    @Resource
    private DeviceListDefaultService deviceListDefaultService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NetBindChargerDTO netDeviceBind(NetBindChargerVO netBindChargerVO) {
        Long userId = Long.parseLong(UserInfoUtil.currentUserId());
        String wifiSn = netBindChargerVO.getWifiSn();

        RLock lock = redissonClient.getLock(wifiSn);
        try {
            if (lock.tryLock(10, TimeUnit.SECONDS)) {
                try {
                    return netDeviceBind(netBindChargerVO, userId);
                } catch (Exception e) {
                    bindFailMetrics.recordFailure(wifiSn, "充电桩绑定最终失败");
                    log.error(String.format("gateSn:%s, userId:%s, systemId:%s, lon:%s, lat:%s bind failure",
                            wifiSn, userId, netBindChargerVO.getSystemId(), netBindChargerVO.getLon(),
                            netBindChargerVO.getLat()), e);
                    throw new CustomException(ExceptionEnum.DEVICE_BIND_FAILURE);
                } finally {
                    lock.unlock();
                }
            }else {
                throw new CustomException(ExceptionEnum.DEVICE_BIND_ING);
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 绑定账号
     *
     * @param netBindChargerVO 绑定入参
     * @param userId userId
     * @return 绑定结果
     */
    private NetBindChargerDTO netDeviceBind(NetBindChargerVO netBindChargerVO, Long userId) {
        Long systemId = netBindChargerVO.getSystemId();
        // 根据系统id获取当前系统信息
        SystemInfoDO sysInfo = systemInfoService.getById(systemId);
        ActionFlagUtil.assertTrue(sysInfo != null);
        // 根据WiFi Sn查询设备IP
        String ip = getDeviceIpFromReq();
        // 根据IP查询归属地国家
        String installCountry = getDeviceCountryByIp(ip);
        netBindChargerVO.setInstallCountry(installCountry);
        // 查询设备类型、设备型号
        if (BindModeEnum.isNetBind(netBindChargerVO.getBindMode())) {
            CpInfoResponse chargerInfo = ocppIotClient.getChargerInfo(netBindChargerVO.getChargerSn());
            if (chargerInfo == null || StrUtil.isBlank(chargerInfo.getModel())) {
                bindFailMetrics.recordFailure(netBindChargerVO.getChargerSn(), "获取充电桩型号失败");
                log.error(String.format("getChargerInfo failed, chargerInfo is empty: %s", netBindChargerVO.getChargerSn()));
                throw new CustomException(ExceptionEnum.DEVICE_BIND_FAILURE);
            }
            Integer typeId = ChargerTypeModelEnum.getTypeByCode(Integer.parseInt(chargerInfo.getModel()));
            String model = ChargerTypeModelEnum.getModelByCode(Integer.parseInt(chargerInfo.getModel()));
            netBindChargerVO.setResourceType(typeId);
            netBindChargerVO.setDeviceModel(model);
        }
        // 执行绑定逻辑
        bindDeviceAction(netBindChargerVO, userId, sysInfo);
        // 事务提交后，异步执行更新设备状态
        TransactionUtils.afterCommit(() -> threadPoolTaskExecutor.execute(() -> retryService.syncChargerState(netBindChargerVO.getChargerSn())));
        return NetBindChargerDTO.builder()
                .gateSn(netBindChargerVO.getWifiSn())
                .chargerSn(netBindChargerVO.getChargerSn())
                .build();
    }

    /**
     * 根据IP获取设备归属地国家
     *
     * @param ip ip
     * @return 国家
     */
    private String getDeviceCountryByIp(String ip) {
        return Optional.ofNullable(ip)
                .map(i -> ecosCountryService.queryIpList(Collections.singletonList(ip)))
                .map(i -> i.get(ip))
                .orElse(ip);
    }

    /**
     * 获取tuya设备的IP
     *
     * @return ip
     */
    private String getDeviceIpFromReq() {
        return Optional.ofNullable(RequestContextHolder.getRequestAttributes())
                .map(i -> (ServletRequestAttributes) i)
                .map(ServletRequestAttributes::getRequest)
                .map(ServletRequest::getRemoteAddr)
                .orElse(null);
    }

    /**
     * 是否已经被绑定过，如果被其他安装商绑定过，则清除原有安装商绑定关系
     *
     * @param deviceInfo deviceInfo
     * @param userId userId
     * @return 绑定关系
     */
    private void checkBindExists(DeviceListDO deviceInfo, Long userId) {
        Long deviceId = deviceInfo.getId();
        log.info("gate sn already bind with device: {}", deviceInfo.getDeviceSn());
        List<BindInfoDTO> bindInfoList = authCenterResClient.getBindInfo(String.valueOf(deviceId), RoleConstants.ROLE_INSTALLER);
        if (CollUtil.isEmpty(bindInfoList)) {
            return;
        }
        BindInfoDTO bindInfo = bindInfoList.get(0);
        log.info("device already bind with user: {}", JSONObject.toJSONString(bindInfo));
        if (!String.valueOf(userId).equals(bindInfo.getUserId())) {
            // 如果已被其他安装商绑定:1、解除已有安装商设备与系统关系 2、退回已有安装商的系统的操作步骤
            Long installerId = Long.parseLong(bindInfo.getUserId());
            Long curDeviceId = Long.parseLong(bindInfo.getResourceId());
            boolean flag = systemInfoService.backStepSystem(installerId, curDeviceId);
            ActionFlagUtil.assertTrue(flag);
            boolean res = systemDeviceRelService.removeRel(installerId, curDeviceId);
            ActionFlagUtil.assertTrue(res);
        }
    }

    /**
     * 执行绑定过程，如果被其他安装商绑定过，则覆盖
     *
     * @param userId wifiSn
     * @param systemInfo 系统信息
     */
    private void bindDeviceAction(NetBindChargerVO bindParam, Long userId, SystemInfoDO systemInfo) {
        String deviceSn = bindParam.getChargerSn();
        DeviceListDO deviceInfo = deviceListService.lambdaQuery().eq(DeviceListDO::getDeviceName, deviceSn).one();
        DeviceListDefaultDO defaultCenterDevice = deviceListDefaultService.lambdaQuery().eq(DeviceListDefaultDO::getDeviceName, deviceSn).one();
        Long deviceId;
        if (null == deviceInfo && defaultCenterDevice == null) {
            log.info("device and default device is not exists, {} has been created", deviceSn);
            bindParam.buildParam();
            if (bindParam.getResourceType() == null) {
                bindParam.setResourceType(UNKNOWN_CHARGER);
            }
            deviceId = deviceListService.saveDeviceInfo(deviceSn, bindParam,
                    systemInfo.getDatacenterId());
        } else if (null != defaultCenterDevice) {
            log.info("default device: [{}] is exists", deviceSn);
            deviceInfo = new DeviceListDO();
            BeanUtils.copyProperties(defaultCenterDevice, deviceInfo);
            deviceListService.save(deviceInfo);
            log.info("create local device: [{}] and update", deviceSn);
            deviceId = deviceInfo.getId();
            // 检查当前设备是否已经被绑定过
            checkBindExists(deviceInfo, userId);
            // 更新设备信息
            deviceInfo.setDatacenterId(systemInfo.getDatacenterId());
            bindParam.buildParam();
            deviceListService.updateDeviceInfo(deviceInfo, bindParam);
        } else {
            log.info("device: [{}] is exists, has been updated", deviceSn);
            deviceId = deviceInfo.getId();
            // 检查当前设备是否已经被绑定过
            checkBindExists(deviceInfo, userId);
            // 更新设备信息
            deviceInfo.setDatacenterId(systemInfo.getDatacenterId());
            bindParam.buildParam();
            deviceListService.updateDeviceInfo(deviceInfo, bindParam);
        }
        // 绑定中间表
        authCenterResClient.bindInstaller(BindReqDTO.builder()
                .userId(String.valueOf(userId))
                .resourceId(String.valueOf(deviceId))
                .build());
        // 添加系统设备
        boolean addFlag = systemDeviceRelService.addDevice(systemInfo.getId(), deviceId);
        ActionFlagUtil.assertTrue(addFlag);
        // 如果系统的所有设备中没有储能机，则直接更新为步骤三，否则为步骤二
        if (BindModeEnum.isFinishCurStep(bindParam.getBindMode())) {
            updateSysStep(systemInfo);
        }
    }

    /**
     * 更新系统步骤
     *
     * @param systemInfo 系统信息
     */
    private void updateSysStep(SystemInfoDO systemInfo) {
        List<SystemDeviceRelDO> deviceList = systemDeviceRelService.listBySystemId(systemInfo.getId());
        boolean isContainEnergyStorage = deviceList.stream()
                .anyMatch(i -> SeriesEnum.isEnergyStorageDevice(i.getResourceSeries()));
        boolean stepFlag = systemInfoService.updateById(SystemInfoDO.builder()
                .id(systemInfo.getId())
                .currentStep(isContainEnergyStorage ? StepEnum.STEP_2.getStepNum() : StepEnum.STEP_3.getStepNum())
                .build());
        ActionFlagUtil.assertTrue(stepFlag);
    }
}
