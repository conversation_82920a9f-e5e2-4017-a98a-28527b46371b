package com.weihengtech.service.charger.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import com.weihengtech.auth.dto.ResourceResDTO;
import com.weihengtech.auth.dto.ResourceSearchReqDTO;
import com.weihengtech.auth.model.bo.PageInfo;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.consts.CommonConstants;
import com.weihengtech.consts.TsdbMetricsConstants;
import com.weihengtech.enums.charger.ChargerStatusEnum;
import com.weihengtech.enums.charger.ChargerTypeModelEnum;
import com.weihengtech.enums.tsdb.TsdbSampleEnum;
import com.weihengtech.pojo.dos.charger.ChargeRecordDO;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.charger.ChargerRunDataDTO;
import com.weihengtech.pojo.dtos.device.info.ChargerDeviceInfoDTO;
import com.weihengtech.pojo.dtos.tsdb.TsdbQueryDTO;
import com.weihengtech.service.charger.ChargeRecordService;
import com.weihengtech.service.charger.ChargerDetailService;
import com.weihengtech.service.charger.ChargerInfoService;
import com.weihengtech.service.device.DeviceBoundService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.service.thirdpart.TimeSeriesDatabaseService;
import com.weihengtech.utils.ActionFlagUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * 充电桩详情服务实现类
 *
 * <AUTHOR>
 * @date 2024/1/25 10:51
 * @version 1.0
 */
@Service
public class ChargerDetailServiceImpl implements ChargerDetailService {

    @Value("${custom.datacenter.name}")
    private String datacenter;

    @Resource
    private DeviceBoundService deviceBoundService;
    @Resource
    private DeviceListService deviceListService;
    @Resource
    private AuthCenterResClient authCenterResClient;
    @Resource
    private ChargerInfoService chargerInfoService;
    @Resource
    private ChargeRecordService chargeRecordService;
    @Resource
    private StrategyService strategyService;

    @Override
    public ChargerDeviceInfoDTO queryDeviceInformation(String deviceName) {
        deviceBoundService.onlyProcessBoundDevice(deviceName);
        PageInfo<ResourceResDTO> pageInfo = authCenterResClient.resourceSearch(
                ResourceSearchReqDTO.builder()
                        .name(deviceName)
                        .dataCenterCode(datacenter)
                        .build());
        List<ResourceResDTO> list = pageInfo.getData() == null ? Collections.emptyList() : pageInfo.getData();
        ActionFlagUtil.assertTrue(list.size() == 1);
        ResourceResDTO resourceResDTO = list.get(0);
        DeviceListDO deviceList = deviceListService.getDeviceInfoByDeviceName(deviceName);
        ChargerTypeModelEnum modelEnum = ChargerTypeModelEnum.getEnumById(resourceResDTO.getType());
        return ChargerDeviceInfoDTO.buildParam(resourceResDTO, deviceList, modelEnum);
    }

    @Override
    public ChargerRunDataDTO runData(String deviceId) {
        DeviceListDO deviceInfo = deviceListService.getById(deviceId);
        // 判断充电桩状态
        Integer chargerState = chargerInfoService.queryDeviceState(deviceInfo);
        if (ChargerStatusEnum.Charging.getDbCode() != chargerState) {
            return ChargerRunDataDTO.builder()
                    .state(chargerState)
                    .build();
        }
        ChargerRunDataDTO resItem = ChargerRunDataDTO.builder().build();
        // 上一次的充电记录
        ChargeRecordDO lastRecord = chargeRecordService.queryLastRecord(Long.valueOf(deviceId), false);
        long startTime = lastRecord.getStartTime().getTime() / 1000;
        long endTime = DateUtil.currentSeconds();
        // 查询最新实时数据，统计充电功率、电流、电压
        TimeSeriesDatabaseService tsdbService = strategyService.chooseTimeSeriesDatabaseService(deviceInfo);
        Dict lastPointDict;
        try {
            lastPointDict = tsdbService.lastDataPoint(deviceInfo.getDeviceSn(),
                    CommonConstants.CHARGE_NOW_RUN_DATA, endTime);
        } catch (Exception e) {
            return ChargerRunDataDTO.builder().build();
        }
        Optional.ofNullable(lastPointDict.getStr(TsdbMetricsConstants.POWER_ACTIVE_IMPORT))
                .map(BigDecimal::new)
                .map(i -> NumberUtil.round(i, 1, RoundingMode.HALF_UP))
                .map(i -> Math.abs(i.doubleValue()) < 10d ? BigDecimal.ZERO : i)
                .ifPresent(resItem::setPower);
        Optional.ofNullable(lastPointDict.getStr(TsdbMetricsConstants.VOLTAGE_L1))
                .map(BigDecimal::new)
                .map(i -> NumberUtil.round(i, 1, RoundingMode.HALF_UP))
                .map(i -> Math.abs(i.doubleValue()) < 10d ? BigDecimal.ZERO : i)
                .ifPresent(resItem::setVoltage1);
        Optional.ofNullable(lastPointDict.getStr(TsdbMetricsConstants.VOLTAGE_L2))
                .map(BigDecimal::new)
                .map(i -> NumberUtil.round(i, 1, RoundingMode.HALF_UP))
                .map(i -> Math.abs(i.doubleValue()) < 10d ? BigDecimal.ZERO : i)
                .ifPresent(resItem::setVoltage2);
        Optional.ofNullable(lastPointDict.getStr(TsdbMetricsConstants.VOLTAGE_L3))
                .map(BigDecimal::new)
                .map(i -> NumberUtil.round(i, 1, RoundingMode.HALF_UP))
                .map(i -> Math.abs(i.doubleValue()) < 10d ? BigDecimal.ZERO : i)
                .ifPresent(resItem::setVoltage3);
        Optional.ofNullable(lastPointDict.getStr(TsdbMetricsConstants.CURRENT_IMPORT_L1))
                .map(BigDecimal::new)
                .map(i -> NumberUtil.round(i, 1, RoundingMode.HALF_UP))
                .ifPresent(resItem :: setCurrent1);
        Optional.ofNullable(lastPointDict.getStr(TsdbMetricsConstants.CURRENT_IMPORT_L2))
                .map(BigDecimal::new)
                .map(i -> NumberUtil.round(i, 1, RoundingMode.HALF_UP))
                .ifPresent(resItem :: setCurrent2);
        Optional.ofNullable(lastPointDict.getStr(TsdbMetricsConstants.CURRENT_IMPORT_L3))
                .map(BigDecimal::new)
                .map(i -> NumberUtil.round(i, 1, RoundingMode.HALF_UP))
                .ifPresent(resItem :: setCurrent3);
        resItem.setStartTime(String.valueOf(startTime));
        // 查询实时充电量（开始时间-结束时间之内的充电量充电差值）
        Map<String, LinkedHashMap<Long, Object>> energyMap = tsdbService.deltaQuery(
                TsdbQueryDTO.builder()
                        .cloudId(deviceInfo.getDeviceSn())
                        .metricList(ListUtil.toLinkedList(TsdbMetricsConstants.ENERGY_ACTIVE_IMPORT_REGISTER))
                        .startTime(startTime)
                        .endTime(endTime)
                        .tsdbSampleEnum(TsdbSampleEnum.ONE_MINUTE_NEAR_POINT)
                        .build());
        Optional.ofNullable(energyMap.get(TsdbMetricsConstants.ENERGY_ACTIVE_IMPORT_REGISTER))
                .map(LinkedHashMap::values)
                .map(Collection::stream)
                .map(i -> i.reduce(BigDecimal.ZERO,
                        (a, b) -> new BigDecimal(String.valueOf(a)).add(new BigDecimal(String.valueOf(b)))))
                .map(i -> ((BigDecimal) i).divide(BigDecimal.valueOf(1000), 1, RoundingMode.HALF_UP))
                .ifPresent(resItem :: setChargeCapacity);
        resItem.setState(chargerState);
        return resItem;
    }
}
