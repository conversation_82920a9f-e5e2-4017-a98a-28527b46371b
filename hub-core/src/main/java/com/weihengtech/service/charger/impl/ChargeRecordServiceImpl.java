package com.weihengtech.service.charger.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.weihengtech.dao.charger.ChargeRecordMapper;
import com.weihengtech.enums.charger.ChargingStatusEnum;
import com.weihengtech.pojo.dos.charger.ChargeRecordDO;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.charger.ChargeStatusDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.charger.RecordPageVO;
import com.weihengtech.pojo.vos.charger.RecordQueryVO;
import com.weihengtech.pojo.vos.charger.RecordSaveVO;
import com.weihengtech.pojo.vos.charger.RecordUpdVO;
import com.weihengtech.service.charger.ChargeRecordService;
import com.weihengtech.service.device.DeviceBoundService;
import com.weihengtech.service.device.DeviceListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.OffsetDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 充电桩充电记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Service
@Slf4j
public class ChargeRecordServiceImpl extends ServiceImpl<ChargeRecordMapper, ChargeRecordDO> implements ChargeRecordService {

    @Resource
    private DeviceBoundService deviceBoundService;
    @Resource
    private DeviceListService deviceListService;

    @Override
    public List<ChargeRecordDO> queryChargeRecord(RecordQueryVO req) {
        req.checkParams();
        deviceBoundService.onlyProcessBoundDevice(req.getDeviceName());
        DeviceListDO deviceList = deviceListService.getDeviceInfoByDeviceName(req.getDeviceName());
        LambdaQueryWrapper<ChargeRecordDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChargeRecordDO::getDeviceId, deviceList.getId());
        wrapper.isNotNull(ChargeRecordDO::getEndTime);
        wrapper.between(ChargeRecordDO::getStartTime, req.getStartTime(), req.getEndTime());
        wrapper.orderByDesc(ChargeRecordDO::getStartTime);
        return list(wrapper);
    }

    @Override
    public ChargeRecordDO queryLastRecord(String deviceName) {
        deviceBoundService.onlyProcessBoundDevice(deviceName);
        DeviceListDO deviceList = deviceListService.getDeviceInfoByDeviceName(deviceName);
        return queryLastRecord(deviceList.getId(), true);
    }

    @Override
    public ChargeRecordDO queryLastRecord(Long deviceId, Boolean isFilterCharing) {
        LambdaQueryWrapper<ChargeRecordDO> wrapper = Wrappers.<ChargeRecordDO>lambdaQuery()
                .eq(ChargeRecordDO::getDeviceId, deviceId);
        if (isFilterCharing != null && isFilterCharing) {
            wrapper.isNotNull(ChargeRecordDO::getEndTime);
        }
        wrapper.orderByDesc(ChargeRecordDO::getStartTime)
                .last("limit 1");
        return getOne(wrapper);
    }

    @Override
    public void saveChargeRecord(RecordSaveVO req) {
        ChargeRecordDO params = new ChargeRecordDO();
        BeanUtils.copyProperties(req, params);
        save(params);
    }

    @Override
    public void updChargeRecord(RecordUpdVO req) {
        ChargeRecordDO params = new ChargeRecordDO();
        BeanUtils.copyProperties(req, params);
        updateById(params);
    }

    @Override
    public PageInfoDTO<ChargeRecordDO> pageChargeRecord(RecordPageVO req) {
        req.checkParams();
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        LambdaQueryWrapper<ChargeRecordDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChargeRecordDO::getDeviceId, req.getDeviceId());
        wrapper.isNotNull(ChargeRecordDO::getEndTime);
        wrapper.between(ChargeRecordDO::getStartTime, req.getStartTime(), req.getEndTime());
        wrapper.orderByDesc(ChargeRecordDO::getStartTime);
        List<ChargeRecordDO> resList = list(wrapper);
        // 构造分页信息
        PageInfo<ChargeRecordDO> pageInfo = new PageInfo<>(resList);
        return new PageInfoDTO<>(pageInfo);
    }

    @Override
    public void handleStatus(ChargeStatusDTO req) {
        if (ChargingStatusEnum.start.getCode() == req.getType()) {
            handleStartStatus(req);
        } else if (ChargingStatusEnum.stop.getCode() == req.getType()) {
            handleStopStatus(req);
        }
    }

    /** 处理停止充电状态 */
    private void handleStopStatus(ChargeStatusDTO req) {
        DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(req.getClientId());
        if (deviceInfo == null) return;
        ChargeRecordDO record = queryByDeviceAndTransactionId(deviceInfo.getId(), req.getTransactionId().longValue());
        if (record == null) {
            log.error("未找到充电记录:{}, {}", req.getClientId(), req.getTransactionId());
            return;
        }
        if (record.getEndTime() != null) {
            log.info("stop重复充电记录:{}, {}，忽略", req.getClientId(), req.getTransactionId());
            return;
        }
        OffsetDateTime offsetDateTime = OffsetDateTime.parse(req.getTimestamp());
        record.setEndTime(new Date(offsetDateTime.toInstant().toEpochMilli()));
        record.setDuration(record.getEndTime().getTime() - record.getStartTime().getTime());
        record.setBatCap(new BigDecimal(req.getMeter() - record.getMeterValue())
                .divide(new BigDecimal(1000), 3, RoundingMode.HALF_UP) + "kWh");
        record.setReason(req.getReason());
        updateById(record);
    }

    /** 处理开始充电状态 */
    private void handleStartStatus(ChargeStatusDTO req) {
        DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(req.getClientId());
        if (deviceInfo == null) return;
        ChargeRecordDO record = queryByDeviceAndTransactionId(deviceInfo.getId(), req.getTransactionId().longValue());
        if (record != null) {
            log.info("start重复充电记录:{}, {}，忽略", req.getClientId(), req.getTransactionId());
            return;
        }
        OffsetDateTime offsetDateTime = OffsetDateTime.parse(req.getTimestamp());
        saveChargeRecord(RecordSaveVO.builder()
                .deviceId(deviceInfo.getId())
                .transactionId(req.getTransactionId().longValue())
                .startTime(new Date(offsetDateTime.toInstant().toEpochMilli()))
                .meterValue(req.getMeter())
                .build());
    }

    /** 根据设备Id、事务Id获取记录 */
    private ChargeRecordDO queryByDeviceAndTransactionId(Long deviceId, Long transactionId) {
        LambdaQueryWrapper<ChargeRecordDO> wrapper = Wrappers.<ChargeRecordDO>lambdaQuery().eq(ChargeRecordDO::getDeviceId, deviceId)
                .eq(ChargeRecordDO::getTransactionId, transactionId);
        return getOne(wrapper);
    }
}
