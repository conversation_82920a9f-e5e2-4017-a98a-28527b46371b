package com.weihengtech.service.charger;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.charger.ChargeRecordDO;
import com.weihengtech.pojo.dtos.charger.ChargeStatusDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.charger.RecordPageVO;
import com.weihengtech.pojo.vos.charger.RecordQueryVO;
import com.weihengtech.pojo.vos.charger.RecordSaveVO;
import com.weihengtech.pojo.vos.charger.RecordUpdVO;

import java.util.List;

/**
 * <p>
 * 充电桩充电记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
public interface ChargeRecordService extends IService<ChargeRecordDO> {

    /**
     * 查询充电桩充电记录
     *
     * @param req 请求参数
     * @return 充电记录
     */
    List<ChargeRecordDO> queryChargeRecord(RecordQueryVO req);

    /**
     * 查询最近一次充电记录
     *
     * @param deviceName 设备sn
     * @return 充电记录
     */
    ChargeRecordDO queryLastRecord(String deviceName);

    /**
     * 查询最近一次充电记录
     *
     * @param deviceSn 设备sn
     * @return 充电记录
     */
    ChargeRecordDO queryLastRecord(Long deviceSn, Boolean isFilterCharing);

    /**
     * 保存充电记录
     *
     * @param req 保存参数
     */
    void saveChargeRecord(RecordSaveVO req);

    /**
     * 更新充电记录
     *
     * @param req 更新参数
     */
    void updChargeRecord(RecordUpdVO req);

    /**
     * 充电记录分页
     *
     * @param req 分页参数
     * @return 分页结果
     */
    PageInfoDTO<ChargeRecordDO> pageChargeRecord(RecordPageVO req);

    /**
     * 处理上报的充电状态
     *
     * @param req
     * @return
     */
    void handleStatus(ChargeStatusDTO req);
}
