package com.weihengtech.service.charger;

import com.weihengtech.pojo.dtos.charger.NetBindChargerDTO;
import com.weihengtech.pojo.vos.charger.NetBindChargerVO;

/**
 * 充电桩绑定服务
 *
 * <AUTHOR>
 * @date 2024/1/25 14:03
 * @version 1.0
 */
public interface ChargerBoundService {

	/**
	 * 配网流程账号绑定设备
	 *
	 * @param netBindChargerVO 绑定入参
     * @return 绑定结果
	 */
	NetBindChargerDTO netDeviceBind(NetBindChargerVO netBindChargerVO);
}
