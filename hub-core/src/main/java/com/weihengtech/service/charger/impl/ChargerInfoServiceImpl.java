package com.weihengtech.service.charger.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.dao.charger.ChargerInfoMapper;
import com.weihengtech.enums.charger.BindModeEnum;
import com.weihengtech.enums.charger.ChargerStatusEnum;
import com.weihengtech.enums.charger.ChargerTypeModelEnum;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.pojo.dos.charger.ChargerInfoDO;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.device.info.ChargerDeviceInfoDTO;
import com.weihengtech.pojo.vos.charger.ChargerSaveVO;
import com.weihengtech.pojo.vos.charger.NetBindChargerVO;
import com.weihengtech.pojo.vos.device.DeviceSyncStateVo;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.Connector;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpInfoResponse;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpStatusResponse;
import com.weihengtech.service.charger.ChargerInfoService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.ext.ExtCommonService;
import com.weihengtech.service.iot.IotClientService;
import com.weihengtech.service.other.EcosCountryService;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.utils.OperatingRecordUtil;
import com.weihengtech.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 充电桩信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Service
@Slf4j
public class ChargerInfoServiceImpl extends ServiceImpl<ChargerInfoMapper, ChargerInfoDO>
        implements ChargerInfoService, ExtCommonService<ChargerInfoDO> {

    @Resource
    private DeviceListService deviceListService;
    @Resource
    private IotClientService ocppIotClient;
    @Resource
    private EcosCountryService ecosCountryService;
    @Resource
    private TuyaDatacenterService tuyaDatacenterService;

    @Override
    public ChargerInfoDO getExtInfoByDeviceId(Long deviceId) {
        return getOne(Wrappers.<ChargerInfoDO>lambdaQuery()
                .eq(ChargerInfoDO::getDeviceId, deviceId));
    }

    @Override
    public List<ChargerInfoDO> getExtInfoByDeviceIds(Collection<Long> deviceIdList) {
        return list(Wrappers.<ChargerInfoDO>lambdaQuery()
                .in(ChargerInfoDO::getDeviceId, deviceIdList));
    }

    @Override
    public Map<Long, ChargerInfoDO> getExtMapByDeviceIds(Collection<Long> deviceIdList) {
        List<ChargerInfoDO> extList = getExtInfoByDeviceIds(deviceIdList);
        if (CollUtil.isEmpty(extList)) {
            return Collections.emptyMap();
        }
        return extList.stream()
                .collect(Collectors.toMap(ChargerInfoDO::getDeviceId, Function.identity()));
    }

    @Override
    public void updExtByDeviceId(ChargerInfoDO chargerInfo) {
        ChargerInfoDO existsItem = getExtInfoByDeviceId(chargerInfo.getDeviceId());
        if (existsItem == null) {
            this.save(chargerInfo);
            return;
        }
        chargerInfo.setId(existsItem.getId());
        updateById(chargerInfo);
    }

    @Override
    public ChargerDeviceInfoDTO queryByDeviceId(Long deviceId) {
        DeviceListDO deviceList = deviceListService.getDeviceById(deviceId);
        ChargerTypeModelEnum modelEnum = ChargerTypeModelEnum.getEnumById(deviceList.getResourceTypeId());
        return Optional.ofNullable(modelEnum)
                .map(i -> ChargerDeviceInfoDTO.builder()
                        .ratedPower(i.getRatedPower())
                        .ratedCurrent(i.getRatedCurrent())
                        .voltageUp(i.getVoltageUp())
                        .voltageDown(i.getVoltageDown())
                        .setupTime(deviceList.getFirstInstall())
                        .updateTime(TimeUtil.localDateTimeToSerialString(deviceList.getUpdateTime()))
                        .build())
                .orElse(new ChargerDeviceInfoDTO());
    }

    @Override
    public void saveNewCharger(ChargerSaveVO chargerSaveVO) {
        NetBindChargerVO param = buildNetBindParam(chargerSaveVO);
        deviceListService.saveDeviceInfo(chargerSaveVO.getChargerSn(), param, chargerSaveVO.getDataCenterId());
    }

    @Override
    public void updCharger(ChargerSaveVO chargerSaveVO) {
        NetBindChargerVO param = buildNetBindParam(chargerSaveVO);
        DeviceListDO deviceListDO = deviceListService.getById(chargerSaveVO.getDeviceId());
        deviceListDO.setDatacenterId(chargerSaveVO.getDataCenterId());
        deviceListService.updateDeviceInfo(deviceListDO, param);
    }

    @Override
    public Integer syncDeviceState(DeviceSyncStateVo deviceSyncStateVo) {
        DeviceListDO deviceListDO = deviceListService.getById(deviceSyncStateVo.getDeviceId());
        com.weihengtech.utils.BeanUtil.assertNotNull(deviceListDO);
        return queryDeviceState(deviceListDO);
    }

    @Override
    public Integer queryDeviceState(DeviceListDO deviceInfo) {
        Integer dbStatus;
        if (deviceInfo.getFirstInstall() == 0) {
            dbStatus = DeviceStatusEnum.UNKNOWN.getDbCode();
        } else {
            try {
                CpStatusResponse chargerStatus = ocppIotClient.getChargerStatus(deviceInfo.getDeviceName());
                List<Connector> connectors = chargerStatus.getConnectors();
                Assert.notEmpty(connectors, "charger connectors is empty");
                String status = connectors.get(0).getStatus();
                if (StrUtil.isBlank(status)) {
                    dbStatus = DeviceStatusEnum.OFFLINE.getDbCode();
                } else {
                    dbStatus = ChargerStatusEnum.getCodeByStatus(status);
                }
            } catch (Exception e) {
                dbStatus = DeviceStatusEnum.OFFLINE.getDbCode();
            }
        }
        Assert.notNull(dbStatus, "charger status is not mapped");
        return dbStatus;
    }

    /**
     * 构建充电桩配网参数
     *
     * @param chargerSaveVO 充电桩参数
     * @return netBindParam
     */
    private NetBindChargerVO buildNetBindParam(ChargerSaveVO chargerSaveVO) {
        // 构建新增充电桩参数
        NetBindChargerVO param = new NetBindChargerVO();
        BeanUtils.copyProperties(chargerSaveVO, param);
        param.buildParam();
        param.setWifiSn(chargerSaveVO.getGateSn());
        param.setDeviceId(chargerSaveVO.getDeviceId());
        param.setMaxPower(chargerSaveVO.getMaxPower());
        param.setCpPlugAndChargeMsg(chargerSaveVO.getCpPlugAndChargeMsg());
        param.setCpFirmwareVersion(chargerSaveVO.getCpFirmwareVersion());
        param.setCpMode(chargerSaveVO.getCpMode());
        param.setCpMeterType(chargerSaveVO.getCpMeterType());
        param.setCpMeterRatio(chargerSaveVO.getCpMeterRatio());
        param.setCpHomeCurrent(chargerSaveVO.getCpHomeCurrent());
        param.setCpPvCurrent(chargerSaveVO.getCpPvCurrent());
        // 翻译ip
        String ip = chargerSaveVO.getIp();
        if (StrUtil.isNotBlank(ip)) {
            Map<String, String> ipMap = ecosCountryService.queryIpList(Collections.singletonList(ip));
            param.setInstallCountry(ipMap.getOrDefault(ip, ip));
        }
        if (BindModeEnum.isNetBind(chargerSaveVO.getBindMode())) {
            // 查询设备类型、设备型号
            CpInfoResponse chargerInfo = ocppIotClient.getChargerInfo(chargerSaveVO.getChargerSn());
            if (chargerInfo == null || StrUtil.isBlank(chargerInfo.getModel())) {
                log.error(String.format("getChargerInfo failed, chargerInfo is empty: %s", chargerSaveVO.getChargerSn()));
                throw new RuntimeException("getChargerInfo failed");
            }
            Integer typeId = ChargerTypeModelEnum.getTypeByCode(Integer.parseInt(chargerInfo.getModel()));
            String model = ChargerTypeModelEnum.getModelByCode(Integer.parseInt(chargerInfo.getModel()));
            param.setResourceType(typeId);
            param.setDeviceModel(model);
        } else if (BindModeEnum.isDirectBind(chargerSaveVO.getBindMode())) {
            param.setResourceType(ChargerBoundServiceImpl.UNKNOWN_CHARGER);
        }
        return param;
    }
}
