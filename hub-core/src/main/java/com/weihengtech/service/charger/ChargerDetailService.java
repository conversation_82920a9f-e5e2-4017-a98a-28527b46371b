package com.weihengtech.service.charger;

import com.weihengtech.pojo.dtos.charger.ChargerRunDataDTO;
import com.weihengtech.pojo.dtos.device.info.ChargerDeviceInfoDTO;

/**
 * 充电桩详情服务
 *
 * <AUTHOR>
 * @date 2024/1/25 10:40
 * @version 1.0
 */
public interface ChargerDetailService {

	/**
	 * 获取 device information 页面数据
	 *
	 * @param deviceName    设备标识
	 * @return com.weihengtech.pojo.dtos.device.info.DeviceInformationDTO
	 */
	ChargerDeviceInfoDTO queryDeviceInformation(String deviceName);

	ChargerRunDataDTO runData(String deviceId);
}
