package com.weihengtech.service.parser;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.enums.device.BmsVendorEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.parser.ParseErrorDTO;
import com.weihengtech.pojo.vos.device.DeviceParseErrVO;
import com.weihengtech.pojo.vos.tsdb.TsdbParseErrorVo;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.utils.InitUtil;

import java.util.Optional;

import static com.weihengtech.consts.RoleConstants.ROLE_AGENT;
import static com.weihengtech.consts.RoleConstants.ROLE_DEALER;
import static com.weihengtech.consts.RoleConstants.ROLE_RETAILER;

/**
 * 异常解析服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 15:26
 */
public interface ParseErrorService {

    /**
     * 设备信息页解析错误码
     *
     * @param param 异常解析参数
     * @return 解析结果
     */
    ParseErrorDTO parseErrorInfo(DeviceParseErrVO param);

    /**
     * 多曲线解析错误码
     *
     * @param param 错误码类型与值
     * @return 解析结果
     */
    ParseErrorDTO parseError(TsdbParseErrorVo param);

    /** 是否代理商角色 */
    default boolean isAgent() {
        String userRole = UserInfoUtil.currentUserRoleCategory();
        return ListUtil.toList(ROLE_AGENT, ROLE_DEALER, ROLE_RETAILER).contains(userRole);
    }

    /** 是否宁德厂商 */
    default boolean isCatl(String deviceSn) {
        DeviceListService bean = InitUtil.getBean(DeviceListService.class);
        return Optional.ofNullable(deviceSn)
                .map(bean::getDeviceInfoByDeviceName)
                .map(DeviceListDO::getBmsVendor)
                .map(BmsVendorEnum.CATL.name()::equals)
                .orElse(false);
    }

    /** 通用构建错误信息 */
    default <T extends ParseErrorDTO> void buildErrorInfo(TsdbParseErrorVo param, T resItem) {
        resItem.setArmBit(StrUtil.fillBefore(NumberUtil.getBinaryStr(param.getArmDecimal()), '0', 32));
        resItem.setDspDecimal(param.getDspDecimal().longValue());
        resItem.setBmsDecimal(param.getBmsDecimal().longValue());
        if (isAgent()){
            return;
        }
        resItem.setDspBit1(StrUtil.fillBefore(NumberUtil.getBinaryStr(param.getDspBit1()), '0', 32));
        resItem.setDspBit2(StrUtil.fillBefore(NumberUtil.getBinaryStr(param.getDspBit2()), '0', 32));
        resItem.setDspBit3(StrUtil.fillBefore(NumberUtil.getBinaryStr(param.getDspBit3()), '0', 32));
        resItem.setBmsBitFault(StrUtil.fillBefore(NumberUtil.getBinaryStr(param.getBmsBitFault()), '0', 16));
        resItem.setBmsBitAlarm(StrUtil.fillBefore(NumberUtil.getBinaryStr(param.getBmsBitAlarm()), '0', 16));
        resItem.setBmsBitError(StrUtil.fillBefore(NumberUtil.getBinaryStr(param.getBmsBitError()), '0', 32));
        if (isCatl(param.getDeviceSn())) {
            resItem.setBmsBitErrorCatl(StrUtil.fillBefore(NumberUtil.getBinaryStr(param.getBmsBitError()), '0', 32));
            resItem.setBmsBitError2(StrUtil.fillBefore(NumberUtil.getBinaryStr(param.getBmsBitError2()), '0', 32));
            resItem.setBmsBitError3(StrUtil.fillBefore(NumberUtil.getBinaryStr(param.getBmsBitError3()), '0', 32));
            resItem.setBmsBitError4(StrUtil.fillBefore(NumberUtil.getBinaryStr(param.getBmsBitError4()), '0', 32));
            resItem.setBmsBitError5(StrUtil.fillBefore(NumberUtil.getBinaryStr(param.getBmsBitError5()), '0', 32));
        }
        resItem.setBmsHistory(StrUtil.fillBefore(NumberUtil.getBinaryStr(param.getBmsBitHistory()), '0', 64));
    }

    /** 通用构建错误信息 */
    default <T extends ParseErrorDTO> void buildErrorInfo(DeviceParseErrVO param, T resItem) {
        resItem.setDspBit1Value(param.getError1());
        resItem.setDspBit2Value(param.getError2());
        resItem.setDspBit3Value(param.getError3());
        resItem.setArmBitValue(param.getArmBit());
        resItem.setBmsBitErrorValue(param.getErrorH32());
        resItem.setBmsBitAlarmValue(param.getAlarmL16());
        resItem.setBmsBitFaultValue(param.getFaultH16());
        if (isCatl(param.getDeviceSn())) {
            resItem.setBmsBitErrorValueCatl(param.getErrorH32());
            resItem.setBmsBitError2Value(param.getError2H32());
            resItem.setBmsBitError3Value(param.getError3H32());
            resItem.setBmsBitError4Value(param.getError4H32());
            resItem.setBmsBitError5Value(param.getError5H32());
        }
    }
}
