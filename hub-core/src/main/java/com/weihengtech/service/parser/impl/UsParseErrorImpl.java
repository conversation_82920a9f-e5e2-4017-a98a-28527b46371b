package com.weihengtech.service.parser.impl;

import com.weihengtech.pojo.dtos.parser.ParseErrorDTO;
import com.weihengtech.pojo.dtos.parser.UsParseErrorDTO;
import com.weihengtech.pojo.vos.device.DeviceParseErrVO;
import com.weihengtech.pojo.vos.tsdb.TsdbParseErrorVo;
import com.weihengtech.service.parser.ParseErrorService;
import org.springframework.stereotype.Service;

/**
 * 异常解析服务-北美机实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 15:43
 */
@Service
public class UsParseErrorImpl implements ParseErrorService {

    @Override
    public ParseErrorDTO parseErrorInfo(DeviceParseErrVO param) {
        UsParseErrorDTO resItem = new UsParseErrorDTO();
        buildErrorInfo(param, resItem);
        return resItem;
    }

    @Override
    public UsParseErrorDTO parseError(TsdbParseErrorVo param) {
        UsParseErrorDTO resItem = new UsParseErrorDTO();
        buildErrorInfo(param, resItem);
        return resItem;
    }
}
