package com.weihengtech.service.device;

import com.weihengtech.api.pojo.dtos.AccountBindInfoDTO;
import com.weihengtech.pojo.dtos.device.DeviceBoundBindAccountDto;
import com.weihengtech.pojo.dtos.device.DeviceBoundUnBindAccountDto;
import com.weihengtech.pojo.dtos.device.NetBindDeviceDTO;
import com.weihengtech.pojo.dtos.ecos.InstallBoundDTO;
import com.weihengtech.pojo.dtos.ecos.InstallBoundInfoDTO;
import com.weihengtech.pojo.vos.device.DeviceBoundBindAccountVo;
import com.weihengtech.pojo.vos.device.DeviceBoundBindCountryVo;
import com.weihengtech.pojo.vos.device.NetBindDeviceVO;
import com.weihengtech.pojo.vos.device.NetBindWhVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DeviceBoundService {

	/**
	 * 设备绑定国家
	 *
	 * @param deviceBoundBindCountryVo 国家与设备参数
	 */
	void deviceBindCountry(DeviceBoundBindCountryVo deviceBoundBindCountryVo);

	/**
	 * 配网流程账号绑定设备
	 *
	 * @param netBindDeviceVo WiFi SN
	 * @return 绑定结果
	 */
	NetBindDeviceDTO netDeviceBind(NetBindDeviceVO netBindDeviceVo);

	/**
	 * 设备分享给安装商
	 *
	 * @param code 二维码密文
	 */
    void deviceShareInstaller(String code);

	/**
	 * 给指定vpp经销商账号绑定设备
	 *
	 * @param deviceBoundBindAccountVo 设备绑定入参
	 * @return 设备绑定结果
	 */
	DeviceBoundBindAccountDto deviceBindAgent(DeviceBoundBindAccountVo deviceBoundBindAccountVo);

	/**
	 * 给指定vpp经销商账号解绑设备
	 *
	 * @param deviceBoundBindAccountVo 设备解绑入参
	 * @return 设备解绑结果
	 */
	DeviceBoundUnBindAccountDto deviceUnBindAgent(DeviceBoundBindAccountVo deviceBoundBindAccountVo);

	/**
	 * 只能处理代理商绑定的设备
	 *
	 * @param deviceName 设备sn
	 */
	void onlyProcessBoundDevice(String deviceName);

	/**
	 * 获取当前用户绑定的设备SN
	 * @return 设备SN
	 */
	List<String> filterBoundDevice();

	/**
	 * 绑定安装商
	 *
	 * @param item 指定绑定时长
	 */
	void boundInstall(InstallBoundDTO item);

	/**
	 * 自研iot平台绑定账号
	 *
	 * @param netBindDeviceVo 绑定参数
	 * @return 绑定结果
	 */
    NetBindDeviceDTO iotNetDeviceBind(NetBindWhVO netBindDeviceVo);

	/**
	 * 设备是否在线
	 *
	 * @param wifiSn wifi棒序列号
	 * @return 设备是否在线
	 */
	Boolean iotIsOnline(String wifiSn);

	/**
	 * 获取设备绑定信息
	 *
	 * @param deviceId 设备id
	 * @param roleId 角色id
	 * @return 设备绑定信息
	 */
	InstallBoundInfoDTO getBindInfo(String deviceId, String roleId);

	/**
	 * 根据账号获取各种类型设备
	 *
	 * @param account ecos账号
	 * @return 设备绑定信息
	 */
	AccountBindInfoDTO getDevicesByAccount(String account);

	/**
	 * iot平台重置设备（解绑）
	 *
	 * @param wifiSn wifiSn
	 */
	void iotResetDevice(String wifiSn);
}
