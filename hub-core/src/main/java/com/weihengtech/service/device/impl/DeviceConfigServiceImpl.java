package com.weihengtech.service.device.impl;

import cn.hutool.aop.ProxyUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.weihengtech.api.EcosClientApi;
import com.weihengtech.api.EcosConfigApi;
import com.weihengtech.aspects.TimeCostLogAspect;
import com.weihengtech.auth.dto.ResourceConfigDTO;
import com.weihengtech.auth.dto.ResourceUpdDTO;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.CommonConstants;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.enums.device.DeviceDatasourceEnum;
import com.weihengtech.enums.device.DeviceInfoEncodeTypeEnum;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.enums.firmware.GwyTypeEnum;
import com.weihengtech.pojo.bos.config.OssGlobalConfigBO;
import com.weihengtech.pojo.bos.device.DeviceConfigBO;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.ecos.ClientCustomizeDo;
import com.weihengtech.pojo.dtos.device.DeviceDefaultConfigDto;
import com.weihengtech.pojo.dtos.device.config.ReactiveLimitDTO;
import com.weihengtech.pojo.dtos.device.config.SafetyParameterDTO;
import com.weihengtech.pojo.dtos.device.config.SettingDTO;
import com.weihengtech.pojo.dtos.other.DictItemDTO;
import com.weihengtech.pojo.vos.device.DeviceConfigConfigureVO;
import com.weihengtech.pojo.vos.device.DeviceSnVo;
import com.weihengtech.pojo.vos.device.DeviceSyncStateVo;
import com.weihengtech.service.device.DeviceBoundService;
import com.weihengtech.service.device.DeviceConfigService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.other.DictItemService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeviceConfigServiceImpl implements DeviceConfigService {

	private static final String MULTI_CHARGE_FLAG = "config:battery_setting:loading_shifting:MultistageChargeAndDischarge";
	private static final String ABANDON_PV_FLAG = "config:battery_setting:loading_shifting:abandonPV";

	@Value("${custom.device.config.path}")
	private String path;

	@Resource
	private ModbusRequestUtil modbusRequestUtil;
	@Resource
	private DeviceConfigBO deviceConfig;
	@Resource
	private DeviceListService deviceListService;
	@Resource
	private DeviceBoundService deviceBoundService;
	@Resource
	private StrategyService strategyService;
	@Resource
	private EcosClientApi ecosClientApi;
	@Resource
	private EcosConfigApi ecosConfigApi;
	@Resource
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;
	@Resource
	private DictItemService dictItemService;
	@Resource
	private AuthCenterResClient authCenterResClient;

	@Override
	public Object query(String deviceFlag, String code) {
		// 解码
		String decode = AesUtil.decode(code);
		// 权限校验
		deviceBoundService.onlyProcessBoundDevice(deviceFlag);
		// 构造结果集
		Object o = ReflectUtil.newInstance(path + "." + decode + "DTO");
		// 获取设备信息
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceFlag);
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		DeviceStatusEnum deviceStatusEnum = specificServService.checkDeviceStatus(deviceListDO.getWifiSn());
		// 设备在线进行透传
		if (DeviceStatusEnum.ONLINE.equals(deviceStatusEnum)) {
			int slaveId = "WeiHengBatteryInfoSetting".equals(decode) ? 201 : 1;
			ProxyUtil.proxy(modbusRequestUtil, TimeCostLogAspect.class).processDeviceConfig(
					deviceConfig.getSettings().get(decode), ListUtil.toList("settings", decode), deviceFlag, o,
					slaveId
			);
			// 数据后置处理器
			InterfaceUtil.processConfigParam(decode, o);
		}
		return o;
	}

	@Override
	public DeviceDefaultConfigDto queryDefault(String deviceFlag) {

		DeviceDefaultConfigDto deviceDefaultConfigDto = ReflectUtil.newInstance(DeviceDefaultConfigDto.class);
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceFlag);
		BeanUtil.assertNotNull(deviceListDO);
		DeviceSyncStateVo deviceSyncStateVo = new DeviceSyncStateVo();
		deviceSyncStateVo.setDeviceId(StrUtil.toString(deviceListDO.getId()));
		Integer state = deviceListService.syncDeviceState(deviceSyncStateVo);
		deviceDefaultConfigDto.setDeviceSn(deviceFlag);
		deviceDefaultConfigDto.setState(state);
		deviceDefaultConfigDto.setDsp1SoftwareVersion(deviceListDO.getDsp1SoftwareVersion());
		deviceDefaultConfigDto.setEmsSoftwareVersion(deviceListDO.getEmsSoftwareVersion());

		// 获取电表启用、电表类型、馈网相位
		Map<String, Map<String, Map<String, String>>> settings = deviceConfig.getSettings();
		Map<String, Map<String, String>> protectInformation = settings.get("ProtectFunctionEnable");
		if (state > 0) {
			ProxyUtil.proxy(modbusRequestUtil, TimeCostLogAspect.class).processDeviceConfig(protectInformation, ListUtil.toList( "settings","ProtectFunctionEnableMeter"),
					deviceFlag, deviceDefaultConfigDto, 1
			);
		}
		// meterType点位(1,2)，1代表高8位PV电表类型，2代表低8位电网电表类型
		String meterType = deviceDefaultConfigDto.getMeterType();
		if (StrUtil.isNotBlank(meterType)) {
			int meterTypeInt = Integer.parseInt(meterType);
			deviceDefaultConfigDto.setMeterType(String.format("%d,%d", meterTypeInt / 256, meterTypeInt % 256));
		}
		deviceDefaultConfigDto.setMeterEnable(deviceDefaultConfigDto.getMeterEnable());
		return deviceDefaultConfigDto;
	}

	@Override
	public JSONObject queryInfo() {
		JSONObject json = new JSONObject();
		for (String key : deviceConfig.getSettings().keySet()) {
			json.set(key, AesUtil.encode(key));
		}
		return json;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void configureDeviceConfig(DeviceConfigConfigureVO deviceConfigConfigureVO, String deviceName) {
		String decode = AesUtil.decode(deviceConfigConfigureVO.getCode());
		deviceBoundService.onlyProcessBoundDevice(deviceName);
		List<String> propNameList = deviceConfigConfigureVO.getPropNameList();
		List<String> propValueList = deviceConfigConfigureVO.getPropValueList();
		int slaveId = 1;
		if ("WeiHengBatteryInfoSetting".equals(decode)) {
			slaveId = 201;
		}
		if ("ProtectFunctionEnable".equals(decode) && propNameList.contains("meterType")) {
			int index = propNameList.indexOf("meterType");
			String val = propValueList.get(index);
			// meterType点位(1,2)，1代表高8位PV电表类型，2代表低8位电网电表类型
			if (val.contains(Constants.COMMA)) {
				String[] valArr = val.split(Constants.COMMA);
				String realVal = String.valueOf(256 * Integer.parseInt(valArr[0]) + Integer.parseInt(valArr[1]));
				propValueList.set(index, realVal);
			}
		}
		if ("ParameterSetting".equals(decode) && propNameList.contains("meterType")) {
			int index = propNameList.indexOf("meterType");
			String val = propValueList.get(index);
			// meterType点位(1,2)，1代表高8位PV电表类型，2代表低8位电网电表类型
			if (val.contains(Constants.COMMA)) {
				String[] valArr = val.split(Constants.COMMA);
				String realVal = String.valueOf(256 * Integer.parseInt(valArr[0]) + Integer.parseInt(valArr[1]));
				propValueList.set(index, realVal);
			}
		}
		if ("BatterySetting".equals(decode)) {
			singleFrameForBatterySetting(deviceName, propNameList, propValueList);
		} else {
			ProxyUtil.proxy(modbusRequestUtil, TimeCostLogAspect.class).configureDeviceConfig(
					deviceConfig.getSettings().get(decode), deviceName, propNameList, propValueList, slaveId
			);
		}
	}

	@DSTransactional
	private void singleFrameForBatterySetting(String deviceName, List<String> propNameList, List<String> propValueList) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		if (deviceListDO == null || StrUtil.isBlank(deviceListDO.getWifiSn())) {
			throw new CustomException(ExceptionEnum.DEVICE_NOT_EXIST);
		}
		ClientCustomizeDo clientCustomizeDo = ClientCustomizeDo.builder().deviceId(deviceListDO.getId()).build();
		List<Integer> oldParams = new ArrayList<>(Collections.nCopies(26, 0));
		List<Integer> multiChargeParams = new ArrayList<>(Collections.nCopies(100, 0));
		List<Integer> abandonPvParams = new ArrayList<>(Collections.nCopies(24, 0));

		String wifiSn = deviceListDO.getWifiSn();
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);

		for (int i = 0; i < propNameList.size(); i++) {
			String name = propNameList.get(i);
			Integer val = Integer.parseInt(propValueList.get(i));
			clientCustomizeDo.buildVal(wifiSn, specificServService, name, val, oldParams, multiChargeParams, abandonPvParams);
		}

		Boolean res = specificServService.sendWriteCommand(wifiSn, 1, 41002, 26, oldParams);
		Boolean newRes = true;
		Boolean abandonPvRes = true;
		DataResponse<OssGlobalConfigBO> globalConfig = ecosConfigApi.getGlobalConfig();
		if (globalConfig != null) {
			JSONObject firmware = globalConfig.getData().getFirmware();
			if (firmware.containsKey("EMS_version")) {
				if (firmware.getJSONObject("EMS_version").containsKey(deviceListDO.getEmsSoftwareVersion())) {
					List<String> optionList = firmware.getJSONObject("EMS_version").getBeanList(deviceListDO.getEmsSoftwareVersion(), String.class);
					if (optionList.contains(MULTI_CHARGE_FLAG)) {
						newRes = specificServService.sendWriteCommand(wifiSn, 1, 41043, 100, multiChargeParams);
					}
					if (optionList.contains(ABANDON_PV_FLAG)) {
						abandonPvRes = specificServService.sendWriteCommand(wifiSn, 1, 41143, 24, abandonPvParams);
					}
				}
			}
		}
		if (Boolean.FALSE.equals(res) || Boolean.FALSE.equals(newRes) || Boolean.FALSE.equals(abandonPvRes)) {
			throw new CustomException(ExceptionEnum.DEVICE_CONFIG_CHANGE_ERROR);
		} else {
			ecosClientApi.updateCustomizeInfo(clientCustomizeDo);
		}
	}

	@Override
	public void restartBatSps(String deviceName) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		Boolean res = specificServService.sendWriteCommand(deviceListDO.getWifiSn(), 201, 158, 1,
				ListUtil.toList(1)
		);
		if (!res) {
			throw new CustomException(ExceptionEnum.DEVICE_CONFIG_CHANGE_ERROR);
		}
	}

	@Override
	public String checkSelfTestState(String deviceName) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		List<Integer> res;
		try {
			res = specificServService.sendReadCommand(deviceListDO.getWifiSn(), 1, 30148, 1);
			return ModbusParseUtil.parseForDeviceInfoTypeEnum(DeviceInfoEncodeTypeEnum.U16, res);
		} catch (Exception e) {
			return "";
		}
//		if (ResultUtil.isRequestSuccess(jsonObject)) {
//			return ModbusParseUtil.parseData(DeviceInfoEncodeTypeEnum.U16, jsonObject);
//		}
//		return "";
	}

    @Override
    public void export(DeviceSnVo deviceSnVo, HttpServletResponse response) throws Exception {
		Future<?> reactiveLimit = threadPoolTaskExecutor.submit(() -> {
			ReactiveLimitDTO reactiveLimitDTO = new ReactiveLimitDTO();
			ProxyUtil.proxy(modbusRequestUtil, TimeCostLogAspect.class).processDeviceConfig(
					deviceConfig.getSettings().get("ReactiveLimit"), ListUtil.toList("settings", "ReactiveLimit"),
					deviceSnVo.getDeviceName(), reactiveLimitDTO, 1);
			return reactiveLimitDTO;
		});
		Future<?> standardSetting = threadPoolTaskExecutor.submit(() -> {
			SettingDTO settingDTO = new SettingDTO();
			ProxyUtil.proxy(modbusRequestUtil, TimeCostLogAspect.class).processDeviceConfig(
					deviceConfig.getSettings().get("Setting"), ListUtil.toList("settings", "Setting"),
					deviceSnVo.getDeviceName(), settingDTO, 1);
			return settingDTO;
		});
		Future<?> connectionConditions = threadPoolTaskExecutor.submit(() -> {
			SafetyParameterDTO safetyParameterDTO = new SafetyParameterDTO();
			ProxyUtil.proxy(modbusRequestUtil, TimeCostLogAspect.class).processDeviceConfig(
					deviceConfig.getSettings().get("SafetyParameter"), ListUtil.toList("settings", "SafetyParameter"),
					deviceSnVo.getDeviceName(), safetyParameterDTO, 1);
			return safetyParameterDTO;
		});
		Future<?> rtc = threadPoolTaskExecutor.submit(() -> modbusRequestUtil.postFromU32(deviceSnVo.getDeviceName(), 40034, 2, 1f, 1));
		ReactiveLimitDTO reactiveLimitDTO = (ReactiveLimitDTO) reactiveLimit.get();
		SettingDTO settingDTO = (SettingDTO) standardSetting.get();
		SafetyParameterDTO safetyParameterDTO = (SafetyParameterDTO) connectionConditions.get();
		String rtcTime = (String) rtc.get();
		DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(deviceSnVo.getDeviceName());
		// 配置response
		setResponse(response);
		try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
				.registerWriteHandler(new SimpleColumnWidthStyleStrategy(50))
				.build()) {
			WriteSheet sheet = EasyExcel.writerSheet("sheet").build();
			excelWriter.write(dataList(deviceInfo, reactiveLimitDTO, settingDTO, safetyParameterDTO, rtcTime, deviceSnVo.getTimezone()), sheet);
		} catch (Exception e) {
			log.error("excel write error: ", e);
		}
	}

	@Override
	public void setBatteryParallel(String deviceName) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		DeviceListDO deviceList = deviceListService.getDeviceById(deviceListDO.getId());

		// 判断是否绑定了自研棒子，直接看WiFiSN是否是IoT的棒子SN
		if (deviceListDO.getWifiSn() == null || GwyTypeEnum.getCodeByPre(deviceListDO.getWifiSn().substring(0, 5)) == null) {
			// 设备未绑定自研网关，无法配置电池并机
			throw new CustomException(ExceptionEnum.DEVICE_NOT_BIND_CHOLLA_GWY);
		}

		// 判断当前棒子的版本号是否是最新的
		if (checkGwyVersionTooOld(deviceListDO)) {
			throw new CustomException(ExceptionEnum.CHOLLA_VERSION_TOO_OLD);
		}

		// 下发并机配置
		Map<Integer, String> resourceTypeMap = getTypeMap();
		if (resourceTypeMap.get(deviceList.getResourceTypeId()) == null) {
			throw  new CustomException(ExceptionEnum.DEVICE_NOT_BIND_CHOLLA_GWY);
		}

		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceList);
		specificServService.changeDeviceCategory(deviceList.getDeviceName(), deviceList.getWifiSn(), resourceTypeMap.get(deviceList.getResourceTypeId()) + " P");
	}

	private boolean checkGwyVersionTooOld(DeviceListDO deviceInfo) {
		// 校验cholla棒子版本是否过低
		if (DeviceDatasourceEnum.WH.getDatasource() == deviceInfo.getDataSource()) {
			String dictItemCode = GwyTypeEnum.getCodeByPre(deviceInfo.getWifiSn().substring(0, 5));
			List<DictItemDTO> itemList = dictItemService.queryDictItemsByDictCode(CommonConstants.DICT_CODE_CHECK_VERSION,
					false, null);
			if (CollUtil.isNotEmpty(itemList)) {
				String wifiVersion = deviceInfo.getWifiSoftwareVersion();
				return itemList.stream()
						.filter(i -> i.getCode().equals(dictItemCode))
						.map(DictItemDTO::getName)
						.findFirst()
						.map(i -> {
							int iVal = Integer.parseInt(i.substring(i.length() - 2));
							int curVal = Integer.parseInt(wifiVersion.substring(wifiVersion.length() - 2));
							return curVal < iVal;
						})
						.orElse(false);
			}
		}
		return false;
	}

	private Map<Integer, String> getTypeMap() {
		Map<Integer, String> resMap = new HashMap<>();
		List<ResourceConfigDTO> tree = authCenterResClient.categoryTypeTree();
		tree.forEach(i -> {
			resMap.put(i.getId(), i.getName());
			if (i.getTypes() != null) {
				i.getTypes().forEach(j -> {
					resMap.put(j.getId(), j.getName());
				});
			}
		});
		return resMap;
	}

	/** 构造Excel数据内容 */
	private List<List<String>> dataList(DeviceListDO deviceInfo, ReactiveLimitDTO reactiveLimitDTO,
										SettingDTO settingDTO, SafetyParameterDTO safetyParameterDTO,
										String rtcTime, String timezone) {
		List<List<String>> list = ListUtils.newArrayList();
		List<List<String>> reactiveLimitList = reactiveLimitDTO.buildDataList();
		List<List<String>> settingList = settingDTO.buildDataList();
		List<List<String>> connectionList = safetyParameterDTO.buildConnectionDataList();
		List<List<String>> interfaceList = safetyParameterDTO.buildInterfaceDataList();
		list.add(Collections.singletonList("Device information"));
		list.add(Arrays.asList("Device SN", deviceInfo.getDeviceSn()));
		list.add(Arrays.asList("Model Name", deviceInfo.getDeviceModel()));
		list.add(Arrays.asList("Brand", deviceInfo.getBrand()));
		list.add(Arrays.asList("Factory", deviceInfo.getFactory()));
		list.add(Arrays.asList("EMS Hardware Version", deviceInfo.getEmsHardwareVersion()));
		list.add(Arrays.asList("EMS Software Version", deviceInfo.getEmsSoftwareVersion()));
		list.add(Arrays.asList("EMS Sub Version", deviceInfo.getEmsSubVersion()));
		list.add(Arrays.asList("DSP1 Software Version", deviceInfo.getDsp1SoftwareVersion()));
		list.add(Arrays.asList("DSP1 Sub Version", deviceInfo.getDsp1SubVersion()));
		list.add(Arrays.asList("DSP2 Software Version", deviceInfo.getDsp2SoftwareVersion()));
		list.add(Arrays.asList("DSP2 Sub Version", deviceInfo.getDsp2SubVersion()));
		list.add(Arrays.asList("BMS Hardware Version", deviceInfo.getBmsHardwareVersion()));
		list.add(Arrays.asList("BMS Software Version", deviceInfo.getBmsSoftwareVersion()));
		list.add(Arrays.asList("SafetyID", "Austria"));
		LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(Long.parseLong(rtcTime)), ZoneId.of(timezone));
		list.add(Arrays.asList("date", localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
		list.add(Collections.singletonList(""));
		list.add(Collections.singletonList(""));

		list.add(Arrays.asList("1.Reactive Power Of Inverter", "", "Unit"));
		list.addAll(reactiveLimitList);
		list.add(Collections.singletonList(""));
		list.add(Collections.singletonList(""));

		list.add(Collections.singletonList("2.Standard setting for active power control"));
		list.addAll(settingList);
		list.add(Collections.singletonList(""));
		list.add(Collections.singletonList(""));

		list.add(Collections.singletonList("3.Default settings for FRT capability"));
		list.add(Arrays.asList("FRT Capability(Under Voltage)", "0.8", "Un"));
		list.add(Collections.singletonList(""));
		list.add(Collections.singletonList(""));

		list.add(Collections.singletonList("4.Default settings for connection conditions"));
		list.addAll(connectionList);
		list.add(Collections.singletonList(""));
		list.add(Collections.singletonList(""));

		list.add(Collections.singletonList("5.Default settings for interface protection"));
		list.addAll(interfaceList);
		return list;
	}

	/** response写入 */
	private void setResponse(HttpServletResponse response) {
		response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
		response.setCharacterEncoding(CharsetUtil.UTF_8);
		response.setHeader(Header.CONTENT_DISPOSITION.getValue(), "attachment;filename*=utf-8''" + "config" + ExcelTypeEnum.XLSX.getValue());

	}
}
