package com.weihengtech.service.device.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.pojo.dos.device.StorageDynamicStageDO;
import com.weihengtech.dao.device.StorageDynamicStageMapper;
import com.weihengtech.service.device.StorageDynamicStageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 设备动态输出校验阶段 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Service
public class StorageDynamicStageServiceImpl extends ServiceImpl<StorageDynamicStageMapper, StorageDynamicStageDO> implements StorageDynamicStageService {

    @Override
    public List<StorageDynamicStageDO> getByExportId(Integer exportId) {
        return list(Wrappers.<StorageDynamicStageDO>lambdaQuery()
                .eq(StorageDynamicStageDO::getStorageId, exportId));
    }
}
