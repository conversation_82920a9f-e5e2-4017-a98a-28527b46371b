package com.weihengtech.service.device.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.weihengtech.auth.model.dto.UserResDTO;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.dao.device.DeviceFocusMapper;
import com.weihengtech.dao.other.SqlMapper;
import com.weihengtech.pojo.dos.device.DeviceFocusDo;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.device.FocusDevicePageDto;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.device.DeviceFocusUpdateVo;
import com.weihengtech.pojo.vos.device.DeviceFocusVo;
import com.weihengtech.service.device.DeviceFocusService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.OperationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeviceFocusServiceImpl extends ServiceImpl<DeviceFocusMapper, DeviceFocusDo>
		implements
		DeviceFocusService {

	@Resource
	private DeviceListService deviceListService;
	@Resource
	private SqlMapper sqlMapper;

	@Value("${custom.datacenter.name}")
	private String datacenter;

	@Override
	@DSTransactional
	public void addFocusDevice(DeviceFocusVo deviceFocusVo, long userId) {
		DeviceListDO device = deviceListService.getDeviceInfoByDeviceName(deviceFocusVo.getDeviceFlag());
		OperationUtil.of(deviceFocusVo.getDeviceRemark()).then(remark -> {
			device.setAlias(remark);
			deviceListService.updateById(device);
		});

		DeviceFocusDo deviceFocusDo = new DeviceFocusDo();
		CglibUtil.copy(deviceFocusVo, deviceFocusDo);

		deviceFocusDo.setUserId(userId);
		deviceFocusDo.setCreateTime(System.currentTimeMillis());
		deviceFocusDo.setUpdateTime(System.currentTimeMillis());

		ActionFlagUtil.assertTrue(this.save(deviceFocusDo));
	}

	@Override
	@DSTransactional
	public void updateFocusDevice(DeviceFocusUpdateVo deviceFocusUpdateVo, long userId) {
		DeviceFocusDo deviceFocusDo = this.getById(deviceFocusUpdateVo.getId());
		BeanUtil.assertNotNull(deviceFocusDo);
		DeviceListDO device = deviceListService.getDeviceInfoByDeviceName(deviceFocusDo.getDeviceFlag());

		device.setAlias(StrUtil.isBlank(deviceFocusUpdateVo.getDeviceRemark()) ? "" : deviceFocusUpdateVo.getDeviceRemark());
		deviceListService.updateById(device);

		CglibUtil.copy(deviceFocusUpdateVo, deviceFocusDo);
		deviceFocusDo.setUpdateTime(System.currentTimeMillis());
		deviceFocusDo.setUserId(userId);

		ActionFlagUtil.assertTrue(this.updateById(deviceFocusDo));
	}

	@Override
	public PageInfoDTO<FocusDevicePageDto> pageFocusDevice(Integer pageNum, Integer pageSize) {
		UserResDTO userInfo = UserInfoUtil.currentUser();
		PageHelper.startPage(pageNum, pageSize);
		List<FocusDevicePageDto> deviceFocusDoList = sqlMapper.queryFocusDevice(Long.parseLong(userInfo.getId()), null);
		PageInfo<FocusDevicePageDto> pageInfo = new PageInfo<>(deviceFocusDoList);
		List<FocusDevicePageDto> list = pageInfo.getList();
		List<String> deviceIdList = list.stream().map(FocusDevicePageDto::getDeviceId).collect(Collectors.toList());
		List<DeviceListDO> deviceList = deviceListService.getDeviceByIds(deviceIdList, false);
		Map<String, DeviceListDO> deviceMap = deviceList.stream()
				.collect(Collectors.toMap(DeviceListDO::getDeviceSn, Function.identity()));
		PageInfoDTO<FocusDevicePageDto> pageInfoDto = new PageInfoDTO<>();
		pageInfoDto.setTotalPages(pageInfo.getPages());
		pageInfoDto.setTotalCount(pageInfo.getTotal());
		deviceFocusDoList.forEach(i -> {
					i.setOperator(userInfo.getNickName());
					i.setId(String.valueOf(i.getId()));
					DeviceListDO device = deviceMap.get(i.getDeviceFlag());
					i.setDeviceRemark(device.getAlias());
				});
		pageInfoDto.setData(deviceFocusDoList);
		return pageInfoDto;
	}
}
