package com.weihengtech.service.device;

import cn.hutool.json.JSONObject;
import com.weihengtech.pojo.dtos.device.DeviceDefaultConfigDto;
import com.weihengtech.pojo.vos.device.DeviceConfigConfigureVO;
import com.weihengtech.pojo.vos.device.DeviceSnVo;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
public interface DeviceConfigService {

	/**
	 * 设备配置查询
	 *
	 * @param deviceFlag 设备标识
	 * @param code       序列码
	 * @return 设备页面信息
	 */
	Object query(String deviceFlag, String code);

	/**
	 * 设备预设配置查询
	 *
	 * @param deviceFlag 设备标识
	 * @return 设备预设配置信息
	 */
	DeviceDefaultConfigDto queryDefault(String deviceFlag);

	/**
	 * 查询序列码生成
	 *
	 * @return 序列码映射
	 */
	JSONObject queryInfo();

	/**
	 * 设备配置
	 *
	 * @param deviceConfigConfigureVO 设备配置入参
	 * @param deviceFlag              设备标识
	 */
	void configureDeviceConfig(DeviceConfigConfigureVO deviceConfigConfigureVO,
							   String deviceFlag);

	/**
	 * 重启电池sps
	 *
	 * @param deviceName 设备名
	 */
	void restartBatSps(String deviceName);

	/**
	 * 检查自检状态
	 *
	 * @param deviceName 设备sn
	 * @return 自检状态
	 */
	String checkSelfTestState(String deviceName);

	/**
	 * 导出配置文件
	 *
	 * @param deviceSnVo 设备sn
	 * @param response   响应
	 */
	void export(DeviceSnVo deviceSnVo, HttpServletResponse response) throws Exception;

	/**
	 * 下发电池并机配置
	 *
	 * @param deviceName 设备名
	 */
	void setBatteryParallel(String deviceName);
}
