package com.weihengtech.service.device;

import com.weihengtech.pojo.dos.device.DeviceRelationDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dtos.relation.DeviceRelPowerDTO;
import com.weihengtech.pojo.vos.relation.DeviceRelSaveVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 设备关联关系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-09
 */
public interface DeviceRelationService extends IService<DeviceRelationDO> {

    List<DeviceRelationDO> listRelation(String deviceName);

    void saveRelation(DeviceRelSaveVO param);

    List<DeviceRelPowerDTO> getRelationInfo(Long deviceId);

    void delRelation(String deviceName);

    Map<Long, Boolean> getRelationInfo(List<Long> deviceIdList);
}
