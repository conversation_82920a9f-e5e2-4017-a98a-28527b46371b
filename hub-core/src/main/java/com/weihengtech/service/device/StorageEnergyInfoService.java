package com.weihengtech.service.device;

import com.weihengtech.pojo.dos.device.StorageEnergyInfoDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dtos.device.DynamicDesignDTO;
import com.weihengtech.pojo.dtos.device.DynamicExportDTO;
import com.weihengtech.pojo.dtos.device.DynamicTestDTO;
import com.weihengtech.pojo.vos.device.DynamicSaveVO;
import com.weihengtech.pojo.vos.device.DynamicSwitchVO;

/**
 * <p>
 * 设备扩展信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
public interface StorageEnergyInfoService extends IService<StorageEnergyInfoDO> {

    DynamicExportDTO dynamicExport(String deviceName);

    StorageEnergyInfoDO getByDeviceId(Long deviceId);

    void removeByDeviceId(Long deviceId);

    Integer maxExportPower(String deviceName);

    DynamicDesignDTO designInfo(String deviceName);

    void dynamicSave(DynamicSaveVO param);

    DynamicTestDTO dynamicTest(String deviceName);

    void dynamicSwitch(DynamicSwitchVO param);
}
