package com.weihengtech.service.device.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.dao.device.RippleTemplateMapper;
import com.weihengtech.enums.device.DeviceInfoEncodeTypeEnum;
import com.weihengtech.pojo.dos.device.RippleTemplateDO;
import com.weihengtech.pojo.dtos.device.RippleControlDTO;
import com.weihengtech.service.device.RippleTemplateService;
import com.weihengtech.utils.ModbusParseUtil;
import com.weihengtech.utils.ModbusRequestUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <p>
 * 脉冲控制模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Service
public class RippleTemplateServiceImpl extends ServiceImpl<RippleTemplateMapper, RippleTemplateDO> implements RippleTemplateService {

    @Resource
    private ModbusRequestUtil modbusRequestUtil;

    @Override
    public RippleControlDTO rippleControlQuery(String deviceName) {
        List<Integer> resList;
        int rippleEnable;

        try {
            resList = modbusRequestUtil.postTransparentRead(deviceName, 40051, 16, 1);
        } catch (Exception e) {
            log.error("rippleControlQuery error", e);
            return RippleControlDTO.builder().deviceName(deviceName).rippleMap(new LinkedHashMap<>()).build();
        }
        LinkedHashMap<Integer, Integer> resMap = new LinkedHashMap<>(16);
        for (int i = 0; i < resList.size(); i++) {
            short signedNum = (short) (resList.get(i) & 0xFFFF);
            resMap.put(i, (int) signedNum);
        }

        try {
            rippleEnable = Integer.parseInt(ModbusParseUtil.parseForDeviceInfoTypeEnum(
                    DeviceInfoEncodeTypeEnum.U16, modbusRequestUtil.postTransparentRead(deviceName, 48012, 1, 1)));
        } catch (Exception e) {
            log.error("rippleControlQuery error", e);
            return RippleControlDTO.builder().deviceName(deviceName).rippleMap(new LinkedHashMap<>()).build();
        }

        return RippleControlDTO.builder().deviceName(deviceName).rippleMap(resMap).rippleEnable(rippleEnable).build();
    }

    @Override
    public void rippleControlConfig(RippleControlDTO param) {
        LinkedHashMap<Integer, Integer> rippleMap = param.getRippleMap();
        Assert.isTrue(rippleMap.size() == 16, "rippleMap size must be 16");
        Integer rippleEnable = param.getRippleEnable();
        if (rippleEnable == 0 || rippleEnable == 1) {
            modbusRequestUtil.postTransparentWrite(param.getDeviceName(), 48012, 1, 1, ListUtil.toLinkedList(rippleEnable));
        }
        modbusRequestUtil.postTransparentWrite(param.getDeviceName(), 40051, 16, 1, new ArrayList<>(rippleMap.values()));
    }

    @Override
    public void saveRippleTemplate(RippleTemplateDO item) {
        Assert.isTrue(!item.getContentMap().isEmpty(), "contentMap can not be empty");
        item.setContent(item.getContentMap().values().toString());
        item.setCrtBy(UserInfoUtil.currentUserEmail());
        item.setCrtTime(new Date());
        if (item.getId() != null) {
            updateById(item);
        } else {
            save(item);
        }
    }

    @Override
    public List<RippleTemplateDO> listRippleTemplate() {
        List<RippleTemplateDO> list = list();
        list.forEach(i -> {
            String[] split = i.getContent().substring(1, i.getContent().length() - 1).split(", ");
            LinkedHashMap<Integer, Integer> resMap = new LinkedHashMap<>(16);
            for (int j = 0; j < split.length; j++) {
                resMap.put(j, Integer.parseInt(split[j]));
            }
            i.setContentMap(resMap);
        });
        return list;
    }
}
