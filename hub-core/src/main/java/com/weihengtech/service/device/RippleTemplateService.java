package com.weihengtech.service.device;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.device.RippleTemplateDO;
import com.weihengtech.pojo.dtos.device.RippleControlDTO;

import java.util.List;

/**
 * <p>
 * 脉冲控制模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
public interface RippleTemplateService extends IService<RippleTemplateDO> {

    /**
     * 脉冲控制查询
     *
     * @param deviceName 设备sn
     * @return 脉冲配置
     */
    RippleControlDTO rippleControlQuery(String deviceName);

    /**
     * 脉冲控制配置
     *
     * @param param param
     */
    void rippleControlConfig(RippleControlDTO param);

    void saveRippleTemplate(RippleTemplateDO item);

    List<RippleTemplateDO> listRippleTemplate();

}
