package com.weihengtech.service.device.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.auth.dto.RemarkUpdDTO;
import com.weihengtech.auth.dto.ResourceResDTO;
import com.weihengtech.auth.dto.UserResourceRemarkDTO;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.dao.device.DeviceRemarkMapper;
import com.weihengtech.pojo.dos.device.DeviceRemarkDO;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.service.device.DeviceRemarkService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.OperatingRecordUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @program ecos-admin-backend
 * @description 设备备注表实现类
 * @create 2023-08-17 14:23
 **/
@Service
public class DeviceRemarkServiceImpl extends ServiceImpl<DeviceRemarkMapper, DeviceRemarkDO> implements DeviceRemarkService {

    @Resource
    private DeviceListService deviceListService;
    @Resource
    private AuthCenterResClient authCenterResClient;

    @Override
    public void updateByDeviceIdAndAccountId(Long deviceId, String newRemark) {
        DeviceListDO deviceInfo = deviceListService.getDeviceById(deviceId);
        List<ResourceResDTO> resourceList = authCenterResClient.resourceList(null, null, null, null,
                null, null, null, null, null, deviceInfo.getDeviceSn(), null, null,
                null, null, null);
        if (CollUtil.isEmpty(resourceList)) {
            return;
        }
        ResourceResDTO curDevice = resourceList.get(0);
        authCenterResClient.updUserResourceRemark(curDevice.getId(),
                UserResourceRemarkDTO.builder()
                        .remark(newRemark)
                        .build());
        // tag
        OperatingRecordUtil
                .log(
                        RecordContentConstants.UPDATE_DEVICE_ALIAS,
                        MapUtil.<String, String>builder().put("deviceName", StrUtil.toString(deviceId)).put("beforeAlias", StrUtil.emptyIfNull(curDevice.getRemark()))
                                .put("afterAlias", newRemark).build(),
                        EnumConstants.RecordModule.DEVICE_OPERATION.getCode()
                );

    }
}
