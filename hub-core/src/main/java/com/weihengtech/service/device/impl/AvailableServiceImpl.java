package com.weihengtech.service.device.impl;

import cn.hutool.core.collection.CollUtil;
import com.weihengtech.auth.dto.AvailableUserDTO;
import com.weihengtech.auth.dto.UserDetailDTO;
import com.weihengtech.auth.model.bo.PageInfo;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.consts.RoleConstants;
import com.weihengtech.pojo.bos.device.AccountWithRoleBo;
import com.weihengtech.service.device.AvailableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 可用账号服务
 *
 * <AUTHOR>
 * @date 2023/11/23 9:36
 * @version 1.0
 */
@Slf4j
@Service
public class AvailableServiceImpl implements AvailableService {

	@Resource
	private AuthCenterResClient authCenterResClient;

	@Override
	public List<AvailableUserDTO> topAccountList() {
		return authCenterResClient.availableUserList();
	}

	@Override
	public List<AvailableUserDTO> secAccountList() {
		List<AvailableUserDTO> userList = authCenterResClient.availableUserList();
		if (CollUtil.isEmpty(userList)) {
			return Collections.emptyList();
		}
		return userList.stream()
				.map(AvailableUserDTO::getChildren)
				.flatMap(Collection::stream)
				.collect(Collectors.toList());
	}

	@Override
	public List<AccountWithRoleBo> getRoleUserList(String roleCategory) {
		String loginRoleId = UserInfoUtil.currentUserRoleCategory();
		// 指定角色可以过滤全部销售
		if (RoleConstants.NEED_FILTER_ROLE.contains(loginRoleId)) {
			List<AvailableUserDTO> roleUserList = authCenterResClient.getRoleUserList(roleCategory);
			if (CollUtil.isEmpty(roleUserList)) {
				return Collections.emptyList();
			}
			return roleUserList.stream()
					.map(i -> AccountWithRoleBo.builder()
							.id(i.getUserId())
							.username(i.getEmail())
							.name(i.getUserNickName())
							.build())
					.collect(Collectors.toList());
		} else {
			PageInfo<UserDetailDTO> res = authCenterResClient.getRoleAllUserList(null, roleCategory, null, null,
					null, null, null);
			if (res.isEmptyData()) {
				return Collections.emptyList();
			}
			return res.getData().stream()
					.map(i -> AccountWithRoleBo.builder()
							.id(i.getId())
							.username(i.getEmail())
							.name(i.getNickName())
							.build())
					.collect(Collectors.toList());
		}
	}
}
