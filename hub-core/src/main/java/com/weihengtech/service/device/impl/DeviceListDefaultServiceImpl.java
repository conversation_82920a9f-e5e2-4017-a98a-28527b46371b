package com.weihengtech.service.device.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.dao.device.DeviceListDefaultMapper;
import com.weihengtech.pojo.dos.device.DeviceListDefaultDO;
import com.weihengtech.service.device.DeviceListDefaultService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/18 16:34
 */
@Service
public class DeviceListDefaultServiceImpl extends ServiceImpl<DeviceListDefaultMapper, DeviceListDefaultDO> implements DeviceListDefaultService {

    @Override
    public DeviceListDefaultDO getDeviceInfoByDeviceName(String deviceName) {
        DeviceListDefaultDO deviceListDO = getOne(Wrappers.<DeviceListDefaultDO>lambdaQuery().eq(DeviceListDefaultDO::getDeviceName, deviceName));
        if (null == deviceListDO) {
            throw new CustomException(ExceptionEnum.DEVICE_NOT_EXIST);
        }
        return deviceListDO;
    }
}