package com.weihengtech.service.device;

import com.aliyun.lindorm.tsdb.client.model.Result;
import com.weihengtech.pojo.vos.tsdb.TsdbQueryVO;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <p>
 * 设备关联关系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-09
 */
public interface TsdbService {

    Map<String, LinkedHashMap<Long, Object>> query(TsdbQueryVO tsdbQueryVO);

    Result queryList(TsdbQueryVO tsdbQueryVO);

    void export(TsdbQueryVO tsdbQueryVO, HttpServletResponse response);

}
