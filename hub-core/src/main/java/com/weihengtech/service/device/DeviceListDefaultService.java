package com.weihengtech.service.device;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.device.DeviceListDefaultDO;

/**
 * <AUTHOR>
 */
public interface DeviceListDefaultService extends IService<DeviceListDefaultDO> {

    /**
     * 根据设备标识查询设备对象
     *
     * @param deviceName 设备标识
     * @return 设备对象
     */
    DeviceListDefaultDO getDeviceInfoByDeviceName(String deviceName);
}
