package com.weihengtech.service.device;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.device.DeviceFocusDo;
import com.weihengtech.pojo.dtos.device.FocusDevicePageDto;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.device.DeviceFocusUpdateVo;
import com.weihengtech.pojo.vos.device.DeviceFocusVo;

/**
 * <AUTHOR>
 */
public interface DeviceFocusService extends IService<DeviceFocusDo> {

	/**
	 * 新增重点关注设备
	 *
	 * @param deviceFocusVo 重点关注设备信息入参
	 * @param userId        用户id
	 */
	void addFocusDevice(DeviceFocusVo deviceFocusVo, long userId);

	/**
	 * 更新重点关注设备
	 *
	 * @param deviceFocusUpdateVo 重点关注设备更新信息入参
	 * @param userId              用户id
	 */
	void updateFocusDevice(DeviceFocusUpdateVo deviceFocusUpdateVo, long userId);

	/**
	 * 分页查询重点关注设备
	 *
	 * @param pageNum  页码
	 * @param pageSize 每页数量
	 * @return 分页数据
	 */
	PageInfoDTO<FocusDevicePageDto> pageFocusDevice(Integer pageNum, Integer pageSize);
}
