package com.weihengtech.service.device;

import com.weihengtech.pojo.dos.device.StorageDynamicStageDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 设备动态输出校验阶段 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
public interface StorageDynamicStageService extends IService<StorageDynamicStageDO> {

    List<StorageDynamicStageDO> getByExportId(Integer exportId);
}
