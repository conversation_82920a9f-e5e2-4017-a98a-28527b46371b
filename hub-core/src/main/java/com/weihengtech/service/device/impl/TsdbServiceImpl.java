package com.weihengtech.service.device.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.http.Header;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.aliyun.lindorm.tsdb.client.model.Result;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.RoleConstants;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.tsdb.TsdbQueryDTO;
import com.weihengtech.pojo.vos.tsdb.TsdbQueryVO;
import com.weihengtech.service.device.DeviceBoundService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.device.TsdbService;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.service.thirdpart.TimeSeriesDatabaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/27 19:41
 */
@Service
@Slf4j
public class TsdbServiceImpl implements TsdbService {

    @Resource
    private StrategyService strategyService;
    @Resource
    private DeviceListService deviceListService;
    @Resource
    private DeviceBoundService deviceBoundService;
    @Resource
    private TuyaDatacenterService tuyaDatacenterService;

    @Override
    public Map<String, LinkedHashMap<Long, Object>> query(TsdbQueryVO tsdbQueryVO) {
        tsdbQueryVO.checkParams();
        TimeSeriesDatabaseService timeSeriesDatabaseService = checkParamAndReturnTimeSeries(tsdbQueryVO);
        operatingRecord(tsdbQueryVO);
        Map<String, LinkedHashMap<Long, Object>> result = timeSeriesDatabaseService.graphQuery(
                TsdbQueryDTO.builder()
                        .cloudId(tsdbQueryVO.getDeviceName())
                        .startTime(tsdbQueryVO.getStartTime())
                        .endTime(tsdbQueryVO.getEndTime())
                        .metricList(tsdbQueryVO.getMetricList())
                        .batteryIdx(tsdbQueryVO.getBatteryIdx())
                        .build());
        for (String metric : result.keySet()) {
            LinkedHashMap<Long, Object> dp = result.get(metric);
            if (CollUtil.isNotEmpty(dp)) {
                dp.put(tsdbQueryVO.getEndTime(), null);
                dp.put(tsdbQueryVO.getStartTime(), null);
            }
            result.put(metric, dp);
        }
        return result;
    }

    @Override
    public Result queryList(TsdbQueryVO param) {
        param.checkParams();
        TimeSeriesDatabaseService timeSeriesDatabaseService = checkParamAndReturnTimeSeries(param);
        operatingRecord(param);
        return timeSeriesDatabaseService.graphQueryList(
                TsdbQueryDTO.builder()
                        .cloudId(param.getDeviceName())
                        .startTime(param.getStartTime())
                        .endTime(param.getEndTime())
                        .metricList(param.getMetricList())
                        .batteryIdx(param.getBatteryIdx())
                        .build()
        );
    }

    @Override
    public void export(TsdbQueryVO param, HttpServletResponse response) {
        // 时间戳按天切片
        LinkedHashMap<Long, Long> timeList = param.cutDayRangeList();
        // 获取tsdb服务
        TimeSeriesDatabaseService timeSeriesDatabaseService = checkParamAndReturnTimeSeries(param);
        // 配置response
        setResponse(param, response);
        boolean isAllEmpty = true;
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build()) {
            // 按天遍历读取、写入流
            int i = 1;
            for (Map.Entry<Long, Long> entry : timeList.entrySet()) {
                Long start = entry.getKey();
                Long end = entry.getValue();
                Result result = timeSeriesDatabaseService.withOutSampleQueryList(TsdbQueryDTO.builder()
                        .cloudId(param.getDeviceName())
                        .startTime(start)
                        .endTime(end)
                        .metricList(param.getMetricList())
                        .build());
                transTimeField(result);
                if (CollUtil.isEmpty(result.getRows())) {
                    continue;
                }
                isAllEmpty = false;
                List<List<String>> headList = head(result.getColumns());
                WriteSheet sheet = EasyExcel.writerSheet("sheet" + i++).head(headList).build();
                excelWriter.write(result.getRows(), sheet);
            }
            if (isAllEmpty) {
                // 全空时写一个空文件
                List<List<String>> headList = head(param.getMetricList());
                WriteSheet sheet = EasyExcel.writerSheet("sheet").head(headList).build();
                excelWriter.write(new ArrayList<>(), sheet);
            }
        } catch (Exception e) {
            log.error("excel write error: ", e);
        }
    }

    /** 转换时间字段 */
    private void transTimeField(Result result) {
        List<String> columns = result.getColumns();
        if (CollUtil.isEmpty(columns)) {
            return;
        }
        int index = columns.indexOf("time");
        if (index < 0) {
            return;
        }
        List<List<Object>> rows = result.getRows();
        if (CollUtil.isEmpty(rows)) {
            return;
        }
        for (List<Object> row : rows) {
            if (CollUtil.isEmpty(row)) {
                continue;
            }
            Long time = (Long)row.get(index);
            row.set(index, DateUtil.formatDateTime(new Date(time)));
        }
    }

    /** 文件名 */
    private String getFileName(TsdbQueryVO param) {
        try {
            return URLEncoder.encode(param.getDeviceName() + "_" + DateUtil.format(new Date(), DatePattern.CHINESE_DATE_TIME_PATTERN), "UTF-8").replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    /** response写入 */
    private void setResponse(TsdbQueryVO param, HttpServletResponse response) {
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setCharacterEncoding(CharsetUtil.UTF_8);
        response.setHeader(Header.CONTENT_DISPOSITION.getValue(), "attachment;filename*=utf-8''" + getFileName(param) + ExcelTypeEnum.XLSX.getValue());

    }

    /** 导出文件标题栏 */
    private List<List<String>> head(List<String> columns) {
        return columns.stream()
                .map(Collections::singletonList)
                .collect(Collectors.toList());
    }

    /** 校验参数并返回时间序列数据库服务 */
    private TimeSeriesDatabaseService checkParamAndReturnTimeSeries(TsdbQueryVO tsdbQueryVO) {
        DeviceListDO deviceListDO = deviceListService
                .getDeviceInfoByDeviceName(tsdbQueryVO.getDeviceName());
        if (deviceListDO == null) {
            throw new CustomException(ExceptionEnum.DEVICE_NOT_EXIST);
        } else if (deviceListDO.getTsdbSource() == 9) {
            log.info("TsdbController#checkParamAndReturnTimeSeries  {}  {}", UserInfoUtil.currentUserId(), JSONUtil.toJsonStr(tsdbQueryVO));
            throw new CustomException(ExceptionEnum.DEVICE_OFFLINE_ERROR);
        } else if (deviceListDO.getFirstInstall() == null) {
            throw new CustomException(ExceptionEnum.DEVICE_OFFLINE_ERROR);
        } else if (tsdbQueryVO.getStartTime() < deviceListDO.getFirstInstall() / 1000) {
            // 当前设备首次安装时间为空或起始时间早于首次安装时间
            tsdbQueryVO.setStartTime(deviceListDO.getFirstInstall() / 1000);
            if (tsdbQueryVO.getEndTime() < deviceListDO.getFirstInstall() / 1000) {
                tsdbQueryVO.setEndTime(DateUtil.currentSeconds());
            }
        }
        return strategyService.chooseTimeSeriesDatabaseService(deviceListDO);
    }

    /** 校验权限和记录操作记录 */
    private void operatingRecord(TsdbQueryVO tsdbQueryVO) {
        String userRole = UserInfoUtil.currentUserRole().getRoleId();
        if (!RoleConstants.ROLE_SYSTEM.equals(userRole)) {
            deviceBoundService.onlyProcessBoundDevice(tsdbQueryVO.getDeviceName());
        }
    }
}
