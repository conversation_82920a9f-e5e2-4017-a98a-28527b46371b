package com.weihengtech.service.device;

import com.weihengtech.auth.dto.AvailableUserDTO;
import com.weihengtech.pojo.bos.device.AccountWithRoleBo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AvailableService {

	/**
	 * 根据当前登录用户获取该用户下的一级账号，如果当前用是超管，获取的就是销售
	 *
	 * @return 最近的10条账号列表
	 */
	List<AvailableUserDTO> topAccountList();

	/**
	 * 根据当前登录用户获取该用户下的二级账号，如果当前用是超管，获取的就是经销商
	 */
	List<AvailableUserDTO> secAccountList();

	/**
	 * 根据当前登录用户获取该用户下置顶角色的账号
	 */
	List<AccountWithRoleBo> getRoleUserList(String roleCategory);
}
