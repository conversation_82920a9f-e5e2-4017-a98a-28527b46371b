package com.weihengtech.service.device.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.dao.device.DeviceSpeedUpMapper;
import com.weihengtech.dao.other.SqlMapper;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.device.DeviceSpeedUpDo;
import com.weihengtech.pojo.vos.device.DeviceSpeedUpVO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.device.DeviceSpeedUpService;
import com.weihengtech.utils.OperatingRecordUtil;
import com.weihengtech.utils.SnowFlakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeviceSpeedUpServiceImpl extends ServiceImpl<DeviceSpeedUpMapper, DeviceSpeedUpDo>
		implements
		DeviceSpeedUpService {

	@Value("${custom.datacenter.name}")
	private String datacenter;

	@Resource
	private SqlMapper sqlMapper;
	@Resource
	private DeviceListService deviceListService;
	@Resource
	private SnowFlakeUtil snowFlakeUtil;



	@Override
	public List<DeviceSpeedUpDo> getAll() {
		List<DeviceListDO> deviceList = deviceListService.getDeviceByDatacenter(datacenter);
		List<Long> idList = deviceList.stream()
				.map(DeviceListDO::getId)
				.collect(Collectors.toList());
		return sqlMapper.queryDeviceSpeedupList(idList);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void turnOffSpeedup(List<String> deviceFlagList) {
		this.remove(Wrappers.<DeviceSpeedUpDo>lambdaQuery().in(DeviceSpeedUpDo::getDeviceFlag, deviceFlagList));
		// tag
		OperatingRecordUtil.log(
				RecordContentConstants.DELETE_HIGH_FREQUENCY_SAMPLING,
				MapUtil.<String, String>builder().put("deviceNameList", JSONUtil.toJsonStr(deviceFlagList)).build(),
				EnumConstants.RecordModule.HIGH_FREQUENCY_SAMPLING.getCode()
		);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void speedupKeep(DeviceSpeedUpVO param, Integer dataSource) {
		DeviceSpeedUpDo existsItem = getOne(Wrappers.<DeviceSpeedUpDo>lambdaQuery().eq(DeviceSpeedUpDo::getDeviceFlag, param.getDeviceFlag()));
		if (existsItem != null) {
			if (existsItem.isValid()) {
				this.updateById(DeviceSpeedUpDo.builder()
						.id(existsItem.getId())
						.reason(param.getReason())
						.build());
				return;
			} else {
				removeById(existsItem.getId());
			}
		}
		boolean res = this.save(DeviceSpeedUpDo.builder()
				.id(snowFlakeUtil.generateId())
				.deviceFlag(param.getDeviceFlag())
				.reason(param.getReason())
				.expireTime(param.getExpireTime())
				.deviceType(dataSource)
				.createUser(UserInfoUtil.currentUserNickName())
				.createTime(DateUtil.current())
				.build());
		Assert.isTrue(res, "speedupKeep save failed");
	}
}
