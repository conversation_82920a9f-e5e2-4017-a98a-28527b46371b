package com.weihengtech.service.device.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.api.DynamicExportApi;
import com.weihengtech.api.DynamicExportAusnetApi;
import com.weihengtech.api.DynamicExportSynergyApi;
import com.weihengtech.api.pojo.vos.SubscribeVO;
import com.weihengtech.auth.dto.ResourceListReqDTO;
import com.weihengtech.auth.dto.ResourceResDTO;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.dao.device.StorageEnergyInfoMapper;
import com.weihengtech.enums.device.DeviceInfoEncodeTypeEnum;
import com.weihengtech.enums.device.DynamicCountryEnum;
import com.weihengtech.enums.device.DynamicGridEnum;
import com.weihengtech.enums.device.DynamicStatusEnum;
import com.weihengtech.enums.device.SubscriptionEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.device.StorageDynamicStageDO;
import com.weihengtech.pojo.dos.device.StorageEnergyInfoDO;
import com.weihengtech.pojo.dtos.device.DynamicDesignDTO;
import com.weihengtech.pojo.dtos.device.DynamicExportDTO;
import com.weihengtech.pojo.dtos.device.DynamicTestDTO;
import com.weihengtech.pojo.dtos.device.info.ParallelInfoDTO;
import com.weihengtech.pojo.vos.device.DynamicSaveVO;
import com.weihengtech.pojo.vos.device.DynamicSwitchVO;
import com.weihengtech.service.device.DeviceDetailService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.device.StorageDynamicStageService;
import com.weihengtech.service.device.StorageEnergyInfoService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.utils.ModbusParseUtil;
import com.weihengtech.utils.ModbusRequestUtil;
import com.weihengtech.utils.SecurityUtil;
import com.weihengtech.utils.TransactionUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Future;

/**
 * <p>
 * 设备扩展信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Service
public class StorageEnergyInfoServiceImpl extends ServiceImpl<StorageEnergyInfoMapper, StorageEnergyInfoDO> implements StorageEnergyInfoService {

    @Resource
    private DeviceListService deviceListService;
    @Resource
    private DeviceDetailService deviceDetailService;
    @Resource
    private ModbusRequestUtil modbusRequestUtil;
    @Resource
    private StrategyService strategyService;
    @Resource
    private DynamicExportApi dynamicExportApi;
    @Resource
    private DynamicExportAusnetApi dynamicExportAusnetApi;
    @Resource
    private DynamicExportSynergyApi dynamicExportSynergyApi;
    @Resource
    private StorageDynamicStageService storageDynamicStageService;
    @Resource
    private AuthCenterResClient authCenterResClient;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public DynamicExportDTO dynamicExport(String deviceName) {
        DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(deviceName);
        if (deviceInfo.getState() < 0) {
            throw new CustomException(ExceptionEnum.DEVICE_OFFLINE_ERROR);
        }
        ParallelInfoDTO parallelInfo = deviceDetailService.queryParallelInfo(deviceName);
        Boolean mode = readDynamicMode(deviceInfo);
        DynamicExportDTO res = DynamicExportDTO.builder()
                .parallelWorkMode(parallelInfo.getParallelWorkMode())
                .parallelNum(parallelInfo.getParallelNum())
                .dynamicExport(mode)
                .build();
        if (mode) {
            StorageEnergyInfoDO exportInfo = getByDeviceId(deviceInfo.getId());
            if (exportInfo == null || exportInfo.getNmi() == null) {
                res.setStatus(DynamicStatusEnum.NOT_CONFIGURED.getCode());
            } else {
                List<StorageDynamicStageDO> stageInfoList = storageDynamicStageService.getByExportId(exportInfo.getId());
                if (CollUtil.isNotEmpty(stageInfoList) && stageInfoList.size() == 4) {
                    boolean isMatch = stageInfoList.stream().allMatch(i -> i.getStageState() == 1);
                    if (isMatch) {
                        res.setStatus(DynamicStatusEnum.VALID.getCode());
                    } else {
                        res.setStatus(DynamicStatusEnum.INVALID.getCode());
                    }
                } else {
                    res.setStatus(DynamicStatusEnum.INVALID.getCode());
                }
                res.setCountry(exportInfo.getCountry());
                res.setGridCompany(exportInfo.getGridCompany());
                res.setNmi(exportInfo.getNmi());
            }
        }
        return res;
    }

    @Override
    public StorageEnergyInfoDO getByDeviceId(Long deviceId) {
        LambdaQueryWrapper<StorageEnergyInfoDO> wrapper = Wrappers.<StorageEnergyInfoDO>lambdaQuery()
                .eq(StorageEnergyInfoDO::getDeviceId, deviceId);
        return getOne(wrapper);
    }

    @Override
    public void removeByDeviceId(Long deviceId) {
        LambdaQueryWrapper<StorageEnergyInfoDO> wrapper = Wrappers.<StorageEnergyInfoDO>lambdaQuery()
                .eq(StorageEnergyInfoDO::getDeviceId, deviceId);
        StorageEnergyInfoDO one = getOne(wrapper);
        if (one == null) {
            return;
        }
        storageDynamicStageService.remove(Wrappers.<StorageDynamicStageDO>lambdaQuery()
                .eq(StorageDynamicStageDO::getStorageId, one.getId()));
        removeById(one.getId());
        save(StorageEnergyInfoDO.builder()
                .deviceId(one.getDeviceId())
                .deviceType(one.getDeviceType())
                .lfdi(one.getLfdi())
                .sfdi(one.getSfdi())
                .crtBy(UserInfoUtil.currentUserEmail())
                .crtTime(new Date())
                .build());
    }

    @Override
    public Integer maxExportPower(String deviceName) {
        DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(deviceName);
        SpecificServService specificServService = strategyService.chooseSpecificServ(deviceInfo);
        Boolean online = specificServService.isOnline(deviceInfo.getWifiSn());
        if (online) {
            List<Integer> valList = modbusRequestUtil.postTransparentRead(deviceName, 37001, 1, 1);
            if (CollUtil.isEmpty(valList)) {
                return 0;
            }
            return valList.get(0);
        }
        return 0;
    }

    @Override
    public DynamicDesignDTO designInfo(String deviceName) {
        DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(deviceName);
        SpecificServService specificServService = strategyService.chooseSpecificServ(deviceInfo);
        Boolean online = specificServService.isOnline(deviceInfo.getWifiSn());
        if (!online) {
            return DynamicDesignDTO.builder().designMaxPower(0).maxWh(0).maxVa(0).maxVar(0).maxVarNeg(0).build();
        }
        Future<List<Integer>> future1 = threadPoolTaskExecutor.submit(() -> modbusRequestUtil.postTransparentRead(deviceInfo.getDeviceName(), 37001, 1, 1));
        Future<List<Integer>> future2 = threadPoolTaskExecutor.submit(() -> modbusRequestUtil.postTransparentRead(deviceInfo.getDeviceName(), 37030, 3, 1));
        Future<List<Integer>> future3 = threadPoolTaskExecutor.submit(() -> modbusRequestUtil.postTransparentRead(deviceInfo.getDeviceName(), 37035, 1, 1));
        List<Integer> result1;
        List<Integer> result2;
        List<Integer> result3;
        try {
            result1 = future1.get();
            result2 = future2.get();
            result3 = future3.get();
        } catch (Exception e) {
            return DynamicDesignDTO.builder().designMaxPower(0).maxWh(0).maxVa(0).maxVar(0).maxVarNeg(0).build();
        }
        String res2 = ModbusParseUtil.parseForDeviceInfoTypeEnum(DeviceInfoEncodeTypeEnum.S16, Collections.singletonList(result2.get(0)));
        String res3 = ModbusParseUtil.parseForDeviceInfoTypeEnum(DeviceInfoEncodeTypeEnum.S16, Collections.singletonList(result2.get(1)));
        String res4 = ModbusParseUtil.parseForDeviceInfoTypeEnum(DeviceInfoEncodeTypeEnum.S16, Collections.singletonList(result2.get(2)));
        String res5 = ModbusParseUtil.parseForDeviceInfoTypeEnum(DeviceInfoEncodeTypeEnum.S16, result3);
        return DynamicDesignDTO.builder()
                .designMaxPower(result1.get(0) * 10)
                .maxVa(Integer.parseInt(res2) * 10)
                .maxVar(Integer.parseInt(res3) * 10)
                .maxVarNeg(Integer.parseInt(res4) * 10)
                .maxWh(Integer.parseInt(res5) * 10)
                .build();

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dynamicSave(DynamicSaveVO param) {
        DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(param.getDeviceName());
        StorageEnergyInfoDO existsItem = getByDeviceId(deviceInfo.getId());
        if (existsItem != null) {
            existsItem.setNmi(param.getNmi());
            existsItem.setCountry(DynamicCountryEnum.Australia.getCode());
            existsItem.setGridCompany(param.getGridCompany());
            existsItem.setGridCompanyCode(Optional.ofNullable(DynamicGridEnum.getLfdiCodeByCode(param.getGridCompany())).orElse("00062011"));
            // 单位是10W
            existsItem.setDesignMaxPower(param.getDesignMaxPower() / 10);
            Optional.ofNullable(param.getMaxVa()).map(i -> i/10).ifPresent(existsItem::setMaxWh);
            Optional.ofNullable(param.getMaxVar()).map(i -> i/10).ifPresent(existsItem::setMaxVar);
            Optional.ofNullable(param.getMaxVarNeg()).map(i -> i/10).ifPresent(existsItem::setMaxVarNeg);
            Optional.ofNullable(param.getMaxWh()).map(i -> i/10).ifPresent(existsItem::setMaxWh);
            updateById(existsItem);
            // 事务提交后发布消息
            TransactionUtils.afterCommit(() ->
                    subscribe(existsItem.getGridCompany(), SubscriptionEnum.SubscriptionUpdate, existsItem));
            return;
        }
        List<ResourceResDTO> resourceList = authCenterResClient.resourceList(ResourceListReqDTO.builder()
                .ids(String.valueOf(deviceInfo.getId())).build());
        Integer category = null;
        if (CollUtil.isNotEmpty(resourceList)) {
            category = resourceList.get(0).getCategory();
        }
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String randomString = uuid.substring(0, 32);
        SecurityUtil.DeviceFDI fdi = SecurityUtil.randomFdi(randomString, DynamicGridEnum.getLfdiCodeByCode(param.getGridCompany()));
        StorageEnergyInfoDO storageEnergyInfo = StorageEnergyInfoDO.builder()
                .deviceId(deviceInfo.getId())
                .lfdi(fdi.lfdi)
                .sfdi(fdi.sfdi)
                .designMaxPower(param.getDesignMaxPower() / 10)
                .country(DynamicCountryEnum.Australia.getCode())
                .gridCompany(param.getGridCompany())
                .gridCompanyCode(Optional.ofNullable(DynamicGridEnum.getLfdiCodeByCode(param.getGridCompany())).orElse("00062011"))
                .nmi(param.getNmi())
                .deviceType(String.valueOf(category))
                .maxVa(Optional.ofNullable(param.getMaxVa()).map(i -> i/10).orElse(null))
                .maxVar(Optional.ofNullable(param.getMaxVar()).map(i -> i/10).orElse(null))
                .maxVarNeg(Optional.ofNullable(param.getMaxVarNeg()).map(i -> i/10).orElse(null))
                .maxWh(Optional.ofNullable(param.getMaxWh()).map(i -> i/10).orElse(null))
                .crtBy(UserInfoUtil.currentUserEmail())
                .crtTime(new Date())
                .build();
        boolean res = save(storageEnergyInfo);
        Assert.isTrue(res, "save dynamic export info failed");
        // 事务提交后发布消息
        TransactionUtils.afterCommit(() ->
                subscribe(param.getGridCompany(), SubscriptionEnum.SubscriptionCreate, storageEnergyInfo));
    }

    /** 设备切换2030.5模式 */
    private void switchDynamicMode(DeviceListDO deviceInfo, int mode) {
        SpecificServService specificServService = strategyService.chooseSpecificServ(deviceInfo);
        specificServService.sendWriteCommand(deviceInfo.getWifiSn(), 1, 61201, 1, Collections.singletonList(mode));
        if (1 == mode) {
            String ratedPower = deviceInfo.getRatedPower();
            if (StrUtil.isNotBlank(ratedPower)) {
                int ratePowerVal = Integer.parseInt(ratedPower.substring(0, ratedPower.length() - 1)) / 10;
                specificServService.sendWriteCommand(deviceInfo.getWifiSn(), 1, 61104, 1, Collections.singletonList(ratePowerVal));
            }
        }
    }

    /** 读取设备2030.5模式使能 */
    private Boolean readDynamicMode(DeviceListDO deviceInfo) {
        SpecificServService specificServService = strategyService.chooseSpecificServ(deviceInfo);
        List<Integer> res = specificServService.sendReadCommand(deviceInfo.getWifiSn(), 1, 61201, 1);
        return res.get(0) == 1;
    }

    @Override
    public DynamicTestDTO dynamicTest(String deviceName) {
        DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(deviceName);
        StorageEnergyInfoDO exportInfo = getByDeviceId(deviceInfo.getId());
        List<StorageDynamicStageDO> stageInfoList = storageDynamicStageService.getByExportId(exportInfo.getId());
        return DynamicTestDTO.builder()
                .stageList(stageInfoList)
                .nmi(exportInfo.getNmi())
                .lfdi(exportInfo.getLfdi())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dynamicSwitch(DynamicSwitchVO param) {
        DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(param.getDeviceName());
        StorageEnergyInfoDO item = getByDeviceId(deviceInfo.getId());
        if (param.getDynamicExport()) {
            switchDynamicMode(deviceInfo, 1);
            return;
        }
        // 关闭动态输出
        removeByDeviceId(deviceInfo.getId());
        // 设备侧关闭2030.5模式
        switchDynamicMode(deviceInfo, 0);
        // 事务提交后发布消息
        TransactionUtils.afterCommit(() -> {
            if (item != null) {
                subscribe(item.getGridCompany(), SubscriptionEnum.SubscriptionDelete, item);
            }
        });
    }

    /** 发布修改动态出口信息的订阅消息 */
    private void subscribe(Integer gridCompany, SubscriptionEnum typeEnum, StorageEnergyInfoDO item) {
        if (DynamicGridEnum.AUSNET.getCode().equals(gridCompany)) {
            dynamicExportAusnetApi.subscribe(SubscribeVO.builder()
                    .type(typeEnum.name())
                    .item(item)
                    .build());
        } else if (DynamicGridEnum.SYNERGY.getCode().equals(gridCompany)) {
            dynamicExportSynergyApi.subscribe(SubscribeVO.builder()
                    .type(typeEnum.name())
                    .item(item)
                    .build());
        } else {
            dynamicExportApi.subscribe(SubscribeVO.builder()
                    .type(typeEnum.name())
                    .item(item)
                    .build());
        }
    }
}
