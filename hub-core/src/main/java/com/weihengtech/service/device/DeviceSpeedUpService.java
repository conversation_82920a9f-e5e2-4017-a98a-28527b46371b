package com.weihengtech.service.device;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.dos.device.DeviceSpeedUpDo;
import com.weihengtech.pojo.vos.device.DeviceSpeedUpVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DeviceSpeedUpService extends IService<DeviceSpeedUpDo> {

	/**
	 * 获取所有的加速设备
	 *
	 * @return 加速设备列表
	 */
	List<DeviceSpeedUpDo> getAll();

	/**
	 * 批量停止设备加速
	 *
	 * @param deviceFlagList 设备Sn
	 */
	void turnOffSpeedup(List<String> deviceFlagList);

	/**
	 * 永久加速列表添加设备
	 *
	 * @param param 设备参数
	 */
	void speedupKeep(DeviceSpeedUpVO param, Integer dataSource);


}
