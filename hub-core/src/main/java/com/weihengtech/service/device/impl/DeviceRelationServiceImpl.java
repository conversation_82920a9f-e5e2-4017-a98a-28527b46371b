package com.weihengtech.service.device.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.dao.device.DeviceRelationMapper;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.device.DeviceRelationDO;
import com.weihengtech.pojo.dtos.relation.DeviceRelPowerDTO;
import com.weihengtech.pojo.vos.relation.DeviceRelSaveVO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.device.DeviceRelationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 设备关联关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-09
 */
@Service
public class DeviceRelationServiceImpl extends ServiceImpl<DeviceRelationMapper, DeviceRelationDO> implements DeviceRelationService {

    @Resource
    private DeviceListService deviceListService;

    @Override
    public List<DeviceRelationDO> listRelation(String deviceName) {
        DeviceListDO deviceList = deviceListService.getDeviceInfoByDeviceName(deviceName);
        List<DeviceRelationDO> list = this.list(Wrappers.<DeviceRelationDO>lambdaQuery()
                .eq(DeviceRelationDO::getMasterId, deviceList.getId())
                .or()
                .eq(DeviceRelationDO::getSlaveId, deviceList.getId()));
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        Set<Long> slaveIdList = list.stream()
                .map(DeviceRelationDO::getSlaveId)
                .collect(Collectors.toSet());
        Set<Long> masterIdList = list.stream()
                .map(DeviceRelationDO::getMasterId)
                .collect(Collectors.toSet());
        slaveIdList.addAll(masterIdList);
        Collection<DeviceListDO> deviceItemList = deviceListService.listByIds(slaveIdList);
        Map<Long, String> map = deviceItemList.stream()
                .collect(Collectors.toMap(DeviceListDO::getId, DeviceListDO::getDeviceSn));
        list.forEach(i -> {
            i.setSlaveName(map.get(i.getSlaveId()));
            i.setMasterName(map.get(i.getMasterId()));
        });
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRelation(DeviceRelSaveVO param) {
        DeviceListDO deviceList = deviceListService.getDeviceInfoByDeviceName(param.getDeviceName());
        if (param.getDeviceMaster() == null && param.getDeviceSlaveList() == null) {
            log.error("deviceMaster is blank, deviceSlaveList is empty");
            return;
        }
        // 不能绑定当前设备本身
        if (deviceList.getId().toString().equals(param.getDeviceMaster()) || (CollUtil.isNotEmpty(param.getDeviceSlaveList())  &&
                param.getDeviceSlaveList().contains(deviceList.getId().toString()))) {
            throw new CustomException(ExceptionEnum.DEVICE_REL_SELF_ERROR);
        }
        // 不为null，有内容绑定主机，无内容解绑主机
        if (param.getDeviceMaster() != null && !param.getDeviceMaster().isEmpty()) {
            // 校验已经是主机且已有4个从机
            int count = this.count(Wrappers.<DeviceRelationDO>lambdaQuery().eq(DeviceRelationDO::getMasterId, param.getDeviceMaster()));
            if (count >= 4) throw new CustomException(ExceptionEnum.DEVICE_ALREADY_MAX_SLAVE);
            // 删除已有关系
            delExistsRelation(deviceList.getId(), DeviceRelationDO::getSlaveId);
            // 新增关系
            this.save(DeviceRelationDO.builder()
                    .masterId(Long.parseLong(param.getDeviceMaster()))
                    .slaveId(deviceList.getId())
                    .crtBy(UserInfoUtil.currentUserEmail())
                    .crtTime(new Date())
                    .build());
        } else if (param.getDeviceMaster() != null) {
            delExistsRelation(deviceList.getId(), DeviceRelationDO::getSlaveId);
        }

        // 不为null，有内容绑定从机，无内容解绑从机
        if (param.getDeviceSlaveList() != null && !param.getDeviceSlaveList().isEmpty()) {
            // 绑定从机
            // 校验添加的从机列表中是否有主机
            List<DeviceRelationDO> masterList = this.list(Wrappers.<DeviceRelationDO>lambdaQuery()
                    .in(DeviceRelationDO::getMasterId, param.getDeviceSlaveList()));
            if (CollUtil.isNotEmpty(masterList)) {
                List<Long> masterIdList = masterList.stream()
                        .map(DeviceRelationDO::getMasterId)
                        .collect(Collectors.toList());
                Collection<DeviceListDO> alreadyMasterList = deviceListService.listByIds(masterIdList);
                throw new CustomException(ExceptionEnum.DEVICE_MODE_NOT_MATCHED, alreadyMasterList.stream()
                        .map(DeviceListDO::getDeviceSn).collect(Collectors.joining(Constants.COMMA)));
            }
            // 校验添加的从机列表是否已经被绑定给别的主机
            List<DeviceRelationDO> slaveList = this.list(Wrappers.<DeviceRelationDO>lambdaQuery()
                    .in(DeviceRelationDO::getSlaveId, param.getDeviceSlaveList())
                    .ne(DeviceRelationDO::getMasterId, deviceList.getId()));
            if (CollUtil.isNotEmpty(slaveList)) {
                List<Long> slaveIdList = slaveList.stream()
                        .map(DeviceRelationDO::getSlaveId)
                        .collect(Collectors.toList());
                Collection<DeviceListDO> alreadySlaveList = deviceListService.listByIds(slaveIdList);
                throw new CustomException(ExceptionEnum.DEVICE_ALREADY_SLAVE_OTHER, alreadySlaveList.stream()
                        .map(DeviceListDO::getDeviceSn).collect(Collectors.joining(Constants.COMMA)));
            }
            // 删除已有关系
            delExistsRelation(deviceList.getId(), DeviceRelationDO::getMasterId);
            // 新增关系
            List<DeviceRelationDO> slaveItemList = param.getDeviceSlaveList().stream()
                    .map(i -> DeviceRelationDO.builder()
                            .masterId(deviceList.getId())
                            .slaveId(Long.parseLong(i))
                            .crtBy(UserInfoUtil.currentUserEmail())
                            .crtTime(new Date())
                            .build())
                    .collect(Collectors.toList());
            this.saveBatch(slaveItemList);
        } else if (param.getDeviceSlaveList() != null) {
            delExistsRelation(deviceList.getId(), DeviceRelationDO::getMasterId);
        }
    }

    /**
     * 删除已存在的设备关系
     *
     * @param deviceId 当前设备id
     * @param function function
     */
    private void delExistsRelation(Long deviceId, SFunction<DeviceRelationDO, ?> function) {
        List<DeviceRelationDO> existsList = this.list(Wrappers.<DeviceRelationDO>lambdaQuery()
                .eq(function, deviceId));
        if (CollUtil.isNotEmpty(existsList)) {
            this.removeByIds(existsList.stream()
                    .map(DeviceRelationDO::getId)
                    .collect(Collectors.toList()));
        }
    }

    @Override
    public List<DeviceRelPowerDTO> getRelationInfo(Long deviceId) {
        List<DeviceRelationDO> list;
        // 当前设备是否是主机
        boolean isMaster = false;
        list = this.list(Wrappers.<DeviceRelationDO>lambdaQuery()
                .eq(DeviceRelationDO::getSlaveId, deviceId));
        if (CollUtil.isEmpty(list)) {
            list = this.list(Wrappers.<DeviceRelationDO>lambdaQuery()
                    .eq(DeviceRelationDO::getMasterId, deviceId));
            isMaster = true;
        }
        final boolean finalIsMaster = isMaster;
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<Long> idList = list.stream()
                .map(finalIsMaster ? DeviceRelationDO::getSlaveId : DeviceRelationDO::getMasterId)
                .collect(Collectors.toList());
        Collection<DeviceListDO> relList = deviceListService.listByIds(idList);
        Map<Long, DeviceListDO> map = relList.stream()
                .collect(Collectors.toMap(DeviceListDO::getId, Function.identity()));
        return list.stream()
                .map(i -> DeviceRelPowerDTO.builder()
                        .deviceId(String.valueOf(map.get(finalIsMaster ? i.getSlaveId() : i.getMasterId()).getId()))
                        .deviceSn(map.get(finalIsMaster ? i.getSlaveId() : i.getMasterId()).getDeviceSn())
                        .wifiSn(map.get(finalIsMaster ? i.getSlaveId() : i.getMasterId()).getWifiSn())
                        .isMaster(!finalIsMaster)
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public void delRelation(String deviceName) {
        DeviceListDO deviceList = deviceListService.getDeviceInfoByDeviceName(deviceName);
        this.remove(Wrappers.<DeviceRelationDO>lambdaQuery()
                .eq(DeviceRelationDO::getMasterId, deviceList.getId())
                .or()
                .eq(DeviceRelationDO::getSlaveId, deviceList.getId()));
    }

    @Override
    public Map<Long, Boolean> getRelationInfo(List<Long> deviceIdList) {
        Map<Long, Boolean> resMap = new HashMap<>();
        List<DeviceRelationDO> relationList = list(Wrappers.<DeviceRelationDO>lambdaQuery()
                .in(DeviceRelationDO::getMasterId, deviceIdList)
                .or()
                .in(DeviceRelationDO::getSlaveId, deviceIdList));
        Set<Long> masterMap = relationList.stream()
                .map(DeviceRelationDO::getMasterId)
                .collect(Collectors.toSet());
        Set<Long> slaveMap = relationList.stream()
                .map(DeviceRelationDO::getSlaveId)
                .collect(Collectors.toSet());
        for (Long deviceId : deviceIdList) {
            if (masterMap.contains(deviceId)) {
                resMap.put(deviceId, true);
            } else if (slaveMap.contains(deviceId)) {
                resMap.put(deviceId, false);
            }
        }
        return resMap;
    }
}
