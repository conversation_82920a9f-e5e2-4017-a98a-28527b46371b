package com.weihengtech.service.mes.impl;

import cn.hutool.json.JSONUtil;
import com.weihengtech.api.EcosClientApi;
import com.weihengtech.api.pojo.vos.ClientUnBindAccountVo;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.mes.MesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MesServiceImpl implements MesService {

    @Value("${custom.datacenter.name}")
    private String datacenter;

    @Resource
    private EcosClientApi ecosClientApi;
    @Resource
    private DeviceListService deviceListService;

    @Override
    public boolean deviceUnbind(List<String> deviceSnList) {
        log.info("unbind param: {} - {}", datacenter, deviceSnList);
        List<DeviceListDO> deviceList = deviceListService.getDeviceByDatacenter(datacenter);
        List<Long> deviceIdList = deviceList.stream()
                .filter(i -> deviceSnList.contains(i.getDeviceSn()))
                .map(DeviceListDO::getId)
                .collect(Collectors.toList());
        log.info("unbind idList: {}", deviceIdList);
        EmptyResponse emptyResponse = ecosClientApi.unbindAccount(new ClientUnBindAccountVo().withDeviceIdList(deviceIdList));
        log.info("unbind response: {}", JSONUtil.toJsonStr(emptyResponse));
        return emptyResponse.getCode() == HttpStatus.SC_OK;
    }
}
