package com.weihengtech.api;

import cn.hutool.json.JSONObject;
import com.weihengtech.api.fallback.EcosElinkFallback;
import com.weihengtech.api.pojo.vos.ElinkParamVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(name = "EcosElink", url = "${custom.url.elinkter}", decode404 = true, fallback = EcosElinkFallback.class)
public interface EcosElinkApi {

	@PostMapping("/api/task/apply/aliyun_to_e_linker_bridge.update_tasks.update_one_device_user_firmware")
	JSONObject upgradeDevice(@RequestBody ElinkParamVo elinkParamVo);

	@PostMapping("/api/task/async-apply/aliyun_to_e_linker_bridge.data_tasks.set_one_device_active")
	JSONObject speedupDevice(@RequestBody ElinkParamVo elinkParamVo);

	@GetMapping("/api/task/apply/aliyun_to_e_linker_bridge.config_tasks.get_wifi_version_information")
	JSONObject getWifiVersion(@RequestBody ElinkParamVo elinkParamVo);

	@PostMapping("/api/task/apply/aliyun_to_e_linker_bridge.data_tasks.transparent_read_data")
	JSONObject readDevice(@RequestBody ElinkParamVo elinkParamVo);

	@PostMapping("/api/task/apply/aliyun_to_e_linker_bridge.data_tasks.transparent_read_data_with_func_code")
	JSONObject readDeviceWithFuncCode(@RequestBody ElinkParamVo elinkParamVo);

	@PostMapping("/api/task/apply/aliyun_to_e_linker_bridge.data_tasks.transparent_write_data")
	JSONObject writeDevice(@RequestBody ElinkParamVo elinkParamVo);
}
