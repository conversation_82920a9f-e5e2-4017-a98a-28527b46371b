package com.weihengtech.api;

import com.weihengtech.api.fallback.IpParserApiFallback;
import com.weihengtech.common.DataResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 解析IP归属地
 *
 * <AUTHOR>
 * @date 2023/9/26 17:10
 * @version 1.0
 */
@FeignClient(name = "WhIpParser", url = "${custom.url.ip}", decode404 = true, fallback = IpParserApiFallback.class)
public interface WhIpParserApi {

	@GetMapping("/loc")
	DataResponse<Map<String, String>> loc(@RequestParam String ip);
}
