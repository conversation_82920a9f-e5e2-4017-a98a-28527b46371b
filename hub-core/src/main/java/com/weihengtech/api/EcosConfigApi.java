package com.weihengtech.api;

import com.weihengtech.api.fallback.EcosConfigFallback;
import com.weihengtech.api.pojo.dtos.EcosConfigDto;
import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.bos.config.OssGlobalConfigBO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 */
@FeignClient(name = "EcosConfig", url = "${custom.url.config}", decode404 = true, fallback = EcosConfigFallback.class)
public interface EcosConfigApi {

	@GetMapping("/ecos_config.json")
	EcosConfigDto get();

	@GetMapping("/config.json")
	DataResponse<OssGlobalConfigBO> getGlobalConfig();
}
