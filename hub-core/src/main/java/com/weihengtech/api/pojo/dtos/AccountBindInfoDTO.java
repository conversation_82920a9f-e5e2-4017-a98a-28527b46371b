package com.weihengtech.api.pojo.dtos;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/8 16:11
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "hub查询ecos账号绑定设备参数")
public class AccountBindInfoDTO {

    private List<String> masterDeviceList;

    private List<String> subDeviceList;

    private List<String> homeDeviceList;
}
