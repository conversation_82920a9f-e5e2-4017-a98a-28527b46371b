package com.weihengtech.api.pojo.vos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "读设备入参")
public class ReadDeviceVO {

	@ApiModelProperty(name = "deviceId", value = "设备Sn", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;

	@ApiModelProperty(name = "slaveId", value = "从机号", required = true)
	@NotNull(message = "err.not.null")
	private Integer slaveId;

	@ApiModelProperty(name = "startAddress", value = "开始地址", required = true)
	@NotNull(message = "err.not.null")
	private Integer startAddress;

	@ApiModelProperty(name = "len", value = "地址长度", required = true)
	@NotNull(message = "err.not.null")
	private Integer len;
}
