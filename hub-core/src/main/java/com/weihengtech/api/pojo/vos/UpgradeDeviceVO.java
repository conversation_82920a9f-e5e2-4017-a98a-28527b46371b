package com.weihengtech.api.pojo.vos;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpgradeDeviceVO {

	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;

	@ApiModelProperty(name = "url", value = "升级url", required = true)
	@NotBlank(message = "err.not.blank")
	private String url;

	@ApiModelProperty(name = "len", value = "升级包长度", required = true)
	@NotNull(message = "err.not.null")
	private Long len;

	@ApiModelProperty(name = "isGateway", value = "是否网关", required = true)
	@NotNull(message = "err.not.null")
	private Boolean isGateway = true;
}
