package com.weihengtech.api.pojo.vos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "读设备带功能码入参")
public class ReadDeviceWithFuncCodeVO extends ReadDeviceVO {

	@ApiModelProperty(name = "funcCode", value = "功能码 3 或 4", required = true)
	@NotNull(message = "err.not.null")
	private Integer funcCode;
}
