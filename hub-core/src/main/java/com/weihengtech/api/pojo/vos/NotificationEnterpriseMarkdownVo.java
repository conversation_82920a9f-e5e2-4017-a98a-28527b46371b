package com.weihengtech.api.pojo.vos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class NotificationEnterpriseMarkdownVo {
	@JsonProperty("content")
	private String content;
	@JsonProperty("toUser")
	private String toUser;
	@JsonProperty("enableDuplicateCheck")
	private Integer enableDuplicateCheck;
	@JsonProperty("duplicateCheckInterval")
	private Integer duplicateCheckInterval;
}
