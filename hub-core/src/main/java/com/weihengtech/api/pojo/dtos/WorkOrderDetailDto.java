package com.weihengtech.api.pojo.dtos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "工单详情回参")
public class WorkOrderDetailDto {
	@ApiModelProperty(value = "工单id")
	private String id;
	@ApiModelProperty(value = "标签")
	private String label;
	@ApiModelProperty(value = "附件")
	private String attachment;
	@ApiModelProperty(value = "通知邮件")
	private String notifyEmail;
	@ApiModelProperty(value = "项目名")
	private String projectName;
	@ApiModelProperty(value = "关闭时间")
	private Long closeTime;
	@ApiModelProperty(value = "创建时间")
	private Long createTime;
	@ApiModelProperty(value = "最后回复内容")
	private String lastReplyContent;
	@ApiModelProperty(value = "最后回复时间")
	private Long lastReplyTime;
	@ApiModelProperty(value = "限制时间")
	private Long limitTime;
	// 0: 关闭 1: 待回复 2: 待确认
	@ApiModelProperty(value = "工单状态 0: 关闭 1: 待回复 2: 待确认")
	private Integer state;
	@ApiModelProperty(value = "标题")
	private String subject;
	@ApiModelProperty(value = "回复列表")
	private List<WorkOrderReplyDto> replyList;

}
