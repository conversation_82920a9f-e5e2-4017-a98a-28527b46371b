package com.weihengtech.api.pojo.vos;

import com.weihengtech.pojo.dos.device.DeviceListDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClientBindAccountVo {

	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	@NotNull(message = "err.not.null")
	private DeviceListDO device;

	@ApiModelProperty(name = "username", value = "Ecos username", required = true)
	@NotBlank(message = "err.not.blank")
	private String username;

}
