package com.weihengtech.api.pojo.vos;

import cn.hutool.core.collection.CollUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.pojo.vos.parent.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "透传写")
public class WriteDeviceVO extends BaseVO {

	@ApiModelProperty(name = "deviceId", value = "设备Sn", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;

	@ApiModelProperty(name = "slaveId", value = "从机号", required = true)
	@NotNull(message = "err.not.null")
	private Integer slaveId;

	@ApiModelProperty(name = "startAddress", value = "开始地址", required = true)
	@NotNull(message = "err.not.null")
	private Integer startAddress;

	@ApiModelProperty(name = "len", value = "地址长度", required = true)
	@NotNull(message = "err.not.null")
	private Integer len;

	@ApiModelProperty(name = "values", value = "写入数值列表", required = true)
	@NotNull(message = "err.not.null")
	private List<Integer> values;

	@Override
	public void checkParams() {
		if (CollUtil.isEmpty(values) || values.size() != len || len >= 127) {
			throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
		}
	}
}
