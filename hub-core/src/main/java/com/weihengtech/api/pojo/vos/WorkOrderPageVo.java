package com.weihengtech.api.pojo.vos;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class WorkOrderPageVo {
	private List<String> attachments;
	private List<String> labels;
	private Integer page;
	private Integer size;
	private Boolean isQuizzer = false;
	private String attachmentField;
	private String labelField;
	private Integer state;
}
