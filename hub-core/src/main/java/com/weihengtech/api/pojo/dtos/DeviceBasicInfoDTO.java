package com.weihengtech.api.pojo.dtos;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@With
public class DeviceBasicInfoDTO {

	private String id;

	private String uuid;

	@J<PERSON>NField(name = "gateway_id")
	private String gatewayId;

	private String name;

	private String icon;

	private String lat;

	private String lon;

	private String ip;

	private Boolean sub;

	private String category;

	@JSONField(name = "category_name")
	private String categoryName;

	@JSONField(name = "product_id")
	private String productId;

	@JSONField(name = "product_name")
	private String productName;

	private String model;

	@JSONField(name = "local_key")
	private String localKey;

	private Boolean online;

	@JSONField(name = "owner_id")
	private String ownerId;

	@JSONField(name = "time_zone")
	private String timeZone;

	// 自研棒子：硬件版本号
	private String hardwareVersion;

	// 自研棒子：软件版本号
	private String softwareVersion;

	@JSONField(name = "active_time")
	private Long activeTime;

	@J<PERSON>NField(name = "create_time")
	private Long createTime;

	@J<PERSON><PERSON>ield(name = "update_time")
	private Long updateTime;

}
