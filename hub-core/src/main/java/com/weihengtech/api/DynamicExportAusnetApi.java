package com.weihengtech.api;

import com.weihengtech.api.fallback.DynamicExportApiFallback;
import com.weihengtech.api.pojo.vos.SubscribeVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 解析IP归属地
 *
 * <AUTHOR>
 * @date 2023/9/26 17:10
 * @version 1.0
 */
@FeignClient(name = "DynamicExportAusnetApi", url = "${custom.url.ieee-ausnet}", decode404 = true, fallback = DynamicExportApiFallback.class)
public interface DynamicExportAusnetApi {

	@PostMapping("/subscribe/listen")
	void subscribe(@RequestBody SubscribeVO param);
}
