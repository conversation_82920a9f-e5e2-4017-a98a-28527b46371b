package com.weihengtech.api;

import com.alibaba.fastjson.JSONObject;
import com.weihengtech.api.fallback.IpParserApiFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 解析IP归属地
 *
 * <AUTHOR>
 * @date 2023/9/26 17:10
 * @version 1.0
 */
@FeignClient(name = "BaiduIpParser", url = "${custom.url.ip}", decode404 = true, fallback = IpParserApiFallback.class)
public interface BaiduIpParserApi {

	@GetMapping("/api/v1/ip-portrait/brief-info")
	JSONObject ip(@RequestParam String ip);
}
