package com.weihengtech.api.fallback;

import cn.hutool.json.JSONUtil;
import com.weihengtech.api.NotificationCenterApi;
import com.weihengtech.api.pojo.base.NotificationResponse;
import com.weihengtech.api.pojo.vos.NotificationBatchMailVo;
import com.weihengtech.api.pojo.vos.NotificationBatchSmsVo;
import com.weihengtech.api.pojo.vos.NotificationEnterpriseMarkdownVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class NotificationCenterFallback implements NotificationCenterApi {
	@Override
	public NotificationResponse<String> v1AsyncMail(NotificationBatchMailVo notificationBatchMailVo) {
		log.warn("NotificationCenterFallback#Mail: {}", JSONUtil.toJsonStr(notificationBatchMailVo));
		return fail();
	}

	@Override
	public NotificationResponse<String> v1AsyncSms(NotificationBatchSmsVo notificationBatchSmsVo) {
		log.warn("NotificationCenterFallback#Sms: {}", JSONUtil.toJsonStr(notificationBatchSmsVo));
		return fail();
	}

	@Override
	public NotificationResponse<String> v1AsyncEnterpriseMarkdown(NotificationEnterpriseMarkdownVo notificationEnterpriseMarkdownVo) {
		log.warn("NotificationCenterFallback#EnterpriseMarkdown {}", JSONUtil.toJsonStr(notificationEnterpriseMarkdownVo));
		return fail();
	}

	private NotificationResponse<String> fail() {
		NotificationResponse<String> stringNotificationResponse = new NotificationResponse<>();
		stringNotificationResponse.setCode(500);
		stringNotificationResponse.setData("");
		stringNotificationResponse.setMsg("failure");
		stringNotificationResponse.setSuccess(false);
		stringNotificationResponse.setT(System.currentTimeMillis());
		return stringNotificationResponse;
	}
}
