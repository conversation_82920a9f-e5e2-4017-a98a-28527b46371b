package com.weihengtech.api.fallback;

import cn.hutool.json.JSONUtil;
import com.weihengtech.api.EcosClientApi;
import com.weihengtech.api.pojo.dtos.AccountBindInfoDTO;
import com.weihengtech.api.pojo.dtos.EcosAccountDto;
import com.weihengtech.api.pojo.vos.ClientBindAccountVo;
import com.weihengtech.api.pojo.vos.ClientUnBindAccountVo;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dos.ecos.ClientCustomizeDo;
import com.weihengtech.pojo.dos.ecos.ClientUserDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EcosClientFallback implements EcosClientApi {

	@Override
	public DataResponse<ClientUserDo> getDeviceMasterAccountDo(Long deviceId) {
		log.warn("com.weihengtech.api.fallback.EcosClientFallback.getDeviceMasterAccountDo  {}", deviceId);
		return DataResponse.success(null);
	}

	@Override
	public DataResponse<Integer> bindAccount(ClientBindAccountVo clientBindAccountVo) {
		log.warn("com.weihengtech.api.fallback.EcosClientFallback.bindAccount  {}", JSONUtil.toJsonStr(clientBindAccountVo));
		return DataResponse.fail(0);
	}

	@Override
	public EmptyResponse updateCustomizeInfo(ClientCustomizeDo clientCustomizeDo) {
		log.warn("com.weihengtech.api.fallback.EcosClientFallback.updateCustomizeInfo  {}", JSONUtil.toJsonStr(clientCustomizeDo));
		return EmptyResponse.fail();
	}

	@Override
	public EmptyResponse unbindAccount(ClientUnBindAccountVo clientUnBindAccountVo) {
		log.warn("com.weihengtech.api.fallback.EcosClientFallback.unbindAccount  {}", JSONUtil.toJsonStr(clientUnBindAccountVo));
		return EmptyResponse.fail();
	}

	@Override
	public DataResponse<EcosAccountDto> getDeviceMasterAndSubAccount(Long deviceId) {
		log.warn("com.weihengtech.api.fallback.EcosClientFallback.getDeviceMasterAndSubAccount  {}", deviceId);
		return DataResponse.success(null);
	}

	@Override
	public DataResponse<AccountBindInfoDTO> getDevicesByAccount(String account) {
		log.warn("com.weihengtech.api.fallback.EcosClientFallback.getDevicesByAccount  {}", account);
		return DataResponse.success(null);
	}
}
