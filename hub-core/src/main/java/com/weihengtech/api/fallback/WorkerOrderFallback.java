package com.weihengtech.api.fallback;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.api.WorkOrderApi;
import com.weihengtech.api.pojo.base.WorkOrderResp;
import com.weihengtech.api.pojo.dtos.WorkOrderDetailDto;
import com.weihengtech.api.pojo.dtos.WorkOrderPageDto;
import com.weihengtech.api.pojo.vos.WorkOrderListVo;
import com.weihengtech.api.pojo.vos.WorkOrderPageVo;
import com.weihengtech.api.pojo.vos.WorkOrderReplyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WorkerOrderFallback implements WorkOrderApi {

	@Override
	public WorkOrderResp<WorkOrderPageDto> pageWorkOrder(String projectToken, WorkOrderPageVo workOrderPageVo) {
		log.warn("WorkerOrderFallback#pageWorkOrder ==> {}", JSONUtil.toJsonStr(workOrderPageVo));
		return failResp(
				new WorkOrderPageDto()
						.setPages(0)
						.setCurrent(0)
						.setSize(0)
						.setTotal(0)
						.setRecords(ListUtil.empty())
		);
	}

	@Override
	public WorkOrderResp<Object> replyWorkOrder(String projectToken, WorkOrderReplyVo workOrderReplyVo) {
		return failResp(null);
	}

	@Override
	public WorkOrderResp<List<WorkOrderDetailDto>> listWorkOrder(String projectToken, WorkOrderListVo workOrderListVo) {
		return failResp(ListUtil.empty());
	}

	private <T> WorkOrderResp<T> failResp(T data) {
		return new WorkOrderResp<T>()
				.setCode(HttpStatus.INTERNAL_SERVER_ERROR.value())
				.setT(System.currentTimeMillis())
				.setSuccess(Boolean.FALSE)
				.setMsg("FallbackError")
				.setData(data);
	}
}
