package com.weihengtech.api.fallback;

import com.weihengtech.api.EcosConfigApi;
import com.weihengtech.api.pojo.dtos.EcosConfigDto;
import com.weihengtech.api.pojo.dtos.EcosConfigDto.UpgradeFirmwareHttpsDTO;
import com.weihengtech.common.DataResponse;
import com.weihengtech.pojo.bos.config.OssGlobalConfigBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EcosConfigFallback implements EcosConfigApi {


	@Override
	public EcosConfigDto get() {
		log.warn("EcosConfigFallback ....");
		EcosConfigDto ecosConfigDto = new EcosConfigDto();
		UpgradeFirmwareHttpsDTO upgradeFirmwareHttpsDTO = new UpgradeFirmwareHttpsDTO();
		upgradeFirmwareHttpsDTO.setTest(false);
		upgradeFirmwareHttpsDTO.setProd(false);
		ecosConfigDto.setUpgradeFirmwareHttps(upgradeFirmwareHttpsDTO);
		return ecosConfigDto;
	}

	@Override
	public DataResponse<OssGlobalConfigBO> getGlobalConfig() {
		log.warn("getGlobalConfig failed");
		return DataResponse.success(new OssGlobalConfigBO());
	}
}
