package com.weihengtech.api.fallback;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.api.EcosElinkApi;
import com.weihengtech.api.pojo.vos.ElinkParamVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EcosElinkFallback implements EcosElinkApi {

	@Override
	public JSONObject upgradeDevice(ElinkParamVo elinkParamVo) {
		log.warn(JSONUtil.toJsonStr(elinkParamVo));
		return FallBackResponse.failResponse();
	}

	@Override
	public JSONObject speedupDevice(ElinkParamVo elinkParamVo) {
		log.warn(JSONUtil.toJsonStr(elinkParamVo));
		return FallBackResponse.failResponse();
	}

	@Override
	public JSONObject getWifiVersion(ElinkParamVo elinkParamVo) {
		log.warn(JSONUtil.toJsonStr(elinkParamVo));
		return FallBackResponse.failResponse();
	}

	@Override
	public JSONObject readDevice(ElinkParamVo elinkParamVo) {
		log.warn(JSONUtil.toJsonStr(elinkParamVo));
		return FallBackResponse.failResponse();
	}

	@Override
	public JSONObject readDeviceWithFuncCode(ElinkParamVo elinkParamVo) {
		log.warn(JSONUtil.toJsonStr(elinkParamVo));
		return FallBackResponse.failResponse();
	}

	@Override
	public JSONObject writeDevice(ElinkParamVo elinkParamVo) {
		log.warn(JSONUtil.toJsonStr(elinkParamVo));
		return FallBackResponse.failResponse();
	}
}
