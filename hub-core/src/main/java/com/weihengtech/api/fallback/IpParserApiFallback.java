package com.weihengtech.api.fallback;

import com.alibaba.fastjson.JSONObject;
import com.weihengtech.api.BaiduIpParserApi;
import com.weihengtech.api.WhIpParserApi;
import com.weihengtech.common.DataResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/26 17:10
 */
@Slf4j
@Service
public class IpParserApiFallback implements BaiduIpParserApi, WhIpParserApi {

    @Override
    public DataResponse<Map<String, String>> loc(String ip) {
        log.error("WhIpParserApi loc failed, ip is : {}", ip);
        return DataResponse.success(null);
    }

    @Override
    public JSONObject ip(String ip) {
        log.error("BaiduIpParserApi ip failed, ip is : {}", ip);
        return null;
    }
}
