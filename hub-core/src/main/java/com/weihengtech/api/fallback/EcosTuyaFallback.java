package com.weihengtech.api.fallback;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.api.EcosTuyaApi;
import com.weihengtech.api.pojo.dtos.DeviceBasicInfoDTO;
import com.weihengtech.api.pojo.vos.*;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dtos.device.TuyaDevicePropertyDto;
import com.weihengtech.pojo.tuya.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EcosTuyaFallback implements EcosTuyaApi {

	@Override
	public EmptyResponse speedupDevice(DeviceSpeedupVo deviceSpeedupVo) {
		log.warn("EcosTuyaFallback#speedupDevice  {}", JSONUtil.toJsonStr(deviceSpeedupVo));
		return EmptyResponse.fail();
	}

	@Override
	public Result<List<TuyaDevicePropertyDto>> getDeviceInfo(String deviceIds) {
		log.warn("EcosTuyaFallback#getDeviceInfo  {}", deviceIds);
		Result<List<TuyaDevicePropertyDto>> devicePropList = new Result<>();
		devicePropList.setCode(500);
		devicePropList.setMsg("error");
		devicePropList.setSuccess(false);
		devicePropList.setT(System.currentTimeMillis());
		devicePropList.setResult(new ArrayList<>());
		return devicePropList;
	}

	@Override
	public JSONObject readDevice(ReadDeviceVO tuyaReadDeviceVo) {
		log.warn("EcosTuyaFallback#readDevice  {}", JSONUtil.toJsonStr(tuyaReadDeviceVo));
		return FallBackResponse.failResponse();
	}

	@Override
	public JSONObject readDevice(ReadDeviceWithFuncCodeVO tuyaReadDeviceWithFuncCodeVo) {
		log.warn("EcosTuyaFallback#readDevice  {}", JSONUtil.toJsonStr(tuyaReadDeviceWithFuncCodeVo));
		return FallBackResponse.failResponse();
	}

	@Override
	public JSONObject writeDevice(WriteDeviceVO tuyaWriteDeviceVo) {
		log.warn("EcosTuyaFallback#writeDevice  {}", JSONUtil.toJsonStr(tuyaWriteDeviceVo));
		return FallBackResponse.failResponse();
	}

	@Override
	public JSONObject upgradeDevice(UpgradeDeviceVO tuyaUpgradeDeviceVo) {
		log.warn("EcosTuyaFallback#upgradeDevice  {}", JSONUtil.toJsonStr(tuyaUpgradeDeviceVo));
		return FallBackResponse.failResponse();
	}

	@Override
	public Result<DeviceBasicInfoDTO> getDeviceBasicInfo(String deviceId) {
		log.warn("EcosTuyaFallback#getDeviceBasicInfo  {}", deviceId);
		Result<DeviceBasicInfoDTO> basicInfoResult = new Result<>();
		basicInfoResult.setCode(500);
		basicInfoResult.setMsg("error");
		basicInfoResult.setSuccess(false);
		basicInfoResult.setT(System.currentTimeMillis());
		basicInfoResult.setResult(new DeviceBasicInfoDTO().withOnline(false));
		return basicInfoResult;
	}

	@Override
	public DataResponse<List<String>> getAllOnlineDevice() {
		log.warn("EcosTuyaFallback#getAllOnlineDevice");
		return DataResponse.fail(ListUtil.empty());
	}
}
