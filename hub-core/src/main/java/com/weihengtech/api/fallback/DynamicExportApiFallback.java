package com.weihengtech.api.fallback;

import cn.hutool.json.JSONUtil;
import com.weihengtech.api.DynamicExportApi;
import com.weihengtech.api.pojo.vos.SubscribeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/26 17:10
 */
@Slf4j
@Service
public class DynamicExportApiFallback implements DynamicExportApi {

    @Override
    public void subscribe(SubscribeVO param) {
        log.error("subscribe failed, param is : {}", JSONUtil.toJsonStr(param));
    }
}
