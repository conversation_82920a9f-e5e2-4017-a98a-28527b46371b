package com.weihengtech.api;

import cn.hutool.json.JSONObject;
import com.weihengtech.api.config.EcosTuyaConfiguration;
import com.weihengtech.api.fallback.EcosTuyaFallback;
import com.weihengtech.api.pojo.dtos.DeviceBasicInfoDTO;
import com.weihengtech.api.pojo.vos.*;
import com.weihengtech.aspects.FeignRetry;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dtos.device.TuyaDevicePropertyDto;
import com.weihengtech.pojo.tuya.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "EcosTuya", url = "${custom.url.tuya}", decode404 = true, fallback = EcosTuyaFallback.class, configuration = EcosTuyaConfiguration.class)
public interface EcosTuyaApi {

	@PostMapping("/device/speedup")
	EmptyResponse speedupDevice(@RequestBody @Valid DeviceSpeedupVo deviceSpeedupVo);

	@GetMapping("/device/info/{deviceIds}")
	Result<List<TuyaDevicePropertyDto>> getDeviceInfo(@PathVariable("deviceIds") String deviceIds);

	@PostMapping("/device/read")
	JSONObject readDevice(@RequestBody @Valid ReadDeviceVO tuyaReadDeviceVo);

	@PostMapping("/device/readWithFuncCode")
	JSONObject readDevice(@RequestBody @Valid ReadDeviceWithFuncCodeVO tuyaReadDeviceWithFuncCodeVo);

	@PostMapping("/device/write")
	JSONObject writeDevice(@RequestBody @Valid WriteDeviceVO tuyaWriteDeviceVo);

	@PostMapping("/device/upgrade")
	JSONObject upgradeDevice(@RequestBody @Valid UpgradeDeviceVO tuyaUpgradeDeviceVo);

	@GetMapping("/device/basicInfo/{deviceId}")
	@FeignRetry
	Result<DeviceBasicInfoDTO> getDeviceBasicInfo(@PathVariable("deviceId") String deviceId);

	@GetMapping("/device/allOnlineDevice")
	@FeignRetry
	DataResponse<List<String>> getAllOnlineDevice();
}
