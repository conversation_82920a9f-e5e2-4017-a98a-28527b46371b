package com.weihengtech.api;

import com.weihengtech.api.config.EcosClientConfiguration;
import com.weihengtech.api.fallback.EcosClientFallback;
import com.weihengtech.api.pojo.dtos.AccountBindInfoDTO;
import com.weihengtech.api.pojo.dtos.EcosAccountDto;
import com.weihengtech.api.pojo.vos.ClientBindAccountVo;
import com.weihengtech.api.pojo.vos.ClientUnBindAccountVo;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.pojo.dos.ecos.ClientCustomizeDo;
import com.weihengtech.pojo.dos.ecos.ClientUserDo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@FeignClient(name = "EcosClient", url = "${custom.url.client}", decode404 = true, fallback = EcosClientFallback.class, configuration = EcosClientConfiguration.class)
public interface EcosClientApi {

	@GetMapping("/api/device/getMasterAccountDo")
	DataResponse<ClientUserDo> getDeviceMasterAccountDo(@RequestParam("deviceId") Long deviceId);

	@PostMapping("/api/device/bindAccount")
	DataResponse<Integer> bindAccount(@RequestBody @Valid ClientBindAccountVo clientBindAccountVo);

	@PostMapping("/api/device/updateCustomize")
	EmptyResponse updateCustomizeInfo(@RequestBody ClientCustomizeDo clientCustomizeDo);

	@PostMapping("/api/device/unbindAccount")
	EmptyResponse unbindAccount(@RequestBody ClientUnBindAccountVo clientUnBindAccountVo);

	@GetMapping("/api/device/getMasterAndSubAccount")
	DataResponse<EcosAccountDto> getDeviceMasterAndSubAccount(@RequestParam("deviceId") Long deviceId);

	@GetMapping("/api/device/by_account")
	DataResponse<AccountBindInfoDTO> getDevicesByAccount(@RequestParam("account") String account);
}
