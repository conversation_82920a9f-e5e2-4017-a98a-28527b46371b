package com.weihengtech.api;

import com.weihengtech.api.config.WorkerOrderConfiguration;
import com.weihengtech.api.fallback.WorkerOrderFallback;
import com.weihengtech.api.pojo.base.WorkOrderResp;
import com.weihengtech.api.pojo.dtos.WorkOrderDetailDto;
import com.weihengtech.api.pojo.dtos.WorkOrderPageDto;
import com.weihengtech.api.pojo.vos.WorkOrderListVo;
import com.weihengtech.api.pojo.vos.WorkOrderPageVo;
import com.weihengtech.api.pojo.vos.WorkOrderReplyVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "WorkOrder", url = "${custom.url.order}", decode404 = true, fallback = WorkerOrderFallback.class, configuration = WorkerOrderConfiguration.class)
public interface WorkOrderApi {

	@PostMapping("/v1/order/{projectToken}/page")
	WorkOrderResp<WorkOrderPageDto> pageWorkOrder(@PathVariable("projectToken") String projectToken, @RequestBody WorkOrderPageVo workOrderPageVo);

	@PostMapping("/v1/order/{projectToken}/reply")
	WorkOrderResp<Object> replyWorkOrder(@PathVariable("projectToken") String projectToken, @RequestBody WorkOrderReplyVo workOrderReplyVo);

	@PostMapping("/v1/order/{projectToken}/list")
	WorkOrderResp<List<WorkOrderDetailDto>> listWorkOrder(@PathVariable("projectToken") String projectToken, @RequestBody WorkOrderListVo workOrderListVo);
}
