package com.weihengtech.containers;

import com.weihengtech.handlers.ICustomProcessHandler;
import com.weihengtech.interfaces.IAroundProcessHandler;
import com.weihengtech.interfaces.IGlobalProcessHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class ProcessHandlerContainer {

	private List<IGlobalProcessHandler> globalProcessHandlerList;

	private List<IAroundProcessHandler> aroundProcessHandlerList;

	private List<ICustomProcessHandler> customProcessHandlerList;
}
