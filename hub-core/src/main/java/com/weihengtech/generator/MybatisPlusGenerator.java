package com.weihengtech.generator;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.FileOutConfig;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.PackageConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.TemplateConfig;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 逆向工程生成器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/24 16:22
 */
public class MybatisPlusGenerator {

    private static final String URL = "****************************************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "test1234";

    /** 这里输入要逆向工程的表名 */
    private static final String[] TABLE_NAMES = new String[]{"storage_dynamic_stage"};

    public static void main(String[] args) {
        // 代码生成器
        AutoGenerator generator = new AutoGenerator();

        // 全局配置
        GlobalConfig gc = new GlobalConfig();
        String projectPath = System.getProperty("user.dir");
        gc.setOutputDir(projectPath + "/hub-core/src/main/java");
        //去除服务接口service前缀
        gc.setServiceName("%sService");
        gc.setAuthor("lujie.shen");
        gc.setOpen(false);
        //使用默认主键
        gc.setIdType(IdType.ID_WORKER);
        //设置默认的时间格式
        gc.setDateType(DateType.ONLY_DATE);
        //实体属性 Swagger2 注解
        gc.setSwagger2(false);
        generator.setGlobalConfig(gc);

        // 数据源配置
        DataSourceConfig dataSourceConfig = new DataSourceConfig();
        dataSourceConfig.setUrl(URL);
        dataSourceConfig.setDriverName("com.mysql.cj.jdbc.Driver");
        dataSourceConfig.setUsername(USERNAME);
        dataSourceConfig.setPassword(PASSWORD);
        generator.setDataSource(dataSourceConfig);

        // 包配置
        PackageConfig pc = new PackageConfig();
        pc.setParent("com.weihengtech");
        pc.setEntity("pojo.dos");
        pc.setMapper("dao");
        pc.setService("service");
        pc.setServiceImpl("service.impl");
//        packageConfig.setController("controller");
        generator.setPackageInfo(pc);

        // 策略配置
        StrategyConfig strategy = new StrategyConfig();
        strategy.setInclude(TABLE_NAMES);
        //设置驼峰命名的自动映射
        strategy.setNaming(NamingStrategy.underline_to_camel);
        //设置列名的驼峰命名自动映射
        strategy.setColumnNaming(NamingStrategy.underline_to_camel);
        //设置是否支持lombok
        strategy.setEntityLombokModel(true);
//        strategy.setRestControllerStyle(true);
        generator.setStrategy(strategy);


        // 注入自定义配置，可以在 VM 中使用 cfg.abc 【可无】
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                Map<String, Object> map = new HashMap<>();
                map.put("abc", this.getConfig().getGlobalConfig().getAuthor() + "-rb");
                this.setMap(map);
            }
        };
        // 调整 xml 生成目录演示
        List<FileOutConfig> focList = new ArrayList<>();
        focList.add(new FileOutConfig("/templates/mapper.xml.vm") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return projectPath + "/hub-core/src/main/resources/mapper/" + tableInfo.getEntityName() + "Mapper.xml";
            }
        });
        cfg.setFileOutConfigList(focList);
        generator.setCfg(cfg);

        // 关闭默认 xml 生成，调整生成 至 根目录
        TemplateConfig tc = new TemplateConfig();
        tc.setXml(null);
        generator.setTemplate(tc);

        // 执行生成代码
        generator.execute();
    }
}
