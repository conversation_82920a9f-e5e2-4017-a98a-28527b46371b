package com.weihengtech.event;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.service.other.CustomEventService;
import com.weihengtech.utils.InitUtil;
import com.weihengtech.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CustomEventListener {

	@EventListener(value = CustomEvent.class)
	public void saveDeviceEvent(CustomEvent customEvent) {
		CustomEventService customEventService = InitUtil.getBean(CustomEventService.class);
		String name = StringUtil.convertToMethodName(customEvent.getType().name());
		log.info("event {} {}", name, JSONUtil.toJsonStr(customEvent.getParams()));
		ReflectUtil.invoke(customEventService, name, customEvent.getParams());
	}
}
