package com.weihengtech.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public class CustomEvent extends ApplicationEvent {

	/**
	 * 方法传参
	 */
	private final Object params;
	/**
	 * 方法名
	 */
	private final Type type;

	public CustomEvent(Type type, Object params) {
		super(type);
		this.params = params;
		this.type = type;
	}

	/**
	 * 方法名
	 */
	public enum Type {
		FOCUSED_DEVICE_OFFLINE, SEND_WRITE_COMMAND
	}
}
