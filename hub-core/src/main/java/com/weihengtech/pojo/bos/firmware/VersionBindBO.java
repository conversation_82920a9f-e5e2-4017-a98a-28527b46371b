package com.weihengtech.pojo.bos.firmware;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class VersionBindBO {

	@ApiModelProperty(name = "id", value = "版本id", required = true)
	private String id;

	@ApiModelProperty(name = "version", value = "版本名", required = true)
	private String version;

	@ApiModelProperty(name = "bindFlag", value = "绑定标识 0: 未绑定; 1: 已绑定", required = true)
	private Integer bindFlag;
}
