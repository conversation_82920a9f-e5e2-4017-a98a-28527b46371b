package com.weihengtech.pojo.bos.parser;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.enums.device.DeviceErrorTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;

import static com.weihengtech.consts.parser.graph.SingleErrorConstants.DSP_1_CN_BIT_PARSE;
import static com.weihengtech.consts.parser.graph.SingleErrorConstants.DSP_1_EN_BIT_PARSE;
import static com.weihengtech.consts.parser.graph.SingleErrorConstants.DSP_2_CN_BIT_PARSE;
import static com.weihengtech.consts.parser.graph.SingleErrorConstants.DSP_2_EN_BIT_PARSE;
import static com.weihengtech.consts.parser.graph.SingleErrorConstants.DSP_3_CN_BIT_PARSE;
import static com.weihengtech.consts.parser.graph.SingleErrorConstants.DSP_3_EN_BIT_PARSE;

/**
 * 异常解析数据结构-单相机实现
 *
 * <AUTHOR>
 * @date 2024/4/30 10:47
 * @version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SingleErrorCollectionBO extends AbsErrorCollectionBO {

	@Override
	public void setValue(DeviceErrorTypeEnum deviceErrorTypeEnum, String binaryStr) {
		if (StrUtil.isBlank(binaryStr)) {
			return;
		}
		ArrayList<String> bitValueList = new ArrayList<>(64);
		ArrayList<String> bitPosList = new ArrayList<>(64);
		int length = binaryStr.length();
		for (int i = 0; i < length; i++) {
			bitValueList.add(String.valueOf(binaryStr.charAt(i)));
			bitPosList.add("Bit" + i);
		}

		this.setBitValueList(bitValueList);
		this.setBitPosList(bitPosList);

		switch (deviceErrorTypeEnum) {
			case DSP_1:
				this.setBitEnglishParseList(DSP_1_EN_BIT_PARSE);
				this.setBitChineseParseList(DSP_1_CN_BIT_PARSE);
				break;
			case DSP_2:
				this.setBitEnglishParseList(DSP_2_EN_BIT_PARSE);
				this.setBitChineseParseList(DSP_2_CN_BIT_PARSE);
				break;
			case DSP_3:
				this.setBitEnglishParseList(DSP_3_EN_BIT_PARSE);
				this.setBitChineseParseList(DSP_3_CN_BIT_PARSE);
				break;
			default:
		}
	}

	@Override
	public void setErrorValue(DeviceErrorTypeEnum deviceErrorTypeEnum, String binaryStr) {
		switch (deviceErrorTypeEnum) {
			case DSP_1:
				parseBitList(DSP_1_CN_BIT_PARSE, DSP_1_EN_BIT_PARSE, binaryStr);
				break;
			case DSP_2:
				parseBitList(DSP_2_CN_BIT_PARSE, DSP_2_EN_BIT_PARSE, binaryStr);
				break;
			case DSP_3:
				parseBitList(DSP_3_CN_BIT_PARSE, DSP_3_EN_BIT_PARSE, binaryStr);
				break;
			default:
		}
	}
}
