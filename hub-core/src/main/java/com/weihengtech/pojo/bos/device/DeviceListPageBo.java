package com.weihengtech.pojo.bos.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeviceListPageBo {

	@ApiModelProperty(name = "deviceName", value = "设备名称")
	private String deviceName;

	@ApiModelProperty(name = "wifiSn", value = "wifi棒序列号")
	private String wifiSn;

	@ApiModelProperty(name = "alias", value = "别名")
	private String alias;

	@ApiModelProperty(name = "desc", value = "描述")
	private String desc;

	@ApiModelProperty(name = "condition", value = "条件", required = true)
	private String condition;

	@ApiModelProperty(name = "agentId", value = "代理商Id")
	private String agentId;

	@ApiModelProperty(value = "分销商id")
	private String dealerId;

	@ApiModelProperty(value = "零售商id")
	private String retailerId;

	@ApiModelProperty(value = "销售id")
	private String saleId;

	private Integer pageNum;

	private Integer pageSize;

	@ApiModelProperty(name = "countryId", value = "国家id", required = true)
	private Integer countryId;

	private Collection<String> deviceIdList;

	@ApiModelProperty(name = "state", value = "状态 -5 未知, -4:已禁用 -3:未激活 -1: 离线 0: 等待 1: 并网 2: EPS 3: 故障 4: 保留 5: 自检, 20: 在线 21 不可用")
	private Integer state;

	private String datacenter;

	private Long accountId;

	private String installCountry;

	private Integer resourceType;

	private Integer resourceSeries;

	private List<String> resourceTypeList;

	private List<Integer> stateList;

	private Integer batSocState;

	private String address;

}
