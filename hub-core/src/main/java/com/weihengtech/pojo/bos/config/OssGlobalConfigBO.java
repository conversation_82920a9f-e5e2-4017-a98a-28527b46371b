package com.weihengtech.pojo.bos.config;

import cn.hutool.json.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class OssGlobalConfigBO {

	private EcosConfig ecos;

	private HubConfig hub;

	private List<String> hosts;

	private List<String> aftersales;

	private JSONObject firmware;

	@Getter
	@Setter
	@ToString
	public static class EcosConfig {
		private Maintain maintain;
		private String feedbackEmail;
	}

	@Getter
	@Setter
	@ToString
	public static class HubConfig {
		private Maintain maintain;
	}

	@Getter
	@Setter
	@ToString
	public static class Maintain {
		private Integer state;
		private String content;
	}

	@Getter
	@Setter
	@ToString
	public static class Firmware {
		private Map EMS_version;
		private Map DSP1_version;
	}
}
