package com.weihengtech.pojo.bos.parser;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.enums.device.DeviceErrorTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.weihengtech.consts.parser.graph.CatlErrorConstants.CATL_BATTERY_ERROR_2_CN_PARSE;
import static com.weihengtech.consts.parser.graph.CatlErrorConstants.CATL_BATTERY_ERROR_2_EN_PARSE;
import static com.weihengtech.consts.parser.graph.CatlErrorConstants.CATL_BATTERY_ERROR_3_CN_PARSE;
import static com.weihengtech.consts.parser.graph.CatlErrorConstants.CATL_BATTERY_ERROR_3_EN_PARSE;
import static com.weihengtech.consts.parser.graph.CatlErrorConstants.CATL_BATTERY_ERROR_4_CN_PARSE;
import static com.weihengtech.consts.parser.graph.CatlErrorConstants.CATL_BATTERY_ERROR_4_EN_PARSE;
import static com.weihengtech.consts.parser.graph.CatlErrorConstants.CATL_BATTERY_ERROR_5_CN_PARSE;
import static com.weihengtech.consts.parser.graph.CatlErrorConstants.CATL_BATTERY_ERROR_5_EN_PARSE;
import static com.weihengtech.consts.parser.graph.CatlErrorConstants.CATL_BATTERY_ERROR_CN_PARSE;
import static com.weihengtech.consts.parser.graph.CatlErrorConstants.CATL_BATTERY_ERROR_EN_PARSE;
import static com.weihengtech.consts.parser.graph.ErrorCodeConstants.ARM_BIT_CN_PARSE;
import static com.weihengtech.consts.parser.graph.ErrorCodeConstants.ARM_BIT_EN_PARSE;
import static com.weihengtech.consts.parser.graph.ErrorCodeConstants.BATTERY_ALARM_CN_PARSE;
import static com.weihengtech.consts.parser.graph.ErrorCodeConstants.BATTERY_ALARM_EN_PARSE;
import static com.weihengtech.consts.parser.graph.ErrorCodeConstants.BATTERY_ERROR_CN_PARSE;
import static com.weihengtech.consts.parser.graph.ErrorCodeConstants.BATTERY_ERROR_EN_PARSE;
import static com.weihengtech.consts.parser.graph.ErrorCodeConstants.BATTERY_FAULT_CN_PARSE;
import static com.weihengtech.consts.parser.graph.ErrorCodeConstants.BATTERY_FAULT_EN_PARSE;
import static com.weihengtech.consts.parser.graph.ErrorCodeConstants.BMS_DECIMAL_CN;
import static com.weihengtech.consts.parser.graph.ErrorCodeConstants.BMS_DECIMAL_EN;
import static com.weihengtech.consts.parser.graph.ErrorCodeConstants.DSP_DECIMAL_CN;
import static com.weihengtech.consts.parser.graph.ErrorCodeConstants.DSP_DECIMAL_EN;
import static com.weihengtech.consts.parser.graph.ErrorCodeConstants.WEIHENG_BATTERY_CN_PARSE;
import static com.weihengtech.consts.parser.graph.ErrorCodeConstants.WEIHENG_BATTERY_EN_PARSE;

/**
 * 异常解析数据结构
 *
 * <AUTHOR>
 * @date 2024/4/30 10:47
 * @version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AbsErrorCollectionBO {

	private List<String> bitPosList = new ArrayList<>();

	private List<String> bitValueList = new ArrayList<>();

	private List<String> bitEnglishParseList = new ArrayList<>();

	private List<String> bitChineseParseList = new ArrayList<>();

	public void setValue(DeviceErrorTypeEnum deviceErrorTypeEnum, String binaryStr) {
		if (StrUtil.isBlank(binaryStr)) {
			return;
		}
		ArrayList<String> bitValueList = new ArrayList<>(64);
		ArrayList<String> bitPosList = new ArrayList<>(64);
		int length = binaryStr.length();
		for (int i = 0; i < length; i++) {
			bitValueList.add(String.valueOf(binaryStr.charAt(i)));
			bitPosList.add("Bit" + i);
		}

		this.setBitValueList(bitValueList);
		this.setBitPosList(bitPosList);

		switch (deviceErrorTypeEnum) {
			case BATTERY_ERROR:
				this.setBitEnglishParseList(BATTERY_ERROR_EN_PARSE);
				this.setBitChineseParseList(BATTERY_ERROR_CN_PARSE);
				break;
			case BATTERY_ALARM:
				this.setBitEnglishParseList(BATTERY_ALARM_EN_PARSE);
				this.setBitChineseParseList(BATTERY_ALARM_CN_PARSE);
				break;
			case BATTERY_FAULT:
				this.setBitEnglishParseList(BATTERY_FAULT_EN_PARSE);
				this.setBitChineseParseList(BATTERY_FAULT_CN_PARSE);
				break;
			case CATL_BATTERY_ERROR:
				this.setBitEnglishParseList(CATL_BATTERY_ERROR_EN_PARSE);
				this.setBitChineseParseList(CATL_BATTERY_ERROR_CN_PARSE);
				break;
			case WEIHENG_BATTERY:
				this.setBitEnglishParseList(WEIHENG_BATTERY_EN_PARSE);
				this.setBitChineseParseList(WEIHENG_BATTERY_CN_PARSE);
				break;
			case ARM:
				this.setBitEnglishParseList(ARM_BIT_EN_PARSE);
				this.setBitChineseParseList(ARM_BIT_CN_PARSE);
				break;
			default:
		}
	}

	/** 构造十进制异常解析 */
	public void setDecimalErrorValue(DeviceErrorTypeEnum deviceErrorTypeEnum, Long decimal) {
		switch (deviceErrorTypeEnum) {
			case DSP_DECIMAL:
				parseDecimalMap(DSP_DECIMAL_CN, DSP_DECIMAL_EN, decimal);
				break;
			case BMS_DECIMAL:
				parseDecimalMap(BMS_DECIMAL_CN, BMS_DECIMAL_EN, decimal);
				break;
		}

	}

	/** 解析十进制数据 */
	protected void parseDecimalMap(Map<String, String> cnMap, Map<String, String> enMap, Long decimal) {
		bitPosList = cnMap.keySet().stream().map(Integer::parseInt).sorted().map(String::valueOf).collect(Collectors.toList());
		bitValueList = bitPosList.stream().map(pos -> {
			if (decimal == Long.parseLong(pos)) {
				return "1";
			}
			return "0";
		}).collect(Collectors.toList());
		bitChineseParseList = bitPosList.stream().map(cnMap::get).collect(Collectors.toList());
		bitEnglishParseList = bitPosList.stream().map(enMap::get).collect(Collectors.toList());
	}

	/** 构造二进制异常解析 */
	public void setErrorValue(DeviceErrorTypeEnum deviceErrorTypeEnum, String binaryStr) {
		switch (deviceErrorTypeEnum) {
			case ARM:
				parseBitList(ARM_BIT_CN_PARSE, ARM_BIT_EN_PARSE, binaryStr);
				break;
			case BATTERY_FAULT:
				parseBitList(BATTERY_FAULT_CN_PARSE, BATTERY_FAULT_EN_PARSE, binaryStr);
				break;
			case BATTERY_ALARM:
				parseBitList(BATTERY_ALARM_CN_PARSE, BATTERY_ALARM_EN_PARSE, binaryStr);
				break;
			case BATTERY_ERROR:
				parseBitList(BATTERY_ERROR_CN_PARSE, BATTERY_ERROR_EN_PARSE, binaryStr);
				break;
			case CATL_BATTERY_ERROR:
				parseBitList(CATL_BATTERY_ERROR_CN_PARSE, CATL_BATTERY_ERROR_EN_PARSE, binaryStr);
				break;
			case CATL_BATTERY_ERROR_2:
				parseBitList(CATL_BATTERY_ERROR_2_CN_PARSE, CATL_BATTERY_ERROR_2_EN_PARSE, binaryStr);
				break;
			case CATL_BATTERY_ERROR_3:
				parseBitList(CATL_BATTERY_ERROR_3_CN_PARSE, CATL_BATTERY_ERROR_3_EN_PARSE, binaryStr);
				break;
			case CATL_BATTERY_ERROR_4:
				parseBitList(CATL_BATTERY_ERROR_4_CN_PARSE, CATL_BATTERY_ERROR_4_EN_PARSE, binaryStr);
				break;
			case CATL_BATTERY_ERROR_5:
				parseBitList(CATL_BATTERY_ERROR_5_CN_PARSE, CATL_BATTERY_ERROR_5_EN_PARSE, binaryStr);
				break;
			default:
		}
	}

	/** 解析二进制数据 */
	protected void parseBitList(List<String> cnList, List<String> enList, String binaryStr) {
		int length = binaryStr.length();
		for (int i = 0; i < length; i++) {
			bitPosList.add("Bit" + i);
			if ('1' == binaryStr.charAt(i)) {
				bitValueList.add("1");
			} else {
				bitValueList.add("0");
			}
		}
		this.bitChineseParseList = cnList;
		this.bitEnglishParseList = enList;
	}
}
