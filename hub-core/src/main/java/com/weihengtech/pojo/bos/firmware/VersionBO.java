package com.weihengtech.pojo.bos.firmware;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class VersionBO {

	private String hardwareCategoryId;

	private String hardwareCategoryName;

	private List<VersionBO.TypeBO> typeList;

	@Getter
	@Setter
	@ToString
	public static class TypeBO {

		private String hardwareTypeId;

		private String hardwareTypeName;

		private List<VersionBindBO> hardwareVersionList;

		private List<VersionBindBO> softwareVersionList;
	}
}
