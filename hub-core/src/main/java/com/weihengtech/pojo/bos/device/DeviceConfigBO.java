package com.weihengtech.pojo.bos.device;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class DeviceConfigBO {

	private Map<String, Map<String, Map<String, String>>> config;

	private Map<String, Map<String, Map<String, String>>> subsystem;

	private Map<String, Map<String, Map<String, String>>> settings;

	private Map<String, Map<String, List<Integer>>> modules;
}
