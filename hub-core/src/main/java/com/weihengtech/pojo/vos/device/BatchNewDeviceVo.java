package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "批量新增设备")
public class BatchNewDeviceVo {

	@NotNull(message = "err.not.null")
	@ApiModelProperty(value = "导入设备类型：0：储能机，1：充电桩", required = true)
	private Integer type;

	@NotNull(message = "err.not.null")
	@ApiModelProperty(name = "deviceSnList", value = "要绑定的deviceSn列表", required = true)
	private List<String> deviceSnList;

	@NotNull(message = "err.not.null")
	@ApiModelProperty(value = "国家id", required = true)
	private Integer countryId;
}
