package com.weihengtech.pojo.vos.tsdb;

import cn.hutool.core.date.CalendarUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.pojo.vos.parent.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "tsdb查询入参")
public class TsdbQueryVO extends BaseVO implements Serializable {

	private static final long serialVersionUID = -2263659853245013963L;

	@ApiModelProperty(name = "startTime", value = "开始时间戳", required = true)
	@NotNull(message = "err.not.null")
	private Long startTime;

	@ApiModelProperty(name = "endTime", value = "结束时间戳", required = true)
	@NotNull(message = "err.not.null")
	private Long endTime;

	@ApiModelProperty(name = "deviceName", value = "设备名", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceName;

	@ApiModelProperty(name = "metricList", value = "查询的资源", required = true)
	@NotEmpty(message = "err.param.valid.exception")
	private List<String> metricList;

	@ApiModelProperty(name = "timeZone", value = "时区")
	private String timeZone;

	@ApiModelProperty(name = "batteryIdx", value = "电池索引")
	private Integer batteryIdx;

	@Override
	public void checkParams() {
		if (startTime > DateUtil.currentSeconds() || endTime < startTime) {
			throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
		} else if (startTime == 0L && endTime == 0L) {
			endTime = DateUtil.currentSeconds();
			startTime = endTime - 60 * 5;
		} else if (startTime != 0L && endTime == 0L) {
			endTime = DateUtil.currentSeconds();
		} else if (startTime == 0L) {
			startTime = endTime - 60 * 5;
		}
	}

	/** 根据时区按天切片 */
	public LinkedHashMap<Long, Long> cutDayRangeList() {
		LinkedHashMap<Long, Long> resMap = new LinkedHashMap<>();
		String timeZone = this.getTimeZone() == null ? "Asia/Shanghai" : this.getTimeZone();
		Calendar calendar = DateUtil.beginOfDay(CalendarUtil.calendar(this.getEndTime()*1000, TimeZone.getTimeZone(timeZone)));
		long beginOfDayTime = calendar.getTime().getTime();
		// 查询区间小于一天，无需切片
		if (beginOfDayTime < this.getStartTime()*1000) {
			resMap.put(this.startTime, this.endTime);
			return resMap;
		}
		if (beginOfDayTime/1000 != this.endTime) {
			resMap.put(beginOfDayTime/1000, this.endTime);
		}
		for (int i = 0; i < 30; i++) {
			DateTime yesterday = DateUtil.offsetDay(new Date(beginOfDayTime), -1);
			if (this.startTime*1000 < yesterday.getTime()) {
				resMap.put(yesterday.getTime()/1000, beginOfDayTime/1000);
				beginOfDayTime = yesterday.getTime();
				continue;
			}
			resMap.put(this.getStartTime(), beginOfDayTime/1000);
			break;
		}
		return resMap;
	}
}
