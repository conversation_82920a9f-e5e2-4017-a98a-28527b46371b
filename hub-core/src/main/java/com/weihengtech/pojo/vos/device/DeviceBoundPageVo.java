package com.weihengtech.pojo.vos.device;

import com.weihengtech.pojo.vos.parent.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备绑定分页入参")
public class DeviceBoundPageVo extends PageInfoVO {

	@ApiModelProperty(value = "设备序列号")
	private String deviceSn;

	@ApiModelProperty(value = "设备备注")
	private String alias;

	@ApiModelProperty(value = "国家id, 传0查未绑定的设备")
	private Integer countryId;

	@ApiModelProperty(value = "经销商id")
	private String agentId;

	@ApiModelProperty(value = "分销商id")
	private String dealerId;

	@ApiModelProperty(value = "零售商id")
	private String retailerId;

	@ApiModelProperty(value = "销售id")
	private String saleId;

	@ApiModelProperty(value = "昵称")
	private String name;
}
