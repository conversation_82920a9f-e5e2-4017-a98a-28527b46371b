package com.weihengtech.pojo.vos.other;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class NotifyHybridPrometheusVo {
	@JsonProperty("receiver")
	private String receiver;
	@JsonProperty("status")
	private String status;
	@JsonProperty("alerts")
	private List<AlertsDTO> alerts;
	@JsonProperty("groupLabels")
	private GroupLabelsDTO groupLabels;
	@JsonProperty("commonLabels")
	private CommonLabelsDTO commonLabels;
	@JsonProperty("commonAnnotations")
	private CommonAnnotationsDTO commonAnnotations;
	@JsonProperty("externalURL")
	private String externalURL;
	@JsonProperty("version")
	private String version;
	@JsonProperty("groupKey")
	private String groupKey;
	@JsonProperty("truncatedAlerts")
	private Integer truncatedAlerts;

	@NoArgsConstructor
	@Data
	public static class GroupLabelsDTO {
		@JsonProperty("alertname")
		private String alertname;
		@JsonProperty("monitor")
		private String monitor;
	}

	@NoArgsConstructor
	@Data
	public static class CommonLabelsDTO {
		@JsonProperty("alertname")
		private String alertname;
		@JsonProperty("group")
		private String group;
		@JsonProperty("level")
		private String level;
		@JsonProperty("monitor")
		private String monitor;
		@JsonProperty("prometheus")
		private String prometheus;
		@JsonProperty("to_software")
		private String toSoftware;
	}

	@NoArgsConstructor
	@Data
	public static class CommonAnnotationsDTO {
		@JsonProperty("description")
		private String description;
		@JsonProperty("value")
		private String value;
	}

	@NoArgsConstructor
	@Data
	public static class AlertsDTO {
		@JsonProperty("status")
		private String status;
		@JsonProperty("labels")
		private LabelsDTO labels;
		@JsonProperty("annotations")
		private AnnotationsDTO annotations;
		@JsonProperty("startsAt")
		private String startsAt;
		@JsonProperty("endsAt")
		private String endsAt;
		@JsonProperty("generatorURL")
		private String generatorURL;
		@JsonProperty("fingerprint")
		private String fingerprint;

		@NoArgsConstructor
		@Data
		public static class LabelsDTO {
			@JsonProperty("alertname")
			private String alertname;
			@JsonProperty("database")
			private String database;
			@JsonProperty("device_id")
			private String deviceId;
			@JsonProperty("group")
			private String group;
			@JsonProperty("level")
			private String level;
			@JsonProperty("monitor")
			private String monitor;
			@JsonProperty("prometheus")
			private String prometheus;
			@JsonProperty("to_software")
			private String toSoftware;
		}

		@NoArgsConstructor
		@Data
		public static class AnnotationsDTO {
			@JsonProperty("description")
			private String description;
			@JsonProperty("value")
			private String value;
		}
	}
}
