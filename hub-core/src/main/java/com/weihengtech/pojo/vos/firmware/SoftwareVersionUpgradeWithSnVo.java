package com.weihengtech.pojo.vos.firmware;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "使用sn固件升级")
public class SoftwareVersionUpgradeWithSnVo {

	@ApiModelProperty(name = "firmwareIdList", value = "固件包id", required = true)
	@NotEmpty(message = "err.param.valid.exception")
	private List<String> firmwareIdList;

	@ApiModelProperty(name = "deviceNameList", value = "设备sn", required = true)
	@NotEmpty(message = "err.param.valid.exception")
	private List<String> deviceNameList;

	@ApiModelProperty(name = "model", value = "适用机型")
	private String model;
}
