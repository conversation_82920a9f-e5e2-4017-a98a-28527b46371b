package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @author: jiahao.jin
 * @create: 2025-09-18 17:56
 * @description:
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备描述更新入参")
public class DeviceDescUpdateVo {

    @ApiModelProperty(name = "id", value = "设备id", required = true)
    @NotBlank(message = "err.not.blank")
    private String id;

    @ApiModelProperty(name = "desc", value = "设备描述", required = true)
    @NotBlank(message = "err.not.blank")
    private String desc;
}
