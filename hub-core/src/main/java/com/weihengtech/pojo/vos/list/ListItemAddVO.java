package com.weihengtech.pojo.vos.list;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: jiahao.jin
 * @create: 2025-07-29 14:44
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ListItemAddVO {

    @ApiModelProperty(name = "分类id", value = "分类id", required = true)
    @NotNull(message = "err.not.null")
    private Integer categoryId;

    @ApiModelProperty(name = "名单类型", value = "名单类型", required = true)
    @NotNull(message = "err.not.null")
    private Integer listType;

    @ApiModelProperty(name = "内容", value = "内容", required = true)
    @NotBlank(message = "err.not.blank")
    private String content;
}
