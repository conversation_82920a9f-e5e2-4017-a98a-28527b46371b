package com.weihengtech.pojo.vos.firmware;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/23 14:14
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GwyConfigVersionVO {

	@ApiModelProperty( value = "置顶wifi版本号")
	private String topWifiVersion;

	@ApiModelProperty(value = "置顶ETH版本号")
	private String topEthVersion;

	@ApiModelProperty(value = "置顶4g版本号")
	private String top4gVersion;

	@ApiModelProperty(value = "是否开启校验网关版本号")
	private Boolean isCheck;

	@ApiModelProperty(value = "校验wifi版本号")
	private String checkWifiVersion;

	@ApiModelProperty(value = "校验ETH版本号")
	private String checkEthVersion;

	@ApiModelProperty(value = "校验4g版本号")
	private String check4gVersion;

	// 逆变器固件配置
	@ApiModelProperty(value = "EMS最新版本号")
	private List<String> lastEmsVersion;

	@ApiModelProperty(value = "DSP1最新版本号")
	private List<String> lastDsp1Version;

	@ApiModelProperty(value = "DSP2最新版本号")
	private List<String> lastDsp2Version;

	@ApiModelProperty(value = "BMS最新版本号")
	private List<String> lastBmsVersion;
}
