package com.weihengtech.pojo.vos.ecosseries;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "系列新增入参")
public class SeriesInsertVO implements Serializable {

	private static final long serialVersionUID = 2032214824333578026L;

	@ApiModelProperty(name = "cnName", value = "中文名", required = true)
	@NotBlank(message = "err.not.blank")
	private String cnName;

	@ApiModelProperty(name = "enName", value = "英文名", required = true)
	@NotBlank(message = "err.not.blank")
	private String enName;

	@ApiModelProperty(name = "seriesRoute", value = "系列路由", required = true)
	@NotBlank(message = "err.not.blank")
	private String seriesRoute;
}
