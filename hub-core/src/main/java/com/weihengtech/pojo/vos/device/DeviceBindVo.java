package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备绑定代理商入参")
public class DeviceBindVo {

	@ApiModelProperty(name = "deviceSnList", value = "设备序列号列表", required = true)
	@NotNull(message = "err.not.null")
	private List<String> deviceSnList;

	@ApiModelProperty(name = "agentId", value = "代理商id", required = true)
	@NotBlank(message = "err.not.blank")
	private String agentId;

	@ApiModelProperty(value = "城市id", required = true)
	@NotNull(message = "err.not.null")
	private Integer countryId;
}
