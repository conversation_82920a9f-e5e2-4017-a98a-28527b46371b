package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class DeviceSpeedUpVO {

	@ApiModelProperty(name = "deviceFlag", value = "设备Sn")
	private String deviceFlag;

	@ApiModelProperty(name = "deviceId", value = "设备Sn")
	private Long deviceId;

	@ApiModelProperty(name = "reason", value = "原因", required = true)
	@NotBlank(message = "err.not.blank")
	private String reason;

	@ApiModelProperty(name = "expireTime", value = "过期时间", required = true)
	@NotBlank(message = "err.not.blank")
	private Long expireTime;
}
