package com.weihengtech.pojo.vos.order;

import com.weihengtech.pojo.vos.parent.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@ApiModel(value = "工单分页入参")
public class PageWorkOrderVo extends PageInfoVO {

	@ApiModelProperty(value = "工单状态 0: 关闭 1: 待回复 2: 待确认")
	private Integer state;

	@ApiModelProperty(value = "设备sn")
	private String deviceSn;
}
