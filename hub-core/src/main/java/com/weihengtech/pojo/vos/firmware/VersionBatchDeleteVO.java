package com.weihengtech.pojo.vos.firmware;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "版本批量删除入参")
public class VersionBatchDeleteVO implements Serializable {

	private static final long serialVersionUID = -181707930406132939L;

	@ApiModelProperty(name = "versionIdList", value = "要删除的版本id列表", required = true)
	@NotEmpty(message = "err.param.valid.exception")
	private List<String> versionIdList;
}
