package com.weihengtech.pojo.vos.firmware;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.util.List;

/**
 * @author: jiahao.jin
 * @create: 2025-07-18 16:02
 * @description: 所有固件名称白名单列表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FirmwareAllWhiteListVo {

    // EMS软件版本号白名单
    private List<String> emsSoftwareWhiteList;

    // EMS子版本号白名单
    private List<String> emsSubWhiteList;

    // DSP1软件版本号白名单
    private List<String> dsp1SoftwareWhiteList;

    // DSP1子版本号白名单
    private List<String> dsp1SubWhiteList;

    // DSP2软件版本号白名单
    private List<String> dsp2SoftwareWhiteList;

    // DSP2子版本号白名单
    private List<String> dsp2SubWhiteList;

    // ARCDSP软件版本号白名单
    private List<String> arcDspSoftwareWhiteList;

    // ARCDSP子版本号白名单
    private List<String> arcDspSubWhiteList;

    // BMS软件版本号白名单
    private List<String> bmsSoftwareWhiteList;
}
