package com.weihengtech.pojo.vos.device;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备信息页异常解析
 *
 * <AUTHOR>
 * @date 2024/5/7 16:01
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeviceParseErrVO {

	private String deviceSn;

	private String error1;

	private String error2;

	private String error3;

	private String armBit;

	private String errorH32;

	private String error2H32;

	private String error3H32;

	private String error4H32;

	private String error5H32;

	private String faultH16;

	private String alarmL16;

	private String currentCode;

	private String faultSaveCode;
}
