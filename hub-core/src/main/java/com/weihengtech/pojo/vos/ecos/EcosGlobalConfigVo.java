package com.weihengtech.pojo.vos.ecos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "ecos全局配置入参")
public class EcosGlobalConfigVo {

	@ApiModelProperty(name = "batteryHealth", value = "电池健康度开关 0: 关 1: 开")
	private Integer batteryHealth;

	@ApiModelProperty(name = "batteryCapacity", value = "电池电量开关 0: 关 1: 开")
	private Integer batteryCapacity;
}
