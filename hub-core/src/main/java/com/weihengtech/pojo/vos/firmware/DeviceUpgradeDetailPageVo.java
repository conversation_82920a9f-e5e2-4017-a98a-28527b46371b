package com.weihengtech.pojo.vos.firmware;

import com.weihengtech.pojo.vos.parent.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * @author: jiahao.jin
 * @create: 2025-07-23 19:57
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "设备升级详情分页入参")
public class DeviceUpgradeDetailPageVo extends PageInfoVO {

    @ApiModelProperty(value = "批量升级任务ID")
    private String batchTaskId;

    @ApiModelProperty(value = "设备SN")
    private String deviceSn;

    @ApiModelProperty(value = "设备升级状态", example = "waiting, upgradeing, success, failure")
    private String upgradeStatus;
}
