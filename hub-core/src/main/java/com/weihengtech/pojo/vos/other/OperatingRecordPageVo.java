package com.weihengtech.pojo.vos.other;

import com.weihengtech.pojo.vos.parent.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "操作记录分页入参")
public class OperatingRecordPageVo extends PageInfoVO {

	@ApiModelProperty(name = "username", value = "用户名")
	private String username;

	@ApiModelProperty(name = "deviceName", value = "设备名称")
	private String deviceName;

	@ApiModelProperty(name = "startTime", value = "开始时间")
	private Long startTime;

	@ApiModelProperty(name = "endTime", value = "结束时间")
	private Long endTime;

	@ApiModelProperty(name = "moduleId", value = "模块id,\"\": 所有模块, 0: 账号相关, 1: 设备操作, 2: 设备信息, 3: 设备配置, 4: 历史曲线, 5: 设备事件, 6: 固件维护, 7: 版本维护, 8: 设备绑定, 9: 重点关注, 10: 高频采样")
	private Integer moduleId;
}
