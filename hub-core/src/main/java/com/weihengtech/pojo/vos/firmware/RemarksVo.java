package com.weihengtech.pojo.vos.firmware;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "备注入参")
public class RemarksVo {

	@ApiModelProperty(name = "remarks", value = "备注")
	private String remarks;

	@ApiModelProperty(name = "id", value = "主键")
	@NotNull(message = "err.not.null")
	private String id;
}
