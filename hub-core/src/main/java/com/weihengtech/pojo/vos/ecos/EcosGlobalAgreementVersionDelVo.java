package com.weihengtech.pojo.vos.ecos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "Ecos Agreement 版本删除入参")
public class EcosGlobalAgreementVersionDelVo {

	@ApiModelProperty(value = "版本id")
	@NotNull(message = "err.not.null")
	private Integer id;
}
