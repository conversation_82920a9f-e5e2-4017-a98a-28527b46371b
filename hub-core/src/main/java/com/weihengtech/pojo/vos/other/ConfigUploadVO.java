package com.weihengtech.pojo.vos.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/12/3 14:55
 * @version 1.0
 */
@Data
@ApiModel(value = "配置文件上传对象")
public class ConfigUploadVO {

	@ApiModelProperty(name = "配置文件内容", value = "配置文件内容")
	@NotBlank(message = "err.not.blank")
	private String content;
}
