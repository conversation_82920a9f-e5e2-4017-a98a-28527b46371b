package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备信息查询入参")
public class DeviceConfigQueryVO extends DeviceFlagVO implements Serializable {

	private static final long serialVersionUID = -9219702593432510372L;

	@NotBlank(message = "err.not.blank")
	@ApiModelProperty(name = "code", value = "序列码", required = true)
	private String code;
}
