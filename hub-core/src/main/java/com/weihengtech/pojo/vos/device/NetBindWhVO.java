package com.weihengtech.pojo.vos.device;

import com.weihengtech.enums.device.DeviceDatasourceEnum;
import com.weihengtech.enums.tsdb.TsdbSourceEnum;
import com.weihengtech.pojo.vos.device.NetBindDeviceVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 充电桩配网入参
 *
 * <AUTHOR>
 * @date 2024/1/25 14:39
 * @version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "配网流程用户绑定设备入参")
public class NetBindWhVO extends NetBindDeviceVO {

	@ApiModelProperty(value = "设备sn", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceSn;

	@Override
	public void buildSourceParam() {
		setDataSource(DeviceDatasourceEnum.WH.getDatasource());
		setTsdbSource(TsdbSourceEnum.WH_LINDORM.getCode());
	}
}
