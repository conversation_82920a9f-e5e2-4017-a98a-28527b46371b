package com.weihengtech.pojo.vos.systeminfo;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 配网进度
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 16:34
 */
@Data
public class SystemInfoSaveVO {

    @ApiModelProperty("系统名称")
    @NotBlank(message = "err.not.blank")
    private String name;

    @ApiModelProperty("地区")
    @NotNull(message = "err.not.null")
    private int datacenterId;

    @ApiModelProperty("用户名")
    private String ownerFirstName;

    @ApiModelProperty("用户姓")
    private String ownerLastName;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("手机")
    private String phone;

    @ApiModelProperty("安装日期")
    @NotNull(message = "err.not.null")
    private Date installDate;

    @ApiModelProperty("详细地址")
    private String address;

    /**
     * 邮箱、手机至少填写一项
     *
     * @param saveVO saveVO
     */
    public static void checkParams(SystemInfoSaveVO saveVO) {
        if (StrUtil.isBlank(saveVO.getPhone()) && StrUtil.isBlank(saveVO.getEmail())) {
            throw new CustomException(ExceptionEnum.PARAM_MISS_EXCEPTION);
        }
    }
}
