package com.weihengtech.pojo.vos.device;

import com.weihengtech.pojo.vos.parent.PageInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class DeviceSingleInputPageVo extends PageInfoVO {

	@ApiModelProperty(name = "condition", value = "条件")
	private String condition;

	@ApiModelProperty(name = "agentId", value = "代理商Id")
	private String agentId;

	@ApiModelProperty(value = "分销商id")
	private String dealerId;

	@ApiModelProperty(value = "零售商id")
	private String retailerId;

	@ApiModelProperty(value = "销售id")
	private String saleId;

	@ApiModelProperty(name = "state", value = "状态 -5 未知, -4:已禁用 -3:未激活 -1: 离线 0: 等待 1: 并网 2: EPS 3: 故障 4: 保留 5: 自检, 20: 在线 21 不可用")
	private Integer state;

	@ApiModelProperty(name = "countryId", value = "国家id")
	private Integer countryId;

	@ApiModelProperty(value = "类型")
	private Integer resourceType;

	@ApiModelProperty(value = "系列")
	private Integer resourceSeries;

	@ApiModelProperty(value = "类型列表")
	private List<String> resourceTypeList;

	@ApiModelProperty(value = "状态列表")
	private List<Integer> stateList;

	// 电量状态
	@ApiModelProperty(value = "电量状态, 0：低电量设备")
	private Integer batSocState;

}
