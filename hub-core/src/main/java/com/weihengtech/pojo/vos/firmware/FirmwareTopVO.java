package com.weihengtech.pojo.vos.firmware;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/10/25 11:02
 * @version 1.0
 */
@Data
@ApiModel(value = "固件置顶入参")
public class FirmwareTopVO {

	@ApiModelProperty(name = "id", value = "固件id", required = true)
	@NotNull(message = "err.not.null")
	private Long id;

	@ApiModelProperty(name = "isTop", value = "是否置顶", required = true)
	@NotNull(message = "err.not.null")
	private int isTop;
}
