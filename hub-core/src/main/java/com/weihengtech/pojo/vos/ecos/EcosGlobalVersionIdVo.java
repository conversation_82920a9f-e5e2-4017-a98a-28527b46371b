package com.weihengtech.pojo.vos.ecos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "删除版本信息入参")
public class EcosGlobalVersionIdVo {

	@ApiModelProperty(name = "Id", value = "版本id", required = true)
	@NotBlank(message = "err.not.blank")
	private String Id;
}
