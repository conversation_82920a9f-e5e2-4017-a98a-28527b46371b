package com.weihengtech.pojo.vos.ecos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "绑定客户端账号入参")
public class EcosBindAccountVo {

	@ApiModelProperty(name = "email", value = "Ecos 邮箱")
	@NotBlank(message = "err.not.blank")
	private String email;

	@ApiModelProperty(name = "deviceName", value = "设备逆变器序列号", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceName;
}
