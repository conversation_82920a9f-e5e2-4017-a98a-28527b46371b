package com.weihengtech.pojo.vos.list;

import com.weihengtech.pojo.vos.parent.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: jiahao.jin
 * @create: 2025-07-21 15:44
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "列表项分页入参")
public class ListItemPageVO extends PageInfoVO {

    @ApiModelProperty(name = "内容", value = "内容")
    private String content;

    @ApiModelProperty(name = "名单类型", value = "名单类型")
    @NotNull(message = "err.not.null")
    private Integer listType;

    @ApiModelProperty(name = "分类id", value = "分类id")
    @NotNull(message = "err.not.null")
    private Integer categoryId;
}
