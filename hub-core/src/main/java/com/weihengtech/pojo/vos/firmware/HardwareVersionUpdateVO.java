package com.weihengtech.pojo.vos.firmware;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "硬件版本更新入参")
public class HardwareVersionUpdateVO extends HardwareVersionInsertVO implements Serializable {

	private static final long serialVersionUID = 6035643831881085598L;

	@ApiModelProperty(name = "hardwareVersionId", value = "要修改的硬件版本id", required = true)
	@NotBlank(message = "err.not.blank")
	private String hardwareVersionId;

	@ApiModelProperty(name = "model", value = "适用机型", required = true)
	private String model;
}
