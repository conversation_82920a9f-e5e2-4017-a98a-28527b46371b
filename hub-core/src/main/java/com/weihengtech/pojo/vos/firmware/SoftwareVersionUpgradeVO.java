package com.weihengtech.pojo.vos.firmware;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "软件版本升级入参")
public class SoftwareVersionUpgradeVO {

	@ApiModelProperty(name = "firmwareIdList", value = "固件包id", required = true)
	@NotEmpty(message = "err.param.valid.exception")
	private List<String> firmwareIdList;

	@ApiModelProperty(name = "deviceIdList", value = "设备id", required = true)
	@NotEmpty(message = "err.param.valid.exception")
	private List<String> deviceIdList;
}
