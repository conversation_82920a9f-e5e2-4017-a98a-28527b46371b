package com.weihengtech.pojo.vos.firmware;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "固件删除入参")
public class FirmwareDelVO implements Serializable {

	private static final long serialVersionUID = -3011029802651912476L;

	@NotEmpty(message = "err.param.valid.exception")
	@ApiModelProperty(name = "firmwareIdList", value = "固件id列表", required = true)
	private List<String> firmwareIdList;
}
