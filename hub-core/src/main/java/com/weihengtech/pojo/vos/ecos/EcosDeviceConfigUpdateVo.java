package com.weihengtech.pojo.vos.ecos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "Ecos设备配置更新入参")
public class EcosDeviceConfigUpdateVo {

	@ApiModelProperty(value = "设备sn", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceName;

	@ApiModelProperty(value = "溃网指示条")
	private Boolean feedback;
}
