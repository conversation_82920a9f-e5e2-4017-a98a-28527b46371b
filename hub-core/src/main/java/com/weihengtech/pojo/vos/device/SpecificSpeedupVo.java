package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "加速采集入参")
public class SpecificSpeedupVo {

	@ApiModelProperty(name = "deviceNameList", value = "设备id列表", required = true)
	@NotNull(message = "err.not.null")
	private List<String> deviceNameList;
}
