package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class DeviceBoundBindCountryVo {

	@ApiModelProperty(value = "设备sn列表")
	@NotEmpty(message = "err.not.null")
	private List<String> deviceSnList;

	@ApiModelProperty(value = "国家id")
	@NotNull(message = "err.not.null")
	private Integer countryId;
}
