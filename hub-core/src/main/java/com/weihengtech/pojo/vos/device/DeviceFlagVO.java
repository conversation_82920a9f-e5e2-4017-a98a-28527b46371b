package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备信息入参类")
public class DeviceFlagVO {

	@ApiModelProperty(name = "deviceName", value = "设备名")
	@NotBlank(message = "err.not.blank")
	private String deviceName;

	@ApiModelProperty(name = "batteryIdx", value = "电池索引")
	private Integer batteryIdx;

	@ApiModelProperty(name = "isStation", value = "是否电站")
	private Boolean isStation = false;
}
