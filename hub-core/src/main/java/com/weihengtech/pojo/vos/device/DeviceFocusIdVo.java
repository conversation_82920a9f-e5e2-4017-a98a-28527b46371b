package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备重点关注id入参")
public class DeviceFocusIdVo {

	@ApiModelProperty(name = "id", value = "重点设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String id;
}
