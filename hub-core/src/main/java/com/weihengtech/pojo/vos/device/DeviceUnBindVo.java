package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "解绑设备入参")
public class DeviceUnBindVo {

	@ApiModelProperty(name = "unBindDeviceSnList", value = "解绑设备sn列表", required = true)
	@NotNull(message = "err.not.null")
	List<String> unBindDeviceSnList;
}
