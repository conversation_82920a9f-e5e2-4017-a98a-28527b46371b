package com.weihengtech.pojo.vos.parent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "分页父类请求入参")
public class PageInfoVO extends BaseVO implements Serializable {

	private static final long serialVersionUID = -3557585043553740482L;

	@ApiModelProperty(value = "页码 从1开始")
	private Integer pageNum;
	@ApiModelProperty(value = "数量")
	private Integer pageSize;

	@Override
	public void checkParams() {
		pageNum = (pageNum == null || pageNum <= 0) ? 1 : pageNum;
		pageSize = (pageSize == null || pageSize <= 0) ? 10 : pageSize > 100 ? 100 : pageSize;
	}
}
