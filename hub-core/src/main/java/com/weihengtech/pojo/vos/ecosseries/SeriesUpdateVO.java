package com.weihengtech.pojo.vos.ecosseries;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.pojo.vos.parent.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "系列更新入参")
public class SeriesUpdateVO extends BaseVO implements Serializable {

	private static final long serialVersionUID = -6538418636440076497L;

	@ApiModelProperty(name = "id", value = "系列id", required = true)
	@NotNull(message = "err.not.null")
	private Integer id;

	@ApiModelProperty(name = "cnName", value = "要修改的中文名", allowEmptyValue = true)
	private String cnName;

	@ApiModelProperty(name = "enName", value = "要修改的英文名", allowEmptyValue = true)
	private String enName;

	@ApiModelProperty(name = "seriesRoute", value = "系列路由", required = true)
	private String seriesRoute;

	@Override
	public void checkParams() {
		if (StrUtil.isBlank(cnName) && StrUtil.isBlank(enName)) {
			throw new CustomException(ExceptionEnum.PARAM_MISS_EXCEPTION);
		}
	}
}
