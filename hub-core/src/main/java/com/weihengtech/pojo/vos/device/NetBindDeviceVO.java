package com.weihengtech.pojo.vos.device;

import com.weihengtech.enums.device.DeviceDatasourceEnum;
import com.weihengtech.enums.tsdb.TsdbSourceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "配网流程用户绑定设备入参")
public class NetBindDeviceVO {

	@ApiModelProperty(value = "wifi棒序列号", required = true)
	@NotBlank(message = "err.not.blank")
	private String wifiSn;

	@ApiModelProperty(value = "系统id", required = true)
	@NotNull(message = "err.not.null")
	private Long systemId;

	@ApiModelProperty(value = "纬度", required = true)
	private Double lat;

	@ApiModelProperty(value = "经度", required = true)
	private Double lon;

	@ApiModelProperty(value = "品类", required = true)
	private String category;

	@ApiModelProperty(value = "型号", required = true)
	private String productId;

	@ApiModelProperty(value = "安装国家", required = true)
	private String installCountry;

	@ApiModelProperty(value = "数据平台", required = true)
	private Integer dataSource;

	@ApiModelProperty(value = "tsdb平台", required = true)
	private Integer tsdbSource;

	@ApiModelProperty(value = "资源类型", required = true)
	private Integer resourceType;

	@ApiModelProperty(value = "设备型号", required = true)
	private String deviceModel;

	@ApiModelProperty(value = "设备类型", required = true)
	private Long deviceId;

	@ApiModelProperty(value = "详细地址")
	private String address;

	public void buildSourceParam() {
		this.setTsdbSource(TsdbSourceEnum.TUYA_LINDORM.getCode());
		this.setDataSource(DeviceDatasourceEnum.TUYA.getDatasource());
	}


}
