package com.weihengtech.pojo.vos.socket;

import com.weihengtech.enums.device.DeviceDatasourceEnum;
import com.weihengtech.enums.tsdb.TsdbSourceEnum;
import com.weihengtech.enums.socket.SocketTypeEnum;
import com.weihengtech.pojo.vos.device.NetBindDeviceVO;
import com.weihengtech.service.socket.impl.SocketBoundServiceImpl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 充电桩配网入参
 *
 * <AUTHOR>
 * @date 2024/1/25 14:39
 * @version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "配网流程用户绑定设备入参")
public class NetBindSocketVO extends NetBindDeviceVO {

	@ApiModelProperty(value = "充电桩sn", required = true)
	@NotBlank(message = "err.not.blank")
	private String socketSn;

	public void buildParam() {
		setResourceType(SocketTypeEnum.FERO.getId());
		setDeviceModel(SocketBoundServiceImpl.SOCKET_MODEL);
		setDataSource(DeviceDatasourceEnum.TUYA.getDatasource());
		setTsdbSource(TsdbSourceEnum.TUYA_LINDORM.getCode());
	}
}
