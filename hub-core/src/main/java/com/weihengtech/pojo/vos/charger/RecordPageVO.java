package com.weihengtech.pojo.vos.charger;

import cn.hutool.core.date.DateUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.pojo.vos.parent.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 充电记录查询入参
 *
 * <AUTHOR>
 * @date 2024/1/29 9:55
 * @version 1.0
 */
@Getter
@Setter
@ToString
@ApiModel(value = "充电记录查询入参")
public class RecordPageVO extends PageInfoVO implements Serializable {

	private static final long serialVersionUID = 5669314646021330342L;

	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;

	@ApiModelProperty(name = "startTime", value = "开始时间戳", required = true)
	private Date startTime;

	@ApiModelProperty(name = "endTime", value = "结束时间戳", required = true)
	private Date endTime;

	@Override
	public void checkParams() {
		super.checkParams();
		// 默认是过去一年数据
		if (startTime == null && endTime == null) {
			endTime = new Date();
			startTime = DateUtil.offsetMonth(endTime, -12);
		} else if (startTime != null && endTime == null) {
			endTime = new Date();
		} else if (startTime == null) {
			startTime = DateUtil.offsetMonth(endTime, -12);
		}
		if (startTime.getTime() > System.currentTimeMillis() || endTime.getTime() < startTime.getTime()) {
			throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
		}
	}
}
