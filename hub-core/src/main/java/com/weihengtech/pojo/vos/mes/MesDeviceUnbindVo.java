package com.weihengtech.pojo.vos.mes;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Getter
@Setter
@ToString
public class MesDeviceUnbindVo {

    @ApiModelProperty(name = "deviceSnList", value = "设备snList", required = true)
    @NotEmpty(message = "err.not.blank")
    private List<String> deviceSnList;
}
