package com.weihengtech.pojo.vos.device;

import com.weihengtech.pojo.vos.parent.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "设备绑定分页入参")
public class DeviceLikePageVo extends PageInfoVO {

	@ApiModelProperty(value = "设备序列号模糊搜索字段")
	private String deviceSn;

	@ApiModelProperty(value = "过滤主从机：0-过滤掉从机|1-过滤掉主机")
	private Integer filter;
}
