package com.weihengtech.pojo.vos.firmware;

import com.weihengtech.annotation.validator.IsAllowedVersionName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "硬件版本新增入参")
public class HardwareVersionInsertVO implements Serializable {

	private static final long serialVersionUID = 4651209489045826018L;

	@ApiModelProperty(name = "typeId", value = "类型id", required = true)
	@NotBlank(message = "err.not.blank")
	private String typeId;

	@ApiModelProperty(name = "softwareVersionList", value = "要绑定的软件版本号id列表")
	private List<String> softwareVersionList;

	@ApiModelProperty(name = "hardwareVersion", value = "硬件版本号", required = true)
	@NotBlank(message = "err.not.blank")
	@IsAllowedVersionName(message = "err.invalid.version.name")
	private String hardwareVersion;

	@ApiModelProperty(name = "model", value = "适用机型", required = true)
	private String model;
}
