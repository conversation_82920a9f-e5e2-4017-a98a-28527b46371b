package com.weihengtech.pojo.vos.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel("设备维护信息更新入参")
public class EcosDeviceMaintenanceInfoUpdateVo {

	@ApiModelProperty(value = "维护记录id")
	@NotBlank(message = "err.not.blank")
	private String id;
	@ApiModelProperty(value = "维护记录")
	private String record;
}
