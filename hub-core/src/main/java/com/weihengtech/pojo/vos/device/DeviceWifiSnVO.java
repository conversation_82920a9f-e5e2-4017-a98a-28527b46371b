package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/6/5 9:51
 * @version 1.0
 */
@Data
@ApiModel(value = "设备相关入参类")
public class DeviceWifiSnVO {

	@ApiModelProperty(name = "wifiSn", value = "wifiSn")
	@NotBlank(message = "err.not.blank")
	private String wifiSn;
}
