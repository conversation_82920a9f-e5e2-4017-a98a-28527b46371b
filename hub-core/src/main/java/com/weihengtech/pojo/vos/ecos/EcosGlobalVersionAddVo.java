package com.weihengtech.pojo.vos.ecos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "新增版本信息入参")
public class EcosGlobalVersionAddVo {

	@ApiModelProperty(name = "androidVersion", value = "安卓版本号", required = true)
	@NotBlank(message = "err.not.blank")
	private String androidVersion;

	@ApiModelProperty(name = "iosVersion", value = "ios版本号", required = true)
	@NotBlank(message = "err.not.blank")
	private String iosVersion;

	@ApiModelProperty(name = "flag", value = "0: 提醒一次 1: 每次提醒 2: 强制更新", required = true)
	@NotNull(message = "err.not.null")
	private Integer flag;

	@ApiModelProperty(name = "zhCn", value = "中国")
	private String zhCn;

	@ApiModelProperty(name = "enUs", value = "美国")
	private String enUs;

}
