package com.weihengtech.pojo.vos.firmware;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.pojo.vos.parent.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "硬件类型更新入参")
public class HardwareTypeUpdateVO extends BaseVO implements Serializable {

	private static final long serialVersionUID = -8876720857971863999L;

	@NotBlank(message = "err.not.blank")
	@ApiModelProperty(name = "id", value = "硬件类型id")
	private String id;

	@ApiModelProperty(name = "typeName", value = "硬件类型名")
	private String typeName;

	@ApiModelProperty(name = "deleteFlag", value = "0: 删除; 1: 正常")
	private Integer deleteFlag;

	@ApiModelProperty(name = "categoryId", value = "分类id")
	private Integer categoryId;

	@Override
	public void checkParams() {
		if (deleteFlag != null && 0 != deleteFlag && 1 != deleteFlag) {
			throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
		}
		if (deleteFlag == null && categoryId == null && StrUtil.isBlank(typeName)) {
			throw new CustomException(ExceptionEnum.PARAM_MISS_EXCEPTION);
		}
	}
}
