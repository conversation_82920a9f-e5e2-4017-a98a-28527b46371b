package com.weihengtech.pojo.vos.firmware;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "软件版本更新入参")
public class SoftwareVersionUpdateVO implements Serializable {

	private static final long serialVersionUID = -6729224992520612264L;

	@ApiModelProperty(name = "softwareVersionId", value = "软件版本id", required = true)
	@NotBlank(message = "err.not.blank")
	private String softwareVersionId;

	@ApiModelProperty(name = "hardwareVersionList", value = "要绑定的硬件版本号id列表")
	@NotEmpty(message = "err.param.valid.exception")
	private List<String> hardwareVersionList;

	@ApiModelProperty(name = "softwareVersion", value = "软件版本号", required = true)
	@NotBlank(message = "err.not.blank")
	private String softwareVersion;

	@ApiModelProperty(name = "model", value = "适用机型", required = true)
	private String model;
}
