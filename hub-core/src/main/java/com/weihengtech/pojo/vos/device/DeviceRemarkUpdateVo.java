package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @program ecos-admin-backend
 * @description
 * @create 2023-08-17 18:35
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "设备备注更新入参")
public class DeviceRemarkUpdateVo {

    @ApiModelProperty(name = "deviceId", value = "设备id", required = true)
    @NotNull(message = "err.not.null")
    private Long deviceId;

    @ApiModelProperty(name = "remark", value = "设备别名", required = true)
    @NotNull(message = "err.not.null")
    private String remark;
}
