package com.weihengtech.pojo.vos.systeminfo;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 配网进度
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 16:34
 */
@Data
public class SystemInfoUpdVO {

    @ApiModelProperty("系统id")
    @NotNull(message = "err.not.blank")
    private Long id;

    @ApiModelProperty("系统名称")
    private String name;

    @ApiModelProperty("系统地址")
    private Integer datacenterId;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("用户名")
    private String ownerFirstName;

    @ApiModelProperty("用户姓")
    private String ownerLastName;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("手机")
    private String phone;

    @ApiModelProperty("安装日期")
    private Date installDate;

    @ApiModelProperty("当前步骤")
    private Integer currentStep;

    @ApiModelProperty("转移时间")
    private Date transferTime;

    @ApiModelProperty("确认检查完毕（与系统详情关联id，非设备表id）")
    private Long confirmDeviceId;

    @ApiModelProperty("安装商保留设备时长:-1,3,999")
    private Integer saveDeviceTime;

    /**
     * 邮箱、手机至少填写一项
     */
    public void checkParams() {
        // 手机、邮箱都更新为空
        if (StrUtil.EMPTY.equals(this.getPhone()) && StrUtil.EMPTY.equals(this.getEmail())) {
            throw new CustomException(ExceptionEnum.ACTION_ASSERT_NOT_FIT_EXCEPTION);
        }
    }
}
