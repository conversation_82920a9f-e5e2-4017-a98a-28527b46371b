package com.weihengtech.pojo.vos.firmware;

import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.pojo.vos.parent.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang.StringUtils;

import java.util.Collection;

/**
 * @author: jiahao.jin
 * @create: 2025-07-18 10:59
 * @description: 批量升级设备分页筛选入参
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "批量升级设备分页入参")
public class FirmwareBatchUpgradeDevicePageVo extends PageInfoVO {

    @ApiModelProperty(value = "设备sn")
    private String deviceSn;

    @ApiModelProperty(value = "设备类型")
    private Integer resourceType;

    @ApiModelProperty(value = "固件类型：DSP1/DSP2/BMS/EMS/ARCDSP")
    private String firmwareType;

    @ApiModelProperty(value = "EMS软件版本条件")
    private VersionCondition emsSoftwareCondition;

    @ApiModelProperty(value = "EMS硬件版本条件")
    private VersionCondition emsHardwareCondition;

    @ApiModelProperty(value = "DSP1软件版本条件")
    private VersionCondition dsp1SoftwareCondition;

    @ApiModelProperty(value = "DSP2软件版本条件")
    private VersionCondition dsp2SoftwareCondition;

    @ApiModelProperty(value = "ARCDSP软件版本条件")
    private VersionCondition arcdspSoftwareCondition;

    @ApiModelProperty(value = "BMS软件版本条件")
    private VersionCondition bmsSoftwareCondition;

    @ApiModelProperty(value = "BMS硬件版本条件")
    private VersionCondition bmsHardwareCondition;

    @ApiModelProperty(value = "安规ID")
    private String safetyStandard;

    @ApiModelProperty(value = "发货地区")
    private Integer countryId;

    @ApiModelProperty(value = "安装地区")
    private String installCountry;

    @ApiModelProperty(value = "电量状态, 0：低电量设备, 1：排除低电量设备")
    private String batSocState;

    @ApiModelProperty(value = "设备id列表")
    private Collection<String> deviceIdList;

    // 软件版本号白名单列表
    private FirmwareAllWhiteListVo firmwareAllWhiteListVo;

    /**
     * 版本比较条件内部类
     */
    @Data
    @ApiModel(value = "版本比较条件")
    public static class VersionCondition {
        @ApiModelProperty(value = "比较操作符(=, <, >, <=, >=)", example = "=")
        private String operator;

        @ApiModelProperty(value = "版本号", example = "1.0.0")
        private String version;

        @ApiModelProperty(value = "子版本号", example = "001")
        private String subVersion;
    }

    @Override
    public void checkParams() {
        super.checkParams();

        // 检查设备类型不为空
        if (resourceType == null) {
            throw new CustomException(ExceptionEnum.SELECT_SERIES_TYPE_FIRST);
        }

        // 检查固件类型不为空
        if (StringUtils.isBlank(firmwareType)) {
            throw new CustomException(ExceptionEnum.SELECT_SERIES_TYPE_FIRST);
        }
    }
}