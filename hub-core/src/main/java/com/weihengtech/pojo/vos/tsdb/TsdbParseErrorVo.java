package com.weihengtech.pojo.vos.tsdb;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TsdbParseErrorVo {

	@ApiModelProperty(name = "deviceSn", value = "设备sn")
	private String deviceSn;

	@ApiModelProperty(name = "dspDecimal", value = "dsp十进制")
	private Double dspDecimal = -1.0D;

	@ApiModelProperty(name = "bmsDecimal", value = "bms十进制")
	private Double bmsDecimal = -1.0D;

	@ApiModelProperty(name = "armDecimal", value = "arm十进制")
	private Double armDecimal = 0D;

	@ApiModelProperty(name = "dspBit1", value = "dsp1 bit")
	private Double dspBit1 = 0D;

	@ApiModelProperty(name = "dspBit2", value = "dsp2 bit")
	private Double dspBit2 = 0D;

	@ApiModelProperty(name = "dspBit3", value = "dsp3 bit")
	private Double dspBit3 = 0D;

	@ApiModelProperty(name = "bmsBitFault", value = "bms bit protection_status")
	private Double bmsBitFault = 0D;

	@ApiModelProperty(name = "bmsBitAlarm", value = "bms bit alarm_status")
	private Double bmsBitAlarm = 0D;

	@ApiModelProperty(name = "bmsBitError", value = "bms bit error_1")
	private Double bmsBitError = 0D;

	@ApiModelProperty(name = "bmsBitError2", value = "bms bit error_2")
	private Double bmsBitError2 = 0D;

	@ApiModelProperty(name = "bmsBitError3", value = "bms bit error_3")
	private Double bmsBitError3 = 0D;

	@ApiModelProperty(name = "bmsBitError4", value = "bms bit error_4")
	private Double bmsBitError4 = 0D;

	@ApiModelProperty(name = "bmsBitError5", value = "bms bit error_5")
	private Double bmsBitError5 = 0D;

	@ApiModelProperty(name = "bmsHistory", value = "bmsHistory")
	private Double bmsBitHistory = 0D;

}
