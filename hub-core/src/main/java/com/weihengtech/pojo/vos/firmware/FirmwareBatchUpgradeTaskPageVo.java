package com.weihengtech.pojo.vos.firmware;

import com.weihengtech.pojo.vos.parent.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: jiahao.jin
 * @create: 2025-07-18 09:25
 * @description: 批量升级任务分页入参
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "批量升级任务分页入参")
public class FirmwareBatchUpgradeTaskPageVo extends PageInfoVO {

    // 任务名称
    @ApiModelProperty(name = "taskName", value = "任务名称")
    private String taskName;

    // 系列
    @ApiModelProperty(name = "resourceSeriesCode", value = "设备系列编号")
    private String resourceSeriesCode;

    // 类型
    @ApiModelProperty(name = "resourceType", value = "设备类型")
    private List<String> resourceType;

    // 固件类型
    @ApiModelProperty(name = "firmwareType", value = "固件类型")
    private String firmwareType;

    // 固件包名称
    @ApiModelProperty(name = "firmwareName", value = "固件包名称")
    private String firmwareName;

    // 升级方式
    @ApiModelProperty(name = "upgradeMode", value = "升级方式")
    private String upgradeMode;

    // 任务状态
    @ApiModelProperty(name = "status", value = "任务状态")
    private String status;

    // 升级开始时间
    @ApiModelProperty(name = "upgradeStartTime", value = "升级开始时间")
    private Long upgradeStartTime;
    @ApiModelProperty(name = "upgradeEndTime", value = "升级结束时间")
    private Long upgradeEndTime;

    // 创建开始时间
    @ApiModelProperty(name = "createTimeStart", value = "创建开始时间")
    private Long createTimeStart;
    @ApiModelProperty(name = "createTimeEnd", value = "创建结束时间")
    private Long createTimeEnd;
}
