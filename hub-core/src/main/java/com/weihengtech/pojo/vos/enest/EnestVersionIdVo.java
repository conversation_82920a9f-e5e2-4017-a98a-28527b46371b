package com.weihengtech.pojo.vos.enest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "enest版本id入参")
public class EnestVersionIdVo {

	@ApiModelProperty(name = "Id", value = "版本id", required = true)
	@NotBlank(message = "err.not.blank")
	private String Id;
}
