package com.weihengtech.pojo.vos.firmware;

import com.weihengtech.annotation.validator.IsAllowedVersionName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "软件版本新增入参")
public class SoftwareVersionInsertVO implements Serializable {

	private static final long serialVersionUID = 5267589907097302985L;

	@ApiModelProperty(name = "hardwareVersionList", value = "要绑定的硬件版本号id列表")
	@NotEmpty(message = "err.param.valid.exception")
	private List<String> hardwareVersionList;

	@ApiModelProperty(name = "softwareVersion", value = "软件版本号", required = true)
	@NotBlank(message = "err.not.blank")
	@IsAllowedVersionName(message = "err.invalid.version.name")
	private String softwareVersion;

	@ApiModelProperty(name = "hardwareTypeId", value = "硬件类型id", required = true)
	@NotBlank(message = "err.not.blank")
	private String hardwareTypeId;

	@ApiModelProperty(name = "model", value = "适用机型", required = true)
	private String model;
}
