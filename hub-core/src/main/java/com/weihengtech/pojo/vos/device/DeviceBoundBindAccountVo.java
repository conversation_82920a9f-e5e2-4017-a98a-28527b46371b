package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备绑定账号入参")
public class DeviceBoundBindAccountVo {

	@ApiModelProperty(value = "设备sn列表")
	@NotEmpty(message = "err.not.null")
	private List<String> deviceSnList;

	@ApiModelProperty(value = "用户id")
	@NotBlank(message = "err.not.blank")
	private String userId;

	@ApiModelProperty(value = "0: 给销售绑 1. 给代理商绑 2: 给分销商绑 3: 给零售商绑")
	@NotNull(message = "err.not.null")
	private Integer actionType;
}
