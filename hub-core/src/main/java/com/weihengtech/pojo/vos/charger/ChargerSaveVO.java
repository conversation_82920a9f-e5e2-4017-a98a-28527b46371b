package com.weihengtech.pojo.vos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * ecos配网创建新设备入参
 *
 * <AUTHOR>
 * @date 2024/1/25 14:39
 * @version 1.0
 */
@Data
@ApiModel(value = "ecos配网创建新设备入参")
public class ChargerSaveVO {

	@ApiModelProperty(value = "设备列表id", required = true)
	@NotBlank(message = "err.not.blank")
	private Long deviceId;

	@ApiModelProperty(value = "数据中心id", required = true)
	private Integer dataCenterId;

	@ApiModelProperty(value = "纬度", required = true)
	private Double lat;

	@ApiModelProperty(value = "经度", required = true)
	private Double lon;

	@ApiModelProperty(value = "ip地址", required = true)
	private String ip;

	@ApiModelProperty(value = "网关sn", required = true)
	@NotBlank(message = "err.not.blank")
	private String gateSn;

	@ApiModelProperty(value = "充电桩sn", required = true)
	@NotBlank(message = "err.not.blank")
	private String chargerSn;

	@ApiModelProperty(value = "绑定模式：即插即充|联网绑定", required = true)
	@NotNull(message = "err.not.null")
	private Integer bindMode;

	@ApiModelProperty(value = "最大充电功率")
	private Double maxPower;

	@ApiModelProperty(value = "固件版本")
	private String cpFirmwareVersion;

	@ApiModelProperty(value = "即插即用开关")
	private String cpPlugAndChargeMsg;

	@ApiModelProperty(value = "充电模式：00---负载平衡、光伏都关闭 01---只打开光伏的绿色模式 02---只打开光伏的混合模式 03---只打开负载平衡 04---打开光伏的绿色模式+负载平衡 05---打开光伏的混合模式+负载平衡")
	private String cpMode;

	@ApiModelProperty(value = "电流监测类型：选择仅CT，则为00；选择仅智能电表，则为01；选择智能电表+CT，则为02；当关闭负载均衡和光伏充电时，传0A。")
	private String cpMeterType;

	@ApiModelProperty(value = "电表变比：选择仅CT，则为00；选择仅智能电表，则为00；智能电表+CT时，如果选择ADL400+CT(150:5)时，则为1E；如果选择SDM120+CT(1000:1)时，则为01。")
	private String cpMeterRatio;

	@ApiModelProperty(value = "家庭总线电流")
	private Double cpHomeCurrent;

	@ApiModelProperty(value = "光伏电流阈值")
	private Double cpPvCurrent;
}
