package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/10/12 16:02
 * @version 1.0
 */
@Data
@ApiModel(value = "设备信息入参类")
public class DynamicSaveVO {

	@ApiModelProperty(name = "deviceName", value = "设备名")
	@NotBlank(message = "err.not.blank")
	private String deviceName;

	@ApiModelProperty(name = "designMaxPower", value = "设计最大功率")
	@NotNull(message = "err.not.null")
	private Integer designMaxPower;

//	@ApiModelProperty(name = "country", value = "国家")
//	@NotBlank(message = "err.not.blank")
//	private String country;

	@ApiModelProperty(name = "gridCompany", value = "电网公司")
	@NotNull(message = "err.not.null")
	private Integer gridCompany;

	@ApiModelProperty(name = "nmi", value = "户号")
	@NotBlank(message = "err.not.blank")
	private String nmi;

	@ApiModelProperty(name = "maxVa", value = "设计最大视在功率")
	private Integer maxVa;

	@ApiModelProperty(name = "maxVar", value = "设计最大无功功率")
	private Integer maxVar;

	@ApiModelProperty(name = "maxVarNeg", value = "设计最大负无功功率")
	private Integer maxVarNeg;

	@ApiModelProperty(name = "maxWh", value = "设计最大储能容量")
	private Integer maxWh;
}
