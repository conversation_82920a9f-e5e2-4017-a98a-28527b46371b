package com.weihengtech.pojo.vos.ecos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "Ecos设备配置查询入参")
public class EcosDeviceConfigQueryVo {

	@ApiModelProperty(value = "设备序列号")
	@NotBlank(message = "err.not.blank")
	private String deviceSn;
}
