package com.weihengtech.pojo.vos.firmware;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: jiahao.jin
 * @create: 2025-07-14 19:09
 * @description: 批量升级定时任务入参
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FirmwareBatchUpgradeTaskVo {

    @ApiModelProperty(name = "taskName", value = "任务名称", required = true)
    @NotBlank(message = "err.not.blank")
    private String taskName;

    @ApiModelProperty(name = "firmwareId", value = "固件包ID", required = true)
    @NotBlank(message = "err.not.blank")
    private String firmwareId;

    @ApiModelProperty(name = "deviceIdList", value = "设备ID列表", required = true)
    @NotNull(message = "err.not.blank")
    private List<String> deviceIdList;

    @ApiModelProperty(name = "resourceSeriesCode", value = "设备系列编号", required = true)
    @NotBlank(message = "err.not.blank")
    private String resourceSeriesCode;

    @ApiModelProperty(name = "resourceType", value = "设备类型", required = true)
    @NotBlank(message = "err.not.blank")
    private String resourceType;

    @ApiModelProperty(name = "upgradeMode", value = "升级方式：立即升级(immediate_execution)/定时升级(scheduled_execution)", required = true)
    @NotBlank(message = "err.not.blank")
    private String upgradeMode;

    @ApiModelProperty(name = "upgradeStartTime", value = "升级开始时间（毫秒级时间戳）")
    private Long upgradeStartTime;

    @ApiModelProperty(name = "remarks", value = "备注")
    private String remarks;
}
