package com.weihengtech.pojo.vos.ecos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "Ecos Agreement 版本新增入参")
public class EcosGlobalAgreementVersionAddVo {

	@ApiModelProperty(value = "新增版本号")
	@NotNull(message = "err.not.null")
	private Integer version;
}
