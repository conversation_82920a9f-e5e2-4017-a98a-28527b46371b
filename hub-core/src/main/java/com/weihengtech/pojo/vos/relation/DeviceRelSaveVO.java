package com.weihengtech.pojo.vos.relation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/10 9:45
 */
@Data
public class DeviceRelSaveVO {

    @ApiModelProperty("设备sn")
    @NotBlank(message = "设备sn不能为空")
    private String deviceName;

    @ApiModelProperty("从机id列表")
    private List<String> deviceSlaveList;

    @ApiModelProperty("主机id列表")
    private String deviceMaster;
}
