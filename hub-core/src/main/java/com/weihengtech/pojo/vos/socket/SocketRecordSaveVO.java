package com.weihengtech.pojo.vos.socket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 单插开关记录查询入参
 *
 * <AUTHOR>
 * @date 2024/1/29 9:55
 * @version 1.0
 */
@Getter
@Setter
@ToString
@ApiModel(value = "单插开关记录查询入参")
public class SocketRecordSaveVO implements Serializable {

	private static final long serialVersionUID = 7419565825568875087L;

	@ApiModelProperty(name = "deviceName", value = "设备名", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceName;

	@ApiModelProperty(name = "startTime", value = "开始时间戳", required = true)
	private Date startTime;

	@ApiModelProperty(name = "endTime", value = "结束时间戳", required = true)
	private Date endTime;

}
