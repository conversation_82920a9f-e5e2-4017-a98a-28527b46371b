package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备别名更新入参")
public class DeviceAliasUpdateVo {

	@ApiModelProperty(name = "id", value = "设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String id;

	@ApiModelProperty(name = "alias", value = "设备别名", required = true)
	@NotNull(message = "err.not.null")
	private String alias;
}
