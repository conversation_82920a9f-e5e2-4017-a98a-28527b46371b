package com.weihengtech.pojo.vos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 充电记录保存入参
 *
 * <AUTHOR>
 * @date 2024/1/29 9:55
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "充电记录查询入参")
public class RecordSaveVO implements Serializable {

	private static final long serialVersionUID = 7419565825568875087L;

	@ApiModelProperty(name = "deviceId", value = "设备名", required = true)
	@NotNull(message = "err.not.blank")
	private Long deviceId;

	@ApiModelProperty(name = "transactionId", value = "事务id", required = true)
	@NotNull(message = "err.not.blank")
	private Long transactionId;

	@ApiModelProperty(name = "startTime", value = "开始时间戳", required = true)
	private Date startTime;

	@ApiModelProperty(name = "endTime", value = "结束时间戳", required = true)
	private Date endTime;

	@ApiModelProperty(name = "duration", value = "充电时长", required = true)
	private Long duration;

	@ApiModelProperty(name = "batCap", value = "充电量kWh", required = true)
	private String batCap;

	@ApiModelProperty(name = "meterValue", value = "电表读数", required = true)
	private Integer meterValue;

}
