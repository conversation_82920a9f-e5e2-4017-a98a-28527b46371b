package com.weihengtech.pojo.vos.device;

import com.weihengtech.annotation.validator.IsDeviceStateHandleAction;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备状态处理入参")
public class DeviceStateHandleVO implements Serializable {

	private static final long serialVersionUID = -911366682971794337L;

	@ApiModelProperty(name = "action", value = "0: 开机; 1: 关机; 2:重启", required = true)
	@NotBlank(message = "err.not.blank")
	@IsDeviceStateHandleAction
	private String action;

	@ApiModelProperty(name = "deviceNameList", value = "设备名称", required = true)
	@NotEmpty(message = "err.param.valid.exception")
	private List<String> deviceNameList;
}
