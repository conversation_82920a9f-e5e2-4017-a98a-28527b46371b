package com.weihengtech.pojo.vos.ecos;

import com.weihengtech.annotation.validator.IsDeviceEventLevel;
import com.weihengtech.pojo.vos.parent.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备事件入参")
public class DeviceEventVO extends PageInfoVO {

	@ApiModelProperty(name = "deviceName", value = "设备名")
	private String deviceName;

	@ApiModelProperty(name = "startTime", value = "开始时间")
	private Long startTime;

	@ApiModelProperty(name = "endTime", value = "结束时间")
	private Long endTime;

	@ApiModelProperty(name = "level", value = "事件等级 fault: 错误; alarm 告警; event: 事件;")
	@IsDeviceEventLevel
	private String level;

	@ApiModelProperty(name = "content", value = "事件描述")
	private String content;

	private Boolean isChinese;
}
