package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@With
@AllArgsConstructor
@NoArgsConstructor
public class DeviceSyncStateVo {

	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;
}
