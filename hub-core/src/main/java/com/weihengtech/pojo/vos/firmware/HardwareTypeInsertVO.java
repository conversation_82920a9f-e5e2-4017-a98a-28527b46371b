package com.weihengtech.pojo.vos.firmware;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "硬件类型入参")
public class HardwareTypeInsertVO implements Serializable {

	@NotBlank(message = "err.not.blank")
	@ApiModelProperty(name = "typeName", value = "类型名称", required = true)
	private String typeName;

	@NotNull(message = "err.not.null")
	@ApiModelProperty(name = "categoryId", value = "分类id", required = true)
	private Integer categoryId;
}
