package com.weihengtech.pojo.vos.device;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.pojo.vos.parent.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "重点设备更新入参")
public class DeviceFocusUpdateVo extends BaseVO {

	@ApiModelProperty(name = "id", value = "重点关注id", required = true)
	@NotBlank(message = "err.not.blank")
	private String id;

	@ApiModelProperty(name = "deviceRemark", value = "设备备注")
	private String deviceRemark;

	@ApiModelProperty(name = "notifyType", value = "通知类型 0: 邮件;  1: 短信;  2: 邮件+短信", required = true)
	@NotNull(message = "err.not.null")
	private Integer notifyType;

	@ApiModelProperty(name = "notifyEmail", value = "通知邮件")
	private String notifyEmail;

	@ApiModelProperty(name = "notifyPhone", value = "通知电话")
	private String notifyPhone;

	@Override
	public void checkParams() {
		List<Integer> notifyTypeList = ListUtil.toList(0, 1, 2);
		if (!notifyTypeList.contains(notifyType) || 0 == notifyType && StrUtil.isBlank(notifyEmail)
				|| 1 == notifyType && StrUtil.isBlank(notifyPhone)
				|| 2 == notifyType && (StrUtil.isBlank(notifyEmail) || StrUtil.isBlank(notifyPhone))) {
			throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
		}
	}
}
