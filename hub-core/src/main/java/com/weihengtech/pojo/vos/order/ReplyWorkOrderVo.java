package com.weihengtech.pojo.vos.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "回复工单入参")
public class ReplyWorkOrderVo {

	@ApiModelProperty(value = "工单id", required = true)
	@NotBlank(message = "err.not.blank")
	private String workOrderId;

	@ApiModelProperty(value = "内容")
	private String content;

	@ApiModelProperty(value = "图片列表")
	private List<String> picList;
}
