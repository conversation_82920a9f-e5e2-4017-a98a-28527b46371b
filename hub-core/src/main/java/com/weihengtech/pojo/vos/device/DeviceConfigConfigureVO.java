package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备配置入参")
public class DeviceConfigConfigureVO extends DeviceFlagVO implements Serializable {

	private static final long serialVersionUID = -411964856840995060L;

	@NotBlank(message = "err.not.blank")
	@ApiModelProperty(name = "code", value = "序列码", required = true)
	private String code;

	@NotEmpty(message = "err.param.valid.exception")
	@ApiModelProperty(name = "propNameList", value = "属性名列表", required = true)
	private List<String> propNameList;

	@NotEmpty(message = "err.param.valid.exception")
	@ApiModelProperty(name = "propValueList", value = "属性值列表", required = true)
	private List<String> propValueList;
}
