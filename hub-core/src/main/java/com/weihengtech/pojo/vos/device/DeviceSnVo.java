package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备序列号入参")
public class DeviceSnVo {

	@ApiModelProperty(name = "deviceName", value = "设备序列号", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceName;

	@ApiModelProperty(name = "timezone", value = "时区")
	private String timezone;
}
