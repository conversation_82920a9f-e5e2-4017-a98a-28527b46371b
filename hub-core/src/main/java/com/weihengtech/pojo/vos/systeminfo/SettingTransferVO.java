package com.weihengtech.pojo.vos.systeminfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设备转移入参
 *
 * <AUTHOR>
 * @date 2023/8/21 16:26
 * @version 1.0
 */
@Data
public class SettingTransferVO {

	@ApiModelProperty("app标识")
	@NotBlank(message = "err.not.blank")
	private String appSchema;

	@ApiModelProperty("国家编码")
	@NotBlank(message = "err.not.blank")
	private String countryCode;

	@ApiModelProperty("安装商uuid：tuya平台的账号")
	@NotBlank(message = "err.not.blank")
	private String installerId;

	@ApiModelProperty("家庭id")
	private Long homeId;

	@ApiModelProperty("设备id：绑定账号设备关系")
	@NotEmpty(message = "err.not.null")
	private List<Long> deviceIds;

	@ApiModelProperty("系统id：更新转移时间")
	@NotNull(message = "err.not.null")
	private Long systemInfoId;

}
