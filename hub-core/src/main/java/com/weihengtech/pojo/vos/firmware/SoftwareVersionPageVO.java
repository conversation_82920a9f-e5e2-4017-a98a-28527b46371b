package com.weihengtech.pojo.vos.firmware;

import com.weihengtech.pojo.vos.parent.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "软件版本分页入参")
public class SoftwareVersionPageVO extends PageInfoVO {

	@ApiModelProperty(name = "hardwareTypeId", value = "硬件类型id")
	private String hardwareTypeId;

	@ApiModelProperty(name = "softwareVersion", value = "软件版本号")
	private String softwareVersion;

	@ApiModelProperty(name = "model", value = "适用机型")
	private String model;

}
