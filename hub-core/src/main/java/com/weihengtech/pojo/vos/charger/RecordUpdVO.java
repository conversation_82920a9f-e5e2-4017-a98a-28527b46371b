package com.weihengtech.pojo.vos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 充电记录保存入参
 *
 * <AUTHOR>
 * @date 2024/1/29 9:55
 * @version 1.0
 */
@Getter
@Setter
@ToString
@ApiModel(value = "充电记录更新入参")
public class RecordUpdVO implements Serializable {

	private static final long serialVersionUID = -1336430993383120477L;

	@ApiModelProperty(name = "id", value = "id", required = true)
	@NotNull(message = "err.not.blank")
	private Integer id;

	@ApiModelProperty(name = "deviceName", value = "设备名", required = true)
	@NotNull(message = "err.not.blank")
	private Long deviceId;

	@ApiModelProperty(name = "transactionId", value = "事务id", required = true)
	@NotNull(message = "err.not.blank")
	private Long transactionId;

	@ApiModelProperty(name = "startTime", value = "开始时间戳", required = true)
	private Date startTime;

	@ApiModelProperty(name = "endTime", value = "结束时间戳", required = true)
	private Date endTime;

	@ApiModelProperty(name = "duration", value = "充电时长", required = true)
	private Long duration;

	@ApiModelProperty(name = "endTime", value = "充电量", required = true)
	private String batCap;

}
