package com.weihengtech.pojo.vos.device;

import com.weihengtech.pojo.vos.parent.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@With
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "单相户用储能一体机分页查询入参")
public class DeviceListPageVO extends PageInfoVO {

	@ApiModelProperty(name = "deviceName", value = "设备名称")
	private String deviceName;

	@ApiModelProperty(name = "wifiSn", value = "wifi棒序列号")
	private String wifiSn;

	@ApiModelProperty(name = "alias", value = "别名")
	private String alias;

	@ApiModelProperty(name = "desc", value = "描述")
	private String desc;

	@ApiModelProperty(name = "agentId", value = "代理商Id")
	private String agentId;

	@ApiModelProperty(value = "分销商id")
	private String dealerId;

	@ApiModelProperty(value = "零售商id")
	private String retailerId;

	@ApiModelProperty(value = "销售id")
	private String saleId;

	@ApiModelProperty(name = "countryId", value = "国家id", required = true)
	private Integer countryId;

	@ApiModelProperty(name = "state", value = "状态 -5 未知, -4:已禁用 -3:未激活 -1: 离线 0: 等待 1: 并网 2: EPS 3: 故障 4: 保留 5: 自检, 20: 在线 21 不可用")
	private Integer state;

	@ApiModelProperty(value = "安装国家")
	private String installCountry;

	@ApiModelProperty(value = "类型")
	private Integer resourceType;

	@ApiModelProperty(value = "系列")
	private Integer resourceSeries;

	@ApiModelProperty(value = "类型列表")
	private List<String> resourceTypeList;

	@ApiModelProperty(value = "状态列表")
	private List<Integer> stateList;

	@ApiModelProperty(value = "电量状态, 0：低电量设备")
	private Integer batSocState;

	@ApiModelProperty(value = "详细地址")
	private String address;

}
