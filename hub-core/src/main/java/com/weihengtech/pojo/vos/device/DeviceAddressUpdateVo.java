package com.weihengtech.pojo.vos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @author: jiahao.jin
 * @create: 2025-09-16 09:39
 * @description:
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备地址更新入参")
public class DeviceAddressUpdateVo {

    @ApiModelProperty(name = "id", value = "设备id", required = true)
    @NotBlank(message = "err.not.blank")
    private String id;

    @ApiModelProperty(name = "address", value = "设备地址", required = true)
    @NotBlank(message = "err.not.blank")
    private String address;
}
