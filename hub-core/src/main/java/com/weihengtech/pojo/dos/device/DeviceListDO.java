package com.weihengtech.pojo.dos.device;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.weihengtech.enums.device.ImportTypeEnum;
import com.weihengtech.enums.device.RatedPowerEnum;
import com.weihengtech.enums.device.ResourceTypeEnum;
import com.weihengtech.pojo.vos.device.NetBindDeviceVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.With;
import org.springframework.data.annotation.Transient;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@With
@AllArgsConstructor
@NoArgsConstructor
@TableName("hybrid_single_phase")
public class DeviceListDO {

	@TableId(value = "id", type = IdType.INPUT)
	private Long id;
	/** 别名 */
	private String alias;
	/** 设备sn */
	private String deviceSn;
	/** 配网sn */
	private String wifiSn;
	/** 设备名称 */
	private String deviceName;
	/** -5 未知, -4: 设备已禁用; -3: 设备未激活; -1 设备离线 0: 等待 1: 并网 2: EPS 3: 故障; 4:保留 5: 自检 */
	private Integer state;
	/** 经度 */
	private Double longitude;
	/** 纬度 */
	private Double latitude;
	/** 首次安装时间 */
	private Long firstInstall;
	/** 发货地区 */
	private Integer countryId;
	/** 数据中心 */
	private Integer datacenterId;
	/** IP地址 */
	private String ip;
	/** 品类 */
	private String category;
	/** 型号 */
	private String model;
	/** 详细地址 */
	private String address;
	/** 资源系列id */
	@TableField(exist = false)
	private Integer resourceSeriesId;
	/** 资源类型id */
	@TableField(exist = false)
	private Integer resourceTypeId;

	/** 平台 */
	@Transient
	private Integer dataSource;
	/** 0: 易联tsdb 1: tuya lindorm 2 易联 lindorm */
	@Transient
	private Integer tsdbSource;
	/** 创建时间 */
	@Transient
	private LocalDateTime createTime;
	/** 更新时间 */
	@Transient
	private LocalDateTime updateTime;

	/** 储能相关扩展字段 */
	private Boolean vppMode;
	private String deviceModel;
	private String brand;
	private String factory;
	private String powerBoardHardwareVersion;
	private String dsp1SoftwareVersion;
	private String dsp2SoftwareVersion;
	private String emsSoftwareVersion;
	private String emsHardwareVersion;
	private String wifiHardwareVersion;
	private String wifiSoftwareVersion;
	private String bmsGaugeVersion;
	private String bmsSn;
	private String bmsVendor;
	private String bmsSoftwareVersion;
	private String bmsHardwareVersion;
	private String dsp1SubVersion;
	private String dsp2SubVersion;
	private String emsSubVersion;
	private String arcDspSoftwareVersion;
	private String arcDspSubVersion;
	private String arcDspBootLoaderSoftwareVersion;
	private String ratedPower;
	private Integer parallelNumber;
	private Integer safetyStandard;
	private BigDecimal batSoc;

	/** 扩展信息 */
	@TableField(exist = false)
	private Object extInfo;
	@TableField(exist = false)
	private Boolean isMain;

	/**
	 * 绑定创建新设备构建入参
	 *
	 * @param deviceId 设备id
	 * @param deviceSn 设备sn
	 * @param datacenterId 数据中心id
	 * @param bindParam 绑定入参
	 * @return 设备列表对象
	 */
	public static DeviceListDO buildSaveParam(Long deviceId, String deviceSn, int datacenterId, NetBindDeviceVO bindParam) {
		DeviceListDO deviceInfo = new DeviceListDO();
		deviceInfo.setId(deviceId);
		deviceInfo.setDeviceName(deviceSn);
		deviceInfo.setDeviceSn(deviceSn);
		deviceInfo.setWifiSn(bindParam.getWifiSn());
		if (StrUtil.isNotBlank(bindParam.getDeviceModel())) {
			deviceInfo.setDeviceModel(bindParam.getDeviceModel());
		}
		deviceInfo.setFirstInstall(System.currentTimeMillis());
		deviceInfo.setDatacenterId(datacenterId);
		deviceInfo.setTsdbSource(bindParam.getTsdbSource() == null ? 9 : bindParam.getTsdbSource());
		deviceInfo.setDataSource(bindParam.getDataSource());
		deviceInfo.setLatitude(bindParam.getLat());
		deviceInfo.setLongitude(bindParam.getLon());
		deviceInfo.setIp(bindParam.getInstallCountry());
		deviceInfo.setCategory(bindParam.getCategory());
		deviceInfo.setModel(bindParam.getProductId());
		deviceInfo.setState(0);
		deviceInfo.setCreateTime(LocalDateTime.now());
		deviceInfo.setUpdateTime(LocalDateTime.now());
		deviceInfo.setVppMode(false);
		if (StrUtil.isNotBlank(bindParam.getAddress())) {
			deviceInfo.setAddress(bindParam.getAddress());
		}
		return deviceInfo;
	}

	/**
	 * 绑定更新参数
	 *
	 * @param deviceInfo 当前设备
	 * @param bindParam 绑定参数
	 */
	public static void buildUpdParam(DeviceListDO deviceInfo, NetBindDeviceVO bindParam) {
		deviceInfo.setWifiSn(bindParam.getWifiSn());
		Long firstInstall = deviceInfo.getFirstInstall();
		if (firstInstall == null || firstInstall < 100) {
			deviceInfo.setFirstInstall(System.currentTimeMillis());
		}
		if (StrUtil.isNotBlank(bindParam.getDeviceModel())) {
			deviceInfo.setDeviceModel(bindParam.getDeviceModel());
		}
		deviceInfo.setDataSource(bindParam.getDataSource());
		deviceInfo.setTsdbSource(bindParam.getTsdbSource());
		deviceInfo.setLatitude(bindParam.getLat());
		deviceInfo.setLongitude(bindParam.getLon());
		deviceInfo.setIp(bindParam.getInstallCountry());
		// 品类
		deviceInfo.setCategory(bindParam.getCategory());
		// 型号
		deviceInfo.setModel(bindParam.getProductId());
		deviceInfo.setUpdateTime(LocalDateTime.now());

		// 如果有address，才设置值
		if (StrUtil.isNotBlank(bindParam.getAddress())) {
			deviceInfo.setAddress(bindParam.getAddress());
		}
	}


	/** mes导入新设备初始化 */
	public void initImportNewDevice(Integer type, Long id, String deviceSn, Integer countryId) {
		if (id != null) {
			this.setId(id);
		}
		if (countryId != null) {
			this.setCountryId(countryId);
		}
		this.setDeviceSn(deviceSn);
		this.setDeviceName(deviceSn);
		this.setDataSource(9);
		this.setTsdbSource(9);
		this.setState(-5);
		this.setLongitude(0D);
		this.setLatitude(0D);
		this.setVppMode(false);
		this.setCreateTime(LocalDateTime.now());
		this.setUpdateTime(LocalDateTime.now());
		if (ImportTypeEnum.storage.getCode().equals(type) && !ResourceTypeEnum.isAgaveSh(deviceSn)) {
			this.setDeviceModel(deviceSn.substring(0, 6));
		}
		if (ImportTypeEnum.storage.getCode().equals(type)) {
			this.setRatedPower(RatedPowerEnum.getPowerBySn(deviceSn));
		}
	}

	public void buildDefaultVersion() {
		setDsp1SoftwareVersion("V1.00");
		setDsp1SubVersion("V1.00");
		setDsp2SoftwareVersion("V1.00");
		setDsp2SubVersion("V1.00");
		setBmsSoftwareVersion("V1.00");
		setBmsHardwareVersion("V1.00");
		setEmsSoftwareVersion("V1.00");
		setEmsHardwareVersion("V1.00");
		setEmsSubVersion("V1.00");
		setWifiSoftwareVersion("V1.00");
		setWifiHardwareVersion("V1.00");
		setPowerBoardHardwareVersion("V1.00");
	}
}
