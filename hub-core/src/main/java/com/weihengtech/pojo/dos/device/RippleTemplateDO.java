package com.weihengtech.pojo.dos.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 脉冲控制模板
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@TableName("ripple_template")
public class RippleTemplateDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板内容
     */
    private String content;

    private Date crtTime;

    private String crtBy;

    /**
     * 模板内容map
     */
    @TableField(exist = false)
    private Map<Integer, Integer> contentMap;

}
