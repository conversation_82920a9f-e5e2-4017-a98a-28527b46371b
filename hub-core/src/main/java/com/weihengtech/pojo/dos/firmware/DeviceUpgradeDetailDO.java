package com.weihengtech.pojo.dos.firmware;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * @author: jiahao.jin
 * @create: 2025-07-14 16:25
 * @description: 设备升级详情实体类
 */
@Getter
@Setter
@ToString
@TableName("device_upgrade_detail")
public class DeviceUpgradeDetailDO{

    @TableId(type = IdType.INPUT)
    private Long id;

    /** 批量升级任务ID */
    private Long batchTaskId;

    /** 设备ID */
    private Long deviceId;

    /** 设备SN */
    private String deviceSn;

    /** 固件类型 */
    private String firmwareType;

    /** 当前固件版本 */
    private String currentVersion;

    /** 目标固件版本 */
    private String targetVersion;

    /** 升级状态：等待中/升级中/成功/失败 */
    private String upgradeStatus;

    /** 升级开始时间 */
    private LocalDateTime startTime;

    /** 升级结束时间 */
    private LocalDateTime endTime;

    /** 失败原因 */
    private String failureReason;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 更新时间 */
    private LocalDateTime updateTime;
}
