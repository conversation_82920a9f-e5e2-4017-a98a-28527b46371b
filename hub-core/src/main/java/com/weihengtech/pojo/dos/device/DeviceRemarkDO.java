package com.weihengtech.pojo.dos.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @program ecos-admin-backend
 * @description 设备备注表参数
 * @create 2023-08-17 14:17
 **/
@Getter
@Setter
@ToString
@TableName("device_remark")
public class DeviceRemarkDO implements Serializable {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    private Long deviceId;
    private Long accountId;
    private String remark;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

}
