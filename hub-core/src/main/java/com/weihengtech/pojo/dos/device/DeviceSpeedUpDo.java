package com.weihengtech.pojo.dos.device;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("device_speed_up")
public class DeviceSpeedUpDo {

	@TableId(type = IdType.INPUT)
	private Long id;

	private String deviceFlag;

	private Long createTime;

	private Integer deviceType;

	private String reason;

	private String createUser;

	private Long expireTime;

	@TableField(exist = false)
	private Boolean state;

	public boolean isValid() {
		return expireTime == null || expireTime > DateUtil.current();
	}
}
