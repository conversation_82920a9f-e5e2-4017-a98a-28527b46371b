package com.weihengtech.pojo.dos.charger;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 充电桩充电记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("charge_record")
public class ChargeRecordDO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 事务id
     */
    private Long transactionId;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 时长
     */
    private Long duration;

    /**
     * 电表读数
     */
    private Integer meterValue;

    /**
     * 充电量
     */
    private String batCap;

    /**
     * 终止原因
     */
    private String reason;

}
