package com.weihengtech.pojo.dos.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p>
 * 设备扩展信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("storage_energy_info")
public class StorageEnergyInfoDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 长id
     */
    private String lfdi;

    /**
     * 短id
     */
    private Long sfdi;

    /**
     * pin值
     */
    private Integer pin;

    /**
     * 设计最大功率
     */
    private Integer designMaxPower;

    /**
     * 国家
     */
    private Integer country;

    /**
     * 电网公司
     */
    private Integer gridCompany;

    /**
     * 电网公司(lfdi固定值)
     */
    private String gridCompanyCode;

    /**
     * 户号
     */
    private String nmi;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 北美机：Designed max charge power
     */
    private Integer maxChargeRate;

    /**
     * 北美机：Designed max apparent power
     */
    private Integer maxVa;

    /**
     * 北美机：Designed max reactive power
     */
    private Integer maxVar;

    /**
     * 北美机：Designed max negative reactive power
     */
    private Integer maxVarNeg;

    /**
     * 北美机：Designed min power factor (over excitation)
     */
    private Integer minPfOverExcited;

    /**
     * 北美机：Designed min power factor (under excited)
     */
    private Integer minPfUnderExcited;

    /**
     * 北美机：Designed max energy storage capacity
     */
    private Integer maxWh;

    private Date crtTime;

    private String crtBy;
}
