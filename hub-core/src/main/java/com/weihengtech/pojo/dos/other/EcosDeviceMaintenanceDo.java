package com.weihengtech.pojo.dos.other;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("ecos_device_maintenance")
public class EcosDeviceMaintenanceDo {

	@TableId(type = IdType.INPUT)
	private Long id;
	private String record;
	private Long updateTime;
	private String updateBy;
}
