package com.weihengtech.pojo.dos.ecos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("client_agreement_version")
public class ClientAgreementVersionDo {
	@TableId(type = IdType.AUTO)
	private Integer id;

	private Integer version;

	private Long createTime = System.currentTimeMillis();
}
