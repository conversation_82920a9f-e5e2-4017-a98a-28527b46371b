package com.weihengtech.pojo.dos.firmware;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("hardware_version")
public class HardwareVersionDO {

	@TableId(type = IdType.INPUT)
	private Long id;

	private Long typeId;

	private String hardwareVersion;

	private Long operatorId;

	private LocalDateTime createTime;

	private LocalDateTime updateTime;

	private String remarks;

	private String model;
}
