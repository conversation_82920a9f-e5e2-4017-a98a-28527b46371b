package com.weihengtech.pojo.dos.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("device_focus")
public class DeviceFocusDo {

	@TableId(type = IdType.AUTO)
	private Integer id;

	private String deviceFlag;

	private String deviceRemark;

	private Long userId;

	private Long createTime;

	private Long updateTime;

	// 0: 邮件 1: 短信 2: 短信+邮件
	private Integer notifyType;

	private String notifyEmail;

	private String notifyPhone;
}
