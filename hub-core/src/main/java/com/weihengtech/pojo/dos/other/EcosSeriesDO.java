package com.weihengtech.pojo.dos.other;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("ecos_series")
public class EcosSeriesDO {

	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	private String cnName;

	private String enName;

	private String seriesRoute;

	private LocalDateTime createTime;

	private Long createUser;
}
