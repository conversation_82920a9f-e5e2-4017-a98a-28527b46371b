package com.weihengtech.pojo.dos.other;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("dict")
public class DictDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String code;

    private String name;

}
