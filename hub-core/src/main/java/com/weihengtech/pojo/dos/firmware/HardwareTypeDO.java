package com.weihengtech.pojo.dos.firmware;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("hardware_type")
public class HardwareTypeDO {

	@TableId(type = IdType.INPUT)
	private Long id;

	private String typeName;

	private Long operatorId;

	private LocalDateTime createTime;

	private LocalDateTime updateTime;

	private Integer categoryId;

	private String categoryName;
}
