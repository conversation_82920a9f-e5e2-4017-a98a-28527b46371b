package com.weihengtech.pojo.dos.other;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("dict_item")
public class DictItemDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字典类型Id
     */
    private Long dictId;

    /**
     * 字典项编码
     */
    private String code;

    @TableField("zh_CN")
    private String zhCN;

    @TableField("en_US")
    private String enUS;

    @TableField("de_DE")
    private String deDE;

    @TableField("es_ES")
    private String esES;

    @TableField("fr_FR")
    private String frFR;

    @TableField("it_IT")
    private String itIT;

    @TableField("nl_NL")
    private String nlNL;

    @TableField("pl_PL")
    private String plPL;

    @TableField("pt_PT")
    private String ptPT;
}
