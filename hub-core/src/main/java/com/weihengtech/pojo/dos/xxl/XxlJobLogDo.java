package com.weihengtech.pojo.dos.xxl;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("xxl_job_log")
public class XxlJobLogDo {
	private Long id;
	private Integer jobGroup;
	private Integer jobId;
	private String executorAddress;
	private String executorHandler;
	private String executorParam;
	private String executorShardingParam;
	private Integer executorFailRetryCount;
	private LocalDateTime triggerTime;
	private Integer triggerCode;
	private String triggerMsg;
	private LocalDateTime handleTime;
	private Integer handleCode;
	private String handleMsg;
	private Integer alarmStatus;
}
