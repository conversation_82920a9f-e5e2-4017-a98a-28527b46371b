package com.weihengtech.pojo.dos.firmware;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("software_version")
public class SoftwareVersionDO {

	@TableId(type = IdType.INPUT)
	private Long id;

	private String softwareVersion;

	private Long operatorId;

	private LocalDateTime createTime;

	private LocalDateTime updateTime;

	private Long typeId;

	private String remarks;

	private String model;
}
