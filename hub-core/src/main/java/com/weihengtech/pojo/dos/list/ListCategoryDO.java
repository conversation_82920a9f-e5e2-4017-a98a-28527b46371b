package com.weihengtech.pojo.dos.list;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * @author: jiahao.jin
 * @create: 2025-07-21
 * @description: 名单分类实体类
 */
@Getter
@Setter
@ToString
@TableName("list_categories")
public class ListCategoryDO {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 分类编码 */
    private String categoryCode;

    /** 分类名称 */
    private String categoryName;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 更新时间 */
    private LocalDateTime updateTime;
}