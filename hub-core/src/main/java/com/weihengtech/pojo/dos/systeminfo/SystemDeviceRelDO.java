package com.weihengtech.pojo.dos.systeminfo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 系统关联设备
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("system_device_rel")
public class SystemDeviceRelDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 系统Id
     */
    private Long systemId;

    /**
     * 设备序列号
     */
    private Long deviceId;

    /**
     * 是否已检查预设配置
     */
    private Integer isCheck;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private String deviceSn;

    @TableField(exist = false)
    private Integer resourceSeries;
}
