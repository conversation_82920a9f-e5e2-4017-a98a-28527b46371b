package com.weihengtech.pojo.dos.other;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("operating_record")
public class OperatingRecordDo {

	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	private Long userId;

	private Long createTime;

	private String ipAddress;

	private String terminal;

	private String content;

	private Integer module;
}
