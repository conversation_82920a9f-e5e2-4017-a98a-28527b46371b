package com.weihengtech.pojo.dos.list;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * @author: jiahao.jin
 * @create: 2025-07-21
 * @description: 名单内容实体类
 */
@Getter
@Setter
@ToString
@TableName("list_items")
public class ListItemDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /** 名单类型(0:黑名单,1:白名单) */
    private Integer listType;

    /** 分类ID */
    private Integer categoryId;

    /** 名单内容 */
    private String content;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 更新时间 */
    private LocalDateTime updateTime;
}