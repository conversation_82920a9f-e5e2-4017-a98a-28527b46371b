package com.weihengtech.pojo.dos.ecos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.weihengtech.enums.other.ClientTypeEnum;
import lombok.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@With
@AllArgsConstructor
@NoArgsConstructor
@TableName("client_user")
public class ClientUserDo {

	@TableId(value = "id", type = IdType.INPUT)
	private Long id;

	private String username;

	private String password;

	private String email;

	private Boolean enabled;

	private Boolean accountNonExpired;

	private Boolean accountNonLocked;

	private Boolean credentialsNonExpired;

	private Long createTime;

	private Long updateTime;

	private Integer timeZoneId;

	private String timeZone;

	private String clientVersion;

	private ClientTypeEnum clientType;

	private Integer countryId;

	private String region;

	private String address;

	private String phone;

	private String contactName;

	private String zipCode;
}
