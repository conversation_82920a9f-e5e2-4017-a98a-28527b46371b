package com.weihengtech.pojo.dos.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("middle_agent_device")
public class MiddleAgentDeviceDo {

	@TableId(type = IdType.AUTO)
	private Integer id;
	private Long deviceId;
	private Long agentId;
	private Long createTime;
	private Long dealerId;
	private Long retailerId;
	private Long saleId;
	private Long installerId;
	private Date shareTime;

	/**
	 * 系统转移时间
	 */
	@TableField(exist = false)
	private Date systemTransTime;

	/**
	 * 安装商展示的设备：转移时间尚未过期
	 *
	 * @param validTime 过期时间
	 * @return
	 */
	public boolean isValidTransTime(long validTime) {
		if (this.getSystemTransTime() == null) {
			return false;
		}
		long expiredTime = System.currentTimeMillis() - this.getSystemTransTime().getTime();
		return expiredTime < validTime;
	}

	/**
	 * 安装商展示的设备：分享时间尚未过期
	 *
	 * @param validTime 过期时间
	 * @return
	 */
	public boolean isValidShareTime(long validTime) {
		if (this.getShareTime() == null) {
			return false;
		}
		long expiredTime = System.currentTimeMillis() - this.getShareTime().getTime();
		return expiredTime < validTime;
	}

	public boolean isExpiredTransTime(long validTime) {
		return this.getSystemTransTime() != null &&
				System.currentTimeMillis() - this.getSystemTransTime().getTime() < validTime;
	}

	public boolean isExpiredShareTime(long validTime) {
		return this.getShareTime() != null &&
				System.currentTimeMillis() - this.getShareTime().getTime() < validTime;
	}
}
