package com.weihengtech.pojo.dos.firmware;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("firmware_upgrade_record")
public class FirmwareUpgradeRecordDO {

	@TableId(type = IdType.INPUT)
	private Long id;

	private Long deviceId;

	private String deviceName;

	private Long firmwareId;

	private String uploadUrl;

	private String addressMap;

	private Integer status;

	private LocalDateTime createTime;

	private LocalDateTime updateTime;

	private String seriesTable;

	private String sourceVersion;

	private String finishVersion;

	private Long size;
}
