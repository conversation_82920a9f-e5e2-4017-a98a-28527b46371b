package com.weihengtech.pojo.dos.other;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("time_zone")
@AllArgsConstructor
@NoArgsConstructor
@With
public class TimezoneDo {

	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	private String timezone;

	private String name;


	public TimezoneDo(String timezone, String name) {
		this.timezone = timezone;
		this.name = name;
	}
}
