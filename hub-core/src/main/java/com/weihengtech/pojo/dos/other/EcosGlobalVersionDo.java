package com.weihengtech.pojo.dos.other;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName(value = "ecos_global_version")
public class EcosGlobalVersionDo {

	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	private String androidVersion;

	private String iosVersion;

	private Long createTime;

	private Integer flag;

	private String zhCn;

	private String enUs;

}
