package com.weihengtech.pojo.dos.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 设备关联关系
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("device_relation")
public class DeviceRelationDO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 主机id
     */
    private Long masterId;

    /**
     * 从机id
     */
    private Long slaveId;

    /**
     * 创建时间
     */
    private Date crtTime;

    /**
     * 创建人
     */
    private String crtBy;

    @TableField(exist = false)
    private String slaveName;

    @TableField(exist = false)
    private String masterName;


}
