package com.weihengtech.pojo.dos.systeminfo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 系统信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("system_info")
public class SystemInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 系统名称
     */
    private String name;

    /**
     * 系统地址
     */
    private Integer datacenterId;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 用户名
     */
    private String ownerFirstName;

    /**
     * 用户姓
     */
    private String ownerLastName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机
     */
    private String phone;

    /**
     * 安装日期
     */
    private Date installDate;

    /**
     * 安装账号
     */
    private Long installerId;

    /**
     * 转移时间
     */
    private Date transferTime;

    /**
     * 当前进度
     */
    private Integer currentStep;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 安装商账号
     */
    @TableField(exist = false)
    private String installerAccount;

    /**
     * 关联设备列表
     */
    @TableField(exist = false)
    private List<SystemDeviceRelDO> deviceList;

    /**
     * 设备Sn列表
     */
    @TableField(exist = false)
    private List<String> deviceSnList;

    /**
     * 已确认的设备Sn列表
     */
    @TableField(exist = false)
    private List<String> confirmedDeviceSnList;

    /**
     * 储能机设备sn列表
     */
    @TableField(exist = false)
    private List<String> energyStorageSnList;

}
