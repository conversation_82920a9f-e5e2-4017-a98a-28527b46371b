package com.weihengtech.pojo.dos.charger;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 充电桩信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("charger_info")
public class ChargerInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Long deviceId;

    private Integer mode;

    // 可扩展字段
    private Double maxPower;

    private String cpFirmwareVersion;

    private String cpPlugAndChargeMsg;

    private String cpMode;

    private String cpMeterType;

    private String cpMeterRatio;

    private Double cpHomeCurrent;

    private Double cpPvCurrent;
}
