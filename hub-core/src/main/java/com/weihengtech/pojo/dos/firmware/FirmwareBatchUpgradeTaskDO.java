package com.weihengtech.pojo.dos.firmware;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * @author: jiahao.jin
 * @create: 2025-07-14 16:05
 * @description: 固件批量升级任务实体类
 */
@Getter
@Setter
@ToString
@TableName("firmware_batch_upgrade_task")
public class FirmwareBatchUpgradeTaskDO{

    @TableId(type = IdType.INPUT)
    private Long id;

    /** 任务名称 */
    private String taskName;

    /** 固件包ID */
    private Long firmwareId;

    /** 设备系列编号 */
    private String resourceSeriesCode;

    /** 设备类型 */
    private String resourceType;

    /** 固件类型 */
    private String firmwareType;

    /** 固件包名称 */
    private String firmwareName;

    /** 升级方式：立即升级/定时升级 */
    private String upgradeMode;

    /** 升级开始时间 */
    private Long upgradeStartTime;

    /** 任务状态：待执行/执行中/已结束/已终止 */
    private String status;

    /** 创建人邮箱 */
    private String creatorEmail;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 更新时间 */
    private LocalDateTime updateTime;

    /** 备注 */
    private String remarks;

    /** 目标设备数量 */
    private Integer targetDeviceCount;

    /** 升级中设备数量 */
    private Integer upgradingCount;

    /** 成功升级数量 */
    private Integer successCount;

    /** 失败升级数量 */
    private Integer failureCount;

    /** 进度百分比 */
    private Integer progress;
}
