package com.weihengtech.pojo.dos.other;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * tuya登录日志
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
@Data
@TableName("tuya_login_log")
public class TuyaLoginLogDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 账号
     */
    private String account;

    /**
     * 登录结果
     */
    private String loginRes;

    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 失败结果
     */
    private String failReason;


}
