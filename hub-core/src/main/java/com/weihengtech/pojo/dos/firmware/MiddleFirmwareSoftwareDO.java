package com.weihengtech.pojo.dos.firmware;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("middle_firmware_software")
public class MiddleFirmwareSoftwareDO implements Serializable {

	private static final long serialVersionUID = 7374832367153784816L;

	@TableId(type = IdType.INPUT)
	private Long id;

	private Long firmwareId;

	private Long softwareId;

	private Long operatorId;

	private LocalDateTime createTime;

	private LocalDateTime updateTime;
}
