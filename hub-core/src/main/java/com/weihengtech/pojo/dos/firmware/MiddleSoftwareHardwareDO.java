package com.weihengtech.pojo.dos.firmware;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("middle_software_hardware")
public class MiddleSoftwareHardwareDO {

	@TableId(type = IdType.INPUT)
	private Long id;

	private Long softwareId;

	private Long hardwareId;

	private Long operatorId;

	private LocalDateTime createTime;

	private LocalDateTime updateTime;

}
