package com.weihengtech.pojo.dos.firmware;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("firmware_upload")
public class FirmwareUploadDO implements Serializable {

	private static final long serialVersionUID = -7689579625527976779L;

	@TableId(type = IdType.INPUT)
	private Long id;

	private String firmwareName;

	private String downloadUrl;

	private Long operatorId;

	private LocalDateTime createTime;

	private LocalDateTime updateTime;

	private String type;

	private Long size;

	private String remarks;

	/** 适用机型 */
	private String model;

	/** 是否置顶 */
	private int isTop;
}
