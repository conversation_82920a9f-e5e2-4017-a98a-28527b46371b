package com.weihengtech.pojo.dos.ecos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("client_global_version")
public class ClientGlobalVersionDo {

	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	private String androidVersion;

	private String iosVersion;

	private String chinese;

	private String english;

	private Boolean isImportant;

	private Long createTime;

	private Integer flag;

	private String zhCn;

	private String enUs;

}
