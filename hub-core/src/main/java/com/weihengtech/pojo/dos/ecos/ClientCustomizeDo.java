package com.weihengtech.pojo.dos.ecos;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.weihengtech.service.specific.SpecificServService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_customize")
public class ClientCustomizeDo {

	@TableId(type = IdType.INPUT)
	private Long id;

	private Long deviceId;

	private Integer chargeMode;

	private Integer batteryMin;

	private Integer maxFeedIn;

	private Integer epsBatteryMin;

	private Integer dischargeToGridFlag;

	// 1充1放
	private Integer chargeStartHour1;
	private Integer chargeStartMinute1;
	private Integer chargeEndHour1;
	private Integer chargeEndMinute1;
	private Integer chargePower1;
	private Integer dischargeStartHour1;
	private Integer dischargeStartMinute1;
	private Integer dischargeEndHour1;
	private Integer dischargeEndMinute1;
	private Integer dischargePower1;

	// 2充2放
	private Integer chargeStartHour2;
	private Integer chargeStartMinute2;
	private Integer chargeEndHour2;
	private Integer chargeEndMinute2;
	private Integer chargePower2;
	private Integer dischargeStartHour2;
	private Integer dischargeStartMinute2;
	private Integer dischargeEndHour2;
	private Integer dischargeEndMinute2;
	private Integer dischargePower2;

	// 3充3放
	private Integer chargeStartHour3;
	private Integer chargeStartMinute3;
	private Integer chargeEndHour3;
	private Integer chargeEndMinute3;
	private Integer chargePower3;
	private Integer dischargeStartHour3;
	private Integer dischargeStartMinute3;
	private Integer dischargeEndHour3;
	private Integer dischargeEndMinute3;
	private Integer dischargePower3;

	// 4充4放
	private Integer chargeStartHour4;
	private Integer chargeStartMinute4;
	private Integer chargeEndHour4;
	private Integer chargeEndMinute4;
	private Integer chargePower4;
	private Integer dischargeStartHour4;
	private Integer dischargeStartMinute4;
	private Integer dischargeEndHour4;
	private Integer dischargeEndMinute4;
	private Integer dischargePower4;

	// 5充5放
	private Integer chargeStartHour5;
	private Integer chargeStartMinute5;
	private Integer chargeEndHour5;
	private Integer chargeEndMinute5;
	private Integer chargePower5;
	private Integer dischargeStartHour5;
	private Integer dischargeStartMinute5;
	private Integer dischargeEndHour5;
	private Integer dischargeEndMinute5;
	private Integer dischargePower5;

	// 6充6放
	private Integer chargeStartHour6;
	private Integer chargeStartMinute6;
	private Integer chargeEndHour6;
	private Integer chargeEndMinute6;
	private Integer chargePower6;
	private Integer dischargeStartHour6;
	private Integer dischargeStartMinute6;
	private Integer dischargeEndHour6;
	private Integer dischargeEndMinute6;
	private Integer dischargePower6;

	// 7充7放
	private Integer chargeStartHour7;
	private Integer chargeStartMinute7;
	private Integer chargeEndHour7;
	private Integer chargeEndMinute7;
	private Integer chargePower7;
	private Integer dischargeStartHour7;
	private Integer dischargeStartMinute7;
	private Integer dischargeEndHour7;
	private Integer dischargeEndMinute7;
	private Integer dischargePower7;

	// 8充8放
	private Integer chargeStartHour8;
	private Integer chargeStartMinute8;
	private Integer chargeEndHour8;
	private Integer chargeEndMinute8;
	private Integer chargePower8;
	private Integer dischargeStartHour8;
	private Integer dischargeStartMinute8;
	private Integer dischargeEndHour8;
	private Integer dischargeEndMinute8;
	private Integer dischargePower8;

	// 9充9放
	private Integer chargeStartHour9;
	private Integer chargeStartMinute9;
	private Integer chargeEndHour9;
	private Integer chargeEndMinute9;
	private Integer chargePower9;
	private Integer dischargeStartHour9;
	private Integer dischargeStartMinute9;
	private Integer dischargeEndHour9;
	private Integer dischargeEndMinute9;
	private Integer dischargePower9;

	// 10充10放
	private Integer chargeStartHour10;
	private Integer chargeStartMinute10;
	private Integer chargeEndHour10;
	private Integer chargeEndMinute10;
	private Integer chargePower10;
	private Integer dischargeStartHour10;
	private Integer dischargeStartMinute10;
	private Integer dischargeEndHour10;
	private Integer dischargeEndMinute10;
	private Integer dischargePower10;

	// 11充11放
	private Integer chargeStartHour11;
	private Integer chargeStartMinute11;
	private Integer chargeEndHour11;
	private Integer chargeEndMinute11;
	private Integer chargePower11;
	private Integer dischargeStartHour11;
	private Integer dischargeStartMinute11;
	private Integer dischargeEndHour11;
	private Integer dischargeEndMinute11;
	private Integer dischargePower11;

	// 12充12放
	private Integer chargeStartHour12;
	private Integer chargeStartMinute12;
	private Integer chargeEndHour12;
	private Integer chargeEndMinute12;
	private Integer chargePower12;
	private Integer dischargeStartHour12;
	private Integer dischargeStartMinute12;
	private Integer dischargeEndHour12;
	private Integer dischargeEndMinute12;
	private Integer dischargePower12;

	// 弃光
	private Integer chargeAbandonPv1;
	private Integer chargeAbandonPv2;
	private Integer chargeAbandonPv3;
	private Integer chargeAbandonPv4;
	private Integer chargeAbandonPv5;
	private Integer chargeAbandonPv6;
	private Integer chargeAbandonPv7;
	private Integer chargeAbandonPv8;
	private Integer chargeAbandonPv9;
	private Integer chargeAbandonPv10;
	private Integer chargeAbandonPv11;
	private Integer chargeAbandonPv12;
	private Integer dischargeAbandonPv1;
	private Integer dischargeAbandonPv2;
	private Integer dischargeAbandonPv3;
	private Integer dischargeAbandonPv4;
	private Integer dischargeAbandonPv5;
	private Integer dischargeAbandonPv6;
	private Integer dischargeAbandonPv7;
	private Integer dischargeAbandonPv8;
	private Integer dischargeAbandonPv9;
	private Integer dischargeAbandonPv10;
	private Integer dischargeAbandonPv11;
	private Integer dischargeAbandonPv12;

	public void buildVal(String wifiSn, SpecificServService specificServService,
						 String name, Integer val, List<Integer> oldParams, List<Integer> multiChargeParams,
						 List<Integer> abandonPvParams) {
		switch (name) {
			case "minBatteryCapacity":
				oldParams.set(0, val);
				this.setBatteryMin(val);
				break;
			case "chargeModeCode":
				oldParams.set(1, val);
				this.setChargeMode(val);
				break;
			case "maxFeedIn":
				specificServService.sendWriteCommand(wifiSn, 1, 41037, 1, ListUtil.toList(val));
				this.setMaxFeedIn(val);
				break;
			case "dischargeToGridFlag":
				specificServService.sendWriteCommand(wifiSn, 1, 40046, 1, ListUtil.toList(val));
				this.setDischargeToGridFlag(val);
				break;
			case "epsMinBatteryCapacity":
				specificServService.sendWriteCommand(wifiSn, 1, 41042, 1, ListUtil.toList(val));
				this.setEpsBatteryMin(val);
				break;
			// 2充2放
			case "chargeBeginHour1":
				oldParams.set(6, val);
				this.setChargeStartHour1(val);
				break;
			case "chargeBeginMinute1":
				oldParams.set(7, val);
				this.setChargeStartMinute1(val);
				break;
			case "chargeEndHour1":
				oldParams.set(8, val);
				this.setChargeEndHour1(val);
				break;
			case "chargeEndMinute1":
				oldParams.set(9, val);
				this.setChargeEndMinute1(val);
				break;
			case "chargePower1":
				oldParams.set(10, val);
				this.setChargePower1(val);
				break;
			case "disChargeBeginHour1":
				oldParams.set(11, val);
				this.setDischargeStartHour1(val);
				break;
			case "disChargeBeginMinute1":
				oldParams.set(12, val);
				this.setDischargeStartMinute1(val);
				break;
			case "disChargeEndHour1":
				oldParams.set(13, val);
				this.setDischargeEndHour1(val);
				break;
			case "disChargeEndMinute1":
				oldParams.set(14, val);
				this.setDischargeEndMinute1(val);
				break;
			case "disChargePower1":
				oldParams.set(15, val);
				this.setDischargePower1(val);
				break;
			case "chargeBeginHour2":
				oldParams.set(16, val);
				this.setChargeStartHour2(val);
				break;
			case "chargeBeginMinute2":
				oldParams.set(17, val);
				this.setChargeStartMinute2(val);
				break;
			case "chargeEndHour2":
				oldParams.set(18, val);
				this.setChargeEndHour2(val);
				break;
			case "chargeEndMinute2":
				oldParams.set(19, val);
				this.setChargeEndMinute2(val);
				break;
			case "chargePower2":
				oldParams.set(20, val);
				this.setChargePower2(val);
				break;
			case "disChargeBeginHour2":
				oldParams.set(21, val);
				this.setDischargeStartHour2(val);
				break;
			case "disChargeBeginMinute2":
				oldParams.set(22, val);
				this.setDischargeStartMinute2(val);
				break;
			case "disChargeEndHour2":
				oldParams.set(23, val);
				this.setDischargeEndHour2(val);
				break;
			case "disChargeEndMinute2":
				oldParams.set(24, val);
				this.setDischargeEndMinute2(val);
				break;
			case "disChargePower2":
				oldParams.set(25, val);
				this.setDischargePower2(val);
				break;
			case "chargeBeginHour3":
				multiChargeParams.set(0, val);
				this.setChargeStartHour3(val);
				break;
			case "chargeBeginMinute3":
				multiChargeParams.set(1, val);
				this.setChargeStartMinute3(val);
				break;
			case "chargeEndHour3":
				multiChargeParams.set(2, val);
				this.setChargeEndHour3(val);
				break;
			case "chargeEndMinute3":
				multiChargeParams.set(3, val);
				this.setChargeEndMinute3(val);
				break;
			case "chargePower3":
				multiChargeParams.set(4, val);
				break;
			case "disChargeBeginHour3":
				multiChargeParams.set(5, val);
				this.setDischargeStartHour3(val);
				break;
			case "disChargeBeginMinute3":
				multiChargeParams.set(6, val);
				this.setDischargeStartMinute3(val);
				break;
			case "disChargeEndHour3":
				multiChargeParams.set(7, val);
				this.setDischargeEndHour3(val);
				break;
			case "disChargeEndMinute3":
				multiChargeParams.set(8, val);
				this.setDischargeEndMinute3(val);
				break;
			case "disChargePower3":
				multiChargeParams.set(9, val);
				this.setDischargePower3(val);
				break;
			case "chargeBeginHour4":
				multiChargeParams.set(10, val);
				this.setChargeStartHour4(val);
				break;
			case "chargeBeginMinute4":
				multiChargeParams.set(11, val);
				this.setChargeStartMinute4(val);
				break;
			case "chargeEndHour4":
				multiChargeParams.set(12, val);
				this.setChargeEndHour4(val);
				break;
			case "chargeEndMinute4":
				multiChargeParams.set(13, val);
				this.setChargeEndMinute4(val);
				break;
			case "chargePower4":
				multiChargeParams.set(14, val);
				this.setChargePower4(val);
				break;
			case "disChargeBeginHour4":
				multiChargeParams.set(15, val);
				this.setDischargeStartHour4(val);
				break;
			case "disChargeBeginMinute4":
				multiChargeParams.set(16, val);
				this.setDischargeStartMinute4(val);
				break;
			case "disChargeEndHour4":
				multiChargeParams.set(17, val);
				this.setDischargeEndHour4(val);
				break;
			case "disChargeEndMinute4":
				multiChargeParams.set(18, val);
				this.setDischargeEndMinute4(val);
				break;
			case "disChargePower4":
				multiChargeParams.set(19, val);
				this.setDischargePower4(val);
				break;
			case "chargeBeginHour5":
				multiChargeParams.set(20, val);
				this.setChargeStartHour5(val);
				break;
			case "chargeBeginMinute5":
				multiChargeParams.set(21, val);
				this.setChargeStartMinute5(val);
				break;
			case "chargeEndHour5":
				multiChargeParams.set(22, val);
				this.setChargeEndHour5(val);
				break;
			case "chargeEndMinute5":
				multiChargeParams.set(23, val);
				this.setChargeEndMinute5(val);
				break;
			case "chargePower5":
				multiChargeParams.set(24, val);
				this.setChargePower5(val);
				break;
			case "disChargeBeginHour5":
				multiChargeParams.set(25, val);
				this.setDischargeStartHour5(val);
				break;
			case "disChargeBeginMinute5":
				multiChargeParams.set(26, val);
				this.setDischargeStartMinute5(val);
				break;
			case "disChargeEndHour5":
				multiChargeParams.set(27, val);
				this.setDischargeEndHour5(val);
				break;
			case "disChargeEndMinute5":
				multiChargeParams.set(28, val);
				this.setDischargeEndMinute5(val);
				break;
			case "disChargePower5":
				multiChargeParams.set(29, val);
				this.setDischargePower5(val);
				break;
			case "chargeBeginHour6":
				multiChargeParams.set(30, val);
				this.setChargeStartHour6(val);
				break;
			case "chargeBeginMinute6":
				multiChargeParams.set(31, val);
				this.setChargeStartMinute6(val);
				break;
			case "chargeEndHour6":
				multiChargeParams.set(32, val);
				this.setChargeEndHour6(val);
				break;
			case "chargeEndMinute6":
				multiChargeParams.set(33, val);
				this.setChargeEndMinute6(val);
				break;
			case "chargePower6":
				multiChargeParams.set(34, val);
				this.setChargePower6(val);
				break;
			case "disChargeBeginHour6":
				multiChargeParams.set(35, val);
				this.setDischargeStartHour6(val);
				break;
			case "disChargeBeginMinute6":
				multiChargeParams.set(36, val);
				this.setDischargeStartMinute6(val);
				break;
			case "disChargeEndHour6":
				multiChargeParams.set(37, val);
				this.setDischargeEndHour6(val);
				break;
			case "disChargeEndMinute6":
				multiChargeParams.set(38, val);
				this.setDischargeEndMinute6(val);
				break;
			case "disChargePower6":
				multiChargeParams.set(39, val);
				this.setDischargePower6(val);
				break;
			case "chargeBeginHour7":
				multiChargeParams.set(40, val);
				this.setChargeStartHour7(val);
				break;
			case "chargeBeginMinute7":
				multiChargeParams.set(41, val);
				this.setChargeStartMinute7(val);
				break;
			case "chargeEndHour7":
				multiChargeParams.set(42, val);
				this.setChargeEndHour7(val);
				break;
			case "chargeEndMinute7":
				multiChargeParams.set(43, val);
				this.setChargeEndMinute7(val);
				break;
			case "chargePower7":
				multiChargeParams.set(44, val);
				this.setChargePower7(val);
				break;
			case "disChargeBeginHour7":
				multiChargeParams.set(45, val);
				this.setDischargeStartHour7(val);
				break;
			case "disChargeBeginMinute7":
				multiChargeParams.set(46, val);
				this.setDischargeStartMinute7(val);
				break;
			case "disChargeEndHour7":
				multiChargeParams.set(47, val);
				this.setDischargeEndHour7(val);
				break;
			case "disChargeEndMinute7":
				multiChargeParams.set(48, val);
				this.setDischargeEndMinute7(val);
				break;
			case "disChargePower7":
				multiChargeParams.set(49, val);
				this.setDischargePower7(val);
				break;
			case "chargeBeginHour8":
				multiChargeParams.set(50, val);
				this.setChargeStartHour8(val);
				break;
			case "chargeBeginMinute8":
				multiChargeParams.set(51, val);
				this.setChargeStartMinute8(val);
				break;
			case "chargeEndHour8":
				multiChargeParams.set(52, val);
				this.setChargeEndHour8(val);
				break;
			case "chargeEndMinute8":
				multiChargeParams.set(53, val);
				this.setChargeEndMinute8(val);
				break;
			case "chargePower8":
				multiChargeParams.set(54, val);
				this.setChargePower8(val);
				break;
			case "disChargeBeginHour8":
				multiChargeParams.set(55, val);
				this.setDischargeStartHour8(val);
				break;
			case "disChargeBeginMinute8":
				multiChargeParams.set(56, val);
				this.setDischargeStartMinute8(val);
				break;
			case "disChargeEndHour8":
				multiChargeParams.set(57, val);
				this.setDischargeEndHour8(val);
				break;
			case "disChargeEndMinute8":
				multiChargeParams.set(58, val);
				this.setDischargeEndMinute8(val);
				break;
			case "disChargePower8":
				multiChargeParams.set(59, val);
				this.setDischargePower8(val);
				break;
			case "chargeBeginHour9":
				multiChargeParams.set(60, val);
				this.setChargeStartHour9(val);
				break;
			case "chargeBeginMinute9":
				multiChargeParams.set(61, val);
				this.setChargeStartMinute9(val);
				break;
			case "chargeEndHour9":
				multiChargeParams.set(62, val);
				this.setChargeEndHour9(val);
				break;
			case "chargeEndMinute9":
				multiChargeParams.set(63, val);
				this.setChargeEndMinute9(val);
				break;
			case "chargePower9":
				multiChargeParams.set(64, val);
				this.setChargePower9(val);
				break;
			case "disChargeBeginHour9":
				multiChargeParams.set(65, val);
				this.setDischargeStartHour9(val);
				break;
			case "disChargeBeginMinute9":
				multiChargeParams.set(66, val);
				this.setDischargeStartMinute9(val);
				break;
			case "disChargeEndHour9":
				multiChargeParams.set(67, val);
				this.setDischargeEndHour9(val);
				break;
			case "disChargeEndMinute9":
				multiChargeParams.set(68, val);
				this.setDischargeEndMinute9(val);
				break;
			case "disChargePower9":
				multiChargeParams.set(69, val);
				this.setDischargePower9(val);
				break;
			case "chargeBeginHour10":
				multiChargeParams.set(70, val);
				this.setChargeStartHour10(val);
				break;
			case "chargeBeginMinute10":
				multiChargeParams.set(71, val);
				this.setChargeStartMinute10(val);
				break;
			case "chargeEndHour10":
				multiChargeParams.set(72, val);
				this.setChargeEndHour10(val);
				break;
			case "chargeEndMinute10":
				multiChargeParams.set(73, val);
				this.setChargeEndMinute10(val);
				break;
			case "chargePower10":
				multiChargeParams.set(74, val);
				this.setChargePower10(val);
				break;
			case "disChargeBeginHour10":
				multiChargeParams.set(75, val);
				this.setDischargeStartHour10(val);
				break;
			case "disChargeBeginMinute10":
				multiChargeParams.set(76, val);
				this.setDischargeStartMinute10(val);
				break;
			case "disChargeEndHour10":
				multiChargeParams.set(77, val);
				this.setDischargeEndHour10(val);
				break;
			case "disChargeEndMinute10":
				multiChargeParams.set(78, val);
				this.setDischargeEndMinute10(val);
				break;
			case "disChargePower10":
				multiChargeParams.set(79, val);
				this.setDischargePower10(val);
				break;
			case "chargeBeginHour11":
				multiChargeParams.set(80, val);
				this.setChargeStartHour11(val);
				break;
			case "chargeBeginMinute11":
				multiChargeParams.set(81, val);
				this.setChargeStartMinute11(val);
				break;
			case "chargeEndHour11":
				multiChargeParams.set(82, val);
				this.setChargeEndHour11(val);
				break;
			case "chargeEndMinute11":
				multiChargeParams.set(83, val);
				this.setChargeEndMinute11(val);
				break;
			case "chargePower11":
				multiChargeParams.set(84, val);
				this.setChargePower11(val);
				break;
			case "disChargeBeginHour11":
				multiChargeParams.set(85, val);
				this.setDischargeStartHour11(val);
				break;
			case "disChargeBeginMinute11":
				multiChargeParams.set(86, val);
				this.setDischargeStartMinute11(val);
				break;
			case "disChargeEndHour11":
				multiChargeParams.set(87, val);
				this.setDischargeEndHour11(val);
				break;
			case "disChargeEndMinute11":
				multiChargeParams.set(88, val);
				this.setDischargeEndMinute11(val);
				break;
			case "disChargePower11":
				multiChargeParams.set(89, val);
				this.setDischargePower11(val);
				break;
			case "chargeBeginHour12":
				multiChargeParams.set(90, val);
				this.setChargeStartHour12(val);
				break;
			case "chargeBeginMinute12":
				multiChargeParams.set(91, val);
				this.setChargeStartMinute12(val);
				break;
			case "chargeEndHour12":
				multiChargeParams.set(92, val);
				this.setChargeEndHour12(val);
				break;
			case "chargeEndMinute12":
				multiChargeParams.set(93, val);
				this.setChargeEndMinute12(val);
				break;
			case "chargePower12":
				multiChargeParams.set(94, val);
				this.setChargePower12(val);
				break;
			case "disChargeBeginHour12":
				multiChargeParams.set(95, val);
				this.setDischargeStartHour12(val);
				break;
			case "disChargeBeginMinute12":
				multiChargeParams.set(96, val);
				this.setDischargeStartMinute12(val);
				break;
			case "disChargeEndHour12":
				multiChargeParams.set(97, val);
				this.setDischargeEndHour12(val);
				break;
			case "disChargeEndMinute12":
				multiChargeParams.set(98, val);
				this.setDischargeEndMinute12(val);
				break;
			case "disChargePower12":
				multiChargeParams.set(99, val);
				this.setDischargePower12(val);
				break;
			case "chargeAbandonPv1":
				abandonPvParams.set(0, val);
				this.setChargeAbandonPv1(val);
				break;
			case "disChargeAbandonPv1":
				abandonPvParams.set(1, val);
				this.setDischargeAbandonPv1(val);
				break;
			case "chargeAbandonPv2":
				abandonPvParams.set(2, val);
				this.setChargeAbandonPv2(val);
				break;
			case "disChargeAbandonPv2":
				abandonPvParams.set(3, val);
				this.setDischargeAbandonPv2(val);
				break;
			case "chargeAbandonPv3":
				abandonPvParams.set(4, val);
				this.setChargeAbandonPv3(val);
				break;
			case "disChargeAbandonPv3":
				abandonPvParams.set(5, val);
				this.setDischargeAbandonPv3(val);
				break;
			case "chargeAbandonPv4":
				abandonPvParams.set(6, val);
				this.setChargeAbandonPv4(val);
				break;
			case "disChargeAbandonPv4":
				abandonPvParams.set(7, val);
				this.setDischargeAbandonPv4(val);
				break;
			case "chargeAbandonPv5":
				abandonPvParams.set(8, val);
				this.setChargeAbandonPv5(val);
				break;
			case "disChargeAbandonPv5":
				abandonPvParams.set(9, val);
				this.setDischargeAbandonPv5(val);
				break;
			case "chargeAbandonPv6":
				abandonPvParams.set(10, val);
				this.setChargeAbandonPv6(val);
				break;
			case "disChargeAbandonPv6":
				abandonPvParams.set(11, val);
				this.setDischargeAbandonPv6(val);
				break;
			case "chargeAbandonPv7":
				abandonPvParams.set(12, val);
				this.setChargeAbandonPv7(val);
				break;
			case "disChargeAbandonPv7":
				abandonPvParams.set(13, val);
				this.setDischargeAbandonPv7(val);
				break;
			case "chargeAbandonPv8":
				abandonPvParams.set(14, val);
				this.setChargeAbandonPv8(val);
				break;
			case "disChargeAbandonPv8":
				abandonPvParams.set(15, val);
				this.setDischargeAbandonPv8(val);
				break;
			case "chargeAbandonPv9":
				abandonPvParams.set(16, val);
				this.setChargeAbandonPv9(val);
				break;
			case "disChargeAbandonPv9":
				abandonPvParams.set(17, val);
				this.setDischargeAbandonPv9(val);
				break;
			case "chargeAbandonPv10":
				abandonPvParams.set(18, val);
				this.setChargeAbandonPv10(val);
				break;
			case "disChargeAbandonPv10":
				abandonPvParams.set(19, val);
				this.setDischargeAbandonPv10(val);
				break;
			case "chargeAbandonPv11":
				abandonPvParams.set(20, val);
				this.setChargeAbandonPv11(val);
				break;
			case "disChargeAbandonPv11":
				abandonPvParams.set(21, val);
				this.setDischargeAbandonPv11(val);
				break;
			case "chargeAbandonPv12":
				abandonPvParams.set(22, val);
				this.setChargeAbandonPv12(val);
				break;
			case "disChargeAbandonPv12":
				abandonPvParams.set(23, val);
				this.setDischargeAbandonPv12(val);
				break;
		}
	}

}
