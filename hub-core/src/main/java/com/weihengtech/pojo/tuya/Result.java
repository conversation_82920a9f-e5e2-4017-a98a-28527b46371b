package com.weihengtech.pojo.tuya;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class Result<T> implements Serializable {

	private static final long serialVersionUID = 1076888268359702742L;

	Integer code;

	String msg;

	Boolean success;

	Long t;

	T result;
}
