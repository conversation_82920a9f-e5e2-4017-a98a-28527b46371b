package com.weihengtech.pojo.dtos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "重点关注设备回参")
public class FocusDeviceDto {

	@ApiModelProperty(name = "deviceFlag", value = "设备标识", required = true)
	private String deviceFlag;

	@ApiModelProperty(name = "deviceRemark", value = "设备备注")
	private String deviceRemark;

	@ApiModelProperty(name = "notifyType", value = "通知类型 0: 邮件;  1: 短信;  2: 邮件+短信", required = true)
	private Integer notifyType;

	@ApiModelProperty(name = "notifyEmail", value = "通知邮件")
	private String notifyEmail;

	@ApiModelProperty(name = "notifyPhone", value = "通知电话")
	private String notifyPhone;
}
