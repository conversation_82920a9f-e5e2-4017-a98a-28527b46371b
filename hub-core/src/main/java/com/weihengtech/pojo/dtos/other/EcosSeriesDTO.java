package com.weihengtech.pojo.dtos.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "系列回参")
public class EcosSeriesDTO {

	@ApiModelProperty(name = "id", value = "系列id")
	private Integer id;

	@ApiModelProperty(name = "cnName", value = "中文名")
	private String cnName;

	@ApiModelProperty(name = "enName", value = "英文名")
	private String enName;

	@ApiModelProperty(name = "seriesRoute", value = "系列路由")
	private String seriesRoute;
}
