package com.weihengtech.pojo.dtos.device;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "deviceList参数")
public class DeviceListPageDTO implements Serializable {

	private static final long serialVersionUID = 334329948362522751L;


	private String deviceName;

	private String deviceSn;

	private String deviceModel;

	private String brand;

	private String factory;

	private String powerBoardHardwareVersion;

	private String dsp1SoftwareVersion;

	private String dsp2SoftwareVersion;

	private String dsp1SubVersion;

	private String dsp2SubVersion;

	private String arcDspSoftwareVersion;
	private String arcDspSubVersion;
	private String arcDspBootLoaderSoftwareVersion;

	private String emsSubVersion;

	private String emsSoftwareVersion;

	private String emsHardwareVersion;

	private String wifiSn;

	private String wifiHardwareVersion;

	private String wifiSoftwareVersion;

	private String bmsGaugeVersion;

	private String bmsSn;

	private String bmsVendor;

	private String bmsSoftwareVersion;

	private String bmsHardwareVersion;

	private Integer state;

	private String alias;

	private String agentName;
	private String agentEmail;
	private String dealerName;
	private String dealerEmail;
	private String retailerName;
	private String retailerEmail;
	private String saleName;
	private String saleEmail;
	private String installerName;
	private String installerEmail;
	private String remark;
	private String desc;

	private String id;

	private String country = "";
	
	private String area;

	private String clientType;

	private String clientVersion;

	private String clientEmail;

	private Long setupTime;

	private LocalDateTime updTime;

	private String updateTime;

	private Long countDownTime;

	private String installCountry;
	/** 品类 */
	private String category;
	/** 型号 */
	private String model;
	/** 资源类型（账号系统） */
	private String resourceType;
	/** 资源系列（账号系统） */
	private String resourceSeries;
	/** 资源系列编号（账号系统） */
	private String resourceSeriesCode;
	/** 是否需要倒计时 */
	private Boolean isNeedCountdown = Boolean.FALSE;
	/** 数据来源 */
	private String dataSource;
	/** 并机数量 */
	private Integer parallelNumber;
	/** 安规 */
	private Integer safetyStandard;
	/** 额定功率 */
	private String ratedPower;
	/** 电池电量 */
	private Double batSoc;
	/** 详细地址 */
	private String address;

	public void buildDefaultVersion() {
		setDsp1SoftwareVersion("V1.00");
		setDsp1SubVersion("V1.00");
		setDsp2SoftwareVersion("V1.00");
		setDsp2SubVersion("V1.00");
		setBmsSoftwareVersion("V1.00");
		setBmsHardwareVersion("V1.00");
		setEmsSoftwareVersion("V1.00");
		setEmsHardwareVersion("V1.00");
		setEmsSubVersion("V1.00");
		setWifiSoftwareVersion("V1.00");
		setWifiHardwareVersion("V1.00");
		setPowerBoardHardwareVersion("V1.00");
	}
}
