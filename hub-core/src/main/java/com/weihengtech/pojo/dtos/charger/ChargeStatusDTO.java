package com.weihengtech.pojo.dtos.charger;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 充电桩配网返参
 *
 * <AUTHOR>
 * @date 2024/1/25 15:17
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "充电桩充电状态回调参数")
public class ChargeStatusDTO {

	/**
	 * 充电桩ID
	 */
	private String clientId;

	/**
	 * 电表读数，单位：Wh（结束-开始=一次充电电量）
	 */
	private Integer meter;

	/**
	 * 结束原因（只有type=2时才有此值）
	 * Reason 表示事务终止的原因，可选字段。Valid values（合法值）包括：
	 * - EmergencyStop   : 紧急停止（例如按下急停按钮）
	 * - EVDisconnected  : 电动车断开连接
	 * - HardReset       : 硬件重置触发终止
	 * - Local           : 本地用户或操作终止
	 * - Other           : 其他未分类原因
	 * - PowerLoss       : 电源中断
	 * - Reboot          : 系统重启
	 * - Remote          : 中央系统远程终止
	 * - SoftReset       : 软件重置
	 * - UnlockCommand   : 解锁连接器命令触发（如用户拔出充电枪）
	 * - DeAuthorized    : 用户被取消授权
	 */
	private String reason;

	/**
	 * 时间, 格式：2006-01-02T15:04:05+7:00
	 */
	private String timestamp;

	/**
	 * 电表数据（只有type=2时才有此值），暂时用不到
	 */
//	private List<TransactionData> transactionData;

	/**
	 * 事务ID，一次充电的开始和结束ID相同
	 */
	private Integer transactionId;

	/**
	 * 开始，结束：1，2
	 */
	private Integer type;

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	public static class TransactionData {

		private List<SampledValue> sampledValue;
		private Timestamp timestamp;

		@Data
		@NoArgsConstructor
		@AllArgsConstructor
		@Builder
		public static class SampledValue {
			private String value;
			private String context;
			private String format;
			private String location;
			private String measurand;
			private String phase;
			private String unit;
		}

		@Data
		@NoArgsConstructor
		@AllArgsConstructor
		@Builder
		public static class Timestamp {
			private String timeTime;
		}
	}

}
