package com.weihengtech.pojo.dtos.device.config;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class ParameterSettingDTO {

	private String remoteOff = "";

	private String upsEnable = "";

	private String pvConnectMode = "";

	private String maxFeedInSoftEn = "";

	private String maxFeedInHardEn = "";

	private String maxFeedInSoftLimit = "";

	private String maxFeedInHardLimit = "";

	private String vppWorkMode = "";

	private String meterEnable = "";

	private String meterType = "";

	private String feedPhase = "";

	private String parallelWorkMode = "";

	private String ParallelNum = "";

	private String accessGridType = "";

	private String houseSideGridLimit = "";

	private String machineSideGridLimit = "";

	private String epsParallel = "";

	private String batEnable = "";
}
