package com.weihengtech.pojo.dtos.device.info;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.pojo.bos.parser.AbsErrorCollectionBO;
import com.weihengtech.pojo.dtos.parser.ParseErrorDTO;
import com.weihengtech.pojo.vos.device.DeviceParseErrVO;
import com.weihengtech.service.parser.ParseErrorService;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "逆变器信息回参")
public class InverterInformationDTO implements Serializable {

	private static final long serialVersionUID = 4030705141616145823L;

	private RunData runData = new RunData();

	private DcData dcData = new DcData();

	private AcData acData = new AcData();

	@Getter
	@Setter
	@ToString
	public static class RunData {

		private String runMode = "";

		private String waitTime = "";

		private String error1 = "";

		private String error2 = "";

		private String error3 = "";

		private String inverterSinkTemp = "";

		private String batterySinkTemp = "";

		private String airTemp = "";

		private String capTemp = "";

		private String inverterErrorCode = "";

		private String batteryErrorCode = "";

		private String armErrorCode = "";

		private AbsErrorCollectionBO armCollection;

		private AbsErrorCollectionBO error1Collection;

		private AbsErrorCollectionBO error2Collection;

		private AbsErrorCollectionBO error3Collection;

		public void buildParseList(ParseErrorService parseErrorService, String deviceSn) {
			ParseErrorDTO parseErrorRes = parseErrorService.parseErrorInfo(DeviceParseErrVO.builder()
					.deviceSn(deviceSn)
					.error1(this.error1)
					.error2(this.error2)
					.error3(this.error3)
					.armBit(this.armErrorCode)
					.build());
			this.error1Collection = parseErrorRes.getDspBit1();
			this.error2Collection = parseErrorRes.getDspBit2();
			this.error3Collection = parseErrorRes.getDspBit3();
			this.armCollection = parseErrorRes.getArmBit();
			this.error1 = new BigInteger(StrUtil.isBlank(error1) ? "0" : error1, 2).toString(16);
			this.error2 = new BigInteger(StrUtil.isBlank(error2) ? "0" : error2, 2).toString(16);
			this.error3 = new BigInteger(StrUtil.isBlank(error3) ? "0" : error3, 2).toString(16);
			this.armErrorCode = new BigInteger(StrUtil.isBlank(armErrorCode) ? "0" : armErrorCode, 2).toString(16);

		}
	}

	@Getter
	@Setter
	@ToString
	public static class DcData {

		private String dcvBus = "";

		private String dcvPv1 = "";

		private String dciPv1 = "";

		private String dcvPv2 = "";

		private String dciPv2 = "";

		private String dcpPv1 = "";

		private String dcpPv2 = "";

		private String dcvBusP = "";

		private String dcvBusM = "";

		private String dcvPv3 = "";

		private String dciPv3 = "";

		private String dcpPv3 = "";

		private String dcvPv4 = "";

		private String dciPv4 = "";

		private String dcpPv4 = "";

		/**
		 * 北美机相关数据，其他不展示
		 */
		public void hideVal() {
			setDcvPv3(StrUtil.EMPTY);
			setDciPv3(StrUtil.EMPTY);
			setDcpPv3(StrUtil.EMPTY);
			setDcvPv4(StrUtil.EMPTY);
			setDciPv4(StrUtil.EMPTY);
			setDcpPv4(StrUtil.EMPTY);
		}
	}

	@Getter
	@Setter
	@ToString
	public static class AcData {

		private String acVacR = "";

		private String acIacR = "";

		private String acFreqR = "";

		private String acPacR = "";

		private String acVacS = "";

		private String acIacS = "";

		private String acFreqS = "";

		private String acPacS = "";

		private String acVacT = "";

		private String acIacT = "";

		private String acFreqT = "";

		private String acPacT = "";

		private String acVacL1 = "";

		private String acIacL1 = "";

		private String acFreqL1 = "";

		private String acPacL1 = "";

		private String acVacL2 = "";

		private String acIacL2 = "";

		private String acFreqL2 = "";

		private String acPacL2 = "";

		private String power = "";

		private String reactivePower = "";

		private String pf = "";
	}
}
