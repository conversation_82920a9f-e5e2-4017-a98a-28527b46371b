package com.weihengtech.pojo.dtos.firmware;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.time.LocalDateTime;

/**
 * @author: jiahao.jin
 * @create: 2025-07-18 10:02
 * @description:
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "批量升级任务分页回参")
public class FirmwareBatchUpgradeTaskDTO {
    private String id;

    private String taskName;

    private String resourceSeriesCode;

    private String resourceType;

    private String firmwareId;

    private String firmwareType;

    private String firmwareName;

    private String upgradeMode;

    private Long upgradeTime;

    private String status;

    private String creatorEmail;

    private LocalDateTime createTime;

    private String remarks;

    private Integer targetDeviceCount;

    private Integer upgradingCount;

    private Integer successCount;

    private Integer failureCount;

    private Integer progress;
}
