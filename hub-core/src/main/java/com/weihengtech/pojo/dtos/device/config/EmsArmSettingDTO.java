package com.weihengtech.pojo.dtos.device.config;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备配置 EMS ARM Setting")
public class EmsArmSettingDTO implements Serializable {

	private static final long serialVersionUID = -5348257648535747037L;

	private String remoteDspControl = "";

	private String chargeDisChargePowerValue = "";

	private String emsControlCommand = "";

	private String powerLimitByBmsChg = "";

	private String powerLineByBmsDisChg = "";

	private String powerLineByDrmChg = "";

	private String powerLineByDrmDischg = "";

	private String drmMode = "";

	private String drmEnable = "";

	private String powerRefInvLimit = "";

	private String batEnable = "";

	private String pvCharge = "";

	private String batSource = "";

	private String rstartAll = "";

	private String meterCTFactor = "";
}
