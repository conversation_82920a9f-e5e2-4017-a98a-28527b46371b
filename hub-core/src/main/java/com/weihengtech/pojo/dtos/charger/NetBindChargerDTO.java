package com.weihengtech.pojo.dtos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 充电桩配网返参
 *
 * <AUTHOR>
 * @date 2024/1/25 15:17
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "配网流程用户绑定设备入参")
public class NetBindChargerDTO {

	@ApiModelProperty(value = "wifi棒序列号", required = true)
	private String gateSn;

	@ApiModelProperty(value = "设备序列号", required = true)
	private String chargerSn;

}
