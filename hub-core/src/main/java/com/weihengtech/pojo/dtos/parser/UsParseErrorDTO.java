package com.weihengtech.pojo.dtos.parser;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.enums.device.DeviceErrorTypeEnum;
import com.weihengtech.pojo.bos.parser.UsErrorCollectionBO;
import lombok.Getter;
import lombok.ToString;

/**
 * 异常解析结果集-北美机实现
 *
 * <AUTHOR>
 * @date 2024/4/29 16:16
 * @version 1.0
 */
@Getter
@ToString
public class UsParseErrorDTO extends ParseErrorDTO{

	@Override
	public void setDspBit1(String binaryStr) {
		UsErrorCollectionBO item = new UsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.DSP_1_US, StrUtil.reverse(binaryStr));
		this.dspBit1 = item;
	}

	@Override
	public void setDspBit2(String binaryStr) {
		UsErrorCollectionBO item = new UsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.DSP_2_US, StrUtil.reverse(binaryStr));
		this.dspBit2 = item;
	}

	@Override
	public void setDspBit3(String binaryStr) {
		UsErrorCollectionBO item = new UsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.DSP_3_US, StrUtil.reverse(binaryStr));
		this.dspBit3 = item;
	}

	@Override
	public void setDspBit1Value(String binaryStr) {
		UsErrorCollectionBO item = new UsErrorCollectionBO();
		item.setValue(DeviceErrorTypeEnum.DSP_1_US, binaryStr);
		this.dspBit1 = item;
	}

	@Override
	public void setDspBit2Value(String binaryStr) {
		UsErrorCollectionBO item = new UsErrorCollectionBO();
		item.setValue(DeviceErrorTypeEnum.DSP_2_US, binaryStr);
		this.dspBit2 = item;
	}

	@Override
	public void setDspBit3Value(String binaryStr) {
		UsErrorCollectionBO item = new UsErrorCollectionBO();
		item.setValue(DeviceErrorTypeEnum.DSP_3_US, binaryStr);
		this.dspBit3 = item;
	}
}
