package com.weihengtech.pojo.dtos.device.config;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "设备配置 SafetyParameter 回参")
public class SafetyParameterDTO implements Serializable {

	private static final long serialVersionUID = -5745346365477193720L;

	private String stepPower = "";

	private String ov3 = "";

	private String ov3T = "";

	private String ov2 = "";

	private String ov2T = "";

	private String ov1 = "";

	private String ov1T = "";

	private String ovRec = "";

	private String ovRecT = "";

	private String uv1 = "";

	private String uv1T = "";

	private String uv2 = "";

	private String uv2T = "";

	private String uv3 = "";

	private String uv3T = "";

	private String uvRec = "";

	private String uvRecT = "";

	private String of3 = "";

	private String of3T = "";

	private String of2 = "";

	private String of2T = "";

	private String of1 = "";

	private String of1T = "";

	private String ofRec = "";

	private String ofRecT = "";

	private String uf1 = "";

	private String uf1T = "";

	private String uf2 = "";

	private String uf2T = "";

	private String uf3 = "";

	private String uf3T = "";

	private String ufRec = "";

	private String ufRecT = "";

	private String ov10Min = "";

	private String startTime = "";

	private String reconnectTime = "";

	private String ofPowerRateLimit = "";

	public List<List<String>> buildConnectionDataList() {
		List<List<String>> resList = new ArrayList<>();
		resList.add(Arrays.asList("OVPRec Volt", Optional.ofNullable(ovRec).orElse(StrUtil.EMPTY), "V"));
		resList.add(Arrays.asList("OVPRec Time", Optional.ofNullable(ovRecT).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("UVPRec Volt", Optional.ofNullable(uvRec).orElse(StrUtil.EMPTY), "V"));
		resList.add(Arrays.asList("UVPRec Time", Optional.ofNullable(uvRecT).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("OFPRec Freq", Optional.ofNullable(ofRec).orElse(StrUtil.EMPTY), "Hz"));
		resList.add(Arrays.asList("OFPRec Time", Optional.ofNullable(ofRecT).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("UFPRec Freq", Optional.ofNullable(ufRec).orElse(StrUtil.EMPTY), "Hz"));
		resList.add(Arrays.asList("UFPRec Time", Optional.ofNullable(ufRecT).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("Start Time", Optional.ofNullable(startTime).orElse(StrUtil.EMPTY), "S"));
		resList.add(Arrays.asList("Fault Reconnect Time", Optional.ofNullable(reconnectTime).orElse(StrUtil.EMPTY), "S"));
		resList.add(Arrays.asList("Power Rate Limit", Optional.ofNullable(ofPowerRateLimit).orElse(StrUtil.EMPTY), "%/min"));
		return resList;
	}

	public List<List<String>> buildInterfaceDataList() {
		List<List<String>> resList = new ArrayList<>();
		resList.add(Arrays.asList("OVP3 Volt", Optional.ofNullable(ov3).orElse(StrUtil.EMPTY), "V"));
		resList.add(Arrays.asList("OVP3 Time", Optional.ofNullable(ov3T).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("OVP2 Volt", Optional.ofNullable(ov2).orElse(StrUtil.EMPTY), "V"));
		resList.add(Arrays.asList("OVP2 Time", Optional.ofNullable(ov2T).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("OVP1 Volt", Optional.ofNullable(ov1).orElse(StrUtil.EMPTY), "V"));
		resList.add(Arrays.asList("OVP1 Time", Optional.ofNullable(ov1T).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("UVP1 Volt", Optional.ofNullable(uv1).orElse(StrUtil.EMPTY), "V"));
		resList.add(Arrays.asList("UVP1 Time", Optional.ofNullable(uv1T).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("UVP2 Volt", Optional.ofNullable(uv2).orElse(StrUtil.EMPTY), "V"));
		resList.add(Arrays.asList("UVP2 Time", Optional.ofNullable(uv2T).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("UVP3 Volt", Optional.ofNullable(uv3).orElse(StrUtil.EMPTY), "V"));
		resList.add(Arrays.asList("UVP3 Time", Optional.ofNullable(uv3T).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("OV 10Min Volt", Optional.ofNullable(ov10Min).orElse(StrUtil.EMPTY), "V"));
		resList.add(Arrays.asList("OV 10Min Time", "100", "ms"));
		resList.add(Arrays.asList("OFP3 Freq", Optional.ofNullable(of3).orElse(StrUtil.EMPTY), "Hz"));
		resList.add(Arrays.asList("OFP3 Time", Optional.ofNullable(of3T).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("OFP2 Freq", Optional.ofNullable(of2).orElse(StrUtil.EMPTY), "Hz"));
		resList.add(Arrays.asList("OFP2 Time", Optional.ofNullable(of2T).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("OFP1 Freq", Optional.ofNullable(of1).orElse(StrUtil.EMPTY), "Hz"));
		resList.add(Arrays.asList("OFP1 Time", Optional.ofNullable(of1T).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("UFP1 Freq", Optional.ofNullable(uf1).orElse(StrUtil.EMPTY), "Hz"));
		resList.add(Arrays.asList("UFP1 Time", Optional.ofNullable(uf1T).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("UFP2 Freq", Optional.ofNullable(uf2).orElse(StrUtil.EMPTY), "Hz"));
		resList.add(Arrays.asList("UFP2 Time", Optional.ofNullable(uf2T).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("UFP3 Freq", Optional.ofNullable(uf3).orElse(StrUtil.EMPTY), "Hz"));
		resList.add(Arrays.asList("UFP3 Time", Optional.ofNullable(uf3T).orElse(StrUtil.EMPTY), "ms"));
		resList.add(Arrays.asList("Island disconnection time", "2", "S"));
		return resList;
	}
}
