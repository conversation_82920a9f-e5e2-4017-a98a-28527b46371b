package com.weihengtech.pojo.dtos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "配网流程用户绑定设备入参")
public class NetBindDeviceDTO {

	@ApiModelProperty(value = "wifi棒序列号", required = true)
	private String wifiSn;

	@ApiModelProperty(value = "设备序列号", required = true)
	private String deviceSn;

}
