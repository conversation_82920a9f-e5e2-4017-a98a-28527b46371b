package com.weihengtech.pojo.dtos.firmware;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "固件上传时需要数据回参")
public class FirmwareUploadAllDTO implements Serializable {

	private String hardwareCategoryName;

	private List<HardwareType> hardwareTypeList;

	@Getter
	@Setter
	@ToString
	public static class HardwareType {

		private String hardwareTypeName;

		private List<SoftwareVersion> softwareVersionList;
	}

	@Getter
	@Setter
	@ToString
	public static class SoftwareVersion {

		private String id;

		private String version;

		@ApiModelProperty(name = "bindFlag", value = "绑定标识 0: 未绑定; 1: 已绑定", required = true)
		private Integer bindFlag;

		public SoftwareVersion(String id, String version, Integer bindFlag) {
			this.id = id;
			this.version = version;
			this.bindFlag = bindFlag;
		}
	}
}
