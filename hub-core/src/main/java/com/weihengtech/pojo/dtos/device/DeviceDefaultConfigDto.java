package com.weihengtech.pojo.dtos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program ecos-admin-backend
 * @description 设备预设配置类
 * @create 2023-08-18 14:35
 **/
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "DeviceDefaultConfig参数")
public class DeviceDefaultConfigDto implements Serializable {

    @ApiModelProperty(name = "deviceSn", value = "设备SN序列号")
    private String deviceSn;

    @ApiModelProperty(name = "state", value = "状态 -5 未知, -4:已禁用 -3:未激活 -1: 离线 0: 等待 1: 并网 2: EPS 3: 故障 4: 保留 5: 自检, 20: 在线 21 不可用")
    private Integer state;

    private String dsp1SoftwareVersion;

    private String emsSoftwareVersion;

    private String meterEnable;

    private String meterType;

    private String feedPhase;

}
