package com.weihengtech.pojo.dtos.firmware;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "软件版本分页回参")
public class SoftwareVersionPageDTO implements Serializable {

	private static final long serialVersionUID = -5541755894253573434L;

	private String hardwareCategory;

	private String hardwareType;

	private String softwareVersion;

	private String softwareId;

	private List<String> hardwareVersionList;

	private String remarks;

	private String model;
}
