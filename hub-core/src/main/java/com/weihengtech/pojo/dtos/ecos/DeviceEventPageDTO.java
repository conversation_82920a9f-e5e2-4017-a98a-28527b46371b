package com.weihengtech.pojo.dtos.ecos;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "设备事件回参")
public class DeviceEventPageDTO implements Serializable {

	private static final long serialVersionUID = 4201179964591081242L;

	private String deviceName;

	private Integer code;

	private String level;

	private Long uploadTime;

	private String chinese;

	private String english;

	private String subsystem;

	private Integer eventAction;
}
