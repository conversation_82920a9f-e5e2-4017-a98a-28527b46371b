package com.weihengtech.pojo.dtos.device.config;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "设备配置 DSP Calibration 回参")
public class DspCalibrationDTO implements Serializable {

	private static final long serialVersionUID = -5759158818317329572L;

	private String stepPower = "";

	private String invPower = "";

	private String vAc = "";

	private String vBus = "";

	private String vPv1 = "";

	private String vPv2 = "";

	private String vPv3 = "";

	private String vPv4 = "";

	private String vBattery = "";

	private String vEps = "";

	private String iEps = "";

	private String iPv1 = "";

	private String iPv2 = "";

	private String iPv3 = "";

	private String iPv4 = "";

	private String iBattery = "";

	private String funcEnPfCalib = "";

	private String reacPwrOffsetX10 = "";
}
