package com.weihengtech.pojo.dtos.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备sn模糊查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/1 9:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeviceLikeSnDTO {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("设备sn")
    private String deviceSn;

    @ApiModelProperty("系列code")
    private String resourceSeriesCode;

    @ApiModelProperty("ems版本")
    private String emsSoftwareVersion;

    @ApiModelProperty("dsp软件版本")
    private String dsp1SoftwareVersion;

    @ApiModelProperty("vpp模式")
    private Boolean vppMode;

    @ApiModelProperty("并机数量")
    private Integer parallelNumber;

    @ApiModelProperty("安规id")
    private Integer safetyStandard;

    @ApiModelProperty("设备型号")
    private String deviceModel;

}
