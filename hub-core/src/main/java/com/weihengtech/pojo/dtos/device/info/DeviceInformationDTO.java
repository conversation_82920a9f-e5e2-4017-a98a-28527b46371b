package com.weihengtech.pojo.dtos.device.info;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "设备信息回参")
public class DeviceInformationDTO implements Serializable {

	private static final long serialVersionUID = -3241943443598836051L;

	private String inverterType = "";

	/**
	 * dsp1SoftwareVersion
	 */
	private String dsp1Version = "";

	/**
	 * dsp2SoftwareVersion
	 */
	private String dsp2Version = "";

	private String powerBoardHardwareVersion = "";

	private String deviceSn = "";

	/**
	 * deviceModel
	 */
	private String modelName = "";

	private String brand = "";

	private String factory = "";

	private String emsSoftwareVersion = "";

	private String bmsSoftwareVersion = "";

	private String emsHardwareVersion = "";

	/**
	 * bmsGaugeVersion
	 */
	private String gaugeVersion = "";

	private String bmsHardwareVersion = "";

	/**
	 * bmsVendor
	 */
	private String bmsManufacturer = "";

	private String bmsSn = "";

	private String emsSubVersion = "";

	private String dsp1SubVersion = "";

	private String dsp2SubVersion = "";

	private String ateUsbStatus = "";

	private String wifiVersion = "";

	private String parallelNumber = "";

	private String arcDspSoftwareVersion = "";

	private String arcDspSubVersion = "";

	private String arcDspBootLoaderSoftwareVersion = "";

	// 安规ID
	private String safetyID = "";

	public void setDsp1SubVersion(String dsp1SubVersion) {
		if (this.dsp1SubVersion == null) {
			this.dsp1SubVersion = "";
		}
		if (StrUtil.isNotBlank(this.dsp1SubVersion)) {
			this.dsp1SubVersion = this.dsp1SubVersion + "-" + dsp1SubVersion;
		} else {
			this.dsp1SubVersion = this.dsp1SubVersion + dsp1SubVersion;
		}
	}

	public void setDsp2SubVersion(String dsp2SubVersion) {
		if (this.dsp2SubVersion == null) {
			this.dsp2SubVersion = "";
		}
		if (StrUtil.isNotBlank(this.dsp2SubVersion)) {
			this.dsp2SubVersion = this.dsp2SubVersion + "-" + dsp2SubVersion;
		} else {
			this.dsp2SubVersion = this.dsp2SubVersion + dsp2SubVersion;
		}
	}

	public void buildDefaultVersion() {
		setDsp1Version("V1.00");
		this.dsp1SubVersion = "V1.00";
		setDsp2Version("V1.00");
		this.dsp2SubVersion = "V1.00";
		setBmsSoftwareVersion("V1.00");
		setBmsHardwareVersion("V1.00");
		setEmsHardwareVersion("V1.00");
		setEmsSoftwareVersion("V1.00");
		setEmsSubVersion("V1.00");
		setWifiVersion("V1.00");
		setPowerBoardHardwareVersion("V1.00");
	}
}
