package com.weihengtech.pojo.dtos.device.info;

import com.weihengtech.pojo.dtos.relation.DeviceRelPowerDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @program ecos-admin-backend
 * @description 设备实时功率数据
 * @create 2023-08-14 11:09
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "设备实时功率回参")
public class PowerFlowDTO {
    @ApiModelProperty(name = "batterySoc", value = "电池剩余电量")
    private BigDecimal batterySoc = BigDecimal.ZERO;

    @ApiModelProperty(name = "batteryPower", value = "电池功率")
    private BigDecimal batteryPower = BigDecimal.ZERO;

    @ApiModelProperty(name = "epsPower", value = "EPS功率")
    private BigDecimal epsPower = BigDecimal.ZERO;

    @ApiModelProperty(name = "gridPower", value = "电网功率(电池功率大于0则指向Home, 反之Home指向电网)")
    private BigDecimal gridPower = BigDecimal.ZERO;

    @ApiModelProperty(name = "homePower", value = "家庭功率")
    private BigDecimal homePower = BigDecimal.ZERO;

    @ApiModelProperty(name = "meterPower", value = "仪表功率")
    private BigDecimal meterPower = BigDecimal.ZERO;

    @ApiModelProperty(name = "solarPower", value = "光伏功率")
    private BigDecimal solarPower = BigDecimal.ZERO;

    @ApiModelProperty(name = "sysPowerConfig", value = "8: 柴发模式")
    private Integer sysPowerConfig = 0;

    @ApiModelProperty(name = "relDevicePower", value = "关联设备功率")
    private List<DeviceRelPowerDTO> relDevicePower;
}
