package com.weihengtech.pojo.dtos.device;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "设备状态对象")
public class DeviceStatusDTO {

	private Long deviceId;
	private Integer state;
	private Long setUpTime;
	private Double batSoc;
	private String bmsSoftwareVersion;
}
