package com.weihengtech.pojo.dtos.firmware;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "固件升级记录回参")
public class FirmwareUpgradeRecordPageDTO implements Serializable {

	private static final long serialVersionUID = -730796171300321147L;

	private String deviceType;

	private String time;

	@ApiModelProperty(name = "status", value = "0:失败; 1: 成功; 2: 升级中; 3: 重试1次; 4:重试2次; 5:重试3次", required = true)
	private Integer status;

	private String startVersion;

	private String endVersion;
}
