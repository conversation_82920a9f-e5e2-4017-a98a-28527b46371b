package com.weihengtech.pojo.dtos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备解绑账号回参")
public class DeviceBoundUnBindAccountDto {

	@ApiModelProperty(value = "不存在的sn")
	private List<String> notExistList = new ArrayList<>();

	@ApiModelProperty(value = "非法的sn")
	private List<String> invalidList = new ArrayList<>();
}
