package com.weihengtech.pojo.dtos.tsdb;

import com.weihengtech.enums.tsdb.TsdbSampleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/20 16:42
 * @version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TsdbQueryDTO {

	private String cloudId;

	private Long startTime;

	private Long endTime;

	private List<String> metricList;

	private String times;

	private Integer batteryIdx;

	private TsdbSampleEnum tsdbSampleEnum;
}
