package com.weihengtech.pojo.dtos.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "app版本回参")
public class EcosGlobalVersionDto {

	private String androidVersion;

	private String iosVersion;

	private Integer flag;

	private String content;

	@ApiModelProperty(name = "preForceIosVersion", value = "上一个强制更新的ios版本", required = true)
	private String preForceIosVersion;

	@ApiModelProperty(name = "preForceAndroidVersion", value = "上一个强制更新的Android版本", required = true)
	private String preForceAndroidVersion;
}
