package com.weihengtech.pojo.dtos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 充电桩配网返参
 *
 * <AUTHOR>
 * @date 2024/1/25 15:17
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "充电桩充电状态回调参数")
public class ChargerRunDataDTO {

	@ApiModelProperty(name = "chargeCapacity", value = "充电量(kWh)")
	private BigDecimal chargeCapacity = BigDecimal.ZERO;

	@ApiModelProperty(name = "power", value = "充电功率(W)")
	private BigDecimal power = BigDecimal.ZERO;

	@ApiModelProperty(name = "current1", value = "充电电流1(A)")
	private BigDecimal current1 = BigDecimal.ZERO;
	@ApiModelProperty(name = "current2", value = "充电电流2")
	private BigDecimal current2 = BigDecimal.ZERO;
	@ApiModelProperty(name = "current3", value = "充电电流3")
	private BigDecimal current3 = BigDecimal.ZERO;

	@ApiModelProperty(name = "voltage1", value = "充电电压1(V)")
	private BigDecimal voltage1 = BigDecimal.ZERO;
	@ApiModelProperty(name = "voltage2", value = "充电电压2")
	private BigDecimal voltage2 = BigDecimal.ZERO;
	@ApiModelProperty(name = "voltage3", value = "充电电压3")
	private BigDecimal voltage3 = BigDecimal.ZERO;

	@ApiModelProperty(name = "sysRunMode", value = "")
	private Integer state = -5;

	@ApiModelProperty(name = "startTime", value = "充电开始时间")
	private String startTime;
}
