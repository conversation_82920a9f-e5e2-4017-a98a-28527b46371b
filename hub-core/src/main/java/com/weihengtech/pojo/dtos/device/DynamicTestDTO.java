package com.weihengtech.pojo.dtos.device;

import com.weihengtech.pojo.dos.device.StorageDynamicStageDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 设备sn模糊查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/1 9:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DynamicTestDTO {

    @ApiModelProperty(name = "stageList", value = "阶段列表")
    private List<StorageDynamicStageDO> stageList;

    @ApiModelProperty(name = "nmi", value = "户号")
    private String nmi;

    @ApiModelProperty(name = "lfdi", value = "lfdi")
    private String lfdi;

}
