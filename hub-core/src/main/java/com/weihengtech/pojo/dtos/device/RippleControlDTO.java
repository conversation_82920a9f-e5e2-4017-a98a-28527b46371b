package com.weihengtech.pojo.dtos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @date 2024/12/16 14:43
 * @version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "脉冲配置回参")
public class RippleControlDTO {

    @ApiModelProperty(name = "deviceName", value = "设备序列号", required = true)
    @NotBlank(message = "err.not.blank")
    private String deviceName;

    @ApiModelProperty(name = "脉冲控制", value = "脉冲控制", required = true)
    @NotEmpty(message = "err.not.blank")
    private LinkedHashMap<Integer, Integer> rippleMap;

    // 脉冲控制使能开关
    @ApiModelProperty(name = "rippleEnable", value = "脉冲控制使能开关")
    private Integer rippleEnable;
}
