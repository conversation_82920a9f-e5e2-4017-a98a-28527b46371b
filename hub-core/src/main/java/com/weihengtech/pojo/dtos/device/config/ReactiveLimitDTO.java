package com.weihengtech.pojo.dtos.device.config;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "设备配置 Reactive Limit回参")
public class ReactiveLimitDTO implements Serializable {

	private static final long serialVersionUID = 2771612088965298670L;

	private String manager = "";

	private String ctrlMode = "";

	private String fixPf = "";

	private String p1 = "";

	private String pf1 = "";

	private String p2 = "";

	private String pf2 = "";

	private String fixedQ = "";

	private String u1 = "";

	private String q1 = "";

	private String u2 = "";

	private String q2 = "";

	private String u3 = "";

	private String q3 = "";

	private String u4 = "";

	private String q4 = "";

	private String responseT = "";

	private String overExcited = "";

	private String filterTimeConst = "";

	public List<List<String>> buildDataList() {
		List<List<String>> resList = new ArrayList<>();
		resList.add(Arrays.asList("Enable", Optional.ofNullable(manager).map(i -> "1".equals(i) ? "Enable" : "Disable").orElse(StrUtil.EMPTY)));
		resList.add(Arrays.asList("Mode", mapCtrlMode(ctrlMode)));
		resList.add(Arrays.asList("Fix PF", fixPf));
		resList.add(Arrays.asList("Fix Q", Optional.ofNullable(fixedQ).orElse(StrUtil.EMPTY), "%Sn"));
		resList.add(Arrays.asList("P0", "0", "%Sn"));
		resList.add(Arrays.asList("PF0", "-100"));
		resList.add(Arrays.asList("P1", Optional.ofNullable(p1).orElse(StrUtil.EMPTY), "%Sn"));
		resList.add(Arrays.asList("PF1", pf1));
		resList.add(Arrays.asList("P2", Optional.ofNullable(p2).orElse(StrUtil.EMPTY), "%Sn"));
		resList.add(Arrays.asList("PF2", pf2));
		resList.add(Arrays.asList("U1", u1, "%Un"));
		resList.add(Arrays.asList("Q1", Optional.ofNullable(q1).orElse(StrUtil.EMPTY), "%Sn"));
		resList.add(Arrays.asList("U2", Optional.ofNullable(u2).orElse(StrUtil.EMPTY), "%Un"));
		resList.add(Arrays.asList("Q2", Optional.ofNullable(q2).orElse(StrUtil.EMPTY), "%Sn"));
		resList.add(Arrays.asList("U3", Optional.ofNullable(u3).orElse(StrUtil.EMPTY), "%Un"));
		resList.add(Arrays.asList("Q3", Optional.ofNullable(q3).orElse(StrUtil.EMPTY), "%Sn"));
		resList.add(Arrays.asList("U4", Optional.ofNullable(u4).orElse(StrUtil.EMPTY), "%Un"));
		resList.add(Arrays.asList("Q4", Optional.ofNullable(q4).orElse(StrUtil.EMPTY), "%Sn"));
		resList.add(Arrays.asList("Time constant of a first-order filter", Optional.ofNullable(filterTimeConst).orElse(StrUtil.EMPTY), "S"));
		resList.add(Arrays.asList("Over/Under Excited", Optional.ofNullable(overExcited).map(i -> "1".equals(i) ? "Under-Excited" : "Over-Excited").orElse(StrUtil.EMPTY)));
		return resList;
	}

	/** ctrlMode映射关系 */
	private String mapCtrlMode(String ctrlMode) {
		switch (ctrlMode) {
			case "0":
				return "none";
			case "1":
				return "Fixed PF";
			case "2":
				return "CosPhi – P";
			case "3":
				return "Fixed Q";
			case "4":
				return "Q-U( In BDEW)";
			default:
				return StrUtil.EMPTY;
		}
	}
}
