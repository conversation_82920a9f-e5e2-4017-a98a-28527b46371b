package com.weihengtech.pojo.dtos.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备sn模糊查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/1 9:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeviceStatisticsDTO {

    @ApiModelProperty("总接入设备数")
    private Integer installedSum;

    @ApiModelProperty("设备总数")
    private Integer totalSum;

    @ApiModelProperty("在线设备数")
    private Integer connectedSum;

    @ApiModelProperty("离线总数")
    private Integer offlineSum;

    @ApiModelProperty("故障总数")
    private Integer faultSum;

    // 低电量设备数 电量小于10%
    @ApiModelProperty("低电量设备数")
    private Integer lowBatterySum;

}
