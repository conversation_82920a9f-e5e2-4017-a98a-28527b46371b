package com.weihengtech.pojo.dtos.ecos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "client版本分页回参")
public class EcosClientGlobalVersionDto {

	@ApiModelProperty(name = "id", value = "版本id", required = true)
	private Integer id;

	@ApiModelProperty(name = "androidVersion", value = "安卓版本号", required = true)
	private String androidVersion;

	@ApiModelProperty(name = "iosVersion", value = "ios版本号", required = true)
	private String iosVersion;

	@ApiModelProperty(name = "createTime", value = "创建时间", required = true)
	private Long createTime;

	@ApiModelProperty(name = "flag", value = "0: 提醒一次 1: 每次提醒 2: 强制更新", required = true)
	@NotNull(message = "err.not.null")
	private Integer flag;

	@ApiModelProperty(name = "zhCn", value = "中国")
	private String zhCn;

	@ApiModelProperty(name = "enUs", value = "美国")
	private String enUs;

}
