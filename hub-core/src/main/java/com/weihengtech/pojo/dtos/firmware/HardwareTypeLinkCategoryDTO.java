package com.weihengtech.pojo.dtos.firmware;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "硬件类型分类组件回参")
public class HardwareTypeLinkCategoryDTO implements Serializable {

	private String categoryId;

	private String categoryName;

	private List<TypeDTO> typeList;

	@Getter
	@Setter
	@ToString
	public static class TypeDTO {

		private String typeId;

		private String typeName;
	}
}
