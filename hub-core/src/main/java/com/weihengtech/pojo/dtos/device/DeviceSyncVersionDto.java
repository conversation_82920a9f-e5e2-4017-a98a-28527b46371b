package com.weihengtech.pojo.dtos.device;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class DeviceSyncVersionDto {

	private String deviceName;

	private String deviceModel;

	private String brand;

	private String factory;

	private String powerBoardHardwareVersion;

	private String dsp1SoftwareVersion;

	private String dsp2SoftwareVersion;

	private String deviceSn;

	private String emsSoftwareVersion;

	private String emsHardwareVersion;

	private String wifiSn;

	private String wifiHardwareVersion;

	private String wifiSoftwareVersion;

	private String bmsGaugeVersion;

	private String bmsSn;

	private String bmsVendor;

	private String bmsSoftwareVersion;

	private String bmsHardwareVersion;

	private String emsSubVersion;

	private String dsp1SubVersion;

	private String dsp2SubVersion;

	private Integer parallelNumber;

	private Integer safetyStandard;

	public void buildDefaultVersion() {
		setDsp1SoftwareVersion("V1.00");
		setDsp1SubVersion("V1.00");
		setDsp2SoftwareVersion("V1.00");
		setDsp2SubVersion("V1.00");
		setBmsSoftwareVersion("V1.00");
		setBmsHardwareVersion("V1.00");
		setEmsHardwareVersion("V1.00");
		setEmsSoftwareVersion("V1.00");
		setEmsSubVersion("V1.00");
		setWifiHardwareVersion("V1.00");
		setWifiSoftwareVersion("V1.00");
		setPowerBoardHardwareVersion("V1.00");
	}
}
