package com.weihengtech.pojo.dtos.device;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TuyaDevicePropertyDto {

	private String id;

	private List<StatusInfo> status;

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	public static class StatusInfo {

		private String code;

		private Object value;
	}
}
