package com.weihengtech.pojo.dtos.device.info;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.pojo.bos.parser.AbsErrorCollectionBO;
import com.weihengtech.pojo.dtos.parser.ParseErrorDTO;
import com.weihengtech.pojo.vos.device.DeviceParseErrVO;
import com.weihengtech.service.parser.ParseErrorService;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "电池数据回参")
public class BatteryDataDTO implements Serializable {

	private static final long serialVersionUID = -876746198101908876L;

	private String voltageDsp;

	private String currentDsp;

	private String powerDsp;

	private String soc;

	private String soh;

	private String errorH32;

	private String error2H32;

	private String error3H32;

	private String error4H32;

	private String error5H32;

	private String bmsLaterChgCapacity;

	private String bmsLaterDischgCapacity;

	private String maxVoltageCharge;

	private String maxCurrentCharge;

	private String voltageBms;

	private String currentBms;

	private String powerBms;

	private String basicStatus;

	private String faultH16;

	private String alarmL16;

	private String temperature;

	private String cycleTimes;

	private String minVoltageDischarge;

	private String maxCurrentDischarge;

	private String remainCapacity;

	private String switchStatus;

	private String cellMaxVoltage;

	private String cellMaxVoltageId;

	private String cellMinVoltage;

	private String cellMinVoltageId;

	private String cellMaxTemp;

	private String cellMaxTempId;

	private String cellMinTemp;

	private String cellMinTempId;

	private String batSerialNumber;

	private String bateTotalCharge;

	private String bateTotalDischarge;

	private String bmsVersion;

	private AbsErrorCollectionBO errorCollection;

	private AbsErrorCollectionBO error2Collection;

	private AbsErrorCollectionBO error3Collection;

	private AbsErrorCollectionBO error4Collection;

	private AbsErrorCollectionBO error5Collection;

	private AbsErrorCollectionBO faultCollection;

	private AbsErrorCollectionBO alarmCollection;

	public void setBmsVersion(String bmsVersion) {
		this.bmsVersion = Optional.ofNullable(this.bmsVersion).orElse("") + bmsVersion;
	}

	public void buildParseList(ParseErrorService parseErrorService, String deviceSn) {
		ParseErrorDTO parseErrorRes = parseErrorService.parseErrorInfo(DeviceParseErrVO.builder()
				.deviceSn(deviceSn)
				.errorH32(this.errorH32)
				.error2H32(this.error2H32)
				.error3H32(this.error3H32)
				.error4H32(this.error4H32)
				.error5H32(this.error5H32)
				.alarmL16(this.alarmL16)
				.faultH16(this.faultH16)
				.build());
		this.errorCollection = parseErrorRes.getBmsBitError();
		this.error2Collection = parseErrorRes.getBmsBitError2();
		this.error3Collection = parseErrorRes.getBmsBitError3();
		this.error4Collection = parseErrorRes.getBmsBitError4();
		this.error5Collection = parseErrorRes.getBmsBitError5();
		this.alarmCollection = parseErrorRes.getBmsBitAlarm();
		this.faultCollection = parseErrorRes.getBmsBitFault();
		this.errorH32 = new BigInteger(StrUtil.isBlank(errorH32) ? "0" : errorH32, 2).toString(16);
		this.error2H32 = new BigInteger(StrUtil.isBlank(error2H32) ? "0" : error2H32, 2).toString(16);
		this.error3H32 = new BigInteger(StrUtil.isBlank(error3H32) ? "0" : error3H32, 2).toString(16);
		this.error4H32 = new BigInteger(StrUtil.isBlank(error4H32) ? "0" : error4H32, 2).toString(16);
		this.error5H32 = new BigInteger(StrUtil.isBlank(error5H32) ? "0" : error5H32, 2).toString(16);
		this.alarmL16 = new BigInteger(StrUtil.isBlank(alarmL16) ? "0" : alarmL16, 2).toString(16);
		this.faultH16 = new BigInteger(StrUtil.isBlank(faultH16) ? "0" : faultH16, 2).toString(16);
	}
}
