package com.weihengtech.pojo.dtos.device.config;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "设备配置 Setting 回参")
public class SettingDTO implements Serializable {

	private static final long serialVersionUID = -8259505260574495286L;

	private String ovPwr = "";

	private String delayTime1 = "";

	private String delayTime2 = "";

	private String ovU1 = "";

	private String ovP1 = "";

	private String ovU2 = "";

	private String ovP2 = "";

	private String ovU3 = "";

	private String ovP3 = "";

	private String ovU4 = "";

	private String ovP4 = "";

	private String ofPwr = "";

	private String ofMode = "";

	private String ofFreqStart = "";

	private String ofFreqStop = "";

	private String ofFreqBack = "";

	private String ofRecoverTime = "";

	private String ofDeRatingTime = "";

	private String ofPowerRateLimit = "";

	private String ufPwr = "";

	private String ufMode = "";

	private String ufStart = "";

	private String ufStop = "";

	private String ufBack = "";

	private String ufRecoverTime = "";

	private String ufDeRatingTime = "";

	private String uvPwr = "";

	private String uvU1 = "";

	private String uvP1 = "";

	private String uvU2 = "";

	private String uvP2 = "";

	private String uvU3 = "";

	private String uvP3 = "";

	private String uvU4 = "";

	private String uvP4 = "";

	private String filterTimeConst = "";

	private String overPref = "";

	private String underPref = "";

	public List<List<String>> buildDataList() {
		List<List<String>> resList = new ArrayList<>();
		resList.add(Arrays.asList("Over Frequency Enable", Optional.ofNullable(ofPwr).map(x -> "1".equals(x) ? "Enable" : "Disable").orElse(StrUtil.EMPTY)));
		resList.add(Arrays.asList("FStart", Optional.ofNullable(ofFreqStart).orElse(StrUtil.EMPTY), "Hz"));
		resList.add(Arrays.asList("Drops", Optional.ofNullable(ofFreqStop).map(i -> (int) (Double.parseDouble(i) * 20) + "").orElse(StrUtil.EMPTY), "%"));
		resList.add(Arrays.asList("Pref", Optional.ofNullable(overPref).map(i -> "1".equals(i) ? "PMAX" : "PM").orElse(StrUtil.EMPTY)));
		resList.add(Arrays.asList("Under Frequency Enable", Optional.ofNullable(ufPwr).map(x -> "1".equals(x) ? "Enable" : "Disable").orElse(StrUtil.EMPTY)));
		resList.add(Arrays.asList("FStart", Optional.ofNullable(ufStart).orElse(StrUtil.EMPTY), "Hz"));
		resList.add(Arrays.asList("Drops", Optional.ofNullable(ufStop).map(i -> (int) (Double.parseDouble(i) * 20) + "").orElse(StrUtil.EMPTY), "%"));
		resList.add(Arrays.asList("Pref", Optional.ofNullable(underPref).map(i -> "1".equals(i) ? "PMAX" : "PM").orElse(StrUtil.EMPTY)));
		resList.add(Arrays.asList("Over Voltage Enable", Optional.ofNullable(ovPwr).map(x -> "1".equals(x) ? "Enable" : "Disable").orElse(StrUtil.EMPTY)));
		resList.add(Arrays.asList("U1", Optional.ofNullable(ovU2).orElse(StrUtil.EMPTY), "%Un"));
		resList.add(Arrays.asList("P1", Optional.ofNullable(ovP2).orElse(StrUtil.EMPTY), "%Sn"));
		resList.add(Arrays.asList("U2", Optional.ofNullable(ovU3).orElse(StrUtil.EMPTY), "%Un"));
		resList.add(Arrays.asList("P2", Optional.ofNullable(ovU3).orElse(StrUtil.EMPTY), "%Sn"));
		resList.add(Arrays.asList("U3", Optional.ofNullable(ovU4).orElse(StrUtil.EMPTY), "%Un"));
		resList.add(Arrays.asList("P3", Optional.ofNullable(ovP4).orElse(StrUtil.EMPTY), "%Sn"));
		resList.add(Arrays.asList("Time constant of a first-order filter", Optional.ofNullable(filterTimeConst).orElse(StrUtil.EMPTY), "S"));
		return resList;
	}
}
