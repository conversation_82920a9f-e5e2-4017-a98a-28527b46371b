package com.weihengtech.pojo.dtos.firmware;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "硬件分类厂家映射回参")
public class HardwareCategoryTypeMapDTO implements Serializable {

	private static final long serialVersionUID = -4606574643943213298L;

	private String hardwareCategoryName;

	private List<HardwareType> hardwareTypeList;

	@Getter
	@Setter
	public static class HardwareType {

		private String hardwareTypeId;

		private String hardwareTypeName;
	}
}
