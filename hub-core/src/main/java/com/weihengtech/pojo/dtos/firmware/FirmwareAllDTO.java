package com.weihengtech.pojo.dtos.firmware;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "选择固件包回参")
public class FirmwareAllDTO implements Serializable {

	private static final long serialVersionUID = 6376192409205385246L;

	private String typeName;

	private List<FirmwareInfo> children;

	@Getter
	@Setter
	@ToString
	public static class FirmwareInfo {

		private String firmwareId;

		private String firmwareName;

		private String remarks;

		private int isTop;

	}
}
