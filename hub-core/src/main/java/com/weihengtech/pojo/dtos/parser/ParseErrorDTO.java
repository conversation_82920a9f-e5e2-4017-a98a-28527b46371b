package com.weihengtech.pojo.dtos.parser;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.enums.device.DeviceErrorTypeEnum;
import com.weihengtech.pojo.bos.parser.AbsErrorCollectionBO;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * 异常解析结果集
 *
 * <AUTHOR>
 * @date 2024/4/29 16:16
 * @version 1.0
 */
@Getter
@ToString
public class ParseErrorDTO {

	protected AbsErrorCollectionBO dspDecimal;

	protected AbsErrorCollectionBO bmsDecimal;

	protected AbsErrorCollectionBO armBit;

	protected AbsErrorCollectionBO dspBit1;

	protected AbsErrorCollectionBO dspBit2;

	protected AbsErrorCollectionBO dspBit3;

	protected AbsErrorCollectionBO bmsBitFault;

	protected AbsErrorCollectionBO bmsBitAlarm;

	protected AbsErrorCollectionBO bmsBitError;

	protected AbsErrorCollectionBO bmsBitError2;
	protected AbsErrorCollectionBO bmsBitError3;
	protected AbsErrorCollectionBO bmsBitError4;
	protected AbsErrorCollectionBO bmsBitError5;

	protected AbsErrorCollectionBO bmsHistory;

	public void setDspDecimal(Long dspDecimal) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setDecimalErrorValue(DeviceErrorTypeEnum.DSP_DECIMAL, dspDecimal);
		this.dspDecimal = item;
	}

	public void setBmsDecimal(Long bmsDecimal) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setDecimalErrorValue(DeviceErrorTypeEnum.BMS_DECIMAL, bmsDecimal);
		this.bmsDecimal = item;
	}

	public void setArmBit(String binaryStr) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.ARM, StrUtil.reverse(binaryStr));
		this.armBit = item;
	}

	public void setBmsBitFault(String fault) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.BATTERY_FAULT, StrUtil.reverse(fault));
		this.bmsBitFault = item;
	}

	public void setBmsBitAlarm(String alarm) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.BATTERY_ALARM, StrUtil.reverse(alarm));
		this.bmsBitAlarm = item;
	}

	public void setBmsBitError(String error) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.BATTERY_ERROR, StrUtil.reverse(error));
		this.bmsBitError = item;
	}

	public void setBmsHistory(String error) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		List<String> bitChineseParseList = item.getBitChineseParseList();
		bitChineseParseList.addAll(this.bmsBitAlarm.getBitChineseParseList());
		bitChineseParseList.addAll(this.bmsBitFault.getBitChineseParseList());
		bitChineseParseList.addAll(this.bmsBitError.getBitChineseParseList());
		List<String> bitEnglishParseList = item.getBitEnglishParseList();
		bitEnglishParseList.addAll(this.bmsBitAlarm.getBitEnglishParseList());
		bitEnglishParseList.addAll(this.bmsBitFault.getBitEnglishParseList());
		bitEnglishParseList.addAll(this.bmsBitError.getBitEnglishParseList());

		List<String> bitPosList = item.getBitPosList();
		List<String> bitValueList = item.getBitValueList();
		int size = bmsBitAlarm.getBitPosList().size() + bmsBitFault.getBitPosList().size()
				+ bmsBitError.getBitPosList().size();
		for (int i = 0; i < size; i++) {
			bitPosList.add("Bit" + i);
			if ('1' == error.charAt(i)) {
				bitValueList.add("1");
			} else {
				bitValueList.add("0");
			}
		}
		this.bmsHistory = item;
	}

	public void setBmsBitErrorCatl(String error) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.CATL_BATTERY_ERROR, StrUtil.reverse(error));
		this.bmsBitError = item;
	}

	public void setBmsBitError2(String error2) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.CATL_BATTERY_ERROR_2, StrUtil.reverse(error2));
		this.bmsBitError2 = item;
	}

	public void setBmsBitError3(String error3) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.CATL_BATTERY_ERROR_3, StrUtil.reverse(error3));
		this.bmsBitError3 = item;
	}

	public void setBmsBitError4(String error4) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.CATL_BATTERY_ERROR_4, StrUtil.reverse(error4));
		this.bmsBitError4 = item;
	}

	public void setBmsBitError5(String error5) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.CATL_BATTERY_ERROR_5, StrUtil.reverse(error5));
		this.bmsBitError5 = item;
	}

	public void setDspBit1(String binaryStr) {}

	public void setDspBit2(String binaryStr) {}

	public void setDspBit3(String binaryStr) {}



	// ----------------------------------------------------设备信息页异常解析----------------------------------------------

	public void setDspBit1Value(String binaryStr) {}

	public void setDspBit2Value(String binaryStr) {}

	public void setDspBit3Value(String binaryStr) {}

	public void setArmBitValue(String binaryStr) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setValue(DeviceErrorTypeEnum.ARM, binaryStr);
		this.armBit = item;
	}

	public void setBmsBitErrorValue(String errorH32) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setValue(DeviceErrorTypeEnum.BATTERY_ERROR, errorH32);
		this.bmsBitError = item;
	}

	public void setBmsBitErrorValueCatl(String errorH32) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setValue(DeviceErrorTypeEnum.CATL_BATTERY_ERROR, errorH32);
		this.bmsBitError = item;
	}

	public void setBmsBitError2Value(String error2H32) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.CATL_BATTERY_ERROR_2, error2H32);
		this.bmsBitError2 = item;
	}

	public void setBmsBitError3Value(String error3H32) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.CATL_BATTERY_ERROR_3, error3H32);
		this.bmsBitError2 = item;
	}

	public void setBmsBitError4Value(String error4H32) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.CATL_BATTERY_ERROR_4, error4H32);
		this.bmsBitError2 = item;
	}

	public void setBmsBitError5Value(String error5H32) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.CATL_BATTERY_ERROR_5, error5H32);
		this.bmsBitError2 = item;
	}

	public void setBmsBitAlarmValue(String alarmL16) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setValue(DeviceErrorTypeEnum.BATTERY_ALARM, alarmL16);
		this.bmsBitAlarm = item;
	}

	public void setBmsBitFaultValue(String faultH16) {
		AbsErrorCollectionBO item = new AbsErrorCollectionBO();
		item.setValue(DeviceErrorTypeEnum.BATTERY_FAULT, faultH16);
		this.bmsBitFault = item;
	}
}
