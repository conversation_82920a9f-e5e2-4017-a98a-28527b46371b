package com.weihengtech.pojo.dtos.parser;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.enums.device.DeviceErrorTypeEnum;
import com.weihengtech.pojo.bos.parser.ThreeErrorCollectionBO;
import lombok.Getter;
import lombok.ToString;

/**
 * 异常解析结果集-三相机实现
 *
 * <AUTHOR>
 * @date 2024/4/29 16:16
 * @version 1.0
 */
@Getter
@ToString
public class ThreeParseErrorDTO extends ParseErrorDTO{

	@Override
	public void setDspBit1(String binaryStr) {
		ThreeErrorCollectionBO item = new ThreeErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.DSP_1, StrUtil.reverse(binaryStr));
		this.dspBit1 = item;
	}

	@Override
	public void setDspBit2(String binaryStr) {
		ThreeErrorCollectionBO item = new ThreeErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.DSP_2, StrUtil.reverse(binaryStr));
		this.dspBit2 = item;
	}

	@Override
	public void setDspBit3(String binaryStr) {
		ThreeErrorCollectionBO item = new ThreeErrorCollectionBO();
		item.setErrorValue(DeviceErrorTypeEnum.DSP_3, StrUtil.reverse(binaryStr));
		this.dspBit3 = item;
	}

	@Override
	public void setDspBit1Value(String binaryStr) {
		ThreeErrorCollectionBO item = new ThreeErrorCollectionBO();
		item.setValue(DeviceErrorTypeEnum.DSP_1, binaryStr);
		this.dspBit1 = item;
	}

	@Override
	public void setDspBit2Value(String binaryStr) {
		ThreeErrorCollectionBO item = new ThreeErrorCollectionBO();
		item.setValue(DeviceErrorTypeEnum.DSP_2, binaryStr);
		this.dspBit2 = item;
	}

	@Override
	public void setDspBit3Value(String binaryStr) {
		ThreeErrorCollectionBO item = new ThreeErrorCollectionBO();
		item.setValue(DeviceErrorTypeEnum.DSP_3, binaryStr);
		this.dspBit3 = item;
	}
}
