package com.weihengtech.pojo.dtos.device.info;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "esp参数回参")
public class EspParameterDTO implements Serializable {

	private static final long serialVersionUID = 2119144808808006779L;

	private String voltageR;

	private String voltageS;

	private String voltageT;

	private String voltageL1;

	private String voltageL2;

	private String currentR;

	private String currentS;

	private String currentT;

	private String currentL1;

	private String currentL2;

	private String freqR;

	private String freqS;

	private String freqT;

	private String qR;

	private String qS;

	private String qT;

	private String qL1;

	private String qL2;

	private String pR;

	private String pS;

	private String pT;

	private String pL1;

	private String pL2;
}
