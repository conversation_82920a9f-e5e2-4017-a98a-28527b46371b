package com.weihengtech.pojo.dtos.device;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备sn模糊查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/1 9:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DynamicDesignDTO {

    /**
     * 最大设计功率
     */
    private Integer designMaxPower;

    /**
     * 北美机：Designed max apparent power
     */
    private Integer maxVa;

    /**
     * 北美机：Designed max reactive power
     */
    private Integer maxVar;

    /**
     * 北美机：Designed max negative reactive power
     */
    private Integer maxVarNeg;

    /**
     * 北美机：Designed max energy storage capacity
     */
    private Integer maxWh;

}
