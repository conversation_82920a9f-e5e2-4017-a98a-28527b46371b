package com.weihengtech.pojo.dtos.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "国家信息回参")
public class EcosCountryDTO implements Serializable {

	private static final long serialVersionUID = -537363082984773817L;

	@ApiModelProperty(name = "id", value = "国家id")
	private Integer id;

	@ApiModelProperty(name = "cnName", value = "中文国家名称")
	private String cnName;

	@ApiModelProperty(name = "enName", value = "英文国家名")
	private String enName;

	@ApiModelProperty(name = "name", value = "国际化名称")
	private String name;
}
