package com.weihengtech.pojo.dtos.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "设备维护信息")
public class EcosDeviceMaintenanceInfoDto {

	@ApiModelProperty(value = "维护记录id")
	private String id;
	@ApiModelProperty(value = "维护记录")
	private String record;
}
