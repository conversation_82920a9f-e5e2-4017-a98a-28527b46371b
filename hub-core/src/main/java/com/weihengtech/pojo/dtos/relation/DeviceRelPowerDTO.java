package com.weihengtech.pojo.dtos.relation;

import com.weihengtech.pojo.dtos.device.info.PowerFlowDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/10 9:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeviceRelPowerDTO extends PowerFlowDTO {

    @ApiModelProperty("设备Id")
    private String deviceId;

    @ApiModelProperty("设备sn")
    private String deviceSn;

    @ApiModelProperty("wifi sn")
    private String wifiSn;

    @ApiModelProperty
    private Boolean isMaster;
}
