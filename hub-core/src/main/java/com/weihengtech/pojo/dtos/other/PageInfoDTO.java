package com.weihengtech.pojo.dtos.other;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "分页数据回参")
public class PageInfoDTO<T> implements Serializable {

	private static final long serialVersionUID = -7348297409631081980L;

	private Integer totalPages = 0;

	private Long totalCount = 0L;

	private List<T> data = new ArrayList<>();

	public PageInfoDTO(PageInfo<T> pageInfo) {
		this(pageInfo.getPages(), pageInfo.getTotal(), pageInfo.getList());
	}
}
