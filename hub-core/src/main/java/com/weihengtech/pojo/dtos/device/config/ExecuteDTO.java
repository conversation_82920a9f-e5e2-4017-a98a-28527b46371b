package com.weihengtech.pojo.dtos.device.config;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "设备配置Execute页回参")
public class ExecuteDTO implements Serializable {

	private static final long serialVersionUID = -7467940871497268499L;

	private String remoteOff = "";

	private String ateMode = "";

	private String overload = "";

	private String upsEnable = "";

	private String vpvStart = "";

	private String ratedPower = "";

	private String pvConnectMode = "";

	private String gridLossCheckType = "";

	private String externalSignal = "";

	private String localCmd = "";

	private String autoTestEnable = "";

	private String accessGridType = "";
}
