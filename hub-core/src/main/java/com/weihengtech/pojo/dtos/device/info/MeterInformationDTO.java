package com.weihengtech.pojo.dtos.device.info;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "仪表信息")
public class MeterInformationDTO implements Serializable {

	private static final long serialVersionUID = -6147682037994246888L;

	private String vacR;

	private String vacS;

	private String vacT;

	private String vacL1;

	private String vacL2;

	private String iacR;

	private String iacS;

	private String iacT;

	private String iacL1;

	private String iacL2;

	private String freqR;

	private String freqS;

	private String freqT;

	private String freqL1;

	private String freqL2;

	private String pacR;

	private String pacS;

	private String pacT;

	private String pacL1;

	private String pacL2;

	private String activePower;

	private String reactivePower;

	private String pf;
}
