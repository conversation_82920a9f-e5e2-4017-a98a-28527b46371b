package com.weihengtech.pojo.dtos.firmware;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "固件包分页回参")
public class FirmwarePageDTO implements Serializable {

	private static final long serialVersionUID = -6836666307327466896L;

	private String firmwareId;

	private String firmwareName;

	private List<String> hardwareTypeList;

	private String uploadTime;

	private String operatorId;

	private String remarks;

	private String model;

	private int isTop;
}
