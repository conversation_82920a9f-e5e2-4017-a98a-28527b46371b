package com.weihengtech.pojo.dtos.device.config;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "设备配置 Active Limit 回参")
public class ActiveLimitDTO implements Serializable {

	private static final long serialVersionUID = -5208067645101191055L;

	private String pwrManager = "";

	private String pwrCtrlMode = "";

	private String pwrLimitPercent = "";

	private String responseTime = "";
}
