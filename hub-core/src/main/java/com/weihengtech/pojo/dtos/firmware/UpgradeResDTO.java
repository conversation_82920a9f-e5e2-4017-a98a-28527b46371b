package com.weihengtech.pojo.dtos.firmware;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 升级结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/25 17:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpgradeResDTO {

    @ApiModelProperty("请求成功的设备")
    private List<String> successList;

    @ApiModelProperty("非法的设备")
    private List<String> invalidList;

    @ApiModelProperty("不符合条件的设备")
    private List<String> nonCompliantList;

    @ApiModelProperty("需要升级棒子的设备")
    private List<String> needUpgradeGwyList;

    @ApiModelProperty("已经是最新版本的设备")
    private List<String> latestVersionList;
}
