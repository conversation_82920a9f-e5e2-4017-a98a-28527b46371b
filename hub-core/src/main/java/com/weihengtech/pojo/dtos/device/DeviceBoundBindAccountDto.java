package com.weihengtech.pojo.dtos.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "设备绑定账号回参")
public class DeviceBoundBindAccountDto {

	@ApiModelProperty(value = "已经被绑定的sn")
	private List<String> bindedList = new ArrayList<>();

	@ApiModelProperty(value = "不存在的sn")
	private List<String> notExistList = new ArrayList<>();

	@ApiModelProperty(value = "非法的sn")
	private List<String> invalidList = new ArrayList<>();
}
