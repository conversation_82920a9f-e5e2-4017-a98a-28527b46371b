package com.weihengtech.pojo.dtos.ecos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/11 15:57
 */
@Data
public class InstallBoundDTO {

    @ApiModelProperty("安装商id")
    @NotNull(message = "安装商id不能为空")
    private Long userId;

    @ApiModelProperty("设备id")
    @NotNull(message = "设备id不能为空")
    private Long deviceId;

    @ApiModelProperty("保留时长：-1：不保留，999：永久")
    @NotNull(message = "保留时长不能为空")
    private Integer saveDeviceTime;


}
