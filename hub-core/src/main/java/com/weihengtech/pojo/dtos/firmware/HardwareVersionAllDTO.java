package com.weihengtech.pojo.dtos.firmware;

import com.weihengtech.pojo.bos.firmware.VersionBO;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "添加软件版本页面所有硬件版本信息回参")
public class HardwareVersionAllDTO implements Serializable {

	private static final long serialVersionUID = 2292776578716261490L;

	private String softwareVersionName;

	List<VersionBO> hardwareVersionList;
}
