package com.weihengtech.pojo.dtos.device;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: jiahao.jin
 * @create: 2025-07-09 15:48
 * @description: 设备状态和SOC
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "设备状态和SOC对象")
public class DeviceStatusAndSocDTO {
    private String deviceName;
    private Integer state;
    private BigDecimal batSoc;
}
