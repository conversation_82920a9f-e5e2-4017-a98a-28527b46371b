package com.weihengtech.pojo.dtos.device.info;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.enums.device.DeviceErrorTypeEnum;
import com.weihengtech.pojo.bos.parser.AbsErrorCollectionBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class WeiHengBatteryInfoDTO {

	private String brand = "";
	private String model = "";
	private String sn = "";
	private String hardwareVersion = "";
	private String hardwareVersionCode = "";
	private String internalBatteryPackType = "";
	private String softwareVersion = "";
	private String softwareVersionCode = "";
	private String systemState = "";
	private String systemUptime = "";
	private String accumulativeInputEnergy = "";
	private String accumulativeOutputEnergy = "";
	private String accumulativeInputCharge = "";
	private String accumulativeOutputCharge = "";
	private String internalTemperature = "";
	private String soc = "";
	private String soh = "";
	private String sohReady = "";
	private String fullChargeCapacity = "";
	private String remainingCapacity = "";
	private String availableEnergy = "";
	// todo 协议修改读取位到143
	private String contactorOcCount = "";
	private String resistorUpdateCount = "";
	private String eocStatus = "";
	private String cycleCount;
	private String requireForceCharge = "";
	private String batteryInSerial = "";
	private String temperatureChannels = "";
	private String voltage = "";
	private String current = "";
	private String chargeCurrentLimit = "";
	private String dischargeCurrentLimit = "";
	private String chargeVoltageLimit = "";
	private String dischargeVoltageLimit = "";
	private String highestCellVoltage = "";
	private String highestCellVoltageId = "";
	private String lowestCellVoltage = "";
	private String lowestCellVoltageId = "";
	private String highestCellTemperature = "";
	private String highestCellTemperatureId = "";
	private String lowestCellTemperature = "";
	private String lowestCellTemperatureId = "";
	private String cellFaultBits = "";
	private String z100SoftwareVersion = "";

	private String currentCode;
	private String faultSaveCode;
	private AbsErrorCollectionBO currentCodeCollection;
	private AbsErrorCollectionBO faultSaveCodeCollection;

	public void setCurrentCode(String currentCode) {
		this.currentCode = new BigInteger(StrUtil.isBlank(currentCode) ? "0" : currentCode, 2).toString(16);
		this.currentCodeCollection = buildErrorCollection(currentCode);
	}

	public void setFaultSaveCode(String faultSaveCode) {
		this.faultSaveCode = new BigInteger(StrUtil.isBlank(faultSaveCode) ? "0" : faultSaveCode, 2).toString(16);
		this.faultSaveCodeCollection = buildErrorCollection(faultSaveCode);
	}

	public void setCellFaultBits(String cellFaultBits) {
		this.cellFaultBits = new BigInteger(StrUtil.isBlank(cellFaultBits) ? "0" : cellFaultBits, 2).toString(16);
	}

	private AbsErrorCollectionBO buildErrorCollection(String binaryStr) {
		AbsErrorCollectionBO errorCollectionBO = new AbsErrorCollectionBO();
		errorCollectionBO.setValue(DeviceErrorTypeEnum.WEIHENG_BATTERY, binaryStr);
		return errorCollectionBO;
	}
}
