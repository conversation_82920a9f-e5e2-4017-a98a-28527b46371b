package com.weihengtech.pojo.dtos.device.config;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "能源配置")
public class EnergySettingDTO implements Serializable {

	private static final long serialVersionUID = -5104237431963289653L;

	private String eTotalPvToGrid = "";

	private String eTotalToGrid = "";

	private String eTotalFromGrid = "";

	private String eTotalPv = "";

	private String eTotalCharge = "";

	private String eTotalDischarge = "";

	private String eTotalEps = "";

	private String hTotal = "";

	private String hTotalGridMode = "";

	private String hTotalEpsMode = "";

	private String eTodayToGrid = "";

	private String eTodayFromGrid = "";

	private String eTodayPv = "";

	private String eTodayCharge = "";

	private String eTodayDischarge = "";

	private String eTodayEps = "";
}
