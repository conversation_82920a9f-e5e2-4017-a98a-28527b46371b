package com.weihengtech.pojo.dtos.firmware;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "硬件版本分页回参")
public class HardwareVersionPageDTO implements Serializable {

	private static final long serialVersionUID = 2528619546702713076L;

	private String hardwareId;

	private String hardwareCategory;

	private String hardwareType;

	private String hardwareVersion;

	private List<String> softwareVersionList;

	private String remarks;

	private String model;
}
