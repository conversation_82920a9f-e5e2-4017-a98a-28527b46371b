package com.weihengtech.pojo.dtos.device.config;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "设备配置 Auto Test 回参")
public class AutoTestDTO {

	private String tripLimitSet59S1 = "";
	private String tripLimitSet59S2 = "";
	private String tripLimitSet27S1 = "";
	private String tripLimitSet27S2 = "";

	private String tripTimeSet59S1 = "602";
	private String tripTimeSet59S2 = "";
	private String tripTimeSet27S1 = "";
	private String tripTimeSet27S2 = "";

	private String testResult59S1 = "";
	private String testResult59S2 = "";
	private String testResult27S1 = "";
	private String testResult27S2 = "";

	private String vac59S1 = "";
	private String vac59S2 = "";
	private String vac27S1 = "";
	private String vac27S2 = "";

	private String vacOff59S1 = "";
	private String vacOff59S2 = "";
	private String vacOff27S1 = "";
	private String vacOff27S2 = "";

	private String timeOff59S1 = "";
	private String timeOff59S2 = "";
	private String timeOff27S1 = "";
	private String timeOff27S2 = "";

	private String tripLimitSet81GtS1 = "";
	private String tripLimitSet81LtS1 = "";
	private String tripLimitSet81GtS2 = "";
	private String tripLimitSet81LtS2 = "";

	private String tripTimeSet81GtS1 = "";
	private String tripTimeSet81LtS1 = "";
	private String tripTimeSet81GtS2 = "";
	private String tripTimeSet81LtS2 = "";

	private String testResult81GtS1 = "";
	private String testResult81LtS1 = "";
	private String testResult81GtS2 = "";
	private String testResult81LtS2 = "";

	private String fac81GtS1 = "";
	private String fac81LtS1 = "";
	private String fac81GtS2 = "";
	private String fac81LtS2 = "";

	private String facOff81GtS1 = "";
	private String facOff81LtS1 = "";
	private String facOff81GtS2 = "";
	private String facOff81LtS2 = "";

	private String timeOff81GtS1 = "";
	private String timeOff81LtS1 = "";
	private String timeOff81GtS2 = "";
	private String timeOff81LtS2 = "";

	private String externalSignal = "";
	private String localCmd = "";
	private String autoTestEnable = "";
}
