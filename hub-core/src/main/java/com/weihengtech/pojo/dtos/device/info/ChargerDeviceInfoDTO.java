package com.weihengtech.pojo.dtos.device.info;

import com.weihengtech.auth.dto.ResourceResDTO;
import com.weihengtech.enums.charger.ChargerTypeModelEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 充电桩设备信息
 *
 * <AUTHOR>
 * @date 2024/1/25 10:15
 * @version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "设备信息回参")
public class ChargerDeviceInfoDTO implements Serializable {

	private static final long serialVersionUID = -6849632513211558918L;

	@ApiModelProperty("设备sn")
	private String deviceSn;

	@ApiModelProperty("设备系列")
	private String resourceSeries;

	@ApiModelProperty("设备类型")
	private String resourceType;

	@ApiModelProperty("设备型号")
	private String deviceModel;

	@ApiModelProperty("额定功率")
	private String ratedPower = "";

	@ApiModelProperty("额定电流")
	private String ratedCurrent = "";

	@ApiModelProperty("电压上限")
	private String voltageUp = "";

	@ApiModelProperty("电压下限")
	private String voltageDown = "";

	@ApiModelProperty("首次安装时间")
	private Long setupTime;

	@ApiModelProperty("更新时间")
	private String updateTime;

	public static ChargerDeviceInfoDTO buildParam(ResourceResDTO resource, DeviceListDO deviceList,
												  ChargerTypeModelEnum modelEnum) {
		ChargerDeviceInfoDTO item = ChargerDeviceInfoDTO.builder()
				.deviceSn(deviceList.getDeviceSn())
				.resourceSeries(resource.getCategoryName())
				.resourceType(resource.getTypeName())
				.deviceModel(deviceList.getDeviceModel())
				.build();
		if (modelEnum != null) {
			item.setRatedPower(modelEnum.getRatedPower());
			item.setRatedCurrent(modelEnum.getRatedCurrent());
			item.setVoltageUp(modelEnum.getVoltageUp());
			item.setVoltageDown(modelEnum.getVoltageDown());
		}
		return item;
	}
}
