package com.weihengtech.pojo.dtos.firmware;

import com.weihengtech.pojo.bos.firmware.VersionBO;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "硬件版本添加页面软件版本回参")
public class SoftwareVersionAllDTO implements Serializable {

	private static final long serialVersionUID = -4080559360538785491L;

	private List<VersionBO> hardwareList;

	private String hardwareVersionName;
}
