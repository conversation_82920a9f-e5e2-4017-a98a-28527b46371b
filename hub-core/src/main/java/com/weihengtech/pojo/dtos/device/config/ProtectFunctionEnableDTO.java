package com.weihengtech.pojo.dtos.device.config;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "设备配置 ProjectFunctionEnable")
public class ProtectFunctionEnableDTO implements Serializable {

	private static final long serialVersionUID = -875187950451993209L;

	private String funEnHex = "";

	private String isoLimit = "";

	private String dciLimit = "";

	private String gfcLimit = "";

	private String vpvHighLimit = "";

	private String vpvLowLimit = "";

	private String maxFeedInSoftEn = "";

	private String maxFeedInHardEn = "";

	private String maxFeedInSoftLimit = "";

	private String maxFeedInHardLimit = "";

	private String overTemper = "";

	private String gfciEnable = "";

	private String isoEnable = "";

	private String dciEnable = "";

	private String lvrtEnable = "";

	private String gridVoltProject = "";

	private String gridFreqProject = "";

	private String isLandEnable = "";

	private String fanEnable = "";

	private String shadowMppt = "";

	private String minLoad10 = "";

	private String overFreqDerate = "";

	private String grid110Per = "";

	private String overPowerU = "";

	private String buzzerEnable = "";

	private String underFreqDerate = "";

	private String vppWorkMode = "";

	private String meterEnable = "";

	private String meterType = "";

	private String feedPhase = "";
	
	private String parallelWorkMode = "";

	private String ParallelNum = "";

	private String peChk = "";

	private String gridNChk = "";

	private String funcRstAfci = "";

	private String funcEnAfci = "";

	private String funcEnFrt = "";

	private String houseSideGridLimit = "";

	private String machineSideGridLimit = "";

	private String epsParallel = "";
}
