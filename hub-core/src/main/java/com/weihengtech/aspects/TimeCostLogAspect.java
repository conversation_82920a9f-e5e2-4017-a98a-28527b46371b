package com.weihengtech.aspects;

import cn.hutool.aop.aspects.SimpleAspect;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Console;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 */
public class TimeCostLogAspect extends SimpleAspect {

	private final TimeInterval timeInterval = new TimeInterval();

	@Override
	public boolean before(Object target, Method method, Object[] args) {
		Console.log("Method [{}.{}] begin execute", target.getClass().getName(), method.getName());
		timeInterval.start();
		return true;
	}

	@Override
	public boolean after(Object target, Method method, Object[] args, Object returnVal) {
		Console.log("Method [{}.{}] execute spend [{}]ms", target.getClass().getName(), method.getName(),
				timeInterval.intervalMs()
		);
		return true;
	}
}
