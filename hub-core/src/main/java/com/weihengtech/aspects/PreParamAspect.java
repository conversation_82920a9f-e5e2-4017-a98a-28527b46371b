package com.weihengtech.aspects;

import cn.hutool.aop.aspects.SimpleAspect;
import cn.hutool.extra.spring.SpringUtil;
import com.weihengtech.handlers.IParamHandler;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PreParamAspect extends SimpleAspect {

	@Override
	public boolean before(Object target, Method method, Object[] args) {
		Map<String, IParamHandler> paramHandlerMap = SpringUtil.getBeansOfType(IParamHandler.class);
		for (IParamHandler handler : paramHandlerMap.values()) {
			handler.process(args);
		}
		return super.before(target, method, args);
	}
}
