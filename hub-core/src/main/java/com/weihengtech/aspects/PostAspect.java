package com.weihengtech.aspects;

import cn.hutool.aop.aspects.SimpleAspect;
import cn.hutool.extra.spring.SpringUtil;
import com.weihengtech.handlers.IPostHandler;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PostAspect extends SimpleAspect {

	@Override
	public boolean after(Object target, Method method, Object[] args, Object returnVal) {
		Map<String, IPostHandler> postHandlerMap = SpringUtil.getBeansOfType(IPostHandler.class);
		for (IPostHandler postHandler : postHandlerMap.values()) {
			postHandler.process(args);
		}
		return true;
	}
}
