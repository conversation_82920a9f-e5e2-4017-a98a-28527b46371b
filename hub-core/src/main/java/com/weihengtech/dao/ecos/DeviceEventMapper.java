package com.weihengtech.dao.ecos;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.weihengtech.pojo.bos.ecos.DeviceEventPageBO;
import com.weihengtech.pojo.vos.ecos.DeviceEventVO;
import com.weihengtech.pojo.vos.tuya.TuyaDeviceEventQueryVo;
import com.weihengtech.pojo.vos.tuya.TuyaDeviceEventVo;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DS("ecos-event")
public interface DeviceEventMapper {

	/**
	 * 条件查询
	 *
	 * @param deviceEventVO 条件入参
	 * @return 查询列表
	 */
	List<DeviceEventPageBO> selectCondition(DeviceEventVO deviceEventVO);

	/**
	 * 统计level信息
	 *
	 * @param english        level等级
	 * @param deviceNameList 设备sn列
	 * @return 统计数量
	 */
	Integer selectLevelCount(@Param("english") String english, @Param("deviceNameList") List<String> deviceNameList);

	List<DeviceEventPageBO> selectConditionAgentAndEngineer(
			DeviceEventVO deviceEventVO,
			@Param("deviceNameList") List<String> deviceNameList
	);

	void save(TuyaDeviceEventVo tuyaDeviceEventVo);

	DeviceEventPageBO selectOne(TuyaDeviceEventQueryVo tuyaDeviceEventQueryVo);
}
