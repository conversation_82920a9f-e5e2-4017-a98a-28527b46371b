package com.weihengtech.dao.other;

import com.aliyun.oss.OSS;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
public class OssMapper {

	@Value("${custom.ali.oss.bucket}")
	private String bucket;

	@Resource
	private OSS ossClient;

	public void putObject(String fileName, byte[] bytes) {
		try {
			ossClient.putObject(bucket, fileName, new ByteArrayInputStream(bytes));
		} catch (Exception e) {
			log.warn("oss putObject error: {}", e.getMessage());
			throw new CustomException(ExceptionEnum.FILE_UPLOAD_ERROR);
		}
	}

	public void putFile(String bucket, String fileName, byte[] bytes) {
		try {
			ossClient.putObject(bucket, fileName, new ByteArrayInputStream(bytes));
		} catch (Exception e) {
			log.warn("oss putFile error: {}", e.getMessage());
			throw new CustomException(ExceptionEnum.FILE_UPLOAD_ERROR);
		}
	}
}
