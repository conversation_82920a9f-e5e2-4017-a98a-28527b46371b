package com.weihengtech.dao.other;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.weihengtech.pojo.bos.device.DeviceListPageBo;
import com.weihengtech.pojo.dos.device.DeviceSpeedUpDo;
import com.weihengtech.pojo.dos.firmware.FirmwareUpgradeRecordDO;
import com.weihengtech.pojo.dtos.device.DeviceLikeSnDTO;
import com.weihengtech.pojo.dtos.device.DeviceListPageDTO;
import com.weihengtech.pojo.dtos.device.DeviceStatusDTO;
import com.weihengtech.pojo.dtos.device.FocusDevicePageDto;
import com.weihengtech.pojo.vos.firmware.FirmwareBatchUpgradeDevicePageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DS("ecos-local")
public interface SqlMapper {

	List<String> queryDeviceSnByType(@Param("type") int type);

	List<DeviceListPageDTO> queryDeviceInHomePage(DeviceListPageBo deviceListPageBo);

	List<DeviceListPageDTO> queryFirmwareBatchUpgradeDeviceList(FirmwareBatchUpgradeDevicePageVo firmwareBatchUpgradeDevicePageVo);

	List<DeviceStatusDTO> queryDeviceStatus(@Param("deviceIdList") List<Long> deviceIdList);

	List<String> queryDeviceByLikeWifiSn(@Param("prop")  String prop, @Param("deviceIdList") List<Long> deviceIdList);

	List<FirmwareUpgradeRecordDO> queryCurrentToDoUpgradeDevice(@Param("deviceIdList") List<Long> deviceIdList);

    List<FocusDevicePageDto> queryFocusDevice(@Param("userId") Long userId, @Param("deviceIdList") List<Long> deviceIdList);

	List<DeviceSpeedUpDo> queryDeviceSpeedupList(@Param("deviceIdList") List<Long> deviceIdList);
}
