package com.weihengtech.dao.systeminfo;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.weihengtech.pojo.dos.systeminfo.SystemDeviceRelDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 系统关联设备 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@DS("ecos-local")
public interface SystemDeviceRelMapper extends BaseMapper<SystemDeviceRelDO> {

    /**
     * 安装商安装的系统对应的设备关系id
     *
     * @param installerId 安装商用户id
     * @param deviceId 设备id
     * @return x
     */
    List<Long> getRelByUserAndDevice(@Param("installerId") Long installerId,
                                     @Param("deviceId") Long deviceId);

    /**
     * 安装商安装的系统对应的设备关系id
     *
     * @param systemId 系统id
     * @return 设备详情
     */
    List<SystemDeviceRelDO> listBySystemId(@Param("systemId") Long systemId);
}
