package com.weihengtech.dao.systeminfo;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.weihengtech.pojo.dos.systeminfo.SystemInfoDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.weihengtech.pojo.vos.systeminfo.SystemInfoQueryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 系统信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@DS("ecos-local")
public interface SystemInfoMapper extends BaseMapper<SystemInfoDO> {

    /**
     * 查询系统
     *
     * @param installerId 安装商id
     * @param deviceId 设备id
     * @return 系统
     */
    List<SystemInfoDO> getSystemByUserAndDevice(@Param("installerId") Long installerId,
                                          @Param("deviceId") Long deviceId);

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return
     */
    SystemInfoDO getById(@Param("id") Long id);

    /**
     * 列表查询
     * @param queryParam 查询条件
     * @return
     */
    List<SystemInfoDO> listByCondition(@Param("item") SystemInfoQueryVO queryParam);
}
