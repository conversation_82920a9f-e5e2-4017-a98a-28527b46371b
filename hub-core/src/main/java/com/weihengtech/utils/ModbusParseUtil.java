package com.weihengtech.utils;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.enums.device.DeviceInfoEncodeTypeEnum;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ModbusParseUtil {

	public static void parseMultiData(
			List<Integer> integerList, List<Map<String, String>> configList,
			List<Map<String, String>> result
	) {
//		List<Integer> integerList = objListToIntegerList(jsonObject.get("result"));
		int start = Integer.parseInt(configList.get(0).get("start"));
		for (Map<String, String> config : configList) {
			try {
				List<Integer> fitList = ListUtil.sub(integerList, Integer.parseInt(config.get("start")) - start,
						Integer.parseInt(config.get("start")) - start + Integer.parseInt(config.get("len"))
				);
				config.put("decode", InterfaceUtil.processConfigResult(
						config,
						parseForDeviceInfoTypeEnum(infoTypeToTypeEnum(config.get("type")), fitList)
				));
				result.add(config);
			} catch (Exception e) {
				config.put("decode", "");
			}
		}
	}

	public static String parseData(DeviceInfoEncodeTypeEnum deviceInfoEncodeTypeEnum, List<Integer> integerList,
								   Float multiply
	) {
		String decodeStr = parseForDeviceInfoTypeEnum(deviceInfoEncodeTypeEnum, integerList);
		try {
			Double decodeNum = Double.parseDouble(decodeStr);
			return String.valueOf(BigDecimal.valueOf(decodeNum / multiply));
		} catch (Exception e) {
			return decodeStr;
		}
	}

	public static String parseData(
			DeviceInfoEncodeTypeEnum deviceInfoEncodeTypeEnum, JSONObject jsonObject,
			Float multiply
	) {
		String decodeStr = parseData(deviceInfoEncodeTypeEnum, jsonObject);
		try {
			Double decodeNum = Double.parseDouble(decodeStr);
			return String.valueOf(BigDecimal.valueOf(decodeNum / multiply));
		} catch (Exception e) {
			return decodeStr;
		}
	}

	public static String parseData(DeviceInfoEncodeTypeEnum deviceInfoEncodeTypeEnum, JSONObject jsonObject) {
		if (ResultUtil.isRequestSuccess(jsonObject)) {
			Object result = jsonObject.get("result");
			try {
				List<Integer> intList = objListToIntegerList(result);
				if (intList.size() != 0) {
					return parseForDeviceInfoTypeEnum(deviceInfoEncodeTypeEnum, intList);
				}
			} catch (Exception e) {
				return "";
			}
		}
		return "";
	}

	public static String parseForDeviceInfoTypeEnum(
			DeviceInfoEncodeTypeEnum deviceInfoEncodeTypeEnum,
			List<Integer> intList
	) {
		switch (deviceInfoEncodeTypeEnum) {
			case S16:
				return parseS16(intList);
			case S32:
				return parseS32(intList);
			case U16:
				return parseU16(intList);
			case U32:
				return parseU32(intList);
			case U32R:
				return parseU32R(intList);
			case BIT:
				return parseBit(intList);
			default:
				return parseAscii(intList);
		}
	}

	public static DeviceInfoEncodeTypeEnum infoTypeToTypeEnum(String type) {
		for (DeviceInfoEncodeTypeEnum typeEnum : DeviceInfoEncodeTypeEnum.values()) {
			if (typeEnum.name().equals(type)) {
				return typeEnum;
			}
		}
		return DeviceInfoEncodeTypeEnum.NONE;
	}

	public static List<Integer> objListToIntegerList(Object obj) {
		List<Integer> intList = new ArrayList<>();
		if (obj instanceof List<?>) {
			List<?> objList = (List<?>) obj;
			for (Object o : objList) {
				intList.add((Integer) o);
			}
		}
		return intList;
	}

	public static String parseAscii(List<Integer> list) {
		List<Integer> parseAsciiList = new ArrayList<>();
		for (Integer l : list) {
			parseAsciiList.add(l / 256);
			parseAsciiList.add(l % 256);
		}
		StringBuilder stringBuilder = new StringBuilder();
		for (int num : parseAsciiList) {
			if (0 == num) {
				break;
			}
			stringBuilder.append((char) num);
		}
		return stringBuilder.toString();
	}

	public static String parseU16(List<Integer> list) {
		return new DecimalFormat().format(list.get(0)).replaceAll(",", "");
	}

	public static String parseU32(List<Integer> list) {
		double x = (long) list.get(0) << 16;
		double y = (long) list.get(1);
		return new DecimalFormat().format(x + y).replaceAll(",", "");
	}

	public static String parseU32R(List<Integer> list) {
		long x = (long) list.get(0);
		long y = (long) list.get(1) << 16;
		return new DecimalFormat().format(y + x).replaceAll(",", "");
	}

	public static String parseS16(List<Integer> list) {
		int x = list.get(0);
		return new DecimalFormat().format(x >= (1 << 15) ? x - (1 << 16) : x).replaceAll(",", "");
	}

	public static String parseS32(List<Integer> list) {
		long x = (long) list.get(0) << 16;
		long y = (long) list.get(1);
		long f = x + y;
		return new DecimalFormat().format((f >= (1L << 31)) ? (f - (1L << 32)) : f).replaceAll(",", "");
	}

	public static String parseBit(List<Integer> list) {
		StringBuilder stringBuilder = new StringBuilder();
		for (Integer integer : list) {
			String xBinaryStr = NumberUtil.getBinaryStr(integer);
			stringBuilder.append(StrUtil.fillBefore(xBinaryStr, '0', 16));
		}
		return stringBuilder.reverse().toString();
	}

	public static List<Integer> encodeAscii(String data, int len) {
		List<Integer> list = new ArrayList<>();
		int length = data.length();
		int count = 2;
		for (int i = 0; i < length; i += count) {
			int x = data.charAt(i) * 256;
			int y = 0;
			if (i <= length - 2) {
				y = data.charAt(i + 1);
			}
			list.add(x + y);
		}
		for (int i = list.size(); i < len; i++) {
			list.add(0);
		}
		return list;
	}

	public static List<Integer> encodeU16(String data, Float multiply) {
		return strToDoubleList(data).stream().map(i -> (int) (i * multiply.doubleValue())).collect(Collectors.toList());
	}

	public static List<Integer> encodeU32(String data, Float multiply) {
		List<Double> doubleList = strToDoubleList(data);
		double r = doubleList.get(0) * multiply.doubleValue();
		return doubleToIntList(r);
	}

	public static List<Integer> encodeS16(String data, Float multiply) {
		List<Double> doubleList = strToDoubleList(data);
		double x = doubleList.get(0) * multiply.doubleValue();
		if (x < 0) {
			x += 65536;
		}
		return ListUtil.toLinkedList((int) x);
	}

	public static List<Integer> encodeS32(String data, Float multiply) {
		List<Double> doubleList = strToDoubleList(data);
		double r = doubleList.get(0) * multiply.doubleValue();
		if (r < 0) {
			r += 2147483648D * 2;
		}
		return doubleToIntList(r);
	}

	private static List<Integer> doubleToIntList(double r) {
		return ListUtil.toLinkedList((int) (r / 65536), (int) (r % 65536));
	}

	private static List<Double> strToDoubleList(String data) {
		if (data.length() == 0) {
			throw new CustomException(ExceptionEnum.DEVICE_CONFIG_LENGTH_ERROR);
		}
		data = data.replace("[", "").replace("]", "");
		return Arrays.stream(data.split(",")).map(Double::valueOf).collect(Collectors.toList());
	}
}
