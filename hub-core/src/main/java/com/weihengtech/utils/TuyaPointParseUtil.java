package com.weihengtech.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.consts.TuyaDataPointTypeConstants;
import com.weihengtech.pojo.dtos.device.TuyaDevicePropertyDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public class TuyaPointParseUtil {

	public static JSONObject parsePoint(List<TuyaDevicePropertyDto.StatusInfo> statusInfoList) {
		JSONObject result = new JSONObject();

		for (TuyaDevicePropertyDto.StatusInfo statusInfo : statusInfoList) {
			String code = statusInfo.getCode();

			if (TuyaDataPointTypeConstants.ignoreMap.containsKey(code)) continue;

			if (TuyaDataPointTypeConstants.jsonMap.containsKey(code)) {
				String value = String.valueOf(statusInfo.getValue());
				if (StrUtil.isNotBlank(value)) {
					JSONObject jsonObject = JSONUtil.parseObj(value, false);
					for (String key : jsonObject.keySet()) {
						result.set(key, jsonObject.get(key));
					}
				}
			} else {
				result.set(code, statusInfo.getValue());
			}
		}
		return result;
	}
}