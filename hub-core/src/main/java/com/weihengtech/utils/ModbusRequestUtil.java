package com.weihengtech.utils;

import cn.hutool.aop.ProxyUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.api.pojo.vos.ReadBatchDeviceVO;
import com.weihengtech.api.pojo.vos.ReadDeviceVO;
import com.weihengtech.aspects.PostAspect;
import com.weihengtech.aspects.PreParamAspect;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.containers.PropTransitionContainer;
import com.weihengtech.enums.device.DeviceDatasourceEnum;
import com.weihengtech.enums.device.DeviceInfoEncodeTypeEnum;
import com.weihengtech.handlers.IPropTransitionHandler;
import com.weihengtech.pojo.bos.device.DeviceConfigBO;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.FutureTask;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ModbusRequestUtil {

	@Resource
	private ApplicationContextUtil applicationContextUtil;

	@Resource
	private DeviceConfigBO deviceConfig;

	@Resource
	private DeviceListService deviceListService;

	@Resource
	private StrategyService strategyService;

	public <T> void processDeviceConfig(
			Map<String, Map<String, String>> map, List<String> moduleTree,
			String deviceName, T dto, Integer slaveId
	) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		final int moduleLen = 2;
		if (CollUtil.isEmpty(moduleTree) || moduleTree.size() != moduleLen) {
			throw new CustomException(ExceptionEnum.DEVICE_CONFIG_FILE_ERROR);
		}
		Map<String, Map<String, List<Integer>>> modules = deviceConfig.getModules();
		List<Integer> addressList = modules.get(moduleTree.get(0)).get(moduleTree.get(1));
		List<List<Integer>> splitAddressList = ListUtil.split(addressList, 2);
		if (CollUtil.isEmpty(splitAddressList)) {
			log.warn("配置文件错误");
			throw new CustomException(ExceptionEnum.DEVICE_CONFIG_FILE_ERROR);
		}
		List<FutureTask<Pair<List<Map<String, String>>, List<Map<String, String>>>>> futureTaskList = new ArrayList<>();
		boolean isNotElink = deviceListDO.getDataSource().equals(DeviceDatasourceEnum.TUYA.getDatasource()) ||
				deviceListDO.getDataSource().equals(DeviceDatasourceEnum.WH.getDatasource());
		for (List<Integer> list : splitAddressList) {
			if (CollUtil.isEmpty(list) || list.size() != 2) {
				log.warn("配置文件start end 错误");
				throw new CustomException(ExceptionEnum.DEVICE_CONFIG_FILE_ERROR);
			}
			Integer start = list.get(0);
			Integer end = list.get(1);
			int len = list.get(1) - start + 1;
			List<Map<String, String>> configList = moduleChannelToConfigList(map, start, end);
			if (isNotElink) {
				futureTaskList.add(FutureUtil.multiThreadPostTransparentReadForModule(configList, deviceName, start,
						len, slaveId
				));
			} else {
				try {
					setDtoProperty(postTransparentReadForModule(configList, deviceName, start, len, slaveId), dto);
				} catch (Exception e) {
					log.error(String.format("elink device read metric：%s failed: ", list), e);
				}
			}
		}
		if (isNotElink) {
			for (FutureTask<Pair<List<Map<String, String>>, List<Map<String, String>>>> pairFutureTask : futureTaskList) {
				try {
					setDtoProperty(pairFutureTask.get(), dto);
				} catch (Exception e) {
					log.error("device read pairFutureTask failed: ", e);
				}
			}
		}
	}

	private <T> void setDtoProperty(
			Pair<List<Map<String, String>>, List<Map<String, String>>> resultAndConfigPair,
			T dto
	) {
		List<Map<String, String>> configList = resultAndConfigPair.getValue();
		List<Map<String, String>> resultList = resultAndConfigPair.getKey();
		if (CollUtil.isEmpty(resultList)) {
			for (Map<String, String> config : configList) {
				ReflectUtil.invoke(dto, "set" + config.get("prop"), "");
			}
		}
		for (Map<String, String> config : resultList) {
			ReflectUtil.invoke(dto, "set" + config.get("prop"), extraProcess(config, config.get("decode")));
		}
	}

	public List<Map<String, String>> moduleChannelToConfigList(
			Map<String, Map<String, String>> map, int start,
			int end
	) {
		return map.values().stream()
				.filter(m -> Integer.parseInt(m.get("start")) >= start && Integer.parseInt(m.get("start")) <= end)
				.sorted(Comparator.comparingInt(m -> Integer.parseInt(m.get("start")))).collect(Collectors.toList());
	}

	public Pair<List<Map<String, String>>, List<Map<String, String>>> postTransparentReadForModule(
			List<Map<String, String>> configList, String deviceName, int start, int len, Integer slaveId
	) {
		List<Map<String, String>> result = new ArrayList<>();
		if (configList.size() > 1) {
			List<Integer> integers = postMultiConfig(deviceName, start, len, slaveId);
			ModbusParseUtil.parseMultiData(integers, configList, result);
		} else if (CollUtil.isEmpty(configList)) {
			log.warn("配置文件协议不存在");
			throw new CustomException(ExceptionEnum.DEVICE_CONFIG_NOT_EXIST);
		} else {
			Map<String, String> config = configList.get(0);
			config.put("decode", postWithConfig(config, deviceName, slaveId));
			result.add(config);
		}
		return Pair.of(result, configList);
	}

	private List<Integer> postMultiConfig(String deviceName, int start, int len, Integer slaveId) {
		return postTransparentRead(deviceName, start, len, slaveId);
	}

	private String extraProcess(Map<String, String> config, String decodeStr) {
		PropTransitionContainer propTransitionContainer = applicationContextUtil
				.getSingletonBeanOfType(PropTransitionContainer.class);
		for (IPropTransitionHandler propTransition : propTransitionContainer.getTransitionList()) {
			decodeStr = propTransition.transform(decodeStr, config);
		}
		return decodeStr + config.get("unit");
	}

	public void configureDeviceConfig(
			Map<String, Map<String, String>> map, String deviceName,
			List<String> propNameList, List<String> propValueList, Integer slaveId
	) {
		if (propNameList.size() != propValueList.size()) {
			log.warn("配置key value数量不匹配");
			throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
		}
		for (int i = 0; i < propNameList.size(); i++) {
			String name = propNameList.get(i);
			String value = propValueList.get(i);
			Map<String, String> config = map.get(CharUtil.capitalizeTheFirstLetter(name));
			if (config == null) {
				log.warn("配置文件不存在");
				throw new CustomException(ExceptionEnum.DEVICE_CONFIG_NOT_EXIST);
			}
			String type = config.get("type");
			if (DeviceInfoEncodeTypeEnum.ASCII.toString().equals(type)) {
				writeToAscii(deviceName, Integer.valueOf(config.get("start")), Integer.valueOf(config.get("len")),
						value, slaveId
				);
			} else if (DeviceInfoEncodeTypeEnum.U16.toString().equals(type)) {
				ProxyUtil.proxy(this, PostAspect.class).writeToU16(deviceName, Integer.valueOf(config.get("start")),
						Integer.valueOf(config.get("len")), Float.valueOf(config.get("multiply")), value, slaveId
				);
			} else if (DeviceInfoEncodeTypeEnum.U32.toString().equals(type)) {
				ProxyUtil.proxy(this, PreParamAspect.class).writeToU32(deviceName, Integer.valueOf(config.get("start")),
						Integer.valueOf(config.get("len")), Float.valueOf(config.get("multiply")), value, slaveId
				);
			} else if (DeviceInfoEncodeTypeEnum.S16.toString().equals(type)) {
				writeToS16(deviceName, Integer.valueOf(config.get("start")), Integer.valueOf(config.get("len")),
						Float.valueOf(config.get("multiply")), value, slaveId
				);
			} else if (DeviceInfoEncodeTypeEnum.S32.toString().equals(type)) {
				writeToS32(deviceName, Integer.valueOf(config.get("start")), Integer.valueOf(config.get("len")),
						Float.valueOf(config.get("multiply")), value, slaveId
				);
			}
		}
	}

	private String postWithConfig(Map<String, String> config, String deviceName, Integer slaveId) {
		String type = config.get("type");
		String decodeStr;

		switch (ModbusParseUtil.infoTypeToTypeEnum(type)) {
			case ASCII:
				decodeStr = postFromAscii(deviceName, Integer.valueOf(config.get("start")),
						Integer.valueOf(config.get("len")), slaveId
				);
				break;
			case U16:
				decodeStr = postFromU16(deviceName, Integer.valueOf(config.get("start")),
						Integer.valueOf(config.get("len")), Float.valueOf(config.get("multiply")), slaveId
				);
				break;
			case U32:
				decodeStr = postFromU32(deviceName, Integer.valueOf(config.get("start")),
						Integer.valueOf(config.get("len")), Float.valueOf(config.get("multiply")), slaveId
				);
				break;
			case U32R:
				decodeStr = postFromU32R(deviceName, Integer.valueOf(config.get("start")),
						Integer.valueOf(config.get("len")), Float.valueOf(config.get("multiply")), slaveId
				);
				break;
			case S16:
				decodeStr = postFromS16(deviceName, Integer.valueOf(config.get("start")),
						Integer.valueOf(config.get("len")), Float.valueOf(config.get("multiply")), slaveId
				);
				break;
			case S32:
				decodeStr = postFromS32(deviceName, Integer.valueOf(config.get("start")),
						Integer.valueOf(config.get("len")), Float.valueOf(config.get("multiply")), slaveId
				);
				break;
			case BIT:
				decodeStr = postBit(deviceName, Integer.valueOf(config.get("start")),
						Integer.valueOf(config.get("len")), slaveId
				);
				break;
			default:
				decodeStr = "";
		}
		return decodeStr;
	}

	public String postFromAscii(String deviceName, Integer start, Integer len, Integer slaveId) {
		return ModbusParseUtil.parseForDeviceInfoTypeEnum(
				DeviceInfoEncodeTypeEnum.ASCII,
				postTransparentRead(deviceName, start, len, slaveId)
		);
	}

	public String postBit(String deviceName, Integer start, Integer len, Integer slaveId) {
		return ModbusParseUtil.parseForDeviceInfoTypeEnum(
				DeviceInfoEncodeTypeEnum.BIT,
				postTransparentRead(deviceName, start, len, slaveId)
		);
	}

	public String postFromU16(String deviceName, Integer start, Integer len, Float multiply, Integer slaveId) {
		return ModbusParseUtil.parseData(DeviceInfoEncodeTypeEnum.U16,
				postTransparentRead(deviceName, start, len, slaveId), multiply
		);
	}

	public String postFromU32(String deviceName, Integer start, Integer len, Float multiply, Integer slaveId) {
		return ModbusParseUtil.parseData(DeviceInfoEncodeTypeEnum.U32,
				postTransparentRead(deviceName, start, len, slaveId), multiply
		);
	}

	public String postFromU32R(String deviceName, Integer start, Integer len, Float multiply, Integer slaveId) {
		return ModbusParseUtil.parseData(DeviceInfoEncodeTypeEnum.U32R,
				postTransparentRead(deviceName, start, len, slaveId), multiply
		);
	}

	public String postFromS16(String deviceName, Integer start, Integer len, Float multiply, Integer slaveId) {
		return ModbusParseUtil.parseData(DeviceInfoEncodeTypeEnum.S16,
				postTransparentRead(deviceName, start, len, slaveId), multiply
		);
	}

	public String postFromS32(String deviceName, Integer start, Integer len, Float multiply, Integer slaveId) {
		return ModbusParseUtil.parseData(DeviceInfoEncodeTypeEnum.S32,
				postTransparentRead(deviceName, start, len, slaveId), multiply
		);
	}

	public void writeToAscii(String deviceName, Integer start, Integer len, String source, Integer slaverId) {
		postTransparentWrite(deviceName, start, len, slaverId, ModbusParseUtil.encodeAscii(source, len));
	}

	public void writeToU16(
			String deviceName, Integer start, Integer len, Float multiply, String source,
			Integer slaverId
	) {
		postTransparentWrite(deviceName, start, len, slaverId, ModbusParseUtil.encodeU16(source, multiply));
	}

	public void writeToU32(
			String deviceName, Integer start, Integer len, Float multiply, String source,
			Integer slaverId
	) {
		postTransparentWrite(deviceName, start, len, slaverId, ModbusParseUtil.encodeU32(source, multiply));
	}

	public void writeToS16(
			String deviceName, Integer start, Integer len, Float multiply, String source,
			Integer slaverId
	) {
		postTransparentWrite(deviceName, start, len, slaverId, ModbusParseUtil.encodeS16(source, multiply));
	}

	public void writeToS32(
			String deviceName, Integer start, Integer len, Float multiply, String source,
			Integer slaverId
	) {
		postTransparentWrite(deviceName, start, len, slaverId, ModbusParseUtil.encodeS32(source, multiply));
	}

	public void postTransparentWrite(
			String deviceName, Integer startAddress, Integer len, Integer slaveId,
			List<Integer> list
	) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		Boolean res = specificServService.sendWriteCommand(deviceListDO.getWifiSn(), slaveId,
				startAddress, len, list
		);
		if (!res) {
			log.warn("设备{}写入失败, startAddress={}, len={}, slaveId={}, list={}",
					deviceName, startAddress, len, slaveId, list);
			throw new CustomException(ExceptionEnum.DEVICE_CONFIG_CHANGE_ERROR);
		}
	}

	public List<Integer> postTransparentRead(String deviceName, Integer startAddress, Integer len, Integer slaveId) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		if (deviceListDO == null) {
			throw new CustomException(ExceptionEnum.DEVICE_QUERY_DATA_ERROR);
		}
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		log.info("设备读取开始  {}-{}-{}-{}", deviceListDO.getWifiSn(), slaveId, startAddress, len);
		List<Integer> res = specificServService.sendReadCommand(deviceListDO.getWifiSn(), slaveId,
				startAddress, len
		);
		log.info("设备读取结果 {}", res);
		return res;
	}

	/** 支持多类型设备批量透传 */
	public Map<String, List<Integer>> postBatchTransparentRead(DeviceListDO deviceListDO, List<ReadDeviceVO> valList) {
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		log.info("设备批量读取开始：device: {}, valList: {}", deviceListDO.getWifiSn(), valList);
		Map<String, List<Integer>> resMap = specificServService.sendBatchReadCommand(ReadBatchDeviceVO.builder()
				.deviceId(deviceListDO.getWifiSn())
				.valList(valList)
				.build()
		);
		log.info("设备批量读取结果 {}", resMap);
		return resMap;
	}

	public List<Integer> eLinkPostTransparentRead(String wifiSn, Integer startAddress, Integer len, Integer slaveId) {
		SpecificServService specificServService = InitUtil.getBean("specificServ0", SpecificServService.class);
		return specificServService.sendReadCommand(wifiSn, slaveId, startAddress, len);
	}
}
