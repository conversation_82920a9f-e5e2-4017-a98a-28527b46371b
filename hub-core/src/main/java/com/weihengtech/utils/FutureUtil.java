package com.weihengtech.utils;

import cn.hutool.core.lang.Pair;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.FutureTask;

/**
 * <AUTHOR>
 */
public class FutureUtil {

	private static final ExecutorService tuyaReadService = Executors.newFixedThreadPool(3);

	public static FutureTask<Pair<List<Map<String, String>>, List<Map<String, String>>>> multiThreadPostTransparentReadForModule(
			List<Map<String, String>> configList, String deviceName, int start, int len, int slaveId
	) {
		PostTransparentReadForModuleTask task = new PostTransparentReadForModuleTask(configList, deviceName, start, len,
				slaveId
		);
		FutureTask<Pair<List<Map<String, String>>, List<Map<String, String>>>> futureTask = new FutureTask<>(task);
		tuyaReadService.submit(futureTask);
		return futureTask;
	}

	static class PostTransparentReadForModuleTask
			implements
			Callable<Pair<List<Map<String, String>>, List<Map<String, String>>>> {

		List<Map<String, String>> configList;
		private final String deviceName;
		private final Integer start;
		private final Integer len;
		private final Integer slaveId;

		public PostTransparentReadForModuleTask(
				List<Map<String, String>> configList, String deviceName, int start,
				int len, int slaveId
		) {
			this.configList = configList;
			this.deviceName = deviceName;
			this.start = start;
			this.len = len;
			this.slaveId = slaveId;
		}

		@Override
		public Pair<List<Map<String, String>>, List<Map<String, String>>> call() {
			ModbusRequestUtil modbusRequestUtil = InitUtil.getBean(ModbusRequestUtil.class);
			return modbusRequestUtil.postTransparentReadForModule(configList, deviceName, start, len, slaveId);
		}
	}
}
