package com.weihengtech.utils;

import cn.hutool.core.util.StrUtil;
import com.aliyun.iot20180120.Client;
import com.aliyun.iot20180120.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.pojo.bos.device.DeviceStatisticsInfoBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AliMqttUtil {

	@Resource(type = Client.class)
	private Client mqttClient;

	@Value("${ali.cloud.product.key}")
	private String productKey;

	@Value("${ali.cloud.instance-id}")
	private String instanceId;

	/**
	 * 获取指定状态设备的设备名列表
	 *
	 * @param deviceStatusEnum 设备状态枚举
	 * @return 设备名列表
	 */
	public List<String> queryDeviceByStatusRequest(DeviceStatusEnum deviceStatusEnum) {
		QueryDeviceByStatusRequest queryDeviceByStatusRequest = new QueryDeviceByStatusRequest();
		queryDeviceByStatusRequest.setStatus(deviceStatusEnum.getCode());
		queryDeviceByStatusRequest.setProductKey(productKey);
		if (StrUtil.isNotBlank(instanceId)) {
			queryDeviceByStatusRequest.setIotInstanceId(instanceId);
		}
		try {
			QueryDeviceByStatusResponse queryDeviceByStatusResponse = mqttClient
					.queryDeviceByStatus(queryDeviceByStatusRequest);
			BeanUtil.assertNotNull(queryDeviceByStatusResponse);
			QueryDeviceByStatusResponseBody body = queryDeviceByStatusResponse.getBody();
			if (body.getSuccess()) {
				List<QueryDeviceByStatusResponseBody.QueryDeviceByStatusResponseBodyDataSimpleDeviceInfo> simpleDeviceInfoList = body
						.getData().getSimpleDeviceInfo();
				List<String> chooseDeviceStatusDeviceNameList = new ArrayList<>(64);
				for (QueryDeviceByStatusResponseBody.QueryDeviceByStatusResponseBodyDataSimpleDeviceInfo deviceInfo : simpleDeviceInfoList) {
					chooseDeviceStatusDeviceNameList.add(deviceInfo.getDeviceName());
				}
				return chooseDeviceStatusDeviceNameList;
			}
			log.warn("查询mqtt数据失败");
			throw new CustomException(ExceptionEnum.DEVICE_QUERY_DATA_ERROR);
		} catch (Exception e) {
			log.warn("查询mqtt数据异常 {}", e.getMessage());
			throw new CustomException(ExceptionEnum.DEVICE_STATUS_CHECK_ERROR);
		}
	}

	/**
	 * 获取设备统计信息
	 *
	 * @return 设备统计信息
	 */
	public DeviceStatisticsInfoBO queryDeviceStatisticsRequest() {
		QueryDeviceStatisticsRequest queryDeviceStatisticsRequest = new QueryDeviceStatisticsRequest();
		if (StrUtil.isNotBlank(instanceId)) {
			queryDeviceStatisticsRequest.setIotInstanceId(instanceId);
		}
		try {
			QueryDeviceStatisticsResponse queryDeviceStatisticsResponse = mqttClient
					.queryDeviceStatistics(queryDeviceStatisticsRequest);
			BeanUtil.assertNotNull(queryDeviceStatisticsResponse);
			if (queryDeviceStatisticsResponse.getBody().getSuccess()) {
				QueryDeviceStatisticsResponseBody.QueryDeviceStatisticsResponseBodyData data = queryDeviceStatisticsResponse
						.getBody().getData();
				DeviceStatisticsInfoBO deviceStatisticsInfoBO = new DeviceStatisticsInfoBO();
				deviceStatisticsInfoBO.setDeviceCount(data.getDeviceCount());
				deviceStatisticsInfoBO.setActiveCount(data.getActiveCount());
				deviceStatisticsInfoBO.setOnlineCount(data.getOnlineCount());
				return deviceStatisticsInfoBO;
			}
			log.warn("查询mqtt数据失败");
			throw new CustomException(ExceptionEnum.DEVICE_QUERY_DATA_ERROR);
		} catch (Exception e) {
			log.warn("查询mqtt数据异常 {}", e.getMessage());
			throw new CustomException(ExceptionEnum.DEVICE_STATISTICS_CHECK_ERROR);
		}
	}

	/**
	 * 获取指定设备状态
	 *
	 * @param deviceName 设备名
	 * @return 设备状态枚举
	 */
	public DeviceStatusEnum queryDeviceStatus(String deviceName) {
		GetDeviceStatusRequest getDeviceStatusRequest = new GetDeviceStatusRequest().setDeviceName(deviceName)
				.setProductKey(productKey);
		if (StrUtil.isNotBlank(instanceId)) {
			getDeviceStatusRequest.setIotInstanceId(instanceId);
		}
		try {
			GetDeviceStatusResponse deviceStatus = mqttClient.getDeviceStatus(getDeviceStatusRequest);
			if (deviceStatus.getBody().getSuccess()) {
				GetDeviceStatusResponseBody.GetDeviceStatusResponseBodyData data = deviceStatus.getBody().getData();
				String status = data.getStatus();
				if (status.equals(DeviceStatusEnum.ONLINE.name())) {
					return DeviceStatusEnum.ONLINE;
				} else {
					return DeviceStatusEnum.OFFLINE;
				}
			}
			return DeviceStatusEnum.OFFLINE;
		} catch (Exception e) {
			log.error("MQTT Exception {}", e.getMessage());
			return DeviceStatusEnum.OFFLINE;
		}
	}

	/**
	 * 批量查询设备状态信息
	 *
	 * @param deviceNameList 设备名列表
	 * @return 设备状态映射map
	 */
	public Map<String, DeviceStatusEnum> queryMultiDevicesStatus(List<String> deviceNameList) {
		BatchQueryDeviceDetailRequest batchQueryDeviceDetailRequest = new BatchQueryDeviceDetailRequest()
				.setDeviceName(deviceNameList).setProductKey(productKey);
		if (StrUtil.isNotBlank(instanceId)) {
			batchQueryDeviceDetailRequest.setIotInstanceId(instanceId);
		}
		try {
			BatchQueryDeviceDetailResponse batchQueryDeviceDetailResponse = mqttClient
					.batchQueryDeviceDetail(batchQueryDeviceDetailRequest);
			BeanUtil.assertNotNull(batchQueryDeviceDetailResponse);
			if (batchQueryDeviceDetailResponse.getBody().getSuccess()) {
				BatchQueryDeviceDetailResponseBody.BatchQueryDeviceDetailResponseBodyData data = batchQueryDeviceDetailResponse
						.getBody().getData();
				List<BatchQueryDeviceDetailResponseBody.BatchQueryDeviceDetailResponseBodyDataData> deviceList = data
						.getData();
				Map<String, DeviceStatusEnum> map = new HashMap<>(64);
				for (BatchQueryDeviceDetailResponseBody.BatchQueryDeviceDetailResponseBodyDataData bodyData : deviceList) {
					String deviceName = bodyData.getDeviceName();
					String status = bodyData.getStatus();
					for (DeviceStatusEnum deviceStatusEnum : DeviceStatusEnum.values()) {
						if (deviceStatusEnum.name().equals(status)) {
							map.put(deviceName, deviceStatusEnum);
						}
					}
				}
				return map;
			}
			log.warn("查询mqtt数据失败");
			throw new CustomException(ExceptionEnum.DEVICE_QUERY_DATA_ERROR);
		} catch (Exception e) {
			log.warn("查询mqtt数据异常 {}", e.getMessage());
			throw new CustomException(ExceptionEnum.DEVICE_MULTI_QUERY_ERROR);
		}
	}

	public List<String> queryAllOnlineDevice() {
		QueryDeviceRequest queryDeviceRequest = new QueryDeviceRequest().setPageSize(100).setCurrentPage(1)
				.setProductKey(productKey);
		if (StrUtil.isNotBlank(instanceId)) {
			queryDeviceRequest.setIotInstanceId(instanceId);
		}
		RuntimeOptions runtime = new RuntimeOptions();
		List<String> onlineDeviceNameList = new ArrayList<>();
		try {
			QueryDeviceResponse queryDeviceResponse = mqttClient.queryDeviceWithOptions(queryDeviceRequest, runtime);
			QueryDeviceResponseBody body = queryDeviceResponse.getBody();
			List<QueryDeviceResponseBody.QueryDeviceResponseBodyDataDeviceInfo> deviceInfoList = body.getData()
					.getDeviceInfo();
			Integer pageCount = body.getPageCount();

			onlineDeviceNameList = deviceInfoList.stream()
					.filter(deviceInfo -> deviceInfo.getDeviceStatus().equals("ONLINE"))
					.map(QueryDeviceResponseBody.QueryDeviceResponseBodyDataDeviceInfo::getDeviceName)
					.collect(Collectors.toList());

			for (int i = 2; i < pageCount; i++) {
				QueryDeviceRequest queryRequest = new QueryDeviceRequest().setPageSize(100).setCurrentPage(i)
						.setProductKey(productKey);
				if (StrUtil.isNotBlank(instanceId)) {
					queryRequest.setIotInstanceId(instanceId);
				}
				QueryDeviceResponse resp = mqttClient.queryDeviceWithOptions(queryRequest, runtime);
				List<QueryDeviceResponseBody.QueryDeviceResponseBodyDataDeviceInfo> deviceList = resp.getBody()
						.getData().getDeviceInfo();
				onlineDeviceNameList
						.addAll(deviceList.stream().filter(deviceInfo -> deviceInfo.getDeviceStatus().equals("ONLINE"))
								.map(QueryDeviceResponseBody.QueryDeviceResponseBodyDataDeviceInfo::getDeviceName)
								.collect(Collectors.toList()));
			}

		} catch (Exception _error) {
			TeaException error = new TeaException(_error.getMessage(), _error);
			log.error(error.message);
		}
		return onlineDeviceNameList;
	}
}
