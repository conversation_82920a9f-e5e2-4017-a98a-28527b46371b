package com.weihengtech.utils;

import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.io.resource.UrlResource;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.setting.Setting;
import com.weihengtech.annotation.SetOrder;
import com.weihengtech.consts.CommonConstants;
import com.weihengtech.containers.ConfigParamHandlerContainer;
import com.weihengtech.containers.ConfigResultHandlerContainer;
import com.weihengtech.containers.ProcessHandlerContainer;
import com.weihengtech.containers.PropTransitionContainer;
import com.weihengtech.dao.other.SqlMapper;
import com.weihengtech.handlers.IConfigParamHandler;
import com.weihengtech.handlers.IConfigResultHandler;
import com.weihengtech.handlers.ICustomProcessHandler;
import com.weihengtech.handlers.IPropTransitionHandler;
import com.weihengtech.interfaces.IAroundProcessHandler;
import com.weihengtech.interfaces.IGlobalProcessHandler;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.firmware.FirmwareUpgradeRecordDO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.firmware.FirmwareUpgradeRecordService;
import com.weihengtech.service.firmware.MqService;
import com.weihengtech.tasks.DelayQueueMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.DelayQueue;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class InitUtil {

	public static ConfigurableApplicationContext APPLICATION_CONTEXT;
	public static Setting COUNTRY_SETTING;
	public static DelayQueue<DelayQueueMessage> DELAY_QUEUE = new DelayQueue<>();

	public static void init(ConfigurableApplicationContext applicationContext) {
		APPLICATION_CONTEXT = applicationContext;
		InitUtil initUtil = new InitUtil();
		initUtil.register(applicationContext, initUtil.buildPropTransition(applicationContext));
		initUtil.register(applicationContext, initUtil.buildProcessHandler(applicationContext));
		initUtil.register(applicationContext, initUtil.buildConfigResultHandler(applicationContext));
		initUtil.register(applicationContext, initUtil.buildConfigParamHandler(applicationContext));
		initCountrySetting();
		String enableTask = applicationContext.getEnvironment().getProperty("custom.xxl.job.enable", "false");
		if ("true".equals(enableTask)) {
			initDeviceUpgradeTask();
		}
		initDelayQueueTaskThread();
	}

	public static void initDelayQueueTaskThread() {
		MqService mqService = getBean(MqService.class);
		ThreadUtil.execAsync(() -> {
			log.info("InfiniteLoop DelayQueue start");
			// noinspection InfiniteLoopStatement
			for (; ; ) {
				DelayQueueMessage delayQueueMessage = DELAY_QUEUE.take();
				log.info("DelayQueue take message success: {}", JSONUtil.toJsonStr(delayQueueMessage));
				ThreadUtil.execAsync(() -> ReflectUtil.invoke(mqService, delayQueueMessage.getParams().getStr("type"),
						delayQueueMessage
				));
			}
		});
	}

	private static void initDeviceUpgradeTask() {
		log.info("开启设备升级任务");
		SqlMapper sqlMapper = getBean(SqlMapper.class);
		String datacenter = APPLICATION_CONTEXT.getEnvironment().getProperty("custom.datacenter.name");
		if (StrUtil.isBlank(datacenter)) return;
		DeviceListService deviceListService = getBean(DeviceListService.class);
		List<DeviceListDO> deviceList = deviceListService.getDeviceByDatacenter(datacenter);
		List<Long> idList = deviceList.stream()
				.map(DeviceListDO::getId)
				.collect(Collectors.toList());
		List<FirmwareUpgradeRecordDO> todoUpgradeList = sqlMapper.queryCurrentToDoUpgradeDevice(idList);
		FirmwareUpgradeRecordService recordService = getBean(FirmwareUpgradeRecordService.class);
		todoUpgradeList.parallelStream().forEach(record -> {
			record.setStatus(CommonConstants.DEVICE_UPGRADE_FAILURE);
			recordService.updateById(record);
		});
	}

	private static void initCountrySetting() {
		log.info("初始化country.setting");
		ClassPathResource classPathResource = new ClassPathResource("country.setting");
		cn.hutool.core.io.resource.Resource resource = new UrlResource(classPathResource.getUrl());
		COUNTRY_SETTING = new Setting(resource, CharsetUtil.CHARSET_UTF_8, true);
	}

	private <T> void register(ConfigurableApplicationContext applicationContext, T t) {
		DefaultListableBeanFactory defaultListableBeanFactory = (DefaultListableBeanFactory) applicationContext
				.getAutowireCapableBeanFactory();
		defaultListableBeanFactory.registerSingleton(t.getClass().getSimpleName(), t);
	}

	private PropTransitionContainer buildPropTransition(ConfigurableApplicationContext applicationContext) {
		Map<String, IPropTransitionHandler> propTransitionMap = applicationContext
				.getBeansOfType(IPropTransitionHandler.class);
		PropTransitionContainer propTransition = ReflectUtil.newInstance(PropTransitionContainer.class);
		propTransition.setTransitionList(propTransitionMap.values().stream()
				.sorted(Comparator.comparingInt(p -> p.getClass().getAnnotation(SetOrder.class).value()))
				.collect(Collectors.toList()));
		return propTransition;
	}

	private ProcessHandlerContainer buildProcessHandler(ConfigurableApplicationContext applicationContext) {
		Map<String, IGlobalProcessHandler> globalProcessHandlerMap = applicationContext
				.getBeansOfType(IGlobalProcessHandler.class);
		ProcessHandlerContainer processHandlerContainer = ReflectUtil.newInstance(ProcessHandlerContainer.class);
		processHandlerContainer.setGlobalProcessHandlerList(globalProcessHandlerMap.values().stream()
				.sorted(Comparator.comparingInt(
						h -> h.getClass().getAnnotation(SetOrder.class).value()))
				.collect(Collectors.toList()));
		Map<String, IAroundProcessHandler> aroundProcessHandlerMap = applicationContext
				.getBeansOfType(IAroundProcessHandler.class);
		processHandlerContainer.setAroundProcessHandlerList(aroundProcessHandlerMap.values().stream()
				.sorted(Comparator.comparingInt(
						h -> h.getClass().getAnnotation(SetOrder.class).value()))
				.collect(Collectors.toList()));
		Map<String, ICustomProcessHandler> authorityProcessHandlerMap = applicationContext
				.getBeansOfType(ICustomProcessHandler.class);
		processHandlerContainer.setCustomProcessHandlerList(authorityProcessHandlerMap.values().stream()
				.sorted(Comparator.comparingInt(
						h -> h.getClass().getAnnotation(SetOrder.class).value()))
				.collect(Collectors.toList()));
		return processHandlerContainer;
	}

	private ConfigResultHandlerContainer buildConfigResultHandler(ConfigurableApplicationContext applicationContext) {

		Map<String, IConfigResultHandler> configResultHandlerMap = applicationContext
				.getBeansOfType(IConfigResultHandler.class);
		ConfigResultHandlerContainer configResultHandlerContainer = ReflectUtil
				.newInstance(ConfigResultHandlerContainer.class);
		configResultHandlerContainer.setConfigResultHandlerList(configResultHandlerMap.values().stream()
				.sorted(Comparator.comparingInt(
						h -> h.getClass().getAnnotation(SetOrder.class).value()))
				.collect(Collectors.toList()));

		return configResultHandlerContainer;
	}

	private ConfigParamHandlerContainer buildConfigParamHandler(ConfigurableApplicationContext applicationContext) {

		Map<String, IConfigParamHandler> configParamHandlerMap = applicationContext
				.getBeansOfType(IConfigParamHandler.class);
		ConfigParamHandlerContainer configParamHandlerContainer = ReflectUtil
				.newInstance(ConfigParamHandlerContainer.class);
		configParamHandlerContainer.setConfigParamHandlers(configParamHandlerMap.values().stream()
				.sorted(Comparator.comparingInt(
						h -> h.getClass().getAnnotation(SetOrder.class).value()))
				.collect(Collectors.toList()));

		return configParamHandlerContainer;
	}

	public static <T> T getBean(Class<T> t) {
		return APPLICATION_CONTEXT.getBean(t);
	}

	public static <T> T getBean(String beanName, Class<T> t) {
		return APPLICATION_CONTEXT.getBean(beanName, t);
	}
}
