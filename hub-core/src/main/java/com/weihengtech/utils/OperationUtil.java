package com.weihengtech.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.interfaces.FunctionalAction;

import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public class OperationUtil<T> {

	private final T value;

	public OperationUtil(T value) {
		this.value = value;
	}

	public static <T> OperationUtil<T> of(T value) {
		return new OperationUtil<>(value);
	}

	public void orElse(FunctionalAction functionalAction) {
		if (!isPresent()) {
			functionalAction.action();
		}
	}

	public void then(Consumer<T> consumer) {
		if (isPresent()) {
			consumer.accept(value);
		}
	}

	public void notnullThen(Consumer<T> consumer) {
		if (value != null) {
			consumer.accept(value);
		}
	}

	public void ifPresentOrElseThrow(Consumer<T> consumer, Supplier<? extends RuntimeException> supplier) {
		if (isPresent()) {
			consumer.accept(value);
		} else {
			throw supplier.get();
		}
	}

	public void ifPresentOrElse(Consumer<T> consumer, FunctionalAction functionalAction) {
		if (isPresent()) {
			consumer.accept(value);
		} else {
			functionalAction.action();
		}
	}

	public <R> R orReturn(Function<T, R> f) {
		if (isPresent()) {
			return f.apply(value);
		}
		return null;
	}

	public void orElseThrow(Supplier<? extends RuntimeException> supplier) {
		if (!isPresent()) {
			throw supplier.get();
		}
	}

	public boolean isPresent() {
		return !ObjectUtil.isNull(value) && !StrUtil.isBlankIfStr(value) && !ObjectUtil.isEmpty(value);
	}
}
