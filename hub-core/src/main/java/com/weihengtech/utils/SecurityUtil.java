package com.weihengtech.utils;

import lombok.AllArgsConstructor;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;

import javax.xml.bind.DatatypeConverter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 动态输出生成长短id工具
 *
 * <AUTHOR>
 * @date 2024/7/25 15:51
 * @version 1.0
 */
public class SecurityUtil {

    @AllArgsConstructor
    public static class DeviceFDI {
        public String lfdi;
        public Long sfdi;
    }

    public static int checkDigit(long x) {
        int sum = 0;
        while (x > 0) {
            sum += (int) (x % 10);
            x /= 10;
        }
        return (10 - (sum % 10)) % 10;
    }

    public static long sfdiGen(byte[] lfdi) {
        long sfdi = 0;
        for (int i = 0; i < 5; i++) {
            sfdi = (sfdi << 8) + (lfdi[i] & 0xff);
        }
        sfdi >>= 4;
        sfdi = sfdi * 10 + checkDigit(sfdi);
        return sfdi;
    }

    public static DeviceFDI loadDeviceX509(String path) throws IOException, NoSuchAlgorithmException {
        if (!path.endsWith(".x509")) {
            throw new IllegalArgumentException("Invalid file extension");
        }

        Path filePath = Paths.get(path);
        byte[] buffer = Files.readAllBytes(filePath);

        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashed = digest.digest(buffer);
        String lfdi = DatatypeConverter.printHexBinary(hashed).substring(0, 40).toLowerCase();
        long sfdi = sfdiGen(hashed);

        return new DeviceFDI(lfdi, sfdi);
    }

    public static DeviceFDI randomFdi(String random32Str, String suffix) {
        String lfdi = random32Str + suffix;
        byte[] decodedArr;
        try {
            decodedArr = Hex.decodeHex(lfdi.toCharArray());
        } catch (DecoderException e) {
            throw new RuntimeException(e);
        }
        long sfdi = sfdiGen(decodedArr);
        return new DeviceFDI(lfdi, sfdi);
    }
}

