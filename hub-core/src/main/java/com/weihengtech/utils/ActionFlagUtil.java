package com.weihengtech.utils;

import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;

/**
 * <AUTHOR>
 */
public class ActionFlagUtil {

	public static void singleAction(int flag) {
		if (1 != flag) {
			throw new CustomException(ExceptionEnum.ACTION_ASSERT_NOT_FIT_EXCEPTION);
		}
	}

	public static void actionCheck(int flag, int assertFlag) {
		if (flag != assertFlag) {
			throw new CustomException(ExceptionEnum.ACTION_ASSERT_NOT_FIT_EXCEPTION);
		}
	}

	public static void assertTrue(boolean flag) {
		if (!flag) {
			throw new CustomException(ExceptionEnum.ACTION_ASSERT_NOT_FIT_EXCEPTION);
		}
	}
}
