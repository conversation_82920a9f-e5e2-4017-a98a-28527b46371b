package com.weihengtech.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.weihengtech.containers.ConfigParamHandlerContainer;
import com.weihengtech.containers.ConfigResultHandlerContainer;
import com.weihengtech.handlers.IConfigParamHandler;
import com.weihengtech.handlers.IConfigResultHandler;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class InterfaceUtil {

	public static String processConfigResult(Map<String, String> config, String result) {
		List<IConfigResultHandler> configResultHandlerList = SpringUtil.getBean(ConfigResultHandlerContainer.class)
				.getConfigResultHandlerList();
		String processResult = result;
		for (IConfigResultHandler handler : configResultHandlerList) {
			processResult = handler.process(config, result);
		}
		return processResult;
	}

	public static void processConfigParam(String decode, Object param) {
		List<IConfigParamHandler> handlers = SpringUtil.getBean(ConfigParamHandlerContainer.class).getConfigParamHandlers();
		for (IConfigParamHandler handler : handlers) {
			handler.process(decode, param);
		}
	}
}
