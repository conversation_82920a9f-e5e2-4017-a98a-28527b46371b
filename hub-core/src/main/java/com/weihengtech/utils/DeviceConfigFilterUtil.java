package com.weihengtech.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Pair;
import com.google.common.collect.Lists;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.pojo.bos.device.DeviceConfigBO;
import com.weihengtech.pojo.vos.device.DeviceConfigConfigureVO;
import lombok.val;

import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DeviceConfigFilterUtil {

	public static void filter(DeviceConfigConfigureVO deviceConfigConfigureVO) {
		String code = deviceConfigConfigureVO.getCode();
		DeviceConfigBO deviceConfig = InitUtil.APPLICATION_CONTEXT.getBean(DeviceConfigBO.class);
		for (String key : deviceConfig.getSettings().keySet()) {
			if (AesUtil.encode(key).equals(code)) {
				if ("BatterySetting".equals(key)) {
					filterBatterySetting(deviceConfigConfigureVO);
				}
			}
		}
	}

	private static void filterBatterySetting(DeviceConfigConfigureVO deviceConfigConfigureVO) {
		List<String> propNameList = deviceConfigConfigureVO.getPropNameList();
		List<String> propValueList = deviceConfigConfigureVO.getPropValueList();
		List<Pair<Integer, Integer>> pairList = Lists.newLinkedList();
		if (CollUtil.isNotEmpty(propNameList) && CollUtil.isNotEmpty(propValueList)) {
			if (propNameList.size() != propValueList.size()) {
				throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
			}
			Map<String, String> propMap;
			try {
				propMap = CollUtil.zip(propNameList, propValueList);
			} catch (Exception e) {
				throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
			}
			setLeftParam(propNameList, propValueList);
			buildCrossTimeList(propMap, pairList);
			validChargeTimeCross(pairList);
			deviceConfigConfigureVO.setPropNameList(propNameList);
			deviceConfigConfigureVO.setPropValueList(propValueList);
		}
	}

	private static void validChargeTimeCross(List<Pair<Integer, Integer>> pairList) {

		HashSet<Integer> hashSet = new HashSet<>();
		int count = 0;

		for (Pair<Integer, Integer> pair : pairList) {
			Integer start = pair.getKey();
			Integer end = pair.getValue();

			if (end < start) {
				val max = 24 * 60;
				count += max - start;
				count += end;
				for (int i = start; i < max; i++) {
					hashSet.add(i);
				}
				for (int i = 0; i < end; i++) {
					hashSet.add(i);
				}
			} else {
				count += end - start;
				for (int i = start; i < end; i++) {
					hashSet.add(i);
				}
			}
		}

		if (count > hashSet.size()) {
			throw new CustomException(ExceptionEnum.TIME_CANNOT_CROSS);
		}
	}

	private static void buildCrossTimeList(
			Map<String, String> propMap, List<Pair<Integer, Integer>> pairList
	) {
		String chargeBeginHour1 = propMap.getOrDefault("chargeBeginHour1", "0");
		String chargeBeginMinute1 = propMap.getOrDefault("chargeBeginMinute1", "0");
		String chargeEndHour1 = propMap.getOrDefault("chargeEndHour1", "0");
		String chargeEndMinute1 = propMap.getOrDefault("chargeEndMinute1", "0");
		String chargeBeginHour2 = propMap.getOrDefault("chargeBeginHour2", "0");
		String chargeBeginMinute2 = propMap.getOrDefault("chargeBeginMinute2", "0");
		String chargeEndHour2 = propMap.getOrDefault("chargeEndHour2", "0");
		String chargeEndMinute2 = propMap.getOrDefault("chargeEndMinute2", "0");
		String disChargeBeginHour1 = propMap.getOrDefault("disChargeBeginHour1", "0");
		String disChargeBeginMinute1 = propMap.getOrDefault("disChargeBeginMinute1", "0");
		String disChargeEndHour1 = propMap.getOrDefault("disChargeEndHour1", "0");
		String disChargeEndMinute1 = propMap.getOrDefault("disChargeEndMinute1", "0");
		String disChargeBeginHour2 = propMap.getOrDefault("disChargeBeginHour2", "0");
		String disChargeBeginMinute2 = propMap.getOrDefault("disChargeBeginMinute2", "0");
		String disChargeEndHour2 = propMap.getOrDefault("disChargeEndHour2", "0");
		String disChargeEndMinute2 = propMap.getOrDefault("disChargeEndMinute2", "0");
		addPair(pairList, chargeBeginHour1, chargeBeginMinute1, chargeEndHour1, chargeEndMinute1);
		addPair(pairList, chargeBeginHour2, chargeBeginMinute2, chargeEndHour2, chargeEndMinute2);
		addPair(pairList, disChargeBeginHour1, disChargeBeginMinute1, disChargeEndHour1, disChargeEndMinute1);
		addPair(pairList, disChargeBeginHour2, disChargeBeginMinute2, disChargeEndHour2, disChargeEndMinute2);
	}

	private static void addPair(List<Pair<Integer, Integer>> pairList, String beginHour, String beginMinute, String endHour, String endMinute) {
		val initValue = "0000";
		if (!(beginHour + beginMinute).equals(endHour + endMinute)
				|| initValue.equals(beginHour + beginMinute)) {
			addPairAction(Integer.parseInt(beginHour) * 60 + Integer.parseInt(beginMinute),
					Integer.parseInt(endHour) * 60 + Integer.parseInt(endMinute), pairList
			);
		}
	}

	private static void setLeftParam(List<String> propNameList, List<String> propValueList) {
		LinkedList<String> needNameList = ListUtil.toLinkedList(
				"chargeBeginHour1",
				"chargeBeginMinute1",
				"chargeEndHour1",
				"chargeEndMinute1",
				"chargePower1",
				"chargeBeginHour2",
				"chargeBeginMinute2",
				"chargeEndHour2",
				"chargeEndMinute2",
				"chargePower2",
				"disChargeBeginHour1",
				"disChargeBeginMinute1",
				"disChargeEndHour1",
				"disChargeEndMinute1",
				"disChargePower1",
				"disChargeBeginHour2",
				"disChargeBeginMinute2",
				"disChargeEndHour2",
				"disChargeEndMinute2",
				"disChargePower2",
				"chargeModeCode",
				"maxFeedIn",
				"minBatteryCapacity"
		);
		Map<String, String> map = CollUtil.zip(propNameList, propValueList);
		for (String name : needNameList) {
			if (!map.containsKey(name)) {
				propNameList.add(name);
				if ("minBatteryCapacity".equals(name)) {
					propValueList.add("10");
				} else {
					propValueList.add("0");
				}
			}
		}

	}

	private static void addPairAction(Integer start, Integer end, List<Pair<Integer, Integer>> pairList) {
		if (!(start == 0 && end == 0)) {
			pairList.add(Pair.of(start, end));
		}
	}
}
