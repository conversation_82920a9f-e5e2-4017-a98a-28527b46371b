package com.weihengtech.utils;

import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
public class LocaleUtil {

	public static String getMessage(String position) {
		MessageSource messageSource = (MessageSource) InitUtil.APPLICATION_CONTEXT.getBean("messageSource");
		String message = messageSource.getMessage(position, null, LocaleContextHolder.getLocale());
		if (StrUtil.isBlank(message)) {
			message = position;
		}
		return message;
	}

	public static String getLanguage(HttpServletRequest request) {
		String language = request.getHeader("Language");
		if (StrUtil.isBlank(language) || language.split("_").length != 2) {
			return "en_US";
		}
		return language;
	}

	public static String getLanguage() {
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
				.getRequest();
		String language = request.getHeader("Language");
		if (StrUtil.isBlank(language) || language.split("_").length != 2) {
			return "en_US";
		}
		return language;
	}

	public static String mapLocaleToDatabaseGetMethod(HttpServletRequest request) {
		String language = getLanguage(request);
		return mapLocaleToDatabaseGetMethod(language);
	}

	public static String mapLocaleToDatabaseGetMethod(String language) {
		String[] s = language.split("_");
		StrBuilder strBuilder = new StrBuilder();
		strBuilder.append("get");
		strBuilder.append(s[0].substring(0, 1).toUpperCase());
		strBuilder.append(s[0].substring(1).toLowerCase());
		strBuilder.append(s[1].substring(0, 1).toUpperCase());
		strBuilder.append(s[1].substring(1).toLowerCase());
		return strBuilder.toString();
	}
}
