package com.weihengtech.utils;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import lombok.val;

/**
 * <AUTHOR>
 */
public class BeanUtil {

	public static <T> void assertNotNull(T obj) {
		if (obj == null) {
			throw new CustomException(ExceptionEnum.DATA_ASSERT_EXCEPTION);
		}
	}

	public static <T> void compareAndSetStr(T t, String value, String method) {
		val nullStr = "null";
		OperationUtil.of(value).then(v -> {
			if (!nullStr.equals(v) && StrUtil.isNotBlank(v)) {
				ReflectUtil.invoke(t, method, value);
			}
		});
	}
}
