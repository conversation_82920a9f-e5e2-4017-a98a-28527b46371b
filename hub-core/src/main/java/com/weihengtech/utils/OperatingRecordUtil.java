package com.weihengtech.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.auth.model.dto.UserResDTO;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.pojo.dos.other.OperatingRecordDo;
import com.weihengtech.service.other.OperatingRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
public class OperatingRecordUtil {

	public static void log(Long userId, String format, Map<String, String> params, Integer module) {
		try {
			HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
					.getRequest();
			String terminal = Optional.ofNullable(request.getHeader("client")).orElse("unknown");
			Long createTime = System.currentTimeMillis();
			String content = buildContent(format, params);

			OperatingRecordService operatingRecordService = InitUtil.getBean(OperatingRecordService.class);
			OperatingRecordDo operatingRecordDo = new OperatingRecordDo();
			operatingRecordDo.setUserId(userId);
			operatingRecordDo.setCreateTime(createTime);
			operatingRecordDo.setIpAddress(getRealIp(request));
			operatingRecordDo.setTerminal(terminal);
			operatingRecordDo.setContent(content);
			operatingRecordDo.setModule(module);
			operatingRecordService.save(operatingRecordDo);
		} catch (Exception e) {
			log.error("operate log failed: ", e);
		}
	}

	public static void 	log(String format, Map<String, String> params, Integer module) {
		try {
			String userIdStr = Optional.ofNullable(UserInfoUtil.currentUser())
					.map(UserResDTO::getId)
					.orElse("-1");
			long userId = Long.parseLong(userIdStr);
			if (-1L != userId) {
				log(userId, format, params, module);
			}
		} catch (Exception e) {
			log.error("operate log failed: ", e);
		}
	}

	private static String buildContent(String format, Map<String, String> params) {
		if (null == params || CollUtil.isEmpty(params)) {
			return format;
		}
		return StrUtil.format(format, params);
	}

	private static String getRealIp(HttpServletRequest request) {
		String ip = request.getHeader("x-forwarded-for");
		if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		if (StrUtil.isNotBlank(ip) && ip.contains(",")) {
			ip = ip.split(",")[0];
		}
		log.info("ip {}", ip);
		return ip;
	}
}
