package com.weihengtech.handlers;

import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.common.exceptions.RoleException;
import com.weihengtech.utils.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@ControllerAdvice(basePackages = {"com.weihengtech.controller",
		"com.weihengtech.auth.controller"})
public class GlobalExceptionHandler {

	@ResponseBody
	@ExceptionHandler(value = CustomException.class)
	public EmptyResponse customExceptionHandler(CustomException e) {
		log.error(String.format("GlobalExceptionHandler=====>%s", e.getMessage()), e);
		return EmptyResponse.fail(e.getCode(), e.getMessage());
	}

	@ResponseStatus(HttpStatus.FORBIDDEN)
	@ResponseBody
	@ExceptionHandler(value = RoleException.class)
	public EmptyResponse roleExceptionHandler(RoleException e) {
		log.error(String.format("GlobalExceptionHandler=====>%s", e.getMessage()), e);
		return EmptyResponse.fail(HttpStatus.FORBIDDEN.value(), e.getMessage());
	}

	@ExceptionHandler(value = MethodArgumentNotValidException.class)
	@ResponseBody
	public DataResponse<Map<String, String>> bindExceptionHandler(MethodArgumentNotValidException e) {
		List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
		HashMap<String, String> hashMap = new HashMap<>(64);
		for (FieldError fieldError : fieldErrors) {
			String defaultMessage = fieldError.getDefaultMessage();
			String field = fieldError.getField();
			hashMap.put(field, LocaleUtil.getMessage(defaultMessage));
		}

		DataResponse<Map<String, String>> dataResponse = new DataResponse<>();
		dataResponse.setData(hashMap);
		dataResponse.setCode(ExceptionEnum.PARAM_VALID_EXCEPTION.getCode());
		dataResponse.setMsg(hashMap.size() > 0
				? hashMap.values().stream().findFirst().get()
				: LocaleUtil.getMessage(ExceptionEnum.PARAM_VALID_EXCEPTION.getMsg()));
		log.error("handleBindException======>" + dataResponse);
		return dataResponse;
	}

	@ExceptionHandler(value = SQLIntegrityConstraintViolationException.class)
	@ResponseBody
	public EmptyResponse duplicateKeyExceptionHandler() {
		return EmptyResponse.fail(ExceptionEnum.DUPLICATE_KEY_EXCEPTION);
	}

	@ExceptionHandler(value = ClassNotFoundException.class)
	@ResponseBody
	public EmptyResponse classNotFoundExceptionHandler() {
		return EmptyResponse.fail(ExceptionEnum.PARAM_VALID_EXCEPTION);
	}

	@ExceptionHandler(value = IllegalArgumentException.class)
	@ResponseBody
	public EmptyResponse illegalArgumentExceptionHandler(IllegalArgumentException e) {
		log.error(String.format("GlobalExceptionHandler=====>%s", e.getMessage()), e);
		return EmptyResponse.fail(ExceptionEnum.DATA_ASSERT_EXCEPTION.getCode(), e.getMessage());
	}

	@ExceptionHandler(value = RuntimeException.class)
	@ResponseBody
	public EmptyResponse runtimeExceptionHandler(RuntimeException e) {
		log.error(String.format("GlobalExceptionHandler=====>%s", e.getMessage()), e);
		return EmptyResponse.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage());
	}
}
