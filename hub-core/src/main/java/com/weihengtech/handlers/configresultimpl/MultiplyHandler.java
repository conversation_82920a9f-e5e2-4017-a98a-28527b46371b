package com.weihengtech.handlers.configresultimpl;

import com.weihengtech.annotation.SetOrder;
import com.weihengtech.enums.device.DeviceInfoEncodeTypeEnum;
import com.weihengtech.handlers.IConfigResultHandler;
import org.springframework.stereotype.Component;

import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@SetOrder(-1)
public class MultiplyHandler implements IConfigResultHandler {

	private final List<String> FILTER_TYPE = Arrays.asList(DeviceInfoEncodeTypeEnum.U16.name(),
			DeviceInfoEncodeTypeEnum.U32.name(), DeviceInfoEncodeTypeEnum.S16.name(),
			DeviceInfoEncodeTypeEnum.S32.name(), DeviceInfoEncodeTypeEnum.U32R.name()
	);

	@Override
	public String process(Map<String, String> config, String result) {
		String type = config.get("type");
		String processResult = result;
		if (FILTER_TYPE.contains(type)) {
			double decodeNum = Double.parseDouble(processResult);
			processResult = new DecimalFormat().format(decodeNum / Double.parseDouble(config.get("multiply"))).replaceAll(",", "");
		}
		return processResult;
	}
}
