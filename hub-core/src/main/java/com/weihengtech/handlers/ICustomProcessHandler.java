package com.weihengtech.handlers;

import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
public interface ICustomProcessHandler {

	/**
	 * 认证前置处理
	 *
	 * @param request HttpServletRequest
	 * @param handler MethodHandler
	 */
	void preHandle(HttpServletRequest request, Object handler);

	/**
	 * 人证后置处理
	 *
	 * @param request      HttpServletRequest
	 * @param response     HttpServletResponse
	 * @param handler      MethodHandler
	 * @param modelAndView mv
	 */
	void postHandle(
			HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView
	);

	/**
	 * 认证final处理
	 */
	void afterCompletion();
}
