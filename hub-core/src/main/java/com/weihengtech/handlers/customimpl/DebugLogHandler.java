package com.weihengtech.handlers.customimpl;

import com.weihengtech.annotation.SetOrder;
import com.weihengtech.handlers.ICustomProcessHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Component
@SetOrder(value = -10000)
@Slf4j
@Profile(value = {"dev"})
public class DebugLogHandler implements ICustomProcessHandler {

	@Override
	public void preHandle(HttpServletRequest request, Object handler) {
	}

	@Override
	public void postHandle(
			HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView
	) {
	}

	@Override
	public void afterCompletion() {
	}
}
