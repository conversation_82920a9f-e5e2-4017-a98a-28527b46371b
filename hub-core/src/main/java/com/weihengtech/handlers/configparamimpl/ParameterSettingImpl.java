package com.weihengtech.handlers.configparamimpl;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.annotation.SetOrder;
import com.weihengtech.handlers.IConfigParamHandler;
import com.weihengtech.pojo.dtos.device.config.ParameterSettingDTO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/23 15:18
 */
@Component
@SetOrder(3)
public class ParameterSettingImpl implements IConfigParamHandler {

    @Override
    public void process(String decode, Object param) {
        if (!"ParameterSetting".equals(decode)) {
            return;
        }
        ParameterSettingDTO item = (ParameterSettingDTO) param;
        String meterType = item.getMeterType();
        // meterType点位(1,2)，1代表高8位PV电表类型，2代表低8位电网电表类型
        if (StrUtil.isNotBlank(meterType)) {
            int meterTypeInt = Integer.parseInt(meterType);
            item.setMeterType(String.format("%d,%d", meterTypeInt / 256, meterTypeInt % 256));
        }
    }
}
