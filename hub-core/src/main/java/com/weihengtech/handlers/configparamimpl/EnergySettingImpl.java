package com.weihengtech.handlers.configparamimpl;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.annotation.SetOrder;
import com.weihengtech.handlers.IConfigParamHandler;
import com.weihengtech.pojo.dtos.device.config.EnergySettingDTO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/23 15:18
 */
@Component
@SetOrder(2)
public class EnergySettingImpl implements IConfigParamHandler {

    @Override
    public void process(String decode, Object param) {
        if (!"EnergySetting".equals(decode)) {
            return;
        }
        EnergySettingDTO item = (EnergySettingDTO) param;
        if (StrUtil.isNotBlank(item.getETotalPv()) || StrUtil.isNotBlank(item.getETotalPvToGrid())) {
            double eTotalPv = StrUtil.isBlank(item.getETotalPv()) ? 0 :
                    Double.parseDouble(item.getETotalPv());
            double eTotalPvToGrid = StrUtil.isBlank(item.getETotalPvToGrid()) ? 0 :
                    Double.parseDouble(item.getETotalPvToGrid());
            item.setETotalPv(String.valueOf(eTotalPv + eTotalPvToGrid));
        }
    }
}
