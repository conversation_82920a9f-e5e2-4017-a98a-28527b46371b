package com.weihengtech.handlers.configparamimpl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.annotation.SetOrder;
import com.weihengtech.handlers.IConfigParamHandler;
import com.weihengtech.pojo.dtos.device.config.BatterySettingDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/23 15:18
 */
@Component
@SetOrder(1)
public class BatterySettingImpl implements IConfigParamHandler {

    @Override
    public void process(String decode, Object param) {
        if (!"BatterySetting".equals(decode)) {
            return;
        }
        BatterySettingDTO item = (BatterySettingDTO) param;
        List<BatterySettingDTO.TimeInfo> chargeList = new ArrayList<>(12);
        List<BatterySettingDTO.TimeInfo> dischargeList = new ArrayList<>(12);
        JSONObject jsonItem = JSONUtil.parseObj(item);
        for (int i = 1; i <= 12; i++) {
            chargeList.add(BatterySettingDTO.TimeInfo.builder()
                    .beginHour(jsonItem.getStr("chargeBeginHour" + i))
                    .beginMinute(jsonItem.getStr("chargeBeginMinute" + i))
                    .endHour(jsonItem.getStr("chargeEndHour" + i))
                    .endMinute(jsonItem.getStr("chargeEndMinute" + i))
                    .power(jsonItem.getStr("chargePower" + i))
                    .abandonPv(jsonItem.getStr("chargeAbandonPv" + i))
                    .build());
            dischargeList.add(BatterySettingDTO.TimeInfo.builder()
                    .beginHour(jsonItem.getStr("disChargeBeginHour" + i))
                    .beginMinute(jsonItem.getStr("disChargeBeginMinute" + i))
                    .endHour(jsonItem.getStr("disChargeEndHour" + i))
                    .endMinute(jsonItem.getStr("disChargeEndMinute" + i))
                    .power(jsonItem.getStr("disChargePower" + i))
                    .abandonPv(jsonItem.getStr("disChargeAbandonPv" + i))
                    .build());
        }
        item.setChargeList(chargeList);
        item.setDischargeList(dischargeList);
    }
}
