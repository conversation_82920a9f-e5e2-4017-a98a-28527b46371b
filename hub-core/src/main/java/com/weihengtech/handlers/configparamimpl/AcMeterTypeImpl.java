package com.weihengtech.handlers.configparamimpl;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.annotation.SetOrder;
import com.weihengtech.handlers.IConfigParamHandler;
import com.weihengtech.pojo.dtos.device.config.AcMeterTypeDTO;
import org.springframework.stereotype.Component;

/**
 * @author: jiahao.jin
 * @create: 2025-07-09 17:26
 * @description:
 */
@Component
@SetOrder(5)
public class AcMeterTypeImpl implements IConfigParamHandler{
    @Override
    public void process(String decode, Object param) {
        if (!"AcMeterType".equals(decode)) {
            return;
        }

        AcMeterTypeDTO acMeterTypeDTO = (AcMeterTypeDTO) param;
        String meterType = acMeterTypeDTO.getMeterType();
        // meterType点位(1,2)，1代表高8位PV电表类型，2代表低8位电网电表类型
        if (StrUtil.isNotBlank(meterType)) {
            int meterTypeInt = Integer.parseInt(meterType);
            acMeterTypeDTO.setMeterType(String.format("%d,%d", meterTypeInt / 256, meterTypeInt % 256));
        }
    }
}
