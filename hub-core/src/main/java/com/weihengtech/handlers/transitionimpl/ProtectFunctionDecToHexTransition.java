package com.weihengtech.handlers.transitionimpl;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.annotation.SetOrder;
import com.weihengtech.handlers.IPropTransitionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@SetOrder
public class ProtectFunctionDecToHexTransition implements IPropTransitionHandler {

	@Override
	public String transform(String decodeStr, Map<String, String> config) {
		String startAddress = config.get("start");
		final String funEnHexAddress = "45001";
		if (funEnHexAddress.equals(startAddress) && StrUtil.isNotBlank(decodeStr)) {
			log.info(decodeStr);
			decodeStr = new BigInteger(decodeStr).toString(16).toUpperCase();
		}
		return decodeStr;
	}
}
