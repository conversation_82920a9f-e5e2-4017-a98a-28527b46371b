package com.weihengtech.handlers.transitionimpl;

import com.weihengtech.annotation.SetOrder;
import com.weihengtech.enums.device.DeviceRunningModeEnum;
import com.weihengtech.handlers.IPropTransitionHandler;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@SetOrder
public class RunModeCodeToDescriptionTransition implements IPropTransitionHandler {

	@Override
	public String transform(String decodeStr, Map<String, String> config) {
		String startAddress = config.get("start");
		final String runModeAddress = "30026";
		if (runModeAddress.equals(startAddress)) {
			for (DeviceRunningModeEnum runModeEnum : DeviceRunningModeEnum.values()) {
				if (runModeEnum.getCode().equals(decodeStr)) {
					return runModeEnum.getName();
				}
			}
		}
		return decodeStr;
	}
}
