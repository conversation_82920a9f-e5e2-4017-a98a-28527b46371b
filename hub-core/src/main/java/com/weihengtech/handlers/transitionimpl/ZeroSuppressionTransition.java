package com.weihengtech.handlers.transitionimpl;

import cn.hutool.core.util.NumberUtil;
import com.weihengtech.annotation.SetOrder;
import com.weihengtech.handlers.IPropTransitionHandler;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@SetOrder(value = 9999)
public class ZeroSuppressionTransition implements IPropTransitionHandler {

	@Override
	public String transform(String decodeStr, Map<String, String> config) {
		String type = config.get("type");
		if (!"BIT".equals(type) && NumberUtil.isNumber(decodeStr)) {
			decodeStr = NumberUtil.toStr(new BigDecimal(decodeStr));
		}
		return decodeStr;
	}
}
