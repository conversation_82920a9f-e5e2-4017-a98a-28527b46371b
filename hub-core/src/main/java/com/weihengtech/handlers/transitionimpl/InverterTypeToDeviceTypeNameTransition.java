package com.weihengtech.handlers.transitionimpl;

import com.weihengtech.annotation.SetOrder;
import com.weihengtech.enums.device.DeviceTypeNameEnum;
import com.weihengtech.handlers.IPropTransitionHandler;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@SetOrder
public class InverterTypeToDeviceTypeNameTransition implements IPropTransitionHandler {

	@Override
	public String transform(String decodeStr, Map<String, String> config) {
		String startAddress = config.get("start");
		final String inverterTypeAddress = "30001";
		// todo 也许要根据类型来给设备添加类型
		if (inverterTypeAddress.equals(startAddress)) {
			for (DeviceTypeNameEnum nameEnum : DeviceTypeNameEnum.values()) {
				if (nameEnum.getCode().equals(decodeStr)) {
					return nameEnum.getName();
				}
			}
		}
		return decodeStr;
	}
}
