package com.weihengtech.handlers.paramimpl;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.handlers.IParamHandler;
import org.springframework.stereotype.Component;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@Component
public class U32ParamHandler implements IParamHandler {

	@Override
	public void process(Object[] args) {
		String value = String.valueOf(args[4]);
		int startAddress = (Integer) args[1];
		int configAddress = 45001;
		if (startAddress == configAddress && StrUtil.isNotBlank(value)) {
			args[4] = new BigInteger(value, 16).toString(10);
		}
	}
}
