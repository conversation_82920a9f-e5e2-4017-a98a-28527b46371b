package com.weihengtech.handlers.postimpl;

import com.weihengtech.handlers.IPostHandler;
import com.weihengtech.service.device.DeviceListService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class VppModeSyncHandler implements IPostHandler {

	private final DeviceListService deviceListService;

	/**
	 * 同步vpp模式
	 *
	 * @param args 参数数组
	 */
	@Override
	public void process(Object[] args) {
		String deviceFlag = String.valueOf(args[0]);
		Integer start = (Integer) args[1];
		val vpp = 41036;
		if (vpp == start) {
			val vppOff = "0";
			String value = String.valueOf(args[4]);
			deviceListService.updateVppMode(!vppOff.equals(value), deviceFlag);
			log.info("vpp mode sync, deviceFlag: {}, value: {}", deviceFlag, value);
		}
	}
}
