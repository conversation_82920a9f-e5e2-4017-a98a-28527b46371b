package com.weihengtech.handlers.postimpl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.api.EcosClientApi;
import com.weihengtech.handlers.IPostHandler;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.ecos.ClientCustomizeDo;
import com.weihengtech.service.device.DeviceListService;
import lombok.val;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class ClientCustomizeSyncHandler implements IPostHandler {

	@Resource
	private EcosClientApi ecosClientApi;

	@Resource
	private DeviceListService deviceListService;

	@Override
	public void process(Object[] args) {
		Integer start = (Integer) args[1];
		val minIndex = 41002;
		val maxIndex = 41026;
		if ((start >= minIndex && start <= maxIndex) || start == 41037 || start == 41042 || start == 40046 || (start > 41043 && start < 41141)) {
			String deviceFlag = String.valueOf(args[0]);
			Optional.ofNullable(deviceListService.getOne(Wrappers.<DeviceListDO>lambdaQuery().eq(DeviceListDO::getDeviceName, deviceFlag)))
					.ifPresent(deviceListDO -> {
						ClientCustomizeDo clientCustomizeDo = new ClientCustomizeDo();
						clientCustomizeDo.setDeviceId(deviceListDO.getId());
						setFitProperty(clientCustomizeDo, Integer.parseInt(String.valueOf(args[4])), start);
						ecosClientApi.updateCustomizeInfo(clientCustomizeDo);
					});
		}
	}

	private void setFitProperty(ClientCustomizeDo clientCustomizeDo, Integer value, Integer start) {
		switch (start) {
			case 41002:
				clientCustomizeDo.setBatteryMin(value < 10 ? 10 : value);
				break;
			case 41003:
				clientCustomizeDo.setChargeMode(value);
				break;
			case 41037:
				clientCustomizeDo.setMaxFeedIn(value);
				break;
			case 41042:
				clientCustomizeDo.setEpsBatteryMin(value);
				break;
			case 40046:
				clientCustomizeDo.setDischargeToGridFlag(value);
				break;
			case 41008:
				clientCustomizeDo.setChargeStartHour1(value);
				break;
			case 41009:
				clientCustomizeDo.setChargeStartMinute1(value);
				break;
			case 41010:
				clientCustomizeDo.setChargeEndHour1(value);
				break;
			case 41011:
				clientCustomizeDo.setChargeEndMinute1(value);
				break;
			case 41013:
				clientCustomizeDo.setDischargeStartHour1(value);
				break;
			case 41014:
				clientCustomizeDo.setDischargeStartMinute1(value);
				break;
			case 41015:
				clientCustomizeDo.setDischargeEndHour1(value);
				break;
			case 41016:
				clientCustomizeDo.setDischargeEndMinute1(value);
				break;
			case 41018:
				clientCustomizeDo.setChargeStartHour2(value);
				break;
			case 41019:
				clientCustomizeDo.setChargeStartMinute2(value);
				break;
			case 41020:
				clientCustomizeDo.setChargeEndHour2(value);
				break;
			case 41021:
				clientCustomizeDo.setChargeEndMinute2(value);
				break;
			case 41023:
				clientCustomizeDo.setDischargeStartHour2(value);
				break;
			case 41024:
				clientCustomizeDo.setDischargeStartMinute2(value);
				break;
			case 41025:
				clientCustomizeDo.setDischargeEndHour2(value);
				break;
			case 41026:
				clientCustomizeDo.setDischargeEndMinute2(value);
				break;
			case 41043:
				clientCustomizeDo.setChargeStartHour3(value);
				break;
			case 41044:
				clientCustomizeDo.setChargeStartMinute3(value);
				break;
			case 41045:
				clientCustomizeDo.setChargeEndHour3(value);
				break;
			case 41046:
				clientCustomizeDo.setChargeEndMinute3(value);
				break;
			case 41048:
				clientCustomizeDo.setDischargeStartHour3(value);
				break;
			case 41049:
				clientCustomizeDo.setDischargeStartMinute3(value);
				break;
			case 41050:
				clientCustomizeDo.setDischargeEndHour3(value);
				break;
			case 41051:
				clientCustomizeDo.setDischargeEndMinute3(value);
				break;
			case 41053:
				clientCustomizeDo.setChargeStartHour4(value);
				break;
			case 41054:
				clientCustomizeDo.setChargeStartMinute4(value);
				break;
			case 41055:
				clientCustomizeDo.setChargeEndHour4(value);
				break;
			case 41056:
				clientCustomizeDo.setChargeEndMinute4(value);
				break;
			case 41058:
				clientCustomizeDo.setDischargeStartHour4(value);
				break;
			case 41059:
				clientCustomizeDo.setDischargeStartMinute4(value);
				break;
			case 41060:
				clientCustomizeDo.setDischargeEndHour4(value);
				break;
			case 41061:
				clientCustomizeDo.setDischargeEndMinute4(value);
				break;
			case 41063:
				clientCustomizeDo.setChargeStartHour5(value);
				break;
			case 41064:
				clientCustomizeDo.setChargeStartMinute5(value);
				break;
			case 41065:
				clientCustomizeDo.setChargeEndHour5(value);
				break;
			case 41066:
				clientCustomizeDo.setChargeEndMinute5(value);
				break;
			case 41068:
				clientCustomizeDo.setDischargeStartHour5(value);
				break;
			case 41069:
				clientCustomizeDo.setDischargeStartMinute5(value);
				break;
			case 41070:
				clientCustomizeDo.setDischargeEndHour5(value);
				break;
			case 41071:
				clientCustomizeDo.setDischargeEndMinute5(value);
				break;
			case 41073:
				clientCustomizeDo.setChargeStartHour6(value);
				break;
			case 41074:
				clientCustomizeDo.setChargeStartMinute6(value);
				break;
			case 41075:
				clientCustomizeDo.setChargeEndHour6(value);
				break;
			case 41076:
				clientCustomizeDo.setChargeEndMinute6(value);
				break;
			case 41078:
				clientCustomizeDo.setDischargeStartHour6(value);
				break;
			case 41079:
				clientCustomizeDo.setDischargeStartMinute6(value);
				break;
			case 41080:
				clientCustomizeDo.setDischargeEndHour6(value);
				break;
			case 41081:
				clientCustomizeDo.setDischargeEndMinute6(value);
				break;
			case 41083:
				clientCustomizeDo.setChargeStartHour7(value);
				break;
			case 41084:
				clientCustomizeDo.setChargeStartMinute7(value);
				break;
			case 41085:
				clientCustomizeDo.setChargeEndHour7(value);
				break;
			case 41086:
				clientCustomizeDo.setChargeEndMinute7(value);
				break;
			case 41088:
				clientCustomizeDo.setDischargeStartHour7(value);
				break;
			case 41089:
				clientCustomizeDo.setDischargeStartMinute7(value);
				break;
			case 41090:
				clientCustomizeDo.setDischargeEndHour7(value);
				break;
			case 41091:
				clientCustomizeDo.setDischargeEndMinute7(value);
				break;
			case 41093:
				clientCustomizeDo.setChargeStartHour8(value);
				break;
			case 41094:
				clientCustomizeDo.setChargeStartMinute8(value);
				break;
			case 41095:
				clientCustomizeDo.setChargeEndHour8(value);
				break;
			case 41096:
				clientCustomizeDo.setChargeEndMinute8(value);
				break;
			case 41098:
				clientCustomizeDo.setDischargeStartHour8(value);
				break;
			case 41099:
				clientCustomizeDo.setDischargeStartMinute8(value);
				break;
			case 41100:
				clientCustomizeDo.setDischargeEndHour8(value);
				break;
			case 41101:
				clientCustomizeDo.setDischargeEndMinute8(value);
				break;
			case 41103:
				clientCustomizeDo.setChargeStartHour9(value);
				break;
			case 41104:
				clientCustomizeDo.setChargeStartMinute9(value);
				break;
			case 41105:
				clientCustomizeDo.setChargeEndHour9(value);
				break;
			case 41106:
				clientCustomizeDo.setChargeEndMinute9(value);
				break;
			case 41108:
				clientCustomizeDo.setDischargeStartHour9(value);
				break;
			case 41109:
				clientCustomizeDo.setDischargeStartMinute9(value);
				break;
			case 41110:
				clientCustomizeDo.setDischargeEndHour9(value);
				break;
			case 41111:
				clientCustomizeDo.setDischargeEndMinute9(value);
				break;
			case 41113:
				clientCustomizeDo.setChargeStartHour10(value);
				break;
			case 41114:
				clientCustomizeDo.setChargeStartMinute10(value);
				break;
			case 41115:
				clientCustomizeDo.setChargeEndHour10(value);
				break;
			case 41116:
				clientCustomizeDo.setChargeEndMinute10(value);
				break;
			case 41118:
				clientCustomizeDo.setDischargeStartHour10(value);
				break;
			case 41119:
				clientCustomizeDo.setDischargeStartMinute10(value);
				break;
			case 41120:
				clientCustomizeDo.setDischargeEndHour10(value);
				break;
			case 41121:
				clientCustomizeDo.setDischargeEndMinute10(value);
				break;
			case 41123:
				clientCustomizeDo.setChargeStartHour11(value);
				break;
			case 41124:
				clientCustomizeDo.setChargeStartMinute11(value);
				break;
			case 41125:
				clientCustomizeDo.setChargeEndHour11(value);
				break;
			case 41126:
				clientCustomizeDo.setChargeEndMinute11(value);
				break;
			case 41128:
				clientCustomizeDo.setDischargeStartHour11(value);
				break;
			case 41129:
				clientCustomizeDo.setDischargeStartMinute11(value);
				break;
			case 41130:
				clientCustomizeDo.setDischargeEndHour11(value);
				break;
			case 41131:
				clientCustomizeDo.setDischargeEndMinute11(value);
				break;
			case 41133:
				clientCustomizeDo.setChargeStartHour12(value);
				break;
			case 41134:
				clientCustomizeDo.setChargeStartMinute12(value);
				break;
			case 41135:
				clientCustomizeDo.setChargeEndHour12(value);
				break;
			case 41136:
				clientCustomizeDo.setChargeEndMinute12(value);
				break;
			case 41138:
				clientCustomizeDo.setDischargeStartHour12(value);
				break;
			case 41139:
				clientCustomizeDo.setDischargeStartMinute12(value);
				break;
			case 41140:
				clientCustomizeDo.setDischargeEndHour12(value);
				break;
			case 41141:
				clientCustomizeDo.setDischargeEndMinute12(value);
				break;
			default:
		}
	}
}
