package com.weihengtech.handlers.postimpl;

import com.weihengtech.enums.device.SubscriptionEnum;
import com.weihengtech.handlers.IPostHandler;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.device.StorageEnergyInfoDO;
import com.weihengtech.pojo.dtos.device.DynamicDesignDTO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.device.StorageEnergyInfoService;
import com.weihengtech.utils.TransactionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/8/7 16:46
 * @version 1.0
 */
@Component
public class DynamicExportHandler implements IPostHandler {

	@Resource
	private DeviceListService deviceListService;
	@Resource
	private StorageEnergyInfoService storageEnergyInfoService;

	/**
	 * 同步vpp模式
	 *
	 * @param args 参数数组
	 */
	@Override
	public void process(Object[] args) {
		String deviceFlag = String.valueOf(args[0]);
		Integer start = (Integer) args[1];
		// 并机模式
		if (start == 48120) {
			int value = Integer.parseInt((String) args[4]);
			if (value > 1) {
				// 设备由主机/关闭并机 变为 从机，则该设备的动态出口配置数据清空（如果该设备已配置动态出口）
 				DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(deviceFlag);
				StorageEnergyInfoDO dynamicInfo = storageEnergyInfoService.getByDeviceId(deviceInfo.getId());
				if (dynamicInfo != null) {
					storageEnergyInfoService.removeById(dynamicInfo.getId());
				}
			}
		}
		// 并机数量
		if (start == 48121) {
			// 设备由关闭并机变为主机，则更新最大输出功率
			// 设备由主机变为关闭并机，则更新最大输出功率
			// 添加或减少从机，则更新对应主机的最大输出功率
			DeviceListDO deviceInfo = deviceListService.getDeviceInfoByDeviceName(deviceFlag);
			StorageEnergyInfoDO dynamicInfo = storageEnergyInfoService.getByDeviceId(deviceInfo.getId());
			if (dynamicInfo == null) {
				return;
			}
//			Integer power = storageEnergyInfoService.maxExportPower(deviceFlag);
			DynamicDesignDTO powerRes = storageEnergyInfoService.designInfo(deviceFlag);
			if (powerRes.getDesignMaxPower() != 0 && !powerRes.getDesignMaxPower().equals(dynamicInfo.getDesignMaxPower())) {
				dynamicInfo.setDesignMaxPower(powerRes.getDesignMaxPower());
			}
			if (powerRes.getMaxVa() != 0 && !powerRes.getMaxVa().equals(dynamicInfo.getMaxVa())) {
				dynamicInfo.setMaxVa(powerRes.getMaxVa());
			}
			if (powerRes.getMaxVar() != 0 && !powerRes.getMaxVar().equals(dynamicInfo.getMaxVar())) {
				dynamicInfo.setMaxVar(powerRes.getMaxVar());
			}
			if (powerRes.getMaxWh() != 0 && !powerRes.getMaxWh().equals(dynamicInfo.getMaxWh())) {
				dynamicInfo.setMaxWh(powerRes.getMaxWh());
			}
			storageEnergyInfoService.updateById(dynamicInfo);
		}
	}
}
