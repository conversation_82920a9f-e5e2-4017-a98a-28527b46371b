package com.weihengtech.handlers.postimpl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.handlers.IPostHandler;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.service.device.DeviceListService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 同步安规id
 *
 * <AUTHOR>
 * @date 2025/1/15 15:40
 * @version 1.0
 */
@Component
@RequiredArgsConstructor
public class SafetyStandardSyncHandler implements IPostHandler {

	private final DeviceListService deviceListService;

	/**
	 * 同步安规id
	 *
	 * @param args 参数数组
	 */
	@Override
	public void process(Object[] args) {
		String deviceFlag = String.valueOf(args[0]);
		Integer start = (Integer) args[1];
		if (44001 == start) {
			Integer value = Integer.parseInt(String.valueOf(args[4]));
			deviceListService.update(
					new DeviceListDO().withSafetyStandard(value),
					Wrappers.<DeviceListDO>query().eq("device_name", deviceFlag)
			);
		}
	}
}
