package com.weihengtech.tasks;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Lazy(value = false)
@Slf4j
@Profile(value = {"dev", "prod"})
public class SyncTimeZoneTask {

	@Value("${custom.timezone}")
	private String timeZone;

	@Scheduled(cron = "0 */30 * * * ?")
	public void syncTimeZone() {
		System.setProperty("user.timezone", timeZone);
	}
}
