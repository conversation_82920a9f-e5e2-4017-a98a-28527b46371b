package com.weihengtech.tasks;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.enums.device.RatedPowerEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.utils.PhaseUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 更新设备额定功率定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 10:23
 */
@Service
@Slf4j
@ConditionalOnProperty(prefix = "custom.xxl.job", value = "enable", havingValue = "true")
public class SyncDeviceRatedPowerTask {

    @Autowired
    private DeviceListService deviceListService;
    @Autowired
    private StrategyService strategyService;

    @XxlJob("syncDeviceRatedPower")
    public void syncDeviceRatedPower() {
        LambdaQueryWrapper<DeviceListDO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(DeviceListDO::getDataSource, 0, 1, 3, 9);
        wrapper.isNull(DeviceListDO::getRatedPower);
        List<DeviceListDO> deviceList = deviceListService.list(wrapper);
        List<DeviceListDO> storageList = deviceList.stream()
                .filter(i -> i.getDeviceSn().startsWith("S") || i.getDeviceSn().startsWith("T"))
                .collect(Collectors.toList());
        storageList.forEach(i -> i.setRatedPower(RatedPowerEnum.getPowerBySn(i.getDeviceSn())));
        List<DeviceListDO> successList = storageList.stream()
                .filter(i -> StrUtil.isNotBlank(i.getRatedPower()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(successList)) {
            deviceListService.updateBatchById(successList);
        }
        printLog(String.format("共有%d台设备根据Sn解析额定功率成功", successList.size()));
        List<DeviceListDO> validList = storageList.stream()
                .filter(i -> StrUtil.isBlank(i.getRatedPower()))
                .collect(Collectors.toList());
        printLog(String.format("共有%d台设备无法根据Sn解析额定功率，需要通过透传读取", validList.size()));
        for (DeviceListDO deviceInfo : validList) {
            SpecificServService specificServService = strategyService.chooseSpecificServ(deviceInfo);
            try {
                List<Integer> integers = specificServService.sendReadCommand(deviceInfo.getWifiSn(), 1, 42009, 1);
                if (CollUtil.isEmpty(integers)) {
                    printLog(String.format("设备%s读取额定功率失败", deviceInfo.getDeviceSn()));
                } else {
                    deviceInfo.setRatedPower(getValByDevice(integers.get(0), deviceInfo.getDeviceSn()));
                    deviceListService.updateById(deviceInfo);
                    printLog(String.format("设备%s更新额定功率成功：%s", deviceInfo.getDeviceSn(), integers.get(0)));
                }
            } catch (Exception e) {
                printLog(String.format("设备%s读取额定功率失败", deviceInfo.getDeviceSn()), e);
            }
        }

    }

    /** 不同设备类型解析额定功率 */
    private String getValByDevice(Integer code, String deviceSn) {
        if (PhaseUtil.isUs(deviceSn)) {
            return NaRatedPowerEnum.getVal(code);
        } else if (PhaseUtil.isSinglePhase(deviceSn)) {
            return SingleRatedPowerEnum.getVal(code);
        } else if (PhaseUtil.isThreePhase(deviceSn)) {
            return ThreeRatedPowerEnum.getVal(code);
        } else {
            return null;
        }
    }

    /** 打印日志 */
    private void printLog(String msg, Exception... e) {
        if (e == null) {
            log.info(msg);
            XxlJobHelper.log(msg);
        }else {
            log.error(msg, e);
            XxlJobHelper.log(msg, e);
        }
    }

    @AllArgsConstructor
    private enum SingleRatedPowerEnum {

        _300(0, "3000W"),
        _368(1, "3680W"),
        _460(2, "4600W"),
        _500(3, "5000W"),
        _600(4, "6000W");

        private final Integer code;
        private final String val;

        public static String getVal(int code) {
            return Arrays.stream(SingleRatedPowerEnum.values())
                    .filter(i -> i.code == code)
                    .map(i -> i.val)
                    .findFirst()
                    .orElse(null);
        }
    }

    @AllArgsConstructor
    private enum ThreeRatedPowerEnum {
        _0(0, "5000W"),
        _1(1, "6000W"),
        _2(2, "8000W"),
        _3(3, "1000W"),
        _4(4, "1200W"),
        _5(5, "1300W");

        private final Integer code;
        private final String val;

        public static String getVal(int code) {
            return Arrays.stream(SingleRatedPowerEnum.values())
                    .filter(i -> i.code == code)
                    .map(i -> i.val)
                    .findFirst()
                    .orElse(null);
        }
    }

    @AllArgsConstructor
    private enum NaRatedPowerEnum {
        _0(0, "5000W"),
        _1(1, "6000W"),
        _2(2, "7600W"),
        _3(3, "9600W"),
        _4(4, "11400W");

        private final Integer code;
        private final String val;

        public static String getVal(int code) {
            return Arrays.stream(SingleRatedPowerEnum.values())
                    .filter(i -> i.code == code)
                    .map(i -> i.val)
                    .findFirst()
                    .orElse(null);
        }
    }
}
