package com.weihengtech.tasks;

import cn.hutool.core.lang.Dict;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class DelayQueueMessage implements Delayed {

	private long delayTime;

	private Dict params;

	public DelayQueueMessage(long delayTime) {
		this.delayTime = delayTime;
	}

	@Override
	public long getDelay(@NotNull TimeUnit unit) {
		return unit.convert(this.delayTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
	}

	@Override
	public int compareTo(@NotNull Delayed o) {
		long thisDelay = this.getDelay(TimeUnit.MILLISECONDS);
		long otherDelay = o.getDelay(TimeUnit.MILLISECONDS);
		return Long.compare(thisDelay, otherDelay);
	}
}
