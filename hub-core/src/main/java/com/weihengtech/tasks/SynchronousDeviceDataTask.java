package com.weihengtech.tasks;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.weihengtech.auth.dto.ResourceListReqDTO;
import com.weihengtech.auth.dto.ResourceResDTO;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.other.RetryService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.utils.OperationUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "custom.xxl.job", value = "enable", havingValue = "true")
public class SynchronousDeviceDataTask {

	@Value("${custom.datacenter.name}")
	private String datacenter;

	@Resource
	private DeviceListService deviceListService;
	@Resource
	private StrategyService strategyService;
	@Resource
	private RetryService retryService;
	@Resource
	private StringRedisTemplate stringRedisTemplate;
	@Resource
	private TuyaDatacenterService tuyaDatacenterService;
	@Resource
	private AuthCenterResClient authCenterResClient;

	@XxlJob("syncDeviceBasicInfo")
	public void syncDeviceBasicInfo() {
		log.info("syncDeviceBasicInfo");
		int count = deviceListService.count(Wrappers.<DeviceListDO>lambdaQuery()
				.in(DeviceListDO::getDataSource, 0, 1, 3));
		int pages = count % 100 == 0 ? count / 100 : count / 100 + 1;
		for (int i = 1; i <= pages; i++) {
			List<DeviceListDO> deviceListDOList = listDevice(i);
			deviceListDOList.forEach(this::update);
		}
	}

	/**
	 * 分页获取储能设备
	 *
	 * @param pageNum 当前页
	 * @return 设备列表
	 */
	private List<DeviceListDO> listDevice(int pageNum) {
		PageHelper.startPage(pageNum, 100);
		LambdaQueryWrapper<DeviceListDO> wrapper = Wrappers.<DeviceListDO>lambdaQuery()
				.in(DeviceListDO::getDataSource, 0, 1, 3);
		List<DeviceListDO> deviceListDOList = deviceListService.list(wrapper);
		String idList = deviceListDOList.stream()
				.map(DeviceListDO::getId)
				.map(String::valueOf)
				.collect(Collectors.joining(Constants.COMMA));
		List<ResourceResDTO> resourceList = authCenterResClient.resourceList(ResourceListReqDTO.builder()
				.ids(idList)
				.dataCenter(datacenter)
				.build());
		Map<String, Integer> deviceMap = resourceList.stream()
				.collect(Collectors.toMap(ResourceResDTO::getId, ResourceResDTO::getCategory));
		return deviceListDOList.stream()
				.filter(i -> deviceMap.get(String.valueOf(i.getId())) != null &&
						deviceMap.get(String.valueOf(i.getId()))!= 105)
				.collect(Collectors.toList());
	}

	@DSTransactional
	private void update(DeviceListDO deviceListDO) {
		try {
			String wifiSn = deviceListDO.getWifiSn();
			DeviceStatusEnum deviceStatusEnum = setState(deviceListDO, wifiSn);
			if (deviceStatusEnum.equals(DeviceStatusEnum.ONLINE) && Boolean.FALSE.equals(stringRedisTemplate.hasKey("NEW-BIND:" + wifiSn))) {
				retryService.syncDeviceVersion(deviceListDO, true);
			}
		} catch (Exception e) {
			log.warn("SynchronousDeviceDataTask.update: {}", e.getMessage());
		}
	}

	private DeviceStatusEnum setState(DeviceListDO deviceListDO, String wifiSn) {
		DeviceStatusEnum deviceStatusEnum;
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		// 如果没有首次安装时间就是未知
		if (deviceListDO.getFirstInstall() == 0) {
			deviceStatusEnum =  DeviceStatusEnum.UNKNOWN;
		} else {
			deviceStatusEnum = specificServService.checkDeviceStatus(wifiSn);
		}
		deviceListDO.setState(deviceStatusEnum.getDbCode());
		if (deviceStatusEnum.equals(DeviceStatusEnum.ONLINE)) {
			OperationUtil
					.of(specificServService.getDeviceAssignProperty(
							deviceListDO.getDeviceName(),
							"sys_run_mode"
					))
					.ifPresentOrElse(
							dict -> deviceListDO
									.setState(NumberUtil.round(String.valueOf(dict.get("sys_run_mode")), 0).intValue()),
							() -> deviceListDO.setState(DeviceStatusEnum.OFFLINE.getDbCode())
					);
		}
		return deviceStatusEnum;
	}
}
