package com.weihengtech.tasks;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.pojo.dos.xxl.XxlJobLogDo;
import com.weihengtech.service.xxl.XxlJobLogService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "custom.xxl.job", value = "enable", havingValue = "true")
public class XxlJobTask {

	@Resource
	private XxlJobLogService xxlJobLogService;

	@XxlJob("cleanExecutorLog")
	public void cleanExecutorLog() {
		QueryWrapper<XxlJobLogDo> queryWrapper = Wrappers.query();
		queryWrapper.select("max(id) as id");
		XxlJobLogDo xxlJobLogDo = xxlJobLogService.getOne(queryWrapper);
		if (null != xxlJobLogDo) {
			log.info("cleanExecutorLog");
			xxlJobLogService.remove(
					Wrappers.<XxlJobLogDo>lambdaQuery().lt(XxlJobLogDo::getId, xxlJobLogDo.getId() - 1000)
			);
		}
	}
}
