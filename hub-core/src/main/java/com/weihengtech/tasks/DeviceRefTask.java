package com.weihengtech.tasks;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.auth.dto.ResourceListReqDTO;
import com.weihengtech.auth.dto.ResourceResDTO;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.consts.TsdbMetricsConstants;
import com.weihengtech.dao.device.DeviceListMapper;
import com.weihengtech.enums.charger.ChargerStatusEnum;
import com.weihengtech.enums.device.DeviceDatasourceEnum;
import com.weihengtech.enums.device.DeviceInfoEncodeTypeEnum;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.enums.device.ImportTypeEnum;
import com.weihengtech.enums.device.SeriesEnum;
import com.weihengtech.enums.socket.SocketStatusEnum;
import com.weihengtech.event.CustomEvent;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.Connector;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpStatusResponse;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.iot.IotClientService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.service.specific.impl.SpecificServElinkServiceImpl;
import com.weihengtech.service.specific.impl.SpecificServTuyaServiceImpl;
import com.weihengtech.service.specific.impl.SpecificServWhServiceImpl;
import com.weihengtech.service.thirdpart.TimeSeriesDatabaseService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.AliMqttUtil;
import com.weihengtech.utils.InitUtil;
import com.weihengtech.utils.ModbusParseUtil;
import com.weihengtech.utils.ModbusRequestUtil;
import com.weihengtech.utils.OperationUtil;
import com.weihengtech.utils.SnowFlakeUtil;
import com.weihengtech.utils.SpecialParseUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
//@ConditionalOnProperty(prefix = "custom.xxl.job", value = "enable", havingValue = "true")
public class DeviceRefTask {

	@Value("${custom.datacenter.name}")
	private String datacenter;

	@Resource
	private DeviceListMapper deviceListMapper;
	@Resource
	private DeviceListService deviceListService;
	@Resource
	private SpecificServTuyaServiceImpl specificServTuyaService;
	@Resource
	private SpecificServElinkServiceImpl specificServElinkService;
	@Resource
	private SpecificServWhServiceImpl specificServWhService;
	@Resource
	private AliMqttUtil aliMqttUtil;
	@Resource
	private ModbusRequestUtil modbusRequestUtil;
	@Resource
	private SnowFlakeUtil snowFlakeUtil;
	@Resource
	private IotClientService tuyaIotClient;
	@Resource
	private IotClientService eLinkIotClient;
	@Resource
	private IotClientService ocppIotClient;
	@Resource
	private IotClientService whIotClient;
	@Resource
	private AuthCenterResClient authCenterResClient;
	@Resource
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;
	@Resource
	private StrategyService strategyService;

	@XxlJob("syncDeviceState")
	public void syncDeviceState() {
		log.info("syncDeviceState");
		List<String> offlineDeviceNameList = new LinkedList<>();
		List<String> elinterOnlineWifiList;
		List<String> tuyaOnlineWifiList;
		List<String> chargerOnlineList;
		List<String> whOnlineList;
		try {
			elinterOnlineWifiList = eLinkIotClient.getAllOnlineDevice();
			tuyaOnlineWifiList = tuyaIotClient.getAllOnlineDevice();
			chargerOnlineList = ocppIotClient.getAllOnlineDevice();
			whOnlineList = whIotClient.getAllOnlineDevice();
		} catch (Exception e) {
			log.warn("syncDeviceState#allOnlineDevice  {}", e.getMessage());
			return;
		}

		if (CollUtil.isNotEmpty(elinterOnlineWifiList)) {
			List<DeviceListDO> offlineDeviceList = queryCurrentDatacenterOfflineDevice(
					datacenter, DeviceDatasourceEnum.ELINK.getDatasource(), elinterOnlineWifiList);
				if (CollUtil.isNotEmpty(offlineDeviceList)) {
				updateOfflineDevice(offlineDeviceList, offlineDeviceNameList);
			}
			updateElinterOnlineDevice(elinterOnlineWifiList);
		}

		if (CollUtil.isNotEmpty(tuyaOnlineWifiList)) {
			List<DeviceListDO> offlineDeviceList = queryCurrentDatacenterOfflineDevice(
					datacenter, DeviceDatasourceEnum.TUYA.getDatasource(), tuyaOnlineWifiList);
			if (CollUtil.isNotEmpty(offlineDeviceList)) {
				updateOfflineDevice(offlineDeviceList, offlineDeviceNameList);
			}
			updateTuyaOnlineDevice(tuyaOnlineWifiList);
		}

		if (CollUtil.isNotEmpty(chargerOnlineList)) {
			List<DeviceListDO> offlineDeviceList = queryCurrentDatacenterOfflineDevice(
					datacenter, DeviceDatasourceEnum.OCPP.getDatasource(), chargerOnlineList);
			if (CollUtil.isNotEmpty(offlineDeviceList)) {
				updateOfflineDevice(offlineDeviceList, offlineDeviceNameList);
			}
			updateChargerOnlineDevice(chargerOnlineList);
		}

		if (CollUtil.isNotEmpty(whOnlineList)) {
			List<DeviceListDO> offlineDeviceList = queryCurrentDatacenterOfflineDevice(
					datacenter, DeviceDatasourceEnum.WH.getDatasource(), whOnlineList);
			if (CollUtil.isNotEmpty(offlineDeviceList)) {
				updateOfflineDevice(offlineDeviceList, offlineDeviceNameList);
			}
			updateWhOnlineDevice(whOnlineList);
		}

		if (CollUtil.isNotEmpty(offlineDeviceNameList)) {
			threadPoolTaskExecutor.execute(() -> InitUtil.APPLICATION_CONTEXT
					.publishEvent(new CustomEvent(CustomEvent.Type.FOCUSED_DEVICE_OFFLINE, offlineDeviceNameList)));
		}
	}

	private List<DeviceListDO> queryCurrentDatacenterOfflineDevice(String datacenter,
                                                                   int datasource,
                                                                   List<String> onlineWifiList) {
		List<DeviceListDO> deviceList = deviceListService.getDeviceByDatacenter(datacenter);
		return deviceList.stream()
				.filter(i -> i.getState() >= 0)
				.filter(i -> datasource == i.getDataSource())
				.filter(i -> !onlineWifiList.contains(i.getWifiSn()))
				.collect(Collectors.toList());
	}

	private void updateOfflineDevice(List<DeviceListDO> offlineDeviceList, List<String> offlineDeviceNameList) {
		if (CollUtil.isNotEmpty(offlineDeviceList)) {
			List<Long> unknownDeviceIdList = new ArrayList<>();
			List<Long> offlineDeviceIdList = offlineDeviceList.parallelStream().map(offlineDevice -> {
				offlineDeviceNameList.add(offlineDevice.getDeviceName());
				if (offlineDevice.getFirstInstall() == 0) {
					unknownDeviceIdList.add(offlineDevice.getId());
					return null;
				} else {
					return offlineDevice.getId();
				}
			}).collect(Collectors.toList());
			// 利用filter移除null值
			offlineDeviceIdList = offlineDeviceIdList.parallelStream()
					.filter(Objects::nonNull)
					.collect(Collectors.toList());
			if (CollUtil.isNotEmpty(offlineDeviceList)) {
				LambdaUpdateWrapper<DeviceListDO> updateWrapper = Wrappers.<DeviceListDO>lambdaUpdate()
						.in(DeviceListDO::getId, offlineDeviceIdList)
						.set(DeviceListDO::getState, DeviceStatusEnum.OFFLINE.getDbCode());
				deviceListService.update(updateWrapper);
			}
			if (CollUtil.isNotEmpty(unknownDeviceIdList)) {
				LambdaUpdateWrapper<DeviceListDO> updateWrapper2 = Wrappers.<DeviceListDO>lambdaUpdate()
						.in(DeviceListDO::getId, unknownDeviceIdList)
						.set(DeviceListDO::getState, DeviceStatusEnum.UNKNOWN.getDbCode());
				deviceListService.update(updateWrapper2);
			}
		}
	}

	private void updateElinterOnlineDevice(List<String> onlineWifiList) {
		List<DeviceListDO> onlineDeviceList = deviceListMapper.selectList(
				Wrappers.<DeviceListDO>lambdaQuery()
						.in(DeviceListDO::getWifiSn, onlineWifiList)
						.eq(DeviceListDO::getDataSource, DeviceDatasourceEnum.ELINK.getDatasource())
		);
		// 使用通用方法查询SOC
		Map<String, BigDecimal> deviceSocMap = querySocForOnlineDevices(onlineDeviceList);

		onlineDeviceList.parallelStream().forEach(device -> {
			try {
				OperationUtil
						.of(specificServElinkService.getDeviceAssignProperty(device.getDeviceName(), "sys_run_mode"))
						.ifPresentOrElse(
								dict -> device.setState(NumberUtil.round(String.valueOf(dict.get("sys_run_mode")), 0).intValue()),
								() -> device.setState(DeviceStatusEnum.ONLINE.getCode())
						);

				// 如果没有首次安装时间就是未知
				if (device.getFirstInstall() == 0) {
					device.setState(DeviceStatusEnum.UNKNOWN.getDbCode());
				}

				// 设置SOC
				device.setBatSoc(deviceSocMap.get(device.getDeviceName()));
				deviceListService.updateById(device);
			} catch (Exception e) {
				log.warn("Elinter sys_run_mode get error  {}", e.getMessage());
			}
		});
	}

	private void updateTuyaOnlineDevice(List<String> onlineWifiList) {
		List<DeviceListDO> onlineDeviceList = deviceListMapper.selectList(
				Wrappers.<DeviceListDO>lambdaQuery()
						.in(DeviceListDO::getWifiSn, onlineWifiList)
						.eq(DeviceListDO::getDataSource, DeviceDatasourceEnum.TUYA.getDatasource())
		);
		String ids = onlineDeviceList.stream()
				.map(DeviceListDO::getId)
				.map(String::valueOf)
				.collect(Collectors.joining(Constants.COMMA));
		List<ResourceResDTO> resourceList = authCenterResClient.resourceList(ResourceListReqDTO.builder()
				.ids(ids).build());
		Map<String, Integer> seriesMap = resourceList.stream()
				.collect(Collectors.toMap(ResourceResDTO::getId, ResourceResDTO::getCategory));

		// 查询SOC数据（仅对储能设备）
		List<DeviceListDO> storageDeviceList = onlineDeviceList.stream()
				.filter(i -> !SeriesEnum.Smart_Plug.getId().equals(seriesMap.get(String.valueOf(i.getId()))))
				.collect(Collectors.toList());

		Map<String, BigDecimal> deviceSocMap = querySocForOnlineDevices(storageDeviceList);

		// 没有首次安装时间就是未知
		onlineDeviceList.stream()
				.filter(i -> i.getFirstInstall() == 0)
				.forEach(i -> i.setState(DeviceStatusEnum.UNKNOWN.getDbCode()));
		// 储能机查询sys_run_mode状态
		onlineDeviceList.stream()
				.filter(i -> i.getFirstInstall() != 0)
				.filter(i -> !SeriesEnum.Smart_Plug.getId().equals(seriesMap.get(String.valueOf(i.getId()))))
				.forEach(i -> {
					updateStorageState(i);
					// 设置SOC
					i.setBatSoc(deviceSocMap.get(i.getDeviceName()));
					deviceListService.updateById(i);
				});
		// 单插查询switch_1状态
		onlineDeviceList.stream()
				.filter(i -> i.getFirstInstall() != 0)
				.filter(i -> SeriesEnum.Smart_Plug.getId().equals(seriesMap.get(String.valueOf(i.getId()))))
				.forEach(i -> {
					updateSocketState(i);
					deviceListService.updateById(i);
				});
	}

	/** 更新储能机状态 */
	private void updateStorageState(DeviceListDO device) {
		try {
			OperationUtil
					.of(specificServTuyaService.getDeviceAssignProperty(device.getDeviceName(), "sys_run_mode"))
					.ifPresentOrElse(
							dict -> device.setState(NumberUtil.round(String.valueOf(dict.get("sys_run_mode")), 0).intValue()),
							() -> device.setState(DeviceStatusEnum.ONLINE.getCode())
					);
		} catch (Exception e) {
			log.warn("Tuya sys_run_mode get error  {}", e.getMessage());
		}
	}

	/** 更新单插状态 */
	private void updateSocketState(DeviceListDO device) {
		OperationUtil
				.of(specificServTuyaService.getDeviceAssignProperty(
						device.getDeviceName(),
						"switch_1"
				))
				.ifPresentOrElse(
						dict -> {
							try {
								boolean switch1 = Boolean.parseBoolean(String.valueOf(dict.get("switch_1")));
								Integer dbCode = SocketStatusEnum.getIdByCode(switch1);
								log.info("device: {}, updateState is: {}", device.getDeviceName(), dbCode);
								device.setState(dbCode);
							} catch (Exception e) {
								device.setState(DeviceStatusEnum.ONLINE.getCode());
							}
						},
						() -> device.setState(DeviceStatusEnum.OFFLINE.getDbCode())
				);
	}

	private void updateChargerOnlineDevice(List<String> chargerOnlineList) {
		List<DeviceListDO> onlineDeviceList = deviceListMapper.selectList(
				Wrappers.<DeviceListDO>lambdaQuery()
						.in(DeviceListDO::getWifiSn, chargerOnlineList)
						.eq(DeviceListDO::getDataSource, DeviceDatasourceEnum.OCPP.getDatasource())
		);

		for (DeviceListDO device : onlineDeviceList) {
			try {
				// 如果没有首次安装时间就是未知
				if (device.getFirstInstall() == 0) {
					device.setState(DeviceStatusEnum.UNKNOWN.getDbCode());
				} else {
					CpStatusResponse chargerStatus = ocppIotClient.getChargerStatus(device.getDeviceSn());
					List<Connector> connectors = chargerStatus.getConnectors();
					ActionFlagUtil.assertTrue(CollUtil.isNotEmpty(connectors));
					String status = connectors.get(0).getStatus();
					device.setState(ChargerStatusEnum.getCodeByStatus(status));
				}
				deviceListService.updateById(device);
			} catch (Exception e) {
				log.warn("ocpp get status error  {}", e.getMessage());
			}
		}
	}

	private void updateWhOnlineDevice(List<String> onlineWifiList) {
		List<DeviceListDO> onlineDeviceList = deviceListMapper.selectList(
				Wrappers.<DeviceListDO>lambdaQuery()
						.in(DeviceListDO::getWifiSn, onlineWifiList)
						.eq(DeviceListDO::getDataSource, DeviceDatasourceEnum.WH.getDatasource())
		);

		// 查询SOC数据
		Map<String, BigDecimal> deviceSocMap = querySocForOnlineDevices(onlineDeviceList);

		// 没有首次安装时间就是未知
		onlineDeviceList.stream()
				.filter(i -> i.getFirstInstall() == 0)
				.forEach(i -> i.setState(DeviceStatusEnum.UNKNOWN.getDbCode()));
		// 储能机查询sys_run_mode状态
		onlineDeviceList.stream()
				.filter(i -> i.getFirstInstall() != 0)
				.forEach(i -> {
					updateStorageState(i);
					// 设置SOC
					i.setBatSoc(deviceSocMap.get(i.getDeviceName()));
					deviceListService.updateById(i);
				});
	}

	private Map<String, BigDecimal> querySocForOnlineDevices(List<DeviceListDO> onlineDeviceList) {
		Map<String, BigDecimal> deviceSocMap = new HashMap<>();

		for (DeviceListDO deviceListDO : onlineDeviceList) {
			try {
				TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
						.chooseTimeSeriesDatabaseService(deviceListDO);

				// 获取最近点位数据
				Dict lastPointDict = timeSeriesDatabaseService.lastDataPoint(
						deviceListDO.getDeviceSn(),
						new LinkedList<>(ListUtil.of(TsdbMetricsConstants.BAT_SOC)),
						DateUtil.currentSeconds()
				);

				// 检查数据完整性
				if (lastPointDict.values().stream().anyMatch(Objects::isNull)) {
					log.warn("设备 {} SOC数据存在Null: {}", deviceListDO.getDeviceName(), JSONUtil.toJsonStr(lastPointDict));
					deviceSocMap.put(deviceListDO.getDeviceName(), null);
					continue;
				}

				String batSoc = lastPointDict.getStr(TsdbMetricsConstants.BAT_SOC);
				if (StrUtil.isNotBlank(batSoc)) {
					BigDecimal soc = NumberUtil.div(new BigDecimal(batSoc), new BigDecimal("100"), 2, RoundingMode.HALF_UP);
					deviceSocMap.put(deviceListDO.getDeviceName(), soc);
				} else {
					deviceSocMap.put(deviceListDO.getDeviceName(), null);
				}
			} catch (Exception e) {
				log.warn("查询设备 {} SOC失败: {}", deviceListDO.getDeviceName(), e.getMessage());
			}
		}

		return deviceSocMap;
	}

	@XxlJob("refreshSpeedup")
	public void refreshSpeedup() {
		log.info("refreshSpeedup");
		sendSpeedupCommand(specificServElinkService);
		sendSpeedupCommand(specificServTuyaService);
		sendSpeedupCommand(specificServWhService);
	}

	@XxlJob("syncELinkWifiSnWithDeviceSn")
	public void syncELinkWifiSnWithDeviceSn() {
		log.info("syncELinkWifiSnWithDeviceSn");
		List<String> wifiSnList = aliMqttUtil.queryAllOnlineDevice();

		for (String wifiSn : wifiSnList) {
			try {
				String deviceSn = ModbusParseUtil.parseForDeviceInfoTypeEnum(
						DeviceInfoEncodeTypeEnum.ASCII,
						modbusRequestUtil.eLinkPostTransparentRead(wifiSn, 30042, 10, 1)
				);
				if (StrUtil.isBlank(deviceSn)) {
					continue;
				}
				deviceSn = SpecialParseUtil.parseDeviceSn(ImportTypeEnum.storage.getCode(), deviceSn);
				bindDeviceSnWithWifiSn(deviceSn, wifiSn);
			} catch (Exception ignored) {
			}
		}
	}

	private void sendSpeedupCommand(SpecificServService specificServService) {
		List<String> deviceNameList = specificServService.batchGetSpeedUpDeviceList();
		if (CollUtil.isNotEmpty(deviceNameList)) {
			List<DeviceListDO> deviceList = deviceListMapper.selectList(
					Wrappers.<DeviceListDO>lambdaQuery().in(DeviceListDO::getDeviceName, deviceNameList).ge(DeviceListDO::getState, 0));
			List<String> wifiSnList = deviceList.stream().map(DeviceListDO::getWifiSn)
					.filter(StrUtil::isNotBlank).collect(Collectors.toList());
			for (String wifiSn : wifiSnList) {
				threadPoolTaskExecutor.execute(() -> specificServService.sendSpeedupCommand(wifiSn));
			}
		}
	}

	@DSTransactional
	private void bindDeviceSnWithWifiSn(String deviceSn, String wifiSn) {
		if (StrUtil.isBlank(wifiSn.trim()) || StrUtil.isBlank(deviceSn)) {
			return;
		}
		DeviceListDO existDeviceSnDevice = deviceListMapper.selectOne(
				Wrappers.<DeviceListDO>lambdaQuery().eq(DeviceListDO::getDeviceName, deviceSn));
		List<DeviceListDO> haveBindWifiSnDevice = deviceListMapper
				.selectList(Wrappers.<DeviceListDO>lambdaQuery().eq(DeviceListDO::getWifiSn, wifiSn)
						.ne(DeviceListDO::getDeviceName, deviceSn));
		for (DeviceListDO deviceListDO : haveBindWifiSnDevice) {
			deviceListDO.setWifiSn("");
			deviceListMapper.updateById(deviceListDO);
		}
		if (existDeviceSnDevice == null) {
			DeviceListDO deviceListDO = new DeviceListDO();
			deviceListDO.setId(snowFlakeUtil.generateId());
			deviceListDO.setAlias(deviceSn);
			deviceListDO.setDeviceSn(deviceSn);
			deviceListDO.setWifiSn(wifiSn);
			deviceListDO.setDeviceName(deviceSn);
			deviceListDO.setState(0);
			deviceListDO.setDataSource(0);
			deviceListDO.setTsdbSource(2);
			deviceListDO.setLongitude(0D);
			deviceListDO.setLatitude(0D);
			deviceListDO.setVppMode(false);
			deviceListDO.setDeviceModel("");
			deviceListDO.setBrand("");
			deviceListDO.setFactory("");
			deviceListDO.setPowerBoardHardwareVersion("");
			deviceListDO.setDsp1SoftwareVersion("");
			deviceListDO.setDsp2SoftwareVersion("");
			deviceListDO.setEmsSoftwareVersion("");
			deviceListDO.setEmsHardwareVersion("");
			deviceListDO.setWifiHardwareVersion("");
			deviceListDO.setWifiSoftwareVersion("");
			deviceListDO.setBmsGaugeVersion("");
			deviceListDO.setBmsSn("");
			deviceListDO.setBmsVendor("");
			deviceListDO.setBmsSoftwareVersion("");
			deviceListDO.setBmsHardwareVersion("");
			deviceListDO.setArcDspSoftwareVersion("");
			deviceListDO.setArcDspSubVersion("");
			deviceListDO.setArcDspBootLoaderSoftwareVersion("");
			deviceListDO.setCreateTime(LocalDateTime.now());
			deviceListDO.setUpdateTime(LocalDateTime.now());
			deviceListMapper.insert(deviceListDO);
		} else {
			if (!wifiSn.equals(existDeviceSnDevice.getWifiSn())) {
				existDeviceSnDevice.setWifiSn(wifiSn);
			}
			existDeviceSnDevice.setDataSource(0);
			existDeviceSnDevice.setTsdbSource(2);
			deviceListMapper.updateById(existDeviceSnDevice);
		}
	}
}
