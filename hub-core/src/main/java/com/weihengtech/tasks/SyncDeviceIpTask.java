package com.weihengtech.tasks;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.service.other.EcosCountryService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 更新设备IP定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 10:23
 */
@Service
@Slf4j
@ConditionalOnProperty(prefix = "custom.xxl.job", value = "enable", havingValue = "true")
public class SyncDeviceIpTask {

    /** 以数字开头的正则，匹配IP */
    private static final Pattern PATTERN = Pattern.compile("^[0-9].*");

    @Autowired
    private DeviceListService deviceListService;
    @Autowired
    private EcosCountryService ecosCountryService;
    @Autowired
    private StrategyService strategyService;

    @XxlJob("syncDeviceIp")
    public void syncDeviceIp() {
        // 根据条件获取设备列表
        LambdaQueryWrapper<DeviceListDO> wrapper = Wrappers.lambdaQuery();
        wrapper.isNotNull(DeviceListDO::getWifiSn);
        wrapper.ne(DeviceListDO::getWifiSn, StrUtil.EMPTY);
        wrapper.in(DeviceListDO::getDataSource, 0, 1, 3);
        String wifiSnStr = XxlJobHelper.getJobParam();
        if (StrUtil.isNotBlank(wifiSnStr)) {
            String[] wifiSnArr = wifiSnStr.split(Constants.COMMA);
            wrapper.in(DeviceListDO::getWifiSn, Arrays.asList(wifiSnArr));
        }
        List<DeviceListDO> allDeviceList = deviceListService.list(wrapper);
        printLog(String.format("根据条件获取设备总数为：%d", allDeviceList.size()));
        if (CollUtil.isEmpty(allDeviceList)) {
            printLog("获取设备为空，无需处理");
            return;
        }
        // 已存在IP，不存在IP的设备分开处理
        List<DeviceListDO> existsIpList = allDeviceList.parallelStream()
                .filter(i -> StrUtil.isNotBlank(i.getIp()) && PATTERN.matcher(i.getIp()).matches())
                .collect(Collectors.toList());
        List<DeviceListDO> blankIpList = allDeviceList.parallelStream()
                .filter(i -> StrUtil.isNotBlank(i.getWifiSn()))
                .filter(i -> StrUtil.isBlank(i.getIp()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(existsIpList)) {
            printLog(String.format("共有%d个设备已存在IP", existsIpList.size()));
            updateIp2Country(existsIpList);
        }
        if (CollUtil.isNotEmpty(blankIpList)) {
            printLog(String.format("共有%d个设备IP为空", blankIpList.size()));
            for (DeviceListDO i : blankIpList) {
                SpecificServService specificServService = strategyService.chooseSpecificServ(i);
                String ip = specificServService.getDeviceIpByWifiSn(i.getWifiSn());
                if (StrUtil.isBlank(ip)) {
                    printLog(String.format("根据WiFi SN获取设备IP为空：%s", i.getWifiSn()));
                    continue;
                }
                i.setIp(ip);
            }
            updateIp2Country(blankIpList);
        }
    }

    /**
     * 更新设备的IP为国家
     *
     * @param deviceList 设备列表
     */
    private void updateIp2Country(List<DeviceListDO> deviceList) {
        List<String> ipList = deviceList.stream()
                .map(DeviceListDO::getIp)
                .filter(StrUtil :: isNotBlank)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(ipList)) {
            printLog("没有需要更新IP的设备，无需处理");
            return;
        }
        Map<String, String> ipMap = ecosCountryService.queryIpList(ipList);
        if (CollUtil.isEmpty(ipMap)) {
            printLog(String.format("根据IP获取国家为空，请确认：%s", ipList));
            return;
        }
        List<DeviceListDO> updList = deviceList.stream()
                .filter(i -> ipMap.containsKey(i.getIp()))
                .map(i -> {
                    DeviceListDO upd = new DeviceListDO();
                    upd.setId(i.getId());
                    upd.setIp(ipMap.get(i.getIp()));
                    return upd;
                })
                .collect(Collectors.toList());
        deviceListService.updateBatchById(updList);
        printLog(String.format("已更新%d个设备的IP为国家", ipMap.size()));
    }

    /** 打印日志 */
    private void printLog(String msg, Exception... e) {
        if (e == null) {
            log.info(msg);
            XxlJobHelper.log(msg);
        }else {
            log.error(msg, e);
            XxlJobHelper.log(msg, e);
        }
    }
}
