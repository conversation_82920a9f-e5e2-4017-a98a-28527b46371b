package com.weihengtech.prometheus;

import com.weihengtech.utils.InitUtil;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.MeterBinder;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.concurrent.DelayQueue;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/22 18:46
 */
@Component
public class UpgradeQueueMetrics implements MeterBinder {

    @Override
    public void bindTo(@NotNull MeterRegistry registry) {
        Gauge.builder("delay.queue.size", InitUtil.DELAY_QUEUE, DelayQueue::size)
                .description("当前延迟队列元素数量")
                .baseUnit("elements")
                .register(registry);
    }

}